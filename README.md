# Project Overview

This repository serves as a monorepo containing multiple applications and services. It is managed using Turborepo.  
Look at the [Turborepo documentation](https://turbo.build/) to learn more.

---

## What's inside?

This Turborepo includes the following packages/apps:

### Apps and Packages

- `dsf-int`: a website built with [Nuxt 3](https://nuxt.com/)
- `email-template`: a project created using [Maizzle](https://maizzle.com/) for generating email templates.
- `supabase-hrl-dev`: a project contains the backend logic and database migrations.
  For more details, check the [supabase-hrl-dev README](./apps/supabase-hrl-dev/README.md).
- `ui`: a stub Vue component library shared across the applications.
- `eslint-config-custom`: `eslint` configurations (includes `@nuxtjs/eslint-config-typescript` and `@vue/eslint-config-typescript`).
- `tsconfig`: `tsconfig.json`s used throughout the monorepo.

## OpenAPI Documentation

[See OpenAPI Documentation](./docs/README.md) to how to run

## Environment Variables

Some applications in this monorepo require environment variables for proper functionality. The `.env.example` files can be found in their respective directories. The variables are securely stored in the team Bitwarden vault.

### Steps to Access Environment Variables

1. Check the `.env.example` file inside the app's directory (if available) to see the list of required environment variable keys.
   - Example: `apps/dsf-int/.env.example`
2. Log in to the [Bitwarden Vault](https://vault.bitwarden.com/) using your credentials.
3. For `dsf-int`, navigate to the **Staging CoinConsole Environment** collection in Bitwarden to find the required environment variables.
4. Download or copy the required `.env` values for the app you are working on and place them in the corresponding directory.

### Note

- If you don’t have access to the Bitwarden vault, please contact the admin for assistance.

---

## Quick Start

To get started with the repository, follow these steps:

### Prerequisites

Ensure you have the following tools installed:

- [Node.js](https://nodejs.org/) (version 20.x recommended)

### Scripts

- Install all dependencies for the monorepo.
  ```bash
  pnpm install
  ```
- Start the development servers for all applications.
  ```bash
  pnpm dev
  ```
  Use `pnpm --filter` to start the development server for a specific application.
  ```bash
  pnpm --filter <app-name> dev
  ```
  Example:
  ```bash
  pnpm --filter @dsf/int dev
  ```
- Run linting.
  ```bash
  pnpm lint
  ```
- Run typecheck.
  ```bash
  pnpm typecheck
  ```
- Run prettier.
  ```bash
  pnpm format
  ```

---

## Manage Dependencies

### Common Scripts

- Adding Dependency to a specific app or package.

  ```bash
  pnpm --filter <app-or-package-name> add <dependency-name>
  ```

  Example:

  ```bash
  pnpm --filter @dsf/int add axios
  ```

- Removing Dependency from a specific app or package.

  ```bash
  pnpm --filter <app-or-package-name> remove <dependency-name>
  ```

  Example:

  ```bash
  pnpm --filter @dsf/int remove axios
  ```

### Note

The `<app-or-package-name>` in the commands refers to the `name` property in the `package.json` of each app or package.

For more details about managing dependencies in Turborepo, check the [Turborepo Dependency Management Documentation](https://turbo.build/repo/docs/crafting-your-repository/managing-dependencies).
