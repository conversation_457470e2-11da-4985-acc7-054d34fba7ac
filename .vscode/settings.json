{"typescript.tsdk": "node_modules/typescript/lib", "mdx.experimentalLanguageServer": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.tabSize": 2, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "files.associations": {"*.css": "tailwindcss"}, "editor.quickSuggestions": {"strings": true}, "tailwindCSS.experimental.configFile": "tailwind.config.ts", "tailwindCSS.config": "./apps/dsf-int/tailwind.config.ts", "deno.enable": true}