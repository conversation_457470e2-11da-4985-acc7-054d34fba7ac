{"name": "dsf-monorepo", "private": true, "scripts": {"build": "nx run-many --target=build --all", "dev": "nx run-many --target=dev --all", "test": "nx run-many --target=test --all", "lint": "nx run-many --target=lint --all", "typecheck": "nx run-many --target=typecheck --all", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "affected:build": "nx affected --target=build", "affected:test": "nx affected --target=test"}, "devDependencies": {"@nx/eslint-plugin": "^20.8.0", "@nx/nuxt": "20.8.0", "@nx/vite": "20.8.0", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-unicorn": "^50.0.1", "lefthook": "^1.11.10", "nuxt": "^3.10.0", "nx": "20.8.0", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.11", "tsconfig": "workspace:*", "tslib": "^2.3.0", "typescript": "~5.7.2", "typescript-eslint": "^8.19.0"}, "packageManager": "pnpm@10.8.1", "workspaces": ["apps/*", "packages/*"], "dependencies": {"@vue-flow/core": "^1.41.2", "vue-organization-chart": "^1.1.6"}}