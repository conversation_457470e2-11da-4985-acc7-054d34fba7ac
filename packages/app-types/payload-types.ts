/* eslint-disable */
// @ts-nocheck
/**
 * This file was automatically generated by Payload.
 * DO NOT MODIFY IT BY HAND. Instead, modify your source Payload config,
 * and re-run `payload generate:types` to regenerate this file.
 */

export interface Config {
  collections: {
    users: User
    pages: Page
    faqs: Faq
    'header-pages': HeaderPage
    media: Media
    'climage-comparisons': ClimageComparison
    'cookies-content': CookiesContent
    'payload-preferences': PayloadPreference
    'payload-migrations': PayloadMigration
  }
  globals: {}
}
export interface User {
  id: string
  updatedAt: string
  createdAt: string
  email: string
  resetPasswordToken?: string | null
  resetPasswordExpiration?: string | null
  salt?: string | null
  hash?: string | null
  loginAttempts?: number | null
  lockUntil?: string | null
  password: string | null
}
export interface Page {
  id: string
  title: string
  content: string
  updatedAt: string
  createdAt: string
}
export interface Faq {
  id: string
  question: string
  answerHtml: string
  answer: {
    [k: string]: unknown
  }[]
  enabled: boolean
  position: number
  category: 'general' | 'community-indices' | 'financial-indices' | 'climate-indices'
  updatedAt: string
  createdAt: string
}
export interface HeaderPage {
  id: string
  title: string
  subtitle?: string | null
  updatedAt: string
  createdAt: string
}
export interface Media {
  id: string
  alt?: string | null
  updatedAt: string
  createdAt: string
  url?: string | null
  filename?: string | null
  mimeType?: string | null
  filesize?: number | null
  width?: number | null
  height?: number | null
  sizes?: {
    thumbnail?: {
      url?: string | null
      width?: number | null
      height?: number | null
      mimeType?: string | null
      filesize?: number | null
      filename?: string | null
    }
    card?: {
      url?: string | null
      width?: number | null
      height?: number | null
      mimeType?: string | null
      filesize?: number | null
      filename?: string | null
    }
    tablet?: {
      url?: string | null
      width?: number | null
      height?: number | null
      mimeType?: string | null
      filesize?: number | null
      filename?: string | null
    }
  }
}
export interface ClimageComparison {
  id: string
  position: number
  logo: string | Media
  logo_light: string | Media
  title: string
  l1_pow?: string | null
  l1_pos?: string | null
  l1_others?: string | null
  aggregate_figures?: boolean | null
  per_tx_figures?: boolean | null
  per_node_figures?: boolean | null
  upper_lower_bounds?: boolean | null
  category: 'community-indices' | 'financial-indices' | 'climate-indices'
  updatedAt: string
  createdAt: string
}
export interface CookiesContent {
  id: string
  description: {
    [k: string]: unknown
  }[]
  strict_cookies: {
    [k: string]: unknown
  }[]
  analytical_cookies: {
    [k: string]: unknown
  }[]
  functionality_cookies: {
    [k: string]: unknown
  }[]
  targeting_cookies: {
    [k: string]: unknown
  }[]
  updatedAt: string
  createdAt: string
}
export interface PayloadPreference {
  id: string
  user: {
    relationTo: 'users'
    value: string | User
  }
  key?: string | null
  value?:
    | {
        [k: string]: unknown
      }
    | unknown[]
    | string
    | number
    | boolean
    | null
  updatedAt: string
  createdAt: string
}
export interface PayloadMigration {
  id: string
  name?: string | null
  batch?: number | null
  updatedAt: string
  createdAt: string
}

declare module 'payload' {
  export interface GeneratedTypes extends Config {}
}
