## API Documentation

Our nuxt application was have API services to serve all data we need for platforms, currently we're using tRPC make an IO for API transaction but we have complete documentation on OpenAPI format.

### Step to Access API Documentation

All of API documentation was located on `/docs` directory, we will found several file based on API purposes. OpenAPI was standardize format to documenting all REST APIs, we have to many ways to open this documentation.

#### OpenAPI (Swagger) Editor

This tools was VSCode extension to help us managing OpenAPI documentation with some kind integrations like intellisense to autocompletion and validate our documentation.

You can check this tool using this link : https://marketplace.visualstudio.com/items/?itemName=42Crunch.vscode-openapi

#### Online Swagger

This was online editor tools to manage our OpenAPI documentation using own browser, we don't need to install anything in our local device.

You can check this tool using this link : https://editor.swagger.io/

#### Scallar

This was online editor tools to manage our OpenAPI documentation using own browser with modern UI, with scallar we can use this tools with own browser or try to used on desktop application.

You can check this tool using this link : https://docs.scalar.com/swagger-editor

### OpenAPI Version

We're using latest version of OpenAPI `3.1.1`

Please make sure you're using same version between your tools and OpenAPI version.
