openapi: '3.1.1'
info:
  title: Nodiens Dashboard
  version: '1.0'
  description: |-
    This was Open API Documentation for Nodiens Dashboard, currently we're implementing API using Rest API & tRPC API.
    This documentation will mainly focus at tRPC API. Please see instructions on [notion page](https://www.notion.so/CoinConsole-Documentation-API-for-App-Layer-10b2c7b693fe80e588b7f5af45d881ca?pvs=4)
servers:
  - url: https://dev--dsf-hrl.netlify.app
  - url: http://localhost:3001
  - url: https://beta.nodiens.com
components:
  parameters:
    AuthCookie:
      name: sb-fpcckoxopdiokehfxcdf-auth-token
      in: cookie
      required: true
      schema:
        type: string
      description: This session was created by backend, so we don't need to add this manually    
tags:
  - name: Home
    description: This API was used for home pages, but API can work without authentication session

  - name: Newsletters
    description: This API was used for managing newsletters session, API can work without authentication session

  - name: Pricings
    description: This API was used for getting pricings plan for platform

  - name: Community - Public
    description: This API was used for community metrics without using authentication session

  - name: Authentication
    description: This API was used for authentication

  - name: Account
    description: This API was used for account functionality

  - name: Subscriptions
    description: This API was used to manage subscriptions

  - name: Community
    description: This API was used for community pages

  - name: ESG
    description: This API was used for climate pages

  - name: Energy Consumption - ESG
    description: This API was used for energy consumption metrics on ESG

  - name: Carbon Emission - ESG
    description: This API was used for carbon emission metrics on ESG

  - name: Financial
    description: This API was used for financial pages

  - name: Topic
    description: This API was used for topic trends

  - name: Decentralisation
    description: This API was used for decentralisation pages

  - name : Asset
    description: ThisAPI was used for listing available asset data

paths:
  /api/trpc/v2.climate.energyConsumption.asset:
    get:
      tags:
        - Energy Consumption - ESG
      summary: Getting available asset for energy consumption
      description: Getting available asset for energy consumption
      parameters:
        - $ref: '#/components/parameters/AuthCookie'
      requestBody:
        content:
          application/json:
            example: {"terms": "btc"}
      responses:
        200:
          description: "Success to pull available asset for energy consumption"
          content:
            application/json:
              example: {"result":{"data": [{"logo":"https://s2.coinmarketcap.com/static/img/coins/64x64/1.png", "symbol": "btc", "slug": "btc-x2x3", "name": "bitcoin"}]}}
  /api/trpc/v2.climate.energyConsumption.list:
    get:
      tags:
        - Energy Consumption - ESG
      summary: Getting latest data of energy consumption
      description: Getting latest data of each asset on energy consumption
      parameters:
        - $ref: '#/components/parameters/AuthCookie'
      requestBody:
        content:
          application/json:
            example: {"layer":["L1","L2"],"type":["Platform","Token"],"algorithm":["PoS","PoSt","PoW","Other"],"page":1,"order":"emission","sort":"desc","terms":"btc"}
      responses:
        200:
          description: 'Success to get latest data on each asset of energy consumption'
          content:
            application/json:
              example: {"result":{"data":{"page":1,"perPage":10,"totalItem":296,"totalPage":30,"data":[{"name":"Bitcoin","asset_type":"Platform","layer_type":"L1","logo":"https://s2.coinmarketcap.com/static/img/coins/64x64/1.png","slug":"bitcoin-nx40tG","consensus_mechanism":["PoW"],"energy_cons":"26.81 GW","energy_cons_tx":"1,073 kWh","energy_cons_tx_node":"0.169 Wh","validators":"21,680","tps":"6.94 tps"}]}}}
  /api/trpc/v2.climate.energyConsumption.chart:
    get:
      tags:
        - Energy Consumption - ESG
      summary: Getting all historical data of energy consumption metrics
      description: Getting all historical data on a asset of energy consumption metric
      parameters:
        - $ref: '#/components/parameters/AuthCookie'
      requestBody:
        content:
          application/json:
            example: {"slug": "litecoin-L7ykcRj", "metric": "emission"}
      responses:
        200:
          description: 'Success to get historical on a asset of energy consumption metric'
          content:
            application/json:
              example: {"result":{"data":{"feeds":{"name":"Litecoin","slug":"litecoin-L7ykcRj","symbol":"LTC","logo":"https://s2.coinmarketcap.com/static/img/coins/64x64/2.png","type":"Platform","layer":"L1"},"available_metric":{"energy_consumption":true,"carbon_emission":true},"group":[],"metric_timeseries":[[1719446400000,{"upper":1688940107.2732234,"avg":411985819.2390605,"lower":211567031.31462717}]],"daily_timeseries":[[1744416000000,{"tps":2.28,"validator":937}]]}}}

  /api/trpc/v2.climate.carbonEmission.asset:
    get:
      tags:
        - Carbon Emission - ESG
      summary: Getting available asset for carbon emission
      description: Getting available asset for carbon emission
      parameters:
        - $ref: '#/components/parameters/AuthCookie'
      requestBody:
        content:
          application/json:
            example: {"terms": "btc"}
      responses:
        200:
          description: "Success to pull available asset for carbon emission"
          content:
            application/json:
              example: {"result":{"data": [{"logo":"https://s2.coinmarketcap.com/static/img/coins/64x64/1.png", "symbol": "btc", "slug": "btc-x2x3", "name": "bitcoin"}]}}

  /api/trpc/v2.climate.carbonEmission.list:
    get:
      tags:
        - Carbon Emission - ESG
      summary: Getting latest data of carbon emission
      description: Getting latest data of each asset on carbon emission
      parameters:
        - $ref: '#/components/parameters/AuthCookie'
      requestBody:
        content:
          application/json:
            example: {"layer":["L1","L2"],"type":["Platform","Token"],"algorithm":["PoS","PoSt","PoW","Other"],"page":1,"order":"emission","sort":"desc","terms":"btc"}
      responses:
        200:
          description: 'Success to get latest data on each asset of carbon emission'
          content:
            application/json:
              example: {"result":{"data":{"data":[{"name":"Bitcoin","logo":"https://s2.coinmarketcap.com/static/img/coins/64x64/1.png","slug":"bitcoin-nx40tG","asset_type":"Platform","layer_type":"L1","consensus_mechanism":["PoW"],"co_emission":"224,873 t","co_emission_tx":"375.09 kg","co_emission_tx_node":"17.30 g","tps":"6.94 tps","validators":"21,680"}],"page":1,"perPage":10,"totalItem":230,"totalPage":23}}}
  /api/trpc/v2.climate.carbonEmission.chart:
    get:
      tags:
        - Carbon Emission - ESG
      summary: Getting all historical data of carbon emission metrics
      description: Getting all historical data on a asset of carbon emission metric
      parameters:
        - $ref: '#/components/parameters/AuthCookie'
      requestBody:
        content:
          application/json:
            example: {"slug": "litecoin-L7ykcRj", "metric": "emission"}
      responses:
        200:
          description: 'Success to get historical on a asset of energy consumption metric'
          content:
            application/json:
              examples:
                annualized:
                  description: "Value of annualized metrics"
                  value: {"result":{"data":{"feeds":{"id":62,"name":"Litecoin","slug":"litecoin","symbol":"LTC","logo":"https://s2.coinmarketcap.com/static/img/coins/64x64/2.png","type":"Platform","layer":"L1"},"metric":"emission","entries":{"mode":"annualized","entries":[[1714780800000,{"avg":3176987626.696321,"lower":1385083225.8654382,"top":12458477995.766878,"tps":2.542013888888889,"validators":897}]]},"group":[],"available_climate":{"carbon_emission":true,"energy_consumption":true}}}}
                energy share:
                  description: 'Value of energy shared'
                  value: {"result":{"data":{"feeds":{"id":2,"name":"Bitcoin","slug":"bitcoin","symbol":"BTC","logo":"https://assets.coingecko.com/asset_platforms/images/127/small/ordinals.png?1706606816","type":"Platform","layer":"L1"},"metric":"source","entries":{"mode":"source","entries":[{"timeseries":[[1312156800000,4027.751590566486]],"type":"Bioenergy"},{"timeseries":[[1312156800000,8262219.968994093]],"type":"Coal"},{"timeseries":[[1312156800000,826628.8981021377]],"type":"Gas"},{"timeseries":[[1312156800000,68241.31439530222]],"type":"Hydro"},{"timeseries":[[1312156800000,7524.683750987486]],"type":"Nuclear"},{"timeseries":[[1312156800000,238165.24589744752]],"type":"Other Fossil"},{"timeseries":[[1312156800000,321.14589364962467]],"type":"Other Renewables"},{"timeseries":[[1312156800000,789.874293531754]],"type":"Solar"},{"timeseries":[[1312156800000,3118.377673526431]],"type":"Wind"},{"timeseries":[[1514764800000,-117386.495904096]],"type":"Flared"},{"timeseries":[[1561939200000,-39948.397682400006]],"type":"Vented"}]},"group":["PoW"],"available_climate":{"carbon_emission":true,"energy_consumption":true}}}}
  /api/trpc/v2.user.topic.chart:
    get:
      tags:
        - Topic
      summary: Get topic trends data
      description: Get topic trends data for showing topic occurrences for standalone chart
      parameters:
        - $ref: '#/components/parameters/AuthCookie'
      requestBody:
        content:
          schema:
            example: 'top'
      responses:
        200:
          description: 'Success to pull all topic occurrences'
          content:
            application/json:
              example: {"result":{"data":{"name":"top","available_platform":["reddit","telegram","twitter"],"timestamp":[{"all": [5912049210,9],  "reddit":[5912049210,3],"telegram":[5912049210,3],"twitter":[5912049210,3]}],"start_calculation":9120941,"latest_calculation":108250182501}}}
  /api/trpc/v2.user.subscription.currentSubscription:
    get:
      tags:
        - Subscriptions
      summary: Get current subscription
      description: Get current subscriptiopns of user
      parameters:
        - $ref: '#/components/parameters/AuthCookie'
      responses:
        200:
          description: "Success to getting current subscription"
          content:
            application/json:
              example: {"result":{"data":{"id":2,"user_id":"439acecf-6d24-4692-8672-2db65acd8802","plan_id":"4","plan_name":"ENTERPRISE","status":"active","expired_date":"2025-04-23T12:35:31.761+00:00","created_at":"2025-03-24T12:35:31.739196+00:00","raw_data":{"is_annual":false,"product_id":4,"session_id":"cs_live_b11v5oB419L9W9BowDvGcm7NBuTzhTfkn7M1MiLHsNSNVtHeeL2HjUggxA","customer_id":"cus_S0AWAnYx8h8150","addon_enable":false,"require_addon":false,"subscription_id":"sub_1R6ADuK18mBxZPs69yz2S8Fd","last_event_creation":1742819575},"active_at":"2025-03-24T12:35:31.761+00:00","payment_status":"paid","switch_raw_data":null,"plan":{"id":4,"name":"ENTERPRISE","description":"  For large organizations","monthly_price":1190,"annual_price":949,"stripe_price_ids":{"annual":"price_1QtQn6K18mBxZPs6uN69aNK5","monthly":"price_1QtQn6K18mBxZPs6bCBlDBtb","product_id":"prod_Rn0o2YoAmMeyzy"},"features":{"max_assets":10,"add_on_price":99,"financial_metrics":{"price":true,"cex_pairs":true,"dex_pairs":true,"depeg_rata":true,"market_cap":true,"spider_chart":true,"total_volume":true,"cex_volume_24h":true,"dex_volume_24h":true,"cex_liquidity_cost":true,"dex_liquidity_cost":true,"volatility_30_days":true,"cex_liquidity_concentration":true,"dex_liquidity_concentration":true},"sentiment_metrics":{"messages":true,"scam_index":"coming soon","bots_tracker":true,"mood_ranking":true,"topic_trends":true,"trust_ranking":true,"community_size":true,"vulgarity_index":true,"mood_index_level_1":true,"mood_index_level_2":true,"mood_index_level_3":true,"trust_index_level_1":true,"trust_index_level_2":true,"trust_index_level_3":true},"esg_climate_metrics":{"power_use":true,"governance":"coming soon","throughput":true,"validators":true,"social_impact":"coming soon","energy_cons_tx":true,"land_footprint":"coming soon","waste_footprint":"coming soon","water_footprint":"coming soon","decentralisation":true,"energy_cons_tx_node":true}},"created_at":"2025-02-07T08:33:21.000Z","is_enabled":true,"level":4},"features":{"max_assets":10,"add_on_price":99,"financial_metrics":{"price":true,"cex_pairs":true,"dex_pairs":true,"depeg_rata":true,"market_cap":true,"spider_chart":true,"total_volume":true,"cex_volume_24h":true,"dex_volume_24h":true,"cex_liquidity_cost":true,"dex_liquidity_cost":true,"volatility_30_days":true,"cex_liquidity_concentration":true,"dex_liquidity_concentration":true},"sentiment_metrics":{"messages":true,"scam_index":"coming soon","bots_tracker":true,"mood_ranking":true,"topic_trends":true,"trust_ranking":true,"community_size":true,"vulgarity_index":true,"mood_index_level_1":true,"mood_index_level_2":true,"mood_index_level_3":true,"trust_index_level_1":true,"trust_index_level_2":true,"trust_index_level_3":true},"esg_climate_metrics":{"power_use":true,"governance":"coming soon","throughput":true,"validators":true,"social_impact":"coming soon","energy_cons_tx":true,"land_footprint":"coming soon","waste_footprint":"coming soon","water_footprint":"coming soon","decentralisation":true,"energy_cons_tx_node":true}}}}}
  /api/trpc/v2.user.subscription.manage:
    post:
      tags:
        - Subscriptions
      summary: Generate stripe manager link
      description: Generate stripe link for managing subscription
      parameters:
        - $ref: "#/components/parameters/AuthCookie"
      responses:
        200:
          description: "Success to generate stripe subscription management link"
          content:
            application/json:
              example: {"result": { "data": { "customer": "cus_d12d1", "return_url": "https://billing.nodiens.com/d91k9dk1" } }}
  /api/trpc/v2.user.subscription.subscribe:
    post:
      tags:
        - Subscriptions
      summary: Generate stripe billing portal link
      description: Generate stripe billing portal for subscribing platform plans on stripe page
      parameters:
        - $ref: "#/components/parameters/AuthCookie"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                product_id:
                  type: number
                  example: 1
                is_annual:
                  type: boolean
                  example: true
                asset_id:
                  type: array
                  example: [1, 2, 3]
      responses:
        200:
          description: "Success to generate billing portal link"
          content:
            application/json:
              example: {"result": { "data": { "checkout_url": "https://billing.nodiens.com/d91k9dk1" } }}
  /api/trpc/v2.user.subscription.switchPlan:
    post:
      tags:
        - Subscriptions
      summary: Switch subscription plan
      description: Generate stripe billing portal for switching platform plan on stripe billing portal
      parameters:
        - $ref: '#/components/parameters/AuthCookie'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                product_id:
                  type: number
                  example: 1
                is_annual:
                  type: boolean
                  example: true
                asset_id:
                  type: array
                  example: [1, 2, 3]
      responses:
        200:
          description: 'Success to generate checkout url'
          content:
            application/json:
              schema:
                type: object
                properties:
                  upgrade_url:
                    type: string
                    example: https://billing.nodiens.com/9d1k292dk219
  /api/trpc/v2.user.subscription.billingHistory:
    get:
      tags:
        - Subscriptions
      summary: Getting billing history
      parameters:
        - $ref: '#/components/parameters/AuthCookie'
      description: Getting history of users billing based on subscriptions
      responses:
        200:
          description: 'Success to pull billing history'
          content:
            application/json:
              example: {"result":{"data":[{"id":"sub_1R7f7nK18mBxZPs6Ok26JcJo","description":"Nodiens PAY AS YOU GO (Monthly)","amount":"9.90/Monthly","date":1743180199000,"invoice_url":"https://pay.stripe.com/receipts/invoices/CAcaFwoVYWNjdF8xUWtTdFBLMThtQnhaUHM2KMXi-L8GMgZxUqHa8AI6LBYQr69Te-iaEN8ilzp5gkjyyIWzBN2UKEr1PLXxCKzriwEbmuz9_SQhb-4W?s=ap"},{"id":"sub_1R7f7nK18mBxZPs6Ok26JcJo","description":"Nodiens ENTERPRISE (Annual)","amount":"8988.00/Annual","date":1743176838000,"invoice_url":"https://pay.stripe.com/receipts/invoices/CAcaFwoVYWNjdF8xUWtTdFBLMThtQnhaUHM2KMXi-L8GMgaby-egatU6LBah6IqiZEJ_LpUlEKzKAUbf251E_HNXKj32TYwg2NxSffzIhsoDLzD9WieO?s=ap"}]}}
  /api/trpc/v2.public.pricings.getPricing:
    get:
      tags:
        - Pricings
      summary: Get pricing plans
      description: This API was used to getting current pricing plans for platform
      responses:
        200:
          description: "Success to getting pricings data"
          content:
            application/json:
              example: { "result": { "data": [ { "id": 7, "name": "PAY AS YOU GO", "description": "-", "monthly_price": 9.9, "annual_price": 7.9, "features": { "max_assets": 1, "add_on_price": 9.9, "financial_metrics": { "price": true, "cex_pairs": false, "dex_pairs": false, "depeg_rata": false, "market_cap": true, "spider_chart": false, "total_volume": true, "cex_volume_24h": false, "dex_volume_24h": false, "cex_liquidity_cost": false, "dex_liquidity_cost": false, "volatility_30_days": false, "cex_liquidity_concentration": false, "dex_liquidity_concentration": false }, "sentiment_metrics": { "messages": true, "scam_index": "coming soon", "bots_tracker": true, "mood_ranking": true, "topic_trends": false, "trust_ranking": true, "community_size": true, "asset_available": "up to 10 included in the plan", "vulgarity_index": true, "mood_index_level_1": true, "mood_index_level_2": false, "mood_index_level_3": false, "trust_index_level_1": true, "trust_index_level_2": false, "trust_index_level_3": false }, "esg_climate_metrics": { "power_use": false, "governance": false, "throughput": false, "validators": false, "social_impact": false, "energy_cons_tx": false, "land_footprint": false, "asset_available": "up to 10 included in the plan", "waste_footprint": false, "water_footprint": false, "decentralisation": false, "energy_cons_tx_node": false } }, "created_at": "2025-02-24T13:16:10+00:00", "is_enabled": true, "level": 1 }, { "id": 1, "name": "STARTER", "description": "For individuals/small teams", "monthly_price": 49, "annual_price": 39, "features": { "max_assets": 10, "add_on_price": 4.9, "financial_metrics": { "price": true, "cex_pairs": false, "dex_pairs": false, "depeg_rata": false, "market_cap": true, "spider_chart": false, "total_volume": true, "cex_volume_24h": false, "dex_volume_24h": false, "cex_liquidity_cost": false, "dex_liquidity_cost": false, "volatility_30_days": false, "cex_liquidity_concentration": false, "dex_liquidity_concentration": false }, "sentiment_metrics": { "messages": true, "scam_index": "coming soon", "bots_tracker": true, "mood_ranking": true, "topic_trends": false, "trust_ranking": true, "community_size": true, "asset_available": "up to 10 included in the plan", "vulgarity_index": true, "mood_index_level_1": true, "mood_index_level_2": false, "mood_index_level_3": false, "trust_index_level_1": true, "trust_index_level_2": false, "trust_index_level_3": false }, "esg_climate_metrics": { "power_use": false, "governance": false, "throughput": false, "validators": false, "social_impact": false, "energy_cons_tx": false, "land_footprint": false, "asset_available": "up to 10 included in the plan", "waste_footprint": false, "water_footprint": false, "decentralisation": false, "energy_cons_tx_node": false } }, "created_at": "2025-01-28T10:09:54.374428+00:00", "is_enabled": false, "level": 2 }, { "id": 2, "name": "PRO", "description": "For growing businesses", "monthly_price": 690, "annual_price": 490, "features": { "max_assets": 10, "add_on_price": 69, "financial_metrics": { "price": true, "cex_pairs": true, "dex_pairs": true, "depeg_rata": true, "market_cap": true, "spider_chart": true, "total_volume": true, "cex_volume_24h": true, "dex_volume_24h": true, "cex_liquidity_cost": true, "dex_liquidity_cost": true, "volatility_30_days": true, "cex_liquidity_concentration": true, "dex_liquidity_concentration": true }, "sentiment_metrics": { "messages": true, "scam_index": "coming soon", "bots_tracker": true, "mood_ranking": true, "topic_trends": false, "trust_ranking": true, "community_size": true, "asset_available": "up to 10 included in the plan", "vulgarity_index": true, "mood_index_level_1": true, "mood_index_level_2": true, "mood_index_level_3": false, "trust_index_level_1": true, "trust_index_level_2": true, "trust_index_level_3": false }, "esg_climate_metrics": { "power_use": false, "governance": false, "throughput": false, "validators": false, "social_impact": false, "energy_cons_tx": false, "land_footprint": false, "asset_available": "up to 10 included in the plan", "waste_footprint": false, "water_footprint": false, "decentralisation": false, "energy_cons_tx_node": false } }, "created_at": "2025-01-28T10:09:54.374428+00:00", "is_enabled": false, "level": 3 }, { "id": 4, "name": "ENTERPRISE", "description": "  For large organizations", "monthly_price": 990, "annual_price": 749, "features": { "max_assets": 10, "add_on_price": 99, "financial_metrics": { "price": true, "cex_pairs": true, "dex_pairs": true, "depeg_rata": true, "market_cap": true, "spider_chart": true, "total_volume": true, "cex_volume_24h": true, "dex_volume_24h": true, "cex_liquidity_cost": true, "dex_liquidity_cost": true, "volatility_30_days": true, "cex_liquidity_concentration": true, "dex_liquidity_concentration": true }, "sentiment_metrics": { "messages": true, "scam_index": "coming soon", "bots_tracker": true, "mood_ranking": true, "topic_trends": true, "trust_ranking": true, "community_size": true, "vulgarity_index": true, "mood_index_level_1": true, "mood_index_level_2": true, "mood_index_level_3": true, "trust_index_level_1": true, "trust_index_level_2": true, "trust_index_level_3": true }, "esg_climate_metrics": { "power_use": true, "governance": "coming soon", "throughput": true, "validators": true, "social_impact": "coming soon", "energy_cons_tx": true, "land_footprint": "coming soon", "waste_footprint": "coming soon", "water_footprint": "coming soon", "decentralisation": true, "energy_cons_tx_node": true } }, "created_at": "2025-02-07T08:33:21.479173+00:00", "is_enabled": false, "level": 4 } ] } }
  /api/trpc/v2.public.newsletter.subscribeEmail:
    post:
      tags:
        - Newsletters
      summary: Subscribing newsletters
      description: This API was used to subscribing newsletters using registered email, email will attached to existing session if user has login into platform
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
      responses:
        200:
          description: "Success to subscribing newsletters"
          content:
            application/json:
              example:
                {"result":{"data":{"message":"<NAME_EMAIL> to newsletters","data":[{"id":1,"created_at":"2024-06-27T05:13:59.2918+00:00","user_id":null,"email":"<EMAIL>","subscriptions":{}}]}}}
  /api/trpc/v2.public.newsletter.setSubscription:
    post:
      tags: 
        - Newsletters
      summary: Personalized Newsletters
      description: This API was used to personalized registered newsletters for remove subscriptions or manage subscriptions.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                uuid:
                  type: string
                subscription:
                  type: boolean
      responses:
        200:
          description: "Success to personalized newsletters data"
          content:
            application/json:
              example: {"result": { "data": true }}
  /api/trpc/v2.public.newsletter.getSubscription:
    get:
      tags:
        - Newsletters
      summary: Get registered newsletters
      description: This API was used to getting registered newsletters
      requestBody:
        required: true
        content:
          application/json:
            schema:
              properties:
                uuid:
                  type: string
      responses:
        200:
          description: "Success to getting newsletters data"
          content:
            application/json:
              example: {"result": { "data": { "subscribe": {"created_at":"2024-06-27T05:13:59.2918", "email": "<EMAIL>", "id": 1, "unsubscribe": true, "user_id": "uuid-example-dkqkwqd", "uuid": "uuid-9d1d91k-kcskja" }, "uuid": "uuid-9d1d91k-kcskja" } }}
  /api/trpc/v2.user.esg.table:
    get:
      tags:
        - ESG
      summary: Load climate indices
      parameters:
        - $ref: '#/components/parameters/AuthCookie'      
      description: This API was used to getting climate indices into served table
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                page:
                  type: number
                  description: Page number
                perPage:
                  type: number
                  description: Page size
                sort:
                  type: array
                  description: For sorting indices data
                  minimum: 2
                  maximum: 2
                  enum:
                    - name
                    - energy_tx
                    - energy_tx_node
                    - validators
                    - tps
                    - power_cons
                    - asc
                    - desc
                  items:
                    example: ['name', 'desc']
      responses:
        200:
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      data:
                        type: object
                        properties:
                          data:
                            type: array
                            items:
                              type: object
                              properties:
                                kwh:
                                  type: string
                                wh_transaction:
                                  type: string
                                wh_node_trx:
                                  type: string
                                validator:
                                  type: string
                                tps:
                                  type: string
                                slug:
                                  type: string
                                name:
                                  type: string
                                logo:
                                  type: string
                                symbol:
                                  type: string
                          pagination:
                            type: object
                            properties:
                              current:
                                type: number
                              last_page:
                                type: number
                              per_page:
                                type: number
                              total_item:
                                type: number
  /api/trpc/v2.user.esg.chart:
    get:
      tags:
        - ESG
      summary: Load climate indices chart
      parameters:
        - $ref: '#/components/parameters/AuthCookie'            
      description: This API was used to getting climate indices into served chart, this will require slug assets for request body
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: string
            example: 'bitcoin'
      responses:
        default:
          description: Success
          content:
            application/json:
              example: {"result":{"data":{"info":{"name":"Bitcoin","slug":"bitcoin","logo":"https://s2.coinmarketcap.com/static/img/coins/64x64/1.png","symbol":"BTC"},"kwh":[[1696896000000,{"min":10063062.868525185,"max":43461733.31921176,"avg":16121276.85038313}]],"validator":[[1696896000000,468049435]],"tps":[[1696896000000,3.1914699074074075]],"wh_trx":[[1696896000000,{"min":875864.5145827978,"max":3782803.5513542765,"avg":1403156.723504115}]],"wh_trx_node":[[1696896000000,{"min":40737.884399199895,"max":40737.8843991999,"avg":40737.884399199895}]]}}}
  /api/trpc/v2.user.esg.assets:
    get:
      tags:
        - ESG
      summary: Load climate assets list
      parameters:
        - $ref: '#/components/parameters/AuthCookie'      
      description: This API was used to getting available climate assets from data sources
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                page:
                  type: integer
                  default: 1
                perPage:
                  type: integer
                  default: 10
                search:
                  type: string
                  default: ''
            example:
              page: 1
              perPage: 10
              search: 'btc'
      responses:
        default:
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      data:
                        type: object
                        properties:
                          data:
                            type: array
                            items:
                              type: object
                              properties:
                                name:
                                  type: string
                                slug:
                                  type: string
                                symbol:
                                  type: string
                                logo:
                                  type: string
                          pagination:
                            type: object
                            properties:
                              current:
                                type: number
                              per_page:
                                type: number
                              total_item:
                                type: number
                              last_page:
                                type: number
  /api/trpc/v2.public.dashboard.table:
    get:
      tags:
        - Home
      summary: Load dashboard table
      description: This API was used to getting dashboard table, but this API can work without authentication as well.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                selected:
                  type: array
                  items:
                    type: string
                    enum:
                      - crypto
                      - stable
                  default:
                    - crypto
                    - stable
                rank:
                  type: string
                  enum:
                    - mood
                    - trust
                    - energy_trx
                    - number_msg
                    - community_size
                  default: mood
                page:
                  type: number
                  default: 1
                perPage:
                  type: number
                  default: 10
                terms:
                  type: string
      responses:
        default:
          description: Success
          content:
            application/json:
              example: {"result":{"data":{"data":[{"name":"Tether USDt","logo":"https://s2.coinmarketcap.com/static/img/coins/64x64/825.png","symbol":"USDT","type":"STABLECOIN","slug":"tether","mood_index":"-","trust_index":"-","energy":"-","community_size":"-","number_of_message":"2","graph":[[1729728000000,88.73155722749044],[1729814400000,88.73155722749044],[1729900800000,88.73155722749044],[1729987200000,88.73155722749044],[1730073600000,88.73155722749044],[1730160000000,88.73155722749044],[1730246400000,88.73155722749044]]}],"pagination":{"current":1,"last_page":0.1,"per_page":1,"total_item":1}}}}
  /api/trpc/v2.public.dashboard.topDaily:
    get:
      tags:
        - Home
      summary: Load dashboard top daily
      description: This API was used to getting dashboard top daily
      responses:
        default:
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      data:
                        type: object
                        properties:
                          last_calculation:
                            type: number
                          mood:
                            type: array
                            items:
                              type: object
                              properties:
                                logo:
                                  type: string
                                value:
                                  type: number
                                rank:
                                  type: number
                                percentage:
                                  type: number
                                name:
                                  type: string
                                indicator:
                                  type: string
                          trust:
                            type: array
                            items:
                              type: object
                              properties:
                                logo:
                                  type: string
                                value:
                                  type: number
                                rank:
                                  type: number
                                percentage:
                                  type: number
                                name:
                                  type: string
                                indicator:
                                  type: string
              example: {"result":{"data":{"last_calculation":1709181530039,"mood":[{"logo":"https://s2.coinmarketcap.com/static/img/coins/64x64/1768.png","value":1.8149148249524645e+52,"rank":1,"percentage":100,"name":"AdEx","indicator":">"},{"logo":"https://s2.coinmarketcap.com/static/img/coins/64x64/3238.png","value":217586978398.10654,"rank":2,"percentage":-0.0938,"name":"ABCC Token","indicator":"<"},{"logo":"https://s2.coinmarketcap.com/static/img/coins/64x64/2132.png","value":50177169289.03952,"rank":3,"percentage":100,"name":"Powerledger","indicator":">"}],"trust":[{"logo":"https://s2.coinmarketcap.com/static/img/coins/64x64/5552.png","value":5.909090408663837e+40,"rank":1,"percentage":100,"name":"Hathor","indicator":">"},{"logo":"https://s2.coinmarketcap.com/static/img/coins/64x64/3951.png","value":24443588846.626907,"rank":2,"percentage":-42.8,"name":"Pirate Chain","indicator":"<"},{"logo":"https://s2.coinmarketcap.com/static/img/coins/64x64/1298.png","value":11434962966.27921,"rank":3,"percentage":-56,"name":"LBRY Credits","indicator":"<"}],"esg":[{"logo":"https://s2.coinmarketcap.com/static/img/coins/64x64/1.png","value":1198338.33173759,"rank":1,"percentage":7.04,"name":"Bitcoin","indicator":">"},{"logo":"https://s2.coinmarketcap.com/static/img/coins/64x64/1831.png","value":5184.327385789486,"rank":2,"percentage":-2603.65,"name":"Bitcoin Cash","indicator":"<"},{"logo":"https://s2.coinmarketcap.com/static/img/coins/64x64/2010.png","value":169.59484086449478,"rank":3,"percentage":-12.55,"name":"Cardano","indicator":"<"}]}}}
  /api/trpc/v2.auth.register:
    post:
      tags:
        - Authentication
      summary: Register new user
      description: This API was used to register new user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - first_name
                - last_name
                - password
                - confirm_password
                - email
              properties:
                first_name:
                  type: string
                last_name:
                  type: string
                password:
                  type: string
                confirm_password:
                  type: string
                email:
                  type: string
                promo:
                  type: string
                company:
                  type: string
      responses:
        200:
          description: Success
          content:
            application/json:
              example: { "result": { "data": { "message": "Registration success, please check your email inbox", "data": { "user": { "id": "d44b455e-d15b-4707-a010-81e4c9021804", "aud": "authenticated", "role": "authenticated", "email": "<EMAIL>", "phone": "", "confirmation_sent_at": "2024-11-01T03:52:12.671498074Z", "app_metadata": { "provider": "email", "providers": [ "email" ] }, "user_metadata": { "company": "DSF", "display_name": "Ambrizal Suryadinata", "email": "<EMAIL>", "email_verified": false, "phone_verified": false, "sub": "d44b455e-d15b-4707-a010-81e4c9021804" }, "identities": [ { "identity_id": "d13b814a-1c11-4327-8d0a-2776b4536cfe", "id": "d44b455e-d15b-4707-a010-81e4c9021804", "user_id": "d44b455e-d15b-4707-a010-81e4c9021804", "identity_data": { "company": "DSF", "display_name": "Ambrizal Suryadinata", "email": "<EMAIL>", "email_verified": false, "phone_verified": false, "sub": "d44b455e-d15b-4707-a010-81e4c9021804" }, "provider": "email", "last_sign_in_at": "2024-11-01T03:52:12.666431754Z", "created_at": "2024-11-01T03:52:12.666481Z", "updated_at": "2024-11-01T03:52:12.666481Z", "email": "<EMAIL>" } ], "created_at": "2024-11-01T03:52:12.660959Z", "updated_at": "2024-11-01T03:52:15.302892Z", "is_anonymous": false } }, "session": null } } }
        422:
          description: Duplicate
          content:
            application/json:
              example: { "error": { "message": "<EMAIL> was already registered", "code": -32022, "data": { "code": "UNPROCESSABLE_CONTENT", "httpStatus": 422, "path": "v2.auth.register" } } }
  /api/trpc/v2.auth.signIn:
    post:
      tags:
        - Authentication
      summary: Sign in user
      description: This API was used to sign in user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
              properties:
                email:
                  type: string
                password:
                  type: string
      responses:
        200:
          description: Success
          content:
            application/json:
              example: 
                result:
                  data:
                    user:
                      id: "d44b455e-d15b-4707-a010-81e4c9021804"
                      aud: "authenticated"
                      role: "authenticated"
                      email: "<EMAIL>"
                      email_confirmed_at: "2024-11-01T04:07:38.223871Z"
                      phone: ""
                      confirmation_sent_at: "2024-11-01T03:52:12.671498Z"
                      confirmed_at: "2024-11-01T04:07:38.223871Z"
                      last_sign_in_at: "2024-11-01T04:07:52.260827291Z"
                      app_metadata:
                        provider: "email"
                        providers:
                          - "email"
                      user_metadata:
                        company: "DSF"
                        display_name: "Ambrizal Suryadinata"
                        email: "<EMAIL>"
                        email_verified: false
                        phone_verified: false
                        sub: "d44b455e-d15b-4707-a010-81e4c9021804"
                      identities:
                        - identity_id: "d13b814a-1c11-4327-8d0a-2776b4536cfe"
                          id: "d44b455e-d15b-4707-a010-81e4c9021804"
                          user_id: "d44b455e-d15b-4707-a010-81e4c9021804"
                          identity_data:
                            company: "DSF"
                            display_name: "Ambrizal Suryadinata"
                            email: "<EMAIL>"
                            email_verified: false
                            phone_verified: false
                            sub: "d44b455e-d15b-4707-a010-81e4c9021804"
                          provider: "email"
                          last_sign_in_at: "2024-11-01T03:52:12.666431Z"
                          created_at: "2024-11-01T03:52:12.666481Z"
                          updated_at: "2024-11-01T03:52:12.666481Z"
                          email: "<EMAIL>"
                      created_at: "2024-11-01T03:52:12.660959Z"
                      updated_at: "2024-11-01T04:07:52.263671Z"
                      is_anonymous: false
                    session:
                      access_token: "eyJhbGciOiJIUzI1NiIsImtpZCI6IkMzQmpYa3NTRU50U2x4UFoiLCJ0eXAiOiJKV1QifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NWnzVLyHVLB_lYKPufCcsnj5VgDpLJrjPzbkmcdwv3Q"
                      token_type: "bearer"
                      expires_in: 86400
                      expires_at: 1730520472
                      refresh_token: "CMzEFlMbnrEsciUYNJJ4Nw"
                      user:
                        id: "d44b455e-d15b-4707-a010-81e4c9021804"
                        aud: "authenticated"
                        role: "authenticated"
                        email: "<EMAIL>"
                        email_confirmed_at: "2024-11-01T04:07:38.223871Z"
                        phone: ""
                        confirmation_sent_at: "2024-11-01T03:52:12.671498Z"
                        confirmed_at: "2024-11-01T04:07:38.223871Z"
                        last_sign_in_at: "2024-11-01T04:07:52.260827291Z"
                        app_metadata:
                          provider: "email"
                          providers:
                            - "email"
                        user_metadata:
                          company: "DSF"
                          display_name: "Ambrizal Suryadinata"
                          email: "<EMAIL>"
                          email_verified: false
                          phone_verified: false
                          sub: "d44b455e-d15b-4707-a010-81e4c9021804"
                        identities:
                          - identity_id: "d13b814a-1c11-4327-8d0a-2776b4536cfe"
                            id: "d44b455e-d15b-4707-a010-81e4c9021804"
                            user_id: "d44b455e-d15b-4707-a010-81e4c9021804"
                            identity_data:
                              company: "DSF"
                              display_name: "Ambrizal Suryadinata"
                              email: "<EMAIL>"
                              email_verified: false
                              phone_verified: false
                              sub: "d44b455e-d15b-4707-a010-81e4c9021804"
                            provider: "email"
                            last_sign_in_at: "2024-11-01T03:52:12.666431Z"
                            created_at: "2024-11-01T03:52:12.666481Z"
                            updated_at: "2024-11-01T03:52:12.666481Z"
                            email: "<EMAIL>"
                        created_at: "2024-11-01T03:52:12.660959Z"
                        updated_at: "2024-11-01T04:07:52.263671Z"
                        is_anonymous: false
  /api/trpc/v2.auth.signOut:
    post:
      description: Sign out
      summary: Sign out
      tags:
        - Authentication
      responses:
        200:
          description: Success
          content:
            application/json:
              example: {"result":{"data":true}}
  /api/trpc/v2.auth.setPassword:
    post:
      description: Set password for user when user was requesting forgot password
      summary: Set password
      tags:
        - Authentication
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                accessToken:
                  type: string
                refreshToken:
                  type: string
                password:
                  type: string
                confirmPassword:
                  type: string
      responses:
        200:
          description: Success
          content:
            application/json:
              example: {"result":{"data":{"data":{"user":{"id":"d44b455e-d15b-4707-a010-81e4c9021804","aud":"authenticated","role":"authenticated","email":"<EMAIL>","email_confirmed_at":"2024-11-01T04:07:38.223871Z","phone":"","confirmed_at":"2024-11-01T04:07:38.223871Z","last_sign_in_at":"2024-11-01T04:25:19.960631Z","app_metadata":{"provider":"email","providers":["email"]},"user_metadata":{"company":"DSF","display_name":"Ambrizal Suryadinata","email":"<EMAIL>","email_verified":false,"phone_verified":false,"sub":"d44b455e-d15b-4707-a010-81e4c9021804"},"identities":[{"identity_id":"d13b814a-1c11-4327-8d0a-2776b4536cfe","id":"d44b455e-d15b-4707-a010-81e4c9021804","user_id":"d44b455e-d15b-4707-a010-81e4c9021804","identity_data":{"company":"DSF","display_name":"Ambrizal Suryadinata","email":"<EMAIL>","email_verified":false,"phone_verified":false,"sub":"d44b455e-d15b-4707-a010-81e4c9021804"},"provider":"email","last_sign_in_at":"2024-11-01T03:52:12.666431Z","created_at":"2024-11-01T03:52:12.666481Z","updated_at":"2024-11-01T03:52:12.666481Z","email":"<EMAIL>"}],"created_at":"2024-11-01T03:52:12.660959Z","updated_at":"2024-11-01T04:25:32.170197Z","is_anonymous":false}},"error":null}}}
  /api/trpc/v2.user.indices.table:
    get:
      tags:
        - Community
      summary: Get community indices table
      parameters:
        - $ref: '#/components/parameters/AuthCookie'        
      description: Get community indices table
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                selector:
                  type: string
                  enum:
                    - mood
                    - trust
                    - topic_analytic
                  default: mood
                rank:
                  type: string
                  enum:
                    - community
                    - message
                    - indexes
                    - changed
                  default: community
                type:
                  type: array
                  items:
                    type: string
                    enum:
                      - crypto
                      - stable
                  default:
                    - crypto
                    - stable
                page:
                  type: integer
                  default: 1
                perPage:
                  type: integer
                  default: 10
                terms:
                  type: string
      responses:
        200:
          description: Community Indices Response
          content:
            application/json (Mood & Trust):
              example: {"result":{"data":{"indice":[{"changed":"1.36%","changed_symbol":">","chart":[[1729814400000,14075.332021816845],[1729900800000,14207.548806692563],[1729987200000,14301.123010802556],[1730073600000,14381.560330522081],[1730160000000,14433.117431338242],[1730246400000,14595.231293878674],[*************,14793.157041706916]],"community_size":"69,026","indexes":"14,793","name":"Solana","number_of_message":"11,573","type":"CRYPTOCURRENCY","logo":"https://s2.coinmarketcap.com/static/img/coins/64x64/5426.png","slug":"solana","symbol":"SOL"}],"topic_analytic":null,"pagination":{"current":1,"last_page":406.6,"per_page":10,"total_item":100}}}}
            application/json (Topic Analytic):
              example: {"result":{"data":{"indice":null,"topic_analytic":[{"name":"pro","chart":[[1729814400000,0],[1729900800000,0],[1729987200000,2612],[1730073600000,1725],[1730160000000,2051],[1730246400000,0],[*************,2527],[1730419200000,3183]],"occurrences":"3,183","platform":[{"name":"TELEGRAM","value":3183},{"name":"REDDIT","value":0}]}],"pagination":{"current":0,"last_page":0,"per_page":0,"total_item":0}}}}
  /api/trpc/v2.user.indices.moodPreview:
    get:
      tags:
        - Community - Public
      summary: Get community mood table
      parameters:
        - $ref: '#/components/parameters/AuthCookie'        
      description: Get community mood indices for preview result
      responses:
        200:
          description: Mood Preview Response
          content:
            application/json:
              example: {"result":{"data":[{"name":"Solana","logo":"https://s2.coinmarketcap.com/static/img/coins/64x64/5426.png","symbol":"SOL","symbol_compare":">","chart":[[1729814400000,14075.332021816845],[1729900800000,14207.548806692563],[1729987200000,14301.123010802556],[1730073600000,14381.560330522081],[1730160000000,14433.117431338242],[1730246400000,14595.231293878674],[*************,14793.157041706916]],"changed":"1.36%","mood":"14,793"}]}}
  /api/trpc/v2.user.indices.overview:
    get:
      tags:
        - Community
      summary: Get overview data for crypto
      parameters:
        - $ref: '#/components/parameters/AuthCookie'        
      description: Get overview data for crypto assets based on slug
      requestBody:
        content:
          application/json:
            schema:
              type: string
            example: 'solana'
      responses:
        200:
          description: Community Overview Response
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      data:
                        type: object
                        properties:
                          name:
                            type: string
                          url:
                            type: string
                          logo:
                            type: string
                          description:
                            type: string
                          overview:
                            type: object
                            properties:
                              price:
                                type: string
                              mood_rank:
                                type: string
                              trust_rank:
                                type: string
                              messages:
                                type: string
                              bots:
                                type: string
                              bad_words:
                                type: string
                          urls:
                            type: object
                            properties:
                              github:
                                type: string
                              reddit:
                                type: string
                          esg:
                            type: array
                            items:
                              type: object
                              properties:
                                avg_consumption_kwh:
                                  type: number
                                avg_consumption_wh_tx:
                                  type: number
                                lower_consumption_kwh:
                                  type: number
                                lower_consumption_wh_tx:
                                  type: number
                                upper_consumption_kwh:
                                  type: number
                                upper_consumption_wh_tx:
                                  type: number
                                measured_at:
                                  type: integer
                          indices:
                            type: object
                            properties:
                              reddit:
                                type: array
                                items:
                                  type: object
                                  properties:
                                    mood_index:
                                      type: number
                                    trust_index:
                                      type: number
                                    community_size:
                                      type: integer
                                    platform:
                                      type: string
                                    timestamp:
                                      type: integer
                              telegram:
                                type: array
                                items:
                                  type: object
                                  properties:
                                    mood_index:
                                      type: number
                                    trust_index:
                                      type: number
                                    community_size:
                                      type: integer
                                    platform:
                                      type: string
                                    timestamp:
                                      type: integer
                          messages:
                            type: object
                            properties:
                              reddit:
                                type: array
                                items:
                                  type: object
                                  properties:
                                    message:
                                      type: integer
                                    bad_word:
                                      type: integer
                                    timestamp:
                                      type: integer
                                    bots:
                                      type: integer
                              telegram:
                                type: array
                                items:
                                  type: object
                                  properties:
                                    message:
                                      type: integer
                                    bad_word:
                                      type: integer
                                    timestamp:
                                      type: integer
                                    bots:
                                      type: integer
                          prices:
                            type: array
                            items:
                              type: object
                              properties:
                                close:
                                  type: number
                                volume:
                                  type: number
                                timestamp:
                                  type: integer
              example: {"result":{"data":{"name":"Bitcoin","url":"https://bitcoin.org/","logo":"https://s2.coinmarketcap.com/static/img/coins/64x64/1.png","description":"Magna consequat eu occaecat adipisicing esse proident officia officia officia sint minim culpa. Esse commodo et adipisicing ea magna amet nisi ipsum enim eiusmod. Laboris exercitation voluptate minim incididunt et pariatur velit anim magna tempor. Culpa voluptate amet laborum dolor incididunt nisi esse id reprehenderit aliqua fugiat tempor do adipisicing. Veniam ipsum sint excepteur pariatur ut. Commodo minim ut excepteur minim fugiat. Cupidatat commodo ullamco tempor velit laboris consectetur enim sint dolor.","overview":{"price":"$43,450.57","mood_rank":"3","trust_rank":"5","messages":"858","bots":"0","bad_words":"4,287"},"urls":{"github":"https://github.com/bitcoin/bitcoin","reddit":"https://reddit.com/r/bitcoin"},"esg":[{"avg_consumption_kwh":16121276.85038313,"avg_consumption_wh_tx":1403156.723504115,"lower_consumption_kwh":10063062.868525185,"lower_consumption_wh_tx":875864.5145827978,"upper_consumption_kwh":43461733.31921176,"upper_consumption_wh_tx":3782803.5513542765,"measured_at":1696896000000}],"indices":{"reddit":[{"mood_index":169.1818019084787,"trust_index":115.72157470728185,"community_size":4830943,"platform":"reddit","timestamp":1676505600000}],"telegram":[{"mood_index":169.1818019084787,"trust_index":115.72157470728185,"community_size":4830943,"platform":"reddit","timestamp":1676505600000}]},"messages":{"reddit":[{"message":433,"bad_word":2058,"timestamp":1676505600000,"bots":0}],"telegram":[{"message":433,"bad_word":2058,"timestamp":1676505600000,"bots":0}]},"prices":[{"close":24307.8413284844,"volume":32483312909.3,"timestamp":1676505600000}]}}}
  /api/trpc/v2.user.indices.chart:
    get:
      tags:
        - Community
      summary: Get MT chart
      parameters:
        - $ref: '#/components/parameters/AuthCookie'        
      description: Get mood & trust for standalone chart
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - slug
                - selected_1_layer
                - selected_2_layer
                - selected_3_layer
              properties:
                slug:
                  type: string
                selected_1_layer:
                  type: string
                  enum:
                    - mood
                    - trust
                selected_2_layer:
                  type: array
                  items:
                    type: string
                    enum:
                      - reddit
                      - telegram
                selected_3_layer:
                  type: array
                  items:
                    type: string
      responses:
        200:
          description: "Get MT chart"
          content:
            application/json:
              example: {"result":{"data":{"layers":{"l-1":{"graph":{"mood":[["2019-01-01",100]]},"options":[{"id":"mood","label":"Mood"}]},"l-2":{"graph":{"reddit":[["2019-01-01",100]],"telegram":[["2019-01-01",100]]},"options":[{"id":"reddit","label":"Reddit"},{"id":"telegram","label":"Telegram"}]},"l-3":{"graph":{"1290362028":[["2020-04-11",100]],"t5_4wpg07":[["2023-06-21",97.16609772193978]]},"options":[{"id":"1290362028","label":"Solana","group":"Telegram","username":"solana"},{"id":"t5_4wpg07","label":"SolanaNFT","group":"Reddit","username":"SolanaNFT"},{"id":"t5_hcs2n","label":"solana","group":"Reddit","username":"solana"}]}},"latest":{"value":"14,793","percentage":"0 %","indicator":"="},"info":{"logo":"https://s2.coinmarketcap.com/static/img/coins/64x64/5426.png","domain":"solana.com","website":"https://www.solana.com","symbol":"SOL","name":"Solana","slug":"solana","type":"CRYPTOCURRENCY"},"latest_calculation":"2024-10-31T00:00:00.000Z","start_calculation":"2019-01-01T00:00:00.000Z"}}}
  /api/trpc/v2.user.indices.overviewStablecoin:
    get:
      tags:
        - Community
      summary: Get stablecoin summary
      parameters:
        - $ref: '#/components/parameters/AuthCookie'        
      description: Get stablecoin summary data
      requestBody:
        content:
          application/json:
            schema:
              type: string
              example: tether-usdt
      responses:
        200:
          description: "Get stablecoin summary"
          content:
            application/json:
              example: {"result":{"data":{"name":"TrueUSD","description":"TrueUSD is a pioneering digital asset that is fully backed 1:1 by the U.S. dollar, ensuring transparency and security. It features real-time attestations from an independent accounting firm, confirming a robust collateral rate. Moreover, it is integrated with Chainlink Proof of Reserve, establishing it as a reliable USD-backed stablecoin across multiple prominent blockchains.","website":"https://www.tusd.io","logo":"https://s2.coinmarketcap.com/static/img/coins/64x64/2563.png","last_calculation_date":*************,"links":[],"asset":{"logo":"https://s2.coinmarketcap.com/static/img/coins/64x64/2563.png","name":"TrueUSD","slug":"trueusd","symbol":"TUSD","type":"STABLECOIN"},"overview":{"price":"$0.992","mood_rank":"88","trust_rank":"77","number_messages":"215","number_bots":"-","number_bad_word":"44e-4"},"mood":[[*************,102.**************]],"trust":[[*************,100]],"messages":{"telegram":[[*************,0]]},"community":{"telegram":[[*************,12468]]},"marketcap":[[*************,**********.15]],"volume":[[*************,*********.22]],"price":[[*************,1.**********]],"avg_liqudity":{"cex":[[*************,-0.****************]],"dex":[[*************,null]]}}}}
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      data:
                        type: object
                        properties:
                          name:
                            type: string
                          description:
                            type: string
                          website:
                            type: string
                          logo:
                            type: string
                          last_calculation_date:
                            type: number
                          links:
                            type: array
                            items:
                              type: object
                              properties:
                                link:
                                  type: string
                                name:
                                  type: string
                          asset:
                            type: object
                            properties:
                              logo:
                                type: string
                              name:
                                type: string
                              slug:
                                type: string
                              symbol:
                                type: string
                              type:
                                type: string
                          overview:
                            type: object
                            properties:
                              price:
                                type: string
                              mood_rank:
                                type: string
                              trust_rank:
                                type: string
                              number_messages:
                                type: string
                              number_bots:
                                type: string
                              number_bad_word:
                                type: string
                          mood:
                            type: array
                            items:
                              type: array
                              items:
                                type: number
                          trust:
                            type: array
                            items:
                              type: array
                              items:
                                type: number
                          messages:
                            type: object
                            properties:
                              telegram:
                                type: array
                                items:
                                  type: array
                                  items:
                                    type: number
                          community:
                            type: object
                            properties:
                              telegram:
                                type: array
                                items:
                                  type: array
                                  items:
                                    type: number
                          marketcap:
                            type: array
                            items:
                              type: array
                              items:
                                type: number
                          volume:
                            type: array
                            items:
                              type: array
                              items:
                                type: number
                          price:
                            type: array
                            items:
                              type: array
                              items:
                                type: number
                          avg_liqudity:
                            type: object
                            properties:
                              cex:
                                type: array
                                items:
                                  type: array
                                  items:
                                    type: number
                              dex:
                                type: array
                                items:
                                  type: array
                                  items:
                                    type: number
  /api/trpc/v2.user.finance.chart:
    get:
      tags:
        - Financial
      summary: Get metric chart
      parameters:
        - $ref: '#/components/parameters/AuthCookie'        
      description: Get metric chart data for selected coins
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                coins:
                  type: array
                  items:
                    type: string
                sizes:
                  type: string
                  enum:
                    - '10k'
                    - '100'
                    - '1m'
                    - '0.001_24h_vol'
                    - '0.1_24h_vol'
                    - '1_24h_vol'
      responses:
        '200':
          description: Success
          content:
            application/json:
              example: {"result":{"data":[{"metrics":{"cexLiqCost":0.00028022239361276044,"dexLiqCost":2.47095540093817e-7,"cexPairs":0,"dexPairs":0,"dayVolumeCex":1,"dayVolumeDex":1,"depegDays":0,"dexLiqConcentrate":1,"marketCap":1,"monthVolatility":0.42886111594610493,"price":0.9659117182344763},"slug":"tether","symbol":"USDT","rawData":{"cexLiqCost":0,"dexLiqCost":0,"cexPairs":0,"dexPairs":0,"dayVolumeCex":0,"dayVolumeDex":0,"depegDays":0,"dexLiqConcentrate":0,"marketCap":120411037108,"monthVolatility":0.008027889882416681,"price":0.9986226383}},{"metrics":{"cexLiqCost":0,"dexLiqCost":0,"cexPairs":0,"dexPairs":0,"dayVolumeCex":0,"dayVolumeDex":0,"depegDays":0,"dexLiqConcentrate":0,"marketCap":0.8780502156554645,"monthVolatility":0.1466596644959955,"price":0.997283105863125},"slug":"dai","symbol":"DAI","rawData":{"cexLiqCost":0,"dexLiqCost":0,"cexPairs":0,"dexPairs":0,"dayVolumeCex":0,"dayVolumeDex":0,"depegDays":0,"dexLiqConcentrate":0,"marketCap":5364124898,"monthVolatility":0.00218887268045694,"price":0.9997620849}},{"metrics":{"cexLiqCost":0,"dexLiqCost":0,"cexPairs":0,"dexPairs":0,"dayVolumeCex":0,"dayVolumeDex":0,"depegDays":0.16986301369863013,"dexLiqConcentrate":0,"marketCap":0.7844639207589837,"monthVolatility":1,"price":0},"slug":"trueusd","symbol":"TUSD","rawData":{"cexLiqCost":0,"dexLiqCost":0,"cexPairs":0,"dexPairs":0,"dayVolumeCex":0,"dayVolumeDex":0,"depegDays":0.1726027397260274,"dexLiqConcentrate":0,"marketCap":491542948,"monthVolatility":0.018274190472386438,"price":0.9918107525}}]}}
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          properties:
                            metrics:
                              type: object
                              properties:
                                cexLiqCost:
                                  type: number
                                dexLiqCost:
                                  type: number
                                cexPairs:
                                  type: number
                                dexPairs:
                                  type: number
                                dayVolumeCex:
                                  type: number
                                dayVolumeDex:
                                  type: number
                                depegDays:
                                  type: number
                                dexLiqConcentrate:
                                  type: number
                                marketCap:
                                  type: number
                                monthVolatility:
                                  type: number
                                price:
                                  type: number
                            slug:
                              type: string
                            symbol:
                              type: string
                            rawData:
                              type: object
                              properties:
                                cexLiqCost:
                                  type: number
                                dexLiqCost:
                                  type: number
                                cexPairs:
                                  type: number
                                dexPairs:
                                  type: number
                                dayVolumeCex:
                                  type: number
                                dayVolumeDex:
                                  type: number
                                depegDays:
                                  type: number
                                dexLiqConcentrate:
                                  type: number
                                marketCap:
                                  type: number
                                monthVolatility:
                                  type: number
                                price:
                                  type: number
  /api/trpc/v2.user.finance.comparison:
    get:
      tags:
        - Financial
      summary: Get comparison data
      parameters:
        - $ref: '#/components/parameters/AuthCookie'        
      description: Get comparison data for selected coins
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                coins:
                  type: array
                  items:
                    type: string
                type:
                  type: string
                  enum: 
                    - crypto
                    - stable
                compareUnit:
                  type: string
                  enum:
                    - cexLiqCost10k
                    - cexLiqCost100
                    - cexLiqCost1m
                    - cexLiqCost0.001_24h_vol
                    - cexLiqCost0.1_24h_vol
                    - cexLiqCost1_24h_vol
                    - dexLiqCost10k
                    - dexLiqCost100
                    - dexLiqCost1m
                    - dexLiqCost0.001_24h_vol
                    - dexLiqCost0.1_24h_vol
                    - dexLiqCost1_24h_vol
                    - dayVolumeCex
                    - monthVolatility
                    - cexPairs
                    - depegDays
                    - marketCap
                    - dexLiqConcentrate
                    - dexPairs
                    - dayVolumeDex
                    - price
      responses:
        200:
          description: success
          content:
            application/json:
              example: {"result":{"data":[{"firstCalculation":"2023-01-01T00:00:00.000Z","latestCalculation":"2023-01-01T00:00:00.000Z","slug":"trueusd","symbol":"TUSD","name":"TrueUSD","timeseries":[[1672531200000,755145447]],"assetId":9601},{"firstCalculation":"2023-01-01T00:00:00.000Z","latestCalculation":"2023-01-01T00:00:00.000Z","slug":"tether","symbol":"USDT","name":"Tether USDt","timeseries":[[1672531200000,66242103757]],"assetId":9155},{"firstCalculation":"2023-01-01T00:00:00.000Z","latestCalculation":"2023-01-01T00:00:00.000Z","slug":"dai","symbol":"DAI","name":"DAI","timeseries":[[1672531200000,5756764960]],"assetId":18252}]}}
  /api/trpc/v2.user.decentralize.asset:
    get:
      tags:
        - Decentralisation
      summary: Get decentralisation asset
      parameters:
        - $ref: '#/components/parameters/AuthCookie'        
      description: Get decentralisation asset
      responses:
        200:
          description: success
          content:
            application/json:
              example: { "result": { "data": { "assets": [ { "symbol": "BTC", "type": "CRYPTOCURRENCY", "slug": "bitcoin", "logo": "https://s2.coinmarketcap.com/static/img/coins/64x64/1.png", "name": "Bitcoin" } ] } } }
              schema:
                type: object
                required:
                  - result
                properties:
                  result:
                    type: object
                    required:
                      - data
                    properties:
                      data:
                        type: object
                        required:
                          - assets
                        properties:
                          assets:
                            type: array
                            items:
                              type: object
                              required:
                                - symbol
                                - type
                                - slug
                                - logo
                                - name
                              properties:
                                symbol:
                                  type: string
                                type:
                                  type: string
                                slug:
                                  type: string
                                logo:
                                  type: string
                                name:
                                  type: string
  /api/trpc/v2.user.decentralize.comparison:
    get:
      tags:
        - Decentralisation
      summary: Get comparison data
      parameters:
        - $ref: '#/components/parameters/AuthCookie'        
      description: Get comparison data for selected projects
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - slugs
                - compareUnit
              properties:
                slugs:
                  type: array
                  items:
                    type: string
                  example: ['bitcoin', 'ethereum']
                compareUnit:
                  type: string
                  enum:
                    - ipAuthDistrGini
                    - ipParticipantDivGini
                    - ipAuthorInflConcHHI
                    - ipGovOrdinal
                    - rcpDevDistrGini
                    - rcpParticipantDivShannon
                    - rcpDevInflConcHHI
                    - rcdRevrPowerConcHHI
                    - consensusPowerNeGini
                    - consensusPowerNeTheil
                    - consensusPowerConcNakamoto
                    - consensusPowerConcHHI
                    - coinDistrOrdinal
                  example: ipAuthDistrGini
      responses:
        200:
          description: success
          content:
            application/json:
              example: { "result": { "data": [ { "assetId": 9047, "name": "Bitcoin", "slug": "bitcoin", "symbol": "BTC", "firstCalculation": "2013-12-06", "latestCalculation": "2024-10-26", "timeseries": [ [ 1386340274000, 0 ] ] } ] } }
  /api/trpc/v2.user.decentralize.chart:
    get:
      tags:
        - Decentralisation
      summary: Get decentralisation chart metric
      parameters:
        - $ref: '#/components/parameters/AuthCookie'        
      description: Get decentralisation chart metric for selected projects
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                slug:
                  type: array
                  items:
                    type: string
              required:
                - slug
      responses:
        200:
          description: success
          content:
            application/json:
              example: {"result":{"data":[{"assetId":9047,"name":"Bitcoin","slug":"bitcoin","symbol":"BTC","metrics":{"ipAuthDistrGini":0.4607999622821808,"ipParticipantDivGini":0.9673904776573181,"ipAuthorInflConcHHI":0.9915320873260498,"rcpDevDistrGini":0.1888752281665802,"rcpParticipantDivShannon":0.969458281993866,"rcpDevInflConcHHI":0,"rcdRevrPowerConcHHI":0.9678155779838562,"consensusPowerNeGini":0.33581748604774475,"consensusPowerNeTheil":0.6652995347976685,"consensusPowerConcNakamoto":0,"consensusPowerConcHHI":0.7224676012992859}},{"assetId":9048,"name":"Litecoin","slug":"litecoin","symbol":"LTC","metrics":{"ipAuthDistrGini":0.5668142437934875,"ipParticipantDivGini":0.6655524969100952,"ipAuthorInflConcHHI":0.6563688516616821,"rcpDevDistrGini":0.6932994723320007,"rcpParticipantDivShannon":0.9631636738777161,"rcpDevInflConcHHI":0,"rcdRevrPowerConcHHI":0.9039928913116455,"consensusPowerNeGini":0.32100823521614075,"consensusPowerNeTheil":0.6402966976165771,"consensusPowerConcNakamoto":0.024690087884664536,"consensusPowerConcHHI":0.759368896484375}},{"assetId":9063,"name":"XRP","slug":"xrp","symbol":"XRP","metrics":{"ipAuthDistrGini":0.47416093945503235,"ipParticipantDivGini":0.8707646131515503,"ipAuthorInflConcHHI":0.8787835240364075,"rcpDevDistrGini":0.1706002652645111,"rcpParticipantDivShannon":0.9435602426528931,"rcpDevInflConcHHI":0,"rcdRevrPowerConcHHI":0.9363112449645996,"consensusPowerNeGini":0,"consensusPowerNeTheil":0,"consensusPowerConcNakamoto":0,"consensusPowerConcHHI":0}}]}}
  /api/trpc/v2.user.decentralisation-tree.getLatest:
    get:
      tags:
        - Decentralisation
      parameters:
        - $ref: '#/components/parameters/AuthCookie'
      description: "Getting decentralisation tree entries for decision tree"
      summary: "Getting decentralisation tree"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                limit:
                  type: number
                  example: 100
      responses:
        200:
          description: "Success to pull decision tree value"
          content:
            application/json:
              example: {"result":{"data":{"data":[{"metricName":"Coin Distribution (Ordinal)","metricCluster":"Consensus Power","createdAt":"2025-04-03T11:04:38.000Z","rawJsonStr":""},{"metricName":"Improvement Proposal Governance","metricCluster":"Improvement Proposal","createdAt":"2025-04-03T11:02:08.000Z","rawJsonStr":""}]}}}
  /api/trpc/v2.user.asset.list:
    get:
      tags:
        - Asset
      summary: Get list of available assets
      parameters:
        - $ref: '#/components/parameters/AuthCookie'        
      description: Get list of available assets
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                terms:
                  type: string
                page:
                  type: number
                perPage:
                  type: number
                filter:
                  type: string
                  example: crypto
                  description: it can be 'crypto' or 'stable'
              required:
                - slug
      responses:
        200:
          description: success
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    name:
                      type: string
                      example: Bitcoin
                    slug:
                      type: string
                      example: bitcoin
                    symbol:
                      type: string
                      example: BTC
                    logo:
                      type: string
                      example: https://dsfint.com/_next/image?url=%2Fstatic%2Fimg%2Fassets%2Fbitcoin.png&w=256&q=75
                    type:
                      type: string
                      example: CRYPTOCURRENCY
  /api/trpc/v2.user.asset.finance:
    get:
      tags:
        - Asset
      summary: Get asset finance
      parameters:
        - $ref: '#/components/parameters/AuthCookie'        
      description: Get asset finance
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                terms:
                  type: string
                page:
                  type: number
                perPage:
                  type: number
                filter:
                  type: string
                  example: CRYPTOCURRENCY
                  description: it can be 'CRYPTOCURRENCY' or 'STABLECOIN'
                topSlug:
                  type: string
                  example: bitcoin
      responses:
        200:
          description: success
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    name:
                      type: string
                      example: Bitcoin
                    slug:
                      type: string
                      example: bitcoin
                    symbol:
                      type: string
                      example: BTC
                    logo:
                      type: string
                      example: https://dsfint.com/_next/image?url=%2Fstatic%2Fimg%2Fassets%2Fbitcoin.png&w=256&q=75
                    type:
                      type: string
                      example: CRYPTOCURRENCY
  /api/trpc/v2.user.account.onboardingStatus:
    get:
      tags:
        - Account
      summary: Get account onboarding status   
      parameters:
        - $ref: '#/components/parameters/AuthCookie'            
      description: Get account onboarding status
      requestBody:
        content:
          application/json:
            schema:
              type: string
              example: /stablecoin
      responses:
        200:
          description: success
          content:
            application/json:
               example: true
  /api/trpc/v2.user.account.setOnboardingStatus:
    post:
      tags:
        - Account
      summary: Set account onboarding status
      parameters:
        - $ref: '#/components/parameters/AuthCookie'        
      description: Set account onboarding status
      requestBody:
        content:
          application/json:
            schema:
              type: string
              example: /stablecoin
      responses:
        200:
          description: success
          content:
            application/json:
              example: { '/stablecoin': true }
  /api/trpc/v2.user.account.checkProfile:
    get:
      tags:
        - Account
      summary: Get account profile
      parameters:
        - $ref: '#/components/parameters/AuthCookie'        
      description: Get account profile
      responses:
        200:
          description: success
          content:
            application/json:
              example: { "result": { "data": { "user": { "id": "88fadb5a-0f9d-4246-91b2-d294bc93709b", "user_id": "d44b455e-d15b-4707-a010-81e4c9021804", "display_name": "Ambrizal Suryadinata", "dashboard": { "feeds": [], "platforms": [ "reddit" ], "indicators": [ "trust_index", "mood_index" ] }, "dashboard_changes": 0, "created_at": null, "suspended_reason": null, "suspended_until": null, "deleted_at": null, "role": null, "status": null, "raw_onboarding": { "/crypto": true, "/stablecoin": true }, "company": "DSF", "email": "<EMAIL>", "phone": "" } } } }              
  /api/trpc/v2.user.account.availableAssets:
    get:
      tags:
        - Account
      summary: Get available assets
      parameters:
        - $ref: '#/components/parameters/AuthCookie'
      description: Get available asset based on user subscription (if they have)
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                terms:
                  type: string
                  example: "bitcoin"
      responses:
        200:
          description: success
          content:
            application/json:
              example: { "result": { "data": [{ "id": 1, "name": "Token Name", "symbol": "SYM", "logo": "http://logo", "selected": true }] } }      
  /api/trpc/v2.user.account.changePassword:
    post:
      tags:
        - Account
      summary: Change account password
      parameters:
        - $ref: '#/components/parameters/AuthCookie'        
      description: Change account password
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                current_password:
                  type: string  
                  example: password123
                password:     
                  type: string       
                  example: password024
      responses:
        200:
          description: success
          content:
            application/json:
              example: { "result": { "data": true } }
