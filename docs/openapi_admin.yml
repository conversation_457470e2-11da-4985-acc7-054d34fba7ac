openapi: '3.1.1'
info:
  title: Nodiens Dashboard
  version: '1.0'
  description: |-
    This was Open API Documentation for Nodiens Admin Dashboard, currently we're implementing API using Rest API & tRPC API.
    This documentation will mainly focus at tRPC API. Please see instructions on [notion page](https://www.notion.so/CoinConsole-Documentation-API-for-App-Layer-10b2c7b693fe80e588b7f5af45d881ca?pvs=4)
servers:
  - url: https://dev--dsf-hrl.netlify.app
  - url: http://localhost:3001
  - url: https://beta.nodiens.com
components:
  parameters:
    AuthCookie:
      name: sb-fpcckoxopdiokehfxcdf-auth-token
      in: cookie
      required: true
      schema:
        type: string
      description: This session was created by backend, so we don't need to add this manually
tags:
  - name: Excluded Paywall
    description: This API was used to manage assigned user for excluded paywall feature
  - name: User
    description: This API was used to manage user
paths:
  /api/trpc/admin.v1.excludedPaywall.list:
    get:
      tags:
        - Excluded Paywall
      parameters:
        - $ref: '#/components/parameters/AuthCookie'
      summary: Get excluded paywall list
      description: Getting excluded paywall list of assigned users
      requestBody:
        content:
          application/json:
            example: {"page": 1, "column": "userId", "order": "desc", "searchTerm": "paolo", "excludedStatus": true}
      responses:
        200:
          description: 'Success to pull excluded paywall list'
          content:
            application/json:
              example: {"result":{"data":{"page":1,"perPage":10,"totalItem":23,"totalPage":3,"data":[{"company":"DLT Science Foundation","email":"<EMAIL>","name":"Paolo Tasca","role":"super_admin","userId":"f4be6b18-7966-42de-ae69-1e510f0acd63"}]}}}
  /api/trpc/admin.v1.excludedPaywall.assign:
    post:
      tags:
        - Excluded Paywall
      parameters:
        - $ref: '#/components/parameters/AuthCookie'
      summary: Assign user to exclude paywall
      description: Assign user to excluded paywall feature
      requestBody:
        content:
          application/json:
            example: ["12d9j219d", "981d2ubd081"]
      responses:
        200:
          description: 'Success to assign user'
          content:
            application/json:
              example: {"result": { "data": { "message": "All user has excluded from paywall" } }}
  /api/trpc/admin.v1.excludedPaywall.unassign:
    post:
      tags:
        - Excluded Paywall
      parameters:
        - $ref: '#/components/parameters/AuthCookie'
      summary: Un-Assign user to exclude paywall
      description: Un-Assign user to excluded paywall feature
      requestBody:
        content:
          application/json:
            example: ["12d9j219d", "981d2ubd081"]
      responses:
        200:
          description: 'Success to assign user'
          content:
            application/json:
              example: {"result": { "data": { "message": "User has unexcluded from paywall" } }}
  /api/trpc/admin.v1.user.list:
    get:
      tags:
        - User
      summary: Getting user list
      parameters:
        - $ref: '#/components/parameters/AuthCookie'
      description: Getting user list from our platform
      requestBody:
        content:
          application/json:
            example: {"userType": ["admin", "user"], "authProvider": [], "company": [], "emailDomain": [], "registrationDate": "2024-01-01T00:00:00", "status": [], "plan": [], "page": 1}
      responses:
        200:
          description: 'Success to pull user list'
          content:
            application/json:
              example: {"result":{"data":{"data":[{"auth_providers":["email"],"email":"<EMAIL>","first_name":"Daya","last_name":"Ali","id":"9af10881-491e-4db2-b7b3-ab7ab77dd611","registration_date":"2025-04-14T08:45:13.099Z","role":"user","activation_plan_date":null,"company":null,"expired_plan_date":null,"hashpack_account":null,"last_login":null,"onboarding_status":null,"plan":null,"suspended_reason":null}],"page":1,"perPage":10,"totalPage":3,"totalItem":28}}}
  /api/trpc/admin.v1.user.profile:
    get:
      tags:
        - User
      summary: Getting user profile
      parameters:
        - $ref: '#/components/parameters/AuthCookie'
      description: Getting user profile using selected user id
      requestBody:
        content:
          application/json:
            example: a8acce67-3d11-49f7-920a-a8ea105068b8
      responses:
        200:
          description: 'Success to pull user profile'
          content:
            application/json:
              example: {"result":{"data":{"auth_providers":["email"],"email":"<EMAIL>","first_name":"Ambrizal","last_name":"Suryadinata","id":"a8acce67-3d11-49f7-920a-a8ea105068b8","registration_date":"2025-04-07T15:16:12.901Z","role":"admin","activation_plan_date":null,"company":"-","expired_plan_date":null,"hashpack_account":null,"last_login":null,"onboarding_status":null,"plan":null,"suspended_reason":null,"discounts":[],"plan_history":[],"support_logs":[]}}}
  /api/trpc/admin.v1.user.sendResetPassword:
    post:
      tags:
        - User
      summary: Send reset password link
      description: Send reset password link into user email
      parameters:
        - $ref: '#/components/parameters/AuthCookie'
      requestBody:
        content:
          application/json:
            example: { "id": "a8acce67-3d11-49f7-920a-a8ea105068b8" }
      responses:
        200:
          description: 'Success to send reset password link into user email'
          content:
            application/json:
              example: {"result": { "data": { "message": "Reset password success, email will delivered to user email" } }}
  /api/trpc/admin.v1.user.sendMagicLink:
    post:
      tags:
        - User
      summary: Send magic link
      description: Send magic link to automatically make authentication session into platform
      parameters:
        - $ref: '#/components/parameters/AuthCookie'
      requestBody:
        content:
          application/json:
            example: { "id": "a8acce67-3d11-49f7-920a-a8ea105068b8" }
      responses:
        200:
          description: 'Success to send magic link into user email'
          content:
            application/json:
              example: {"result": { "data": { "message": "Magic link success, email will delivered to user email" } }}
  /api/trpc/admin.v1.user.deleteUser:
    post:
      tags:
        - User
      summary: Delete user
      description: Delete user data from platform
      parameters:
        - $ref: '#/components/parameters/AuthCookie'
      requestBody:
        content:
          application/json:
            example: { "id": "a8acce67-3d11-49f7-920a-a8ea105068b8" }
      responses:
        200:
          description: 'Success to delete user'
          content:
            application/json:
              example: {"result": { "data": { "message": "User deleted" } }}
  /api/trpc/admin.v1.user.createUser:
    post:
      tags:
        - User
      summary: Create user
      description: Create user data into platform
      parameters:
        - $ref: '#/components/parameters/AuthCookie'
      requestBody:
        content:
          application/json:
            example: { "role": "user", "first_name": "Ambrizal", "last_name": "Suryadinata", "company": "DSF", "email": "<EMAIL>" }
      responses:
        200:
          description: 'Success to create user'
          content:
            application/json:
              example: {"result": { "data": { "message": "User created" } }}
  /api/trpc/admin.v1.user.suspendUser:
    post:
      tags:
        - User
      summary: Suspend user
      description: Suspend user data from platform
      parameters:
        - $ref: '#/components/parameters/AuthCookie'
      requestBody:
        content:
          application/json:
            example: { "userId": "a8acce67-3d11-49f7-920a-a8ea105068b8", "reason": "To much spam" }
      responses:
        200:
          description: 'Success to suspend user'
          content:
            application/json:
              example: {"result": { "data": { "message": "User suspended" } }}
  /api/trpc/admin.v1.user.updateUser:
    post:
      tags:
        - User
      summary: Update user
      description: Update existing user data on platform
      parameters:
        - $ref: '#/components/parameters/AuthCookie'
      requestBody:
        content:
          application/json:
            example: { "userId": "a8acce67-3d11-49f7-920a-a8ea105068b8", "firstName": "Ambrizal", "lastName": "Suryadinata", "company": "DSF", "email": "<EMAIL>" }
      responses:
        200:
          description: 'Success to update user'
          content:
            application/json:
              example: {"result": { "data": { "message": "User updated" } }}
