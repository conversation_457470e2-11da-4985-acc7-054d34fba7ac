export const TRILLION_NUMBER = 1e12
export const BILLION_NUMBER = 1e9
export const MILLION_NUMBER = 1e6

export function divideArray<T>(array: T[], target: keyof T, max?: number) {
  return Number.parseFloat(
    (array.reduce((sum, stat) => sum + (stat[target] ? (stat[target] as number) : 0), 0) / array.length).toFixed(
      max ?? 3,
    ),
  )
}

export function lastIndexOfArray(arr: unknown[]) {
  return arr.length - 1
}

export function setFloatMaxFractional(data: number, max: number) {
  return Number.parseFloat(data.toFixed(max))
}

export function setPrecisionFloatNumber(data: number, precision: number) {
  return Number.parseFloat(data.toPrecision(precision))
}

/**
 * @deprecated
 *
 * Use numericDisplay as number format
 *
 * @param value
 */
export function numberFormat(value: string | number) {
  if (typeof value === 'string') {
    value = Number.parseFloat(value)
  }

  if (value >= 1000) {
    return setFloatMaxFractional(value, 2)
  }
  if (value >= 1 && value < 1000) {
    return setFloatMaxFractional(value, 2)
  }
  if (value > -1 && value < 1) {
    return setPrecisionFloatNumber(value, 3)
  }
  if (value > -1000 && value <= -1) {
    return setFloatMaxFractional(value, 2)
  }

  return setFloatMaxFractional(value, 2)
}

export function numericDisplay(value: string | number, decimal?: number) {
  const regExp = /(?<!\.\d*)\B(?=(\d{3})+(?!\d))/g
  const prefixDecimal = decimal! < 1 ? 1 : undefined

  if (typeof value === 'string') {
    value = Number(value)
  }

  if (value >= TRILLION_NUMBER) {
    return (value / TRILLION_NUMBER).toFixed(decimal ?? 0).replaceAll(regExp, ',') + 'T'
  }

  if (value >= BILLION_NUMBER && value < TRILLION_NUMBER) {
    return (value / BILLION_NUMBER).toFixed(prefixDecimal ?? 1) + 'B'
  }

  if (value >= MILLION_NUMBER && value < BILLION_NUMBER) {
    return (value / MILLION_NUMBER).toFixed(prefixDecimal ?? 1) + 'M'
  }

  if (value >= 1000 && value < MILLION_NUMBER) {
    return value.toFixed(decimal ?? 0).replaceAll(regExp, ',')
  }

  if (value >= 1 && value < 1000) {
    return value.toFixed(decimal ?? 2)
  }

  if (value > -1 && value < 1) {
    if (value === 0) {
      return value.toString()
    }

    if (Math.abs(value) < 0.001) {
      const strExponential = value.toExponential(1)
      const [numbers = '0', exponential = '0'] = strExponential.split('e-')
      const [, decimal = '0'] = numbers.split('.')
      const nonDecimalNumber = Number(numbers) * (decimal.length * 10)

      return nonDecimalNumber + 'e-' + exponential
    }

    return value.toFixed(decimal ?? 3)
  }

  if (value > -1000 && value <= -1) {
    return value.toFixed(decimal ?? 2)
  }

  if (value > -1 * MILLION_NUMBER && value < -1000) {
    return value.toFixed(decimal ?? 0).replaceAll(regExp, ',')
  }

  if (value > -1 * BILLION_NUMBER && value <= -1 * MILLION_NUMBER) {
    return (value / MILLION_NUMBER).toFixed(prefixDecimal ?? 1) + 'M'
  }

  if (value > -1 * TRILLION_NUMBER && value <= -1 * BILLION_NUMBER) {
    return (value / BILLION_NUMBER).toFixed(prefixDecimal ?? 1) + 'B'
  }

  if (value < -1 * TRILLION_NUMBER) {
    return (
      (value / TRILLION_NUMBER)
        .toFixed(decimal ?? 1)
        .toString()
        .replaceAll(regExp, ',') + 'T'
    )
  }

  if (decimal) {
    return value.toString(decimal).replaceAll(regExp, ',')
  }

  return value.toString().replaceAll(regExp, ',')
}
// References : https://www.nist.gov/pml/owm/metric-si-prefixes
export function displayGrams(value: number, decimal?: number) {
  if (value >= 1000) {
    return numericDisplay(value / 1000, decimal) + ' kg'
  }

  if (value < 0.001) {
    return numericDisplay(value * 1000, decimal) + ' mg'
  }

  return numericDisplay(value, decimal) + ' g'
}

// References : https://www.nist.gov/pml/owm/metric-si-prefixes
export function displayTons(value: number, decimal?: number) {
  if (value >= 1000000) {
    return numericDisplay(value / 1000000, decimal) + ' Mt'
  }

  if (value >= 1000) {
    return numericDisplay(value / 1000, decimal) + ' kt'
  }

  return numericDisplay(value, decimal) + ' t'
}

// References : https://www.nist.gov/pml/owm/metric-si-prefixes
export function displayWattage(value: number, decimal?: number) {
  if (value >= 1000000000000000) {
    return numericDisplay(value / 1000000000000000, decimal) + ' PW'
  }

  if (value >= 1000000000000) {
    return numericDisplay(value / 1000000000000, decimal) + ' TW'
  }

  if (value >= 1000000000) {
    return numericDisplay(value / 1000000000, decimal) + ' GW'
  }

  if (value >= 1000) {
    return numericDisplay(value / 1000, decimal) + ' kW'
  }

  if (value < 0.001 || value < -0.001) {
    return numericDisplay(value * 1000, decimal) + ' mW'
  }

  return numericDisplay(value, decimal) + ' W'
}

export function displayWeight(value: number, decimal?: number) {
  if (value >= 1e6) {
    return numericDisplay(value / 1e6, decimal) + ' t' // Tonnes
  }

  if (value >= 1000) {
    return numericDisplay(value / 1000, decimal) + ' kg'
  }

  if (value < 0.001 || value < -0.001) {
    return numericDisplay(value * 1000, decimal) + ' mg'
  }

  return numericDisplay(value, decimal) + ' g'
}

export function averageOverObject(obj?: { [key: string]: number }) {
  if (obj) {
    return (
      Object.keys(obj).reduce((res: number, value) => {
        if (obj[value]) res = res + obj[value]
        return res
      }, 0) / Object.keys(obj).length
    )
  }
  return null
}

export function formatShortNumber(input: string | number): string {
  // Parse the input to a number if it's a string
  const num = typeof input === 'string' ? parseFloat(input) : input

  // Check if the parsed number is NaN (Not a Number) and return input as is
  if (isNaN(num)) {
    return input.toString()
  }

  if (num >= 1e9) {
    return (num / 1e9).toFixed(1).replace(/\.0$/, '') + 'B'
  } else if (num >= 1e6) {
    return (num / 1e6).toFixed(1).replace(/\.0$/, '') + 'M'
  } else if (num >= 1e3) {
    return (num / 1e3).toFixed(1).replace(/\.0$/, '') + 'K'
  } else {
    return num.toString()
  }
}

/**
 * @deprecated
 *
 * Please use numericDisplay function
 */
export function formatThousand(data: number, decimal?: number) {
  return numericDisplay(data, decimal)
}

export function serializeNumber(...value: (number | string)[]) {
  return value.map((data: number | string) => {
    if (typeof data === 'number') {
      return data
    } else {
      if (data.includes('.')) {
        data = Number.parseFloat(data.replace(/,/g, ''))
        return data
      } else {
        data = data.replace(/\D/g, '')
        data = Number.parseInt(data)
        return data
      }
    }
  })
}

export function countMonths(from: Date, to: Date) {
  const diff = Math.abs(to.getTime() - from.getTime())
  return Math.round(diff / (1000 * 60 * 60 * 24 * 30))
}
