export function isMobileDevice(): boolean {
  if (typeof window !== 'undefined') {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0
  }
  return false
}

export function blobToBase64(blob: Blob, callback: (str: string) => void) {
  const reader = new FileReader()
  reader.onload = function () {
    const dataUrl = reader.result as string | null
    if (dataUrl) {
      const base64String = dataUrl.split(',')[1]
      if (base64String) {
        callback(base64String)
      }
    }
  }
  reader.readAsDataURL(blob)
}

export function checkNumber(value: number | null | undefined) {
  if (typeof value === 'number') {
    if (isNaN(value)) {
      return false
    }

    if (value === 0) {
      return true
    }

    return true
  }
  return false
}

export function generateRandomData(numPoints: number) {
  const data = []
  let currentTimestamp = Date.now() / 1000 // Current timestamp in seconds

  for (let i = 0; i < numPoints; i++) {
    const value = Math.random() * 100 // Random value between 0 and 100
    data.push({
      time: Math.floor(currentTimestamp), // Timestamp in seconds
      value: parseFloat(value.toFixed(2)), // Random value, rounded to 2 decimals
    })
    currentTimestamp -= 60 // Subtract 60 seconds for each point
  }

  return data.reverse() // Ensure data is in ascending order by time
}
export function camelToSnake(str: string): string {
  return str.replace(/([a-z])([A-Z])/g, '$1_$2').toLowerCase()
}

export function toSnakeCase<T>(obj: T): T {
  if (Array.isArray(obj)) {
    return obj.map(toSnakeCase) as T
  } else if (obj !== null && typeof obj === 'object') {
    return Object.fromEntries(Object.entries(obj).map(([key, value]) => [camelToSnake(key), toSnakeCase(value)])) as T
  }
  return obj
}

// Helper type to convert keys to snake_case
export type SnakeCaseKeys<T> = {
  [K in keyof T as K extends string ? SnakeCaseString<K> : K]: T[K]
}

// Helper type to convert a string to snake_case
type SnakeCaseString<S extends string> = S extends `${infer First}${infer Rest}`
  ? First extends Lowercase<First>
    ? `${First}${SnakeCaseString<Rest>}`
    : `${First extends '' ? '' : '_'}${Lowercase<First>}${SnakeCaseString<Rest>}`
  : S
