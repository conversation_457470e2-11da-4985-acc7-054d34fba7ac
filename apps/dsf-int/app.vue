<script setup lang="ts">
import { useRecaptchaProvider } from 'vue-recaptcha'

useRecaptchaProvider()
const { initialize, disconnect } = useHashpackService()

onMounted(async () => {
  await initialize()
})

onUnmounted(() => {
  disconnect()
})
</script>

<template>
  <NuxtLayout>
    <NuxtPage />
    <FeatureFlags />
    <CookieBanner />
  </NuxtLayout>
</template>

<style>
body {
  font-family: 'IBM Plex Mono', monospace;
}

footer a {
  @apply text-neutrals-300 dark:text-neutrals-300 text-base font-normal hover:text-[#000] dark:hover:text-white;
}
</style>
