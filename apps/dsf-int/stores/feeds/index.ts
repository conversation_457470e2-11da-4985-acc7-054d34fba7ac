import type { CommunityTableVariant } from '~/components/organism/CommunityTable/types'
import type { v1FilterOptionsType, v1SearchFeedType, v2IndicesTable, v2TableDataType } from '~/server/trpc/trpc'

type SearchFeed = v1SearchFeedType['feeds'][0]
export type RankType = 'mood' | 'trust' | 'energy_trx' | 'number_msg' | 'community_size'

export interface SelectedFeed {
  id: string
  name: string
  platform: string
  platform_id: string
}

interface searchPageType {
  current: number
  lastPage: number
}

export enum TableRank {
  'mood',
  'trust',
  'energy_trx',
  'number_msg',
  'community_size',
}

export enum TableAssetType {
  'stable',
  'crypto',
}

export const useSearchFeed = defineStore('searchFeedsStore', () => {
  const { $client } = useNuxtApp()
  const searchPage = ref<searchPageType>({
    current: 0,
    lastPage: 0,
  })
  const route = useRoute()
  const loadingTableFeeds = ref<boolean>(false)
  const loadingFeedsResult = ref<boolean>(false)
  const loadingPagination = ref<boolean>(false)
  const tableFeeds = ref<v2TableDataType>()
  const indicesTableFeeds = ref<v2IndicesTable>()
  const pagination = ref({
    page: 1,
    perPage: 10,
    totalItem: 0,
    totalPage: 0,
  })

  const coinTypes = ref([
    [
      {
        label: 'Cryptos',
        disabled: true,
        value: 'crypto',
        checked: (route.query.assetType as string)?.split(',')?.some((assetType) => assetType === 'crypto') ?? true,
        labelClass: '!text-white',
      },
      {
        label: 'Stablecoins',
        disabled: true,
        value: 'stable',
        checked: (route.query.assetType as string)?.split(',').some((assetType) => assetType === 'stable') ?? true,
        labelClass: '!text-white',
      },
    ],
  ])

  const searchFeedsResult = ref<v1SearchFeedType['feeds']>([])
  const selectedFeeds = ref<SelectedFeed[]>([])
  const filterOptions = ref<v1FilterOptionsType | null>()
  const selectedPlatform = ref<string[]>([])
  const selectedIndicators = ref<string[]>([])
  const selectedRank = computed({
    get() {
      return (route.query.rank ?? 'mood') as keyof typeof TableRank
    },
    async set(rank) {
      await navigateTo({
        query: {
          page: 1,
          assetType: route.query.assetType,
          rank,
        },
      })
    },
  })

  const selectedIndicesType = ref<keyof typeof CommunityTableVariant>('mood')

  const isSelected = (feed: SearchFeed) => {
    return selectedFeeds.value.some((selectedFeed) => selectedFeed.platform_id === feed.platform_id)
  }

  const isSelectedOptions = (variant: string, option: string) => {
    if (variant === 'platform') {
      return selectedPlatform.value.includes(option)
    }

    if (variant === 'indicator') {
      return selectedIndicators.value.includes(option)
    }

    return false
  }

  const setSelectedPlatform = (platform: string) => {
    const index = selectedPlatform.value.findIndex((itemPlatform) => itemPlatform === platform)

    if (index === -1) {
      selectedPlatform.value = [...selectedPlatform.value, platform]
    } else {
      selectedPlatform.value.splice(index, 1)
    }
  }

  const setSelectedIndicator = (indicator: string) => {
    const index = selectedIndicators.value.findIndex((itemIndicator) => itemIndicator === indicator)

    if (index === -1) {
      selectedIndicators.value = [...selectedIndicators.value, indicator]
    } else {
      selectedIndicators.value.splice(index, 1)
    }
  }

  const setSelectedFeeds = (feed: SelectedFeed) => {
    if (selectedFeeds.value.some((selectedFeed) => selectedFeed.platform_id === feed.platform_id)) {
      selectedFeeds.value = [
        ...selectedFeeds.value.filter((selectedFeed) => selectedFeed.platform_id !== feed.platform_id),
      ]
      return
    }

    selectedFeeds.value = [...selectedFeeds.value, feed]
    return
  }

  const setSelectedRank = (rank: RankType) => {
    selectedRank.value = rank
  }

  const setSelectedIndicesType = (type: keyof typeof CommunityTableVariant) => {
    selectedIndicesType.value = type
  }

  const setPagination = (page: number, perPage = 10) => {
    pagination.value = {
      ...pagination.value,
      page,
      perPage,
    }
  }

  const onSearchFeed = async (keyword: string, platforms?: string[]) => {
    loadingFeedsResult.value = true
    const result = await $client.v1.dashboard.searchFeeds.query({
      searchTerm: keyword,
      platforms,
    })

    searchPage.value = {
      current: result.pagination.current,
      lastPage: result.pagination.lastPage,
    }

    searchFeedsResult.value = result.feeds
    loadingFeedsResult.value = false
  }

  const onSearchNextPage = async (keyword: string, platforms?: string[]) => {
    loadingPagination.value = true
    const result = await $client.v1.dashboard.searchFeeds.query({
      searchTerm: keyword,
      platforms,
    })

    searchPage.value = {
      current: result.pagination.current,
      lastPage: result.pagination.lastPage,
    }

    searchFeedsResult.value = [...searchFeedsResult.value, ...result.feeds]
    loadingPagination.value = false
  }

  const getFilterOptions = async () => {
    const result = await $client.v1.dashboard.filterOptions.query()

    filterOptions.value = result
  }

  const getIndicesTable = async (forceRefresh?: boolean) => {
    if (indicesTableFeeds.value === undefined || forceRefresh === true) {
      loadingTableFeeds.value = true

      try {
        const result = await $client.v2.user.indices.table.query({
          page: 1,
          selector: selectedIndicesType.value,
          // rank: selectedCommunityRankBy.value,
          // type: ['crypto']
        })
        indicesTableFeeds.value = result
      } catch {
        indicesTableFeeds.value = undefined
      }
      loadingTableFeeds.value = false
    }
  }

  return {
    searchFeedsResult,
    selectedFeeds,
    filterOptions,
    selectedPlatform,
    selectedIndicators,
    selectedRank,

    selectedIndicesType,
    pagination,
    isSelected,
    isSelectedOptions,
    onSearchFeed,
    onSearchNextPage,
    setSelectedFeeds,
    setSelectedPlatform,
    setSelectedIndicator,
    setSelectedRank,

    setSelectedIndicesType,
    setPagination,
    getFilterOptions,

    getIndicesTable,
    indicesTableFeeds,
    tableFeeds,
    loadingTableFeeds,
    loadingFeedsResult,
    searchPage,
    loadingPagination,
    coinTypes,
  }
})
