import { useUserStore } from '~/stores/profile'
import type { Session } from 'better-auth'

export const useSessionStore = defineStore(
  'sessionStore',
  (): {
    userSession: Ref<Session | null>
    isLogged: ComputedRef<boolean>
    setSession: (payload: Session | null) => void
    clearSession: (opt?: { force?: boolean; message?: string }) => void
    getUserProfile: () => Promise<unknown>
    cleanUpClientSession: (opt?: { force?: boolean; message?: string }) => Promise<boolean>
  } => {
    const { $apiClient, $authClient } = useNuxtApp()
    const toast = useToast()
    const { setProfile } = useUserStore()
    const userSession = shallowRef<Session | null>(null)
    const authToast = useState(() => false)

    const isLogged = computed(() => userSession.value !== null)
    // const refreshToken = computed(() => userSession.value?.refresh_token)

    const setSession = (payload: Session | null) => {
      userSession.value = payload
    }

    const clearSession = (opt?: { force?: boolean; message?: string }) => {
      const userStore = useUserStore()

      userSession.value = null
      userStore.clearProfile()

      // Remove all cookies in browser
      if (import.meta.client) {
        const router = useRouter()

        // Get cookies policy agreed
        const cookiePolicyAgreed = localStorage.getItem('dsf_cookies')

        localStorage.clear()

        if (opt?.force === true) {
          if (authToast.value === false) {
            authToast.value = true
            toast.add({
              title: 'Automatically Signed Out !',
              description: opt?.message ?? `We've detect your session was expired`,
              icon: 'i-heroicons-exclamation-triangle-20-solid',
              callback: () => {
                authToast.value = false
              },
            })
          }
        }

        // Restore cookies policy agreed
        if (cookiePolicyAgreed) {
          localStorage.setItem('dsf_cookies', cookiePolicyAgreed)
        }

        // Get all cookies
        const cookies = document.cookie.split(';')

        // Delete all cookies
        for (let i = 0; i < cookies.length; i++) {
          const cookie = cookies[i]?.trim() || ''
          const eqPos = cookie.indexOf('=')
          const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie
          if (name === 'platform-ff') {
            continue
          }
          document.cookie = name + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/'
        }

        router.replace('/')
      }
    }

    /**
     *
     * This function will set legacy sbUser and legacy set profile
     */
    const getUserProfile = async () => {
      const { error, data } = await $apiClient.GET('/nodiens/api/v1/account/profile')

      if (error || !data.payload) {
        return null
      }

      setProfile(data.payload)

      return data.payload
    }

    /**
     * This function was only worked on client or browser only
     * All of server side request will be return false
     */
    const cleanUpClientSession = async (opt?: { force?: boolean; message?: string }) => {
      try {
        // It will be failed if session is invalid
        const { data } = await $authClient.signOut()

        if (data?.success) {
          clearSession(opt)
        }

        return data?.success ?? false
      } catch {
        clearSession(opt)
        return true
      }
    }

    return {
      userSession,
      isLogged,
      setSession,
      clearSession,
      getUserProfile,
      cleanUpClientSession,
    }
  },
)
