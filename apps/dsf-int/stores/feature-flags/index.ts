import { FEATURE_FLAGS } from '~/constants/feature-flags'

export interface FeatureFlag {
  key: string
  label: string
  enabled: boolean
}

export const useFeatureFlags = defineStore('featureFlagsStore', () => {
  const featureFlags = useCookie<FeatureFlag[]>('platform-ff', {
    default: () => FEATURE_FLAGS,
  })

  const config = useRuntimeConfig()

  const syncFeatureFlags = (data: FeatureFlag[]) => (featureFlags.value = data)

  const toggleFeatureFlag = (key: string) => {
    const index = featureFlags.value.findIndex((flag) => flag.key === key)
    if (index !== -1 && featureFlags.value[index]) {
      featureFlags.value[index].enabled = !featureFlags.value[index].enabled
    }
  }

  const isFeatureEnabled = (key: string | string[]) => {
    const envEnabled = config.public.featureFlagsEnabled === 'true'

    if (Array.isArray(key)) {
      return key.map((ffKey) => featureFlags.value.find((ff) => ff.key === ffKey)?.enabled ?? envEnabled)
    }

    return featureFlags.value?.find((ff) => ff.key === key)?.enabled ?? envEnabled
  }

  return {
    syncFeatureFlags,
    featureFlags,
    toggleFeatureFlag,
    isFeatureEnabled,
  }
})
