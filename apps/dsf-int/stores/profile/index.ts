import type { AccountProfilePayload } from '~/types/account'

export interface SbUserType {
  [key: string]: unknown
  access_token: string
  expires_at?: number
  expires_in: number
  refresh_token: string
  token_type: string
}

export const useUserStore = defineStore('userStore', () => {
  const { $apiClient } = useNuxtApp()

  // TODO: We need to make all of camelCase into snake_case on backend
  const userProfile = shallowRef<AccountProfilePayload | null>(null)
  const sbUser = ref<SbUserType | null>(null)

  const fetchUserProfile = async () => {
    const profile = await $apiClient.GET('/nodiens/api/v1/account/profile')

    if (profile.error || !profile.data.payload) {
      return
    }

    userProfile.value = profile.data.payload
  }

  const setSbUser = (data: SbUserType) => {
    sbUser.value = data
  }

  const setProfile = (profile: AccountProfilePayload) => {
    userProfile.value = profile
  }

  const clearProfile = () => {
    userProfile.value = null
    sbUser.value = null
  }

  return {
    userProfile,
    setProfile,
    fetchUserProfile,
    setSbUser,
    sbUser,
    clearProfile,
  }
})
