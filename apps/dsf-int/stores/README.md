## Store Guideline

### Path

When creating a new store, you should follow this path:

```
stores/<store-name>/index.ts
stores/<store-name/types.ts
```

**Notes:**

1. `<store-name>` should written in lowercase, and kebab-case
2. Each store folder should contains `index.ts` and `types.ts`

### Variable Names

1. Addd prefix `use`
2. Variable name written in CamelCase (e.g. useExampleStore)

### Return Order

1. Data should above the action, example

```
return {
    count, // this is data
    data, // this is data
    increment, // this is action function
    updateData, // this is action function
}
```
