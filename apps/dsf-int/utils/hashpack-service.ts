import { HashConnect, HashConnectConnectionState } from 'hashconnect'
import { LedgerId, AccountId } from '@hashgraph/sdk'
import { APP_META_DATA } from '~/constants/dapp'
import type { SessionData } from 'hashconnect'

export enum HashpackNetwork {
  'mainnet',
  'testnet',
}

export class HashpackService {
  private static instance: HashpackService
  private hashconnect: HashConnect
  private _pairingData: SessionData | null = null
  private _connectionState: HashConnectConnectionState = HashConnectConnectionState.Disconnected
  private _initialized = false

  private constructor(network: keyof typeof HashpackNetwork) {
    this.hashconnect = new HashConnect(
      network === 'testnet' ? LedgerId.TESTNET : LedgerId.MAINNET,
      'b7eca15b4351f35edbae4902aefa085f',
      APP_META_DATA,
      network === 'testnet',
    )
  }

  public static getInstance(network: keyof typeof HashpackNetwork): HashpackService {
    if (!HashpackService.instance) {
      HashpackService.instance = new HashpackService(network)
    }
    return HashpackService.instance
  }

  public async initialize() {
    if (this._initialized) {
      return
    }

    await this.hashconnect.init()
    this.setupEvents()
    this._initialized = true
  }

  public get pairingData() {
    return this._pairingData
  }

  public get connectionState() {
    return this._connectionState
  }

  public get client() {
    return this.hashconnect
  }

  private setupEvents() {
    this.hashconnect.pairingEvent.on((newPairing) => {
      this._pairingData = newPairing
      this.emitPairingUpdate(newPairing)
    })

    this.hashconnect.disconnectionEvent.on(() => {
      this._pairingData = null
      this.emitPairingUpdate(null)
    })

    this.hashconnect.connectionStatusChangeEvent.on((status) => {
      this._connectionState = status
    })
  }

  private emitPairingUpdate(data: SessionData | null) {
    window.dispatchEvent(new CustomEvent('hashpack-pairing-update', { detail: data }))
  }

  public async connectWallet() {
    return await this.hashconnect.openPairingModal()
  }

  public async disconnect() {
    this._pairingData = null
    return await this.hashconnect.disconnect()
  }

  public getSigner(accountId: string) {
    return this.hashconnect.getSigner(AccountId.fromString(accountId))
  }
}
