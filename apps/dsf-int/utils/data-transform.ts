import { format } from 'date-fns'

export const prefixMessageToSign = (message: string) => {
  return '\x19Hedera Signed Message:\n' + message.length + message
}

export const transformToStackedBarSeriesData = (data: Record<string, number[][]>) => {
  // Collect all unique timestamps
  const uniqueTimestamps = new Set([
    ...(data.telegram ?? []).map(([time]) => time),
    ...(data.reddit ?? []).map(([time]) => time),
  ])

  // Create a map for each platform
  const mapData = (arr: number[][]) =>
    arr.reduce((acc: Record<string, number>, curr) => {
      const [time, value] = curr as [string, number]
      acc[time] = value
      return acc
    }, {})

  const telegramMap = mapData(data.telegram || [])
  const redditMap = mapData(data.reddit || [])

  return [...uniqueTimestamps].sort().map((time) => ({
    time: time ? format(new Date(time), 'yyyy-MM-dd') : '',
    value: [telegramMap[time ?? 0] ?? null, redditMap[time ?? 0] ?? null],
  }))
}
