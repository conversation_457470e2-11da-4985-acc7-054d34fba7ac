/**
 * Checks if a feature is allowed based on the user's plan features.
 *
 * @param {object} planFeatures - The features object from the user's plan (e.g., plan.features).
 * @param {string} featurePath - The path to the feature in the features object (e.g., "financial_metrics.available_assets").
 * @returns {boolean | number | string} Returns the value of the feature if it exists, otherwise false.
 */
export const isFeatureAllowed = (
  planFeatures: Record<string, unknown>,
  featurePath: string,
): boolean | number | string => {
  if (!planFeatures || !featurePath) {
    return false
  }

  try {
    const pathParts = featurePath.split('.')
    let current: any = planFeatures

    for (const part of pathParts) {
      current = current[part]
      if (current === undefined || current === null) {
        return false
      }
    }

    return current
  } catch (error) {
    console.error('Error checking feature:', error)
    return false
  }
}
