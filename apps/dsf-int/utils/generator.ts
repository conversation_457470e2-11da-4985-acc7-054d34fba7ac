export function generateRandomKey() {
  const timestamp = Date.now().toString(36) // Converts the current timestamp to a base-36 string
  const randomPart = Math.random().toString(36).substring(2) // Generates a random base-36 string
  const combined = timestamp + randomPart

  // Ensure the length is exactly 64 characters
  const padded = combined.padEnd(64, '0') // Pads with '0' if needed
  return padded.substring(0, 64) // Trims to 64 characters if longer
}
