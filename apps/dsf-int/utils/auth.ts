import { useUserStore } from '~/stores/profile'
import { useSessionStore } from '~/stores/session'

export function cleanUpAuth() {
  const userStore = useUserStore()
  const { clearSession } = useSessionStore()

  userStore.clearProfile()
  localStorage.clear()
  clearSession()
  // Get all cookies
  const cookies = document.cookie.split(';')

  // Delete all cookies
  for (let i = 0; i < cookies.length; i++) {
    const cookie = cookies[i]
    const eqPos = cookie?.indexOf('=') || -1
    const name = eqPos > -1 ? cookie?.substr(0, eqPos) : cookie
    document.cookie = name + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/'
  }
}
