import baseConfig from '../../eslint.config.mjs'
import pluginNuxt from 'eslint-plugin-nuxt'
import pluginVue from 'eslint-plugin-vue'

export default [
  ...baseConfig,
  {
    files: ['**/*.ts', '**/*.tsx', '**/*.vue', '**/*.js', '**/*.jsx'],
    languageOptions: {
      parser: (await import('vue-eslint-parser')).default,
      parserOptions: {
        parser: '@typescript-eslint/parser',
        ecmaVersion: 'latest',
        sourceType: 'module',
        extraFileExtensions: ['.vue'],
      },
    },
    plugins: {
      vue: pluginVue,
      nuxt: pluginNuxt,
    },
    rules: {
      ...pluginVue.configs.base.rules,
      ...pluginNuxt.configs.recommended.rules,
      'vue/comment-directive': 'off',
    },
  },
  {
    ignores: ['.nuxt/**', '.output/**', 'node_modules', 'eslint.config.mjs'],
  },
]
