export default defineAppConfig({
  ui: {
    skeleton: {
      base: 'animate-pulse',
      background: 'bg-gray-300 dark:bg-gray-600',
      rounded: 'rounded',
    },
    formGroup: {
      label: {
        base: 'text-base font-normal text-neutrals-800 dark:text-white',
      },
    },
    textarea: {
      placeholder: 'dark:placeholder-white/50 placeholder-neutrals-500',
      color: {
        dark: {
          outline: 'border-2 rounded-lg border-neutrals-900 dark:border-neutrals-50',
        },
      },
      variant: {
        material:
          'bg-neutrals-200 border-b border-neutrals-300 rounded-none dark:bg-neutrals-500 dark:border-neutrals-900',
      },
      default: {
        size: 'lg',
        variant: 'material',
        loading: 'i-heroicons-arrow-path-20-solid',
      },
    },
    input: {
      placeholder: 'dark:placeholder-white/50 placeholder-neutrals-500',
      color: {
        dark: {
          outline: 'border-2 rounded-lg border-neutrals-900 dark:border-neutrals-50',
        },
      },
      variant: {
        material:
          'bg-neutrals-200 border-b border-neutrals-300 rounded-none dark:bg-neutrals-500 dark:border-neutrals-900',
        transparent: 'bg-transparent border-2 border-neutrals-900 rounded-lg dark:border-white',
      },
      default: {
        size: 'lg',
        variant: 'material',
        loading: 'i-heroicons-arrow-path-20-solid',
      },
    },
    toggle: {
      inactive: 'bg-neutrals-200 dark:bg-neutrals-200',
      active: 'bg-base-primary-800 dark:bg-base-primary-800',
      container: {
        base: 'pointer-events-none relative inline-block rounded-full bg-neutrals-300 bg-white shadow transform ring-0 transition ease-in-out duration-200',
      },
    },
    dropdown: {
      background: 'dark:bg-black bg-white',
      rounded: 'rounded-[5px]',
      ring: 'ring-1 ring-neutrals-500',
      item: {
        disabled: 'cursor-not-allowed opacity-100',
      },
    },
    checkbox: {
      wrapper: 'cursor-pointer hover:cursor-pointer',
      container: 'cursor-pointer hover:cursor-pointer',
      inner: 'cursor-pointer hover:cursor-pointer',
      label: 'cursor-pointer hover:cursor-pointer',
      base: 'cursor-pointer hover:cursor-pointer',
    },
    button: {
      rounded: 'rounded',
      size: {
        lg: 'text-base',
      },
      color: {
        'light-primary': {
          solid:
            'bg-base-primary-500 text-white hover:bg-base-primary-600 dark:bg-base-primary-500 dark:hover:bg-base-primary-600',
        },
        'base-primary': {
          solid:
            'bg-base-primary-800 text-white hover:bg-base-primary-900 dark:bg-base-primary-800 dark:hover:bg-base-primary-900',
          outline:
            'dark:bg-black bg-base-primary-900 text-white bg-white text-base-primary-900 dark:text-white border-2 dark:border-white border-base-primary-900 rounded-sm',
        },
        brand: {
          solid: 'bg-brand-300 hover:bg-brand-400 text-white dark:bg-brand-300 dark:hover:bg-brand-400',
          outline: 'bg-transparent border border-brand-300 text-brand-300 hover:border-brand-400 hover:text-brand-400',
        },
        midnight: {
          outline: 'text-neutrals-700 border border-neutrals-600 dark:text-white dark:border-white',
          ghost: 'text-neutrals-700 dark:text-white',
          solid: 'bg-neutrals-700 text-white dark:bg-white dark:text-neutrals-700',
        },
        'midnight-darker': {
          outline: 'text-neutrals-800 border border-neutrals-700 dark:text-white dark:border-white',
          solid: 'bg-neutrals-800 text-white dark:bg-white dark:text-neutrals-800',
        },
        red: {
          solid: 'bg-red-500 text-white hover:bg-red-600 dark:bg-red-500 dark:hover:bg-red-600',
        },
        'soft-gray': {
          solid: 'bg-neutrals-300 hover:bg-neutrals-400 text-white dark:bg-neutrals-300 dark:hover:bg-neutrals-400',
          outline: 'text-neutrals-300 border-neutrals-300 border',
          ghost: 'text-neutrals-300',
        },
        black: {
          solid: 'bg-black dark:bg-white',
        },
        white: {
          solid: 'bg-white dark:bg-black',
          outline: 'border-2 rounded-lg border-black dark:border-white',
          secondary: 'text-black bg-white border-black border rounded-lg dark:border-0',
        },
        transparent: {
          solid: 'bg-transparent',
        },
      },
      variant: {
        email:
          'bg-base-primary-800 inline-block rounded-md p-4 text-base leading-none text-white [text-decoration:none] font-ibm text-sm font-normal',
      },
    },
    table: {
      wrapper: 'relative overflow-x-scroll table-scrollbar md:overflow-x-auto',
      divide: 'divide-y-0',
      tbody: 'divide-y divide-neutrals-200 dark:divide-neutrals-500',
      th: {
        base: 'border-0 bg-white dark:bg-neutrals-900 cursor-default border-neutrals-200 dark:border-neutrals-500',
        font: 'text-left font-light rtl:text-right font-medium',
        size: 'text-xs',
      },
      td: {
        base: 'border-0 whitespace-nowrap border-neutrals-200 dark:border-neutrals-500',
        padding: 'px-3 py-4',
        color: 'text-gray-500 dark:text-white',
      },
      tr: {
        base: 'hover:bg-gray-100 dark:hover:bg-neutrals-500',
      },
    },
    pagination: {
      wrapper: 'space-x-0.5 sm:space-x-1',
      base: 'min-w-8 min-h-8 justify-center first:ring-0 last:ring-0 ring-0 first:shadow-none last:shadow-none dark:last:bg-transparent dark:first:bg-transparent dark:last:disabled:bg-transparent dark:first:disabled:bg-transparent',
      rounded: 'rounded',
      default: {
        prevButton: {
          icon: 'i-heroicons-arrow-left-16-solid',
        },
        nextButton: {
          icon: 'i-heroicons-arrow-right-16-solid',
        },
      },
    },
    notifications: {
      position: 'top-0 bottom-auto',
    },
    select: {
      base: 'min-h-[42px] rounded cursor-pointer hover:cursor-pointer w-full dark:text-white text-black ring-0',
      rounded: 'rounded-lg',
      color: {
        transparent: {
          outline: 'bg-transparent dark:text-white text-black',
        },
        white: {
          solid: 'bg-white dark:bg-black',
        },
        black: {
          solid: 'bg-black dark:bg-white text-white dark:text-black',
        },
        midnight: {
          outline:
            'bg-white border border-neutrals-700 dark:bg-neutrals-700 dark:border-neutrals-800 rounded-md text-black dark:text-white',
          solid:
            'bg-neutrals-700 rounded-md border border-neutrals-700 text-white dark:bg-white dark:text-neutrals-800 dark:border-neutrals-800',
        },
      },
      variant: {
        default: 'border border-neutrals-300 rounded-md dark:bg-black dark:border-neutrals-200',
        material:
          'bg-neutrals-100/50 border-b border-neutrals-300 rounded-none dark:bg-neutrals-600 dark:border-neutrals-700',
        outline: 'ring-2 dark:ring-white ring-black',
        none: 'bg-neutrals-200 border-b border-neutrals-300 dark:bg-neutrals-500 dark:border-neutrals-700 grow rounded-none',
      },
      default: {
        size: 'lg',
        variant: 'material',
        loading: 'i-heroicons-arrow-path-20-solid',
      },
    },
    selectMenu: {
      container: 'w-full min-w-[250px]',
      background: 'dark:bg-black bg-white',
      ring: 'ring-1 ring-neutrals-500',
      rounded: 'rounded-[5px]',
      option: {
        padding: 'py-3 px-4 cursor-pointer hover:cursor-pointer',
      },
      popper: {
        placement: 'bottom-start',
      },
    },
    modal: {
      width: 'md:max-w-2xl',
    },
    card: {
      background: 'dark:bg-black bg-white',
      ring: 'ring-2 dark:ring-neutrals-500',
      rounded: 'rounded-lg',
      defaultVariants: {
        variant: 'solid',
      },
    },
    badge: {
      rounded: 'rounded-lg',
      font: 'font-normal',
      color: {
        'base-primary': {
          asset: 'bg-base-primary-600 text-white',
          tag: 'bg-base-primary-50 dark:bg-base-primary-100 text-black rounded-full',
          subtle:
            'bg-base-primary-600 text-base-primary-600 bg-opacity-10 ring-base-primary-600 ring-1 text-base-primary-600 ring-inset',
        },
        'accent-green': {
          asset: 'bg-accent-green-600 text-white',
          tag: 'bg-accent-green-50 dark:bg-accent-green-100 text-black rounded-full',
        },
        brand: {
          asset: 'bg-brand-400 text-white',
          tag: 'bg-brand-50 dark:bg-brand-100 text-black rounded-full',
        },
      },
      size: {
        xs: 'text-xs px-1.5 py-1',
        sm: 'text-sm p-2',
      },
      defaultVariants: {
        color: 'base-primary',
        size: 'sm',
      },
    },
    popover: {
      background: 'dark:bg-black bg-white',
      ring: 'ring-1 ring-neutrals-500',
    },
  },
})
