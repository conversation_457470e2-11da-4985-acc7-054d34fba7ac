/** @type {import('tailwindcss').Config} */
export default {
  content: ['./apps/dsf-int/**/*.{vue,ts}', './constants/**/*.ts'],
  theme: {
    extend: {
      colors: {
        brand: {
          50: '#FEF2F2',
          100: '#FFDFDF',
          200: '#F4AB8C',
          300: '#EE8054',
          400: '#F08B5E',
          500: '#ED723B', // Reddit
          600: '#E05611',
          700: '#D24F0D',
          800: '#C5490A',
          900: '#AC3C02',
        },
        'base-primary': {
          50: '#EEF6FF',
          100: '#B4E8FF', // Mood L-3
          200: '#81D8FF',
          300: '#4BC9FF', // Mood L-2
          400: '#1DBDFF',
          500: '#00B0FF', // STABLECOIN-COIN2
          600: '#00A3FF', // Main Mood/L-1
          700: '#008EE3',
          800: '#007DCF', // Telegram
          900: '#065CAD',
        },
        'accent-green': {
          50: '#F2FBF4',
          100: '#E1F7E7',
          200: '#C5EDCF',
          300: '#84E9A0', // Trust L-3
          400: '#4FBD6D', // Trust L-2
          500: '#3EA95B',
          600: '#2E8B47', // Main Trust/L-1
          700: '#037823', // DEX
          800: '#245732',
          900: '#1F482B',
        },
        'accent-red': {
          50: '#FFF0F0',
          100: '#FFDDDD',
          200: '#FFC0C0',
          300: '#FF9494',
          400: '#FF9494',
          500: '#FF2323', // STABLECOIN-COIN1
          600: '#FF0000', // CEX
          700: '#D70000',
          800: '#B10303',
          900: '#920A0A',
        },
        'accent-purple': {
          50: '#F5F3FF',
          100: '#EDE9FE',
          200: '#DDD6FE',
          300: '#C4B5FD',
          400: '#A78BFA',
          500: '#8B5CF6',
          600: '#7C3AED', // Market Cap
          700: '#6D28D9',
          800: '#5B21B6',
          900: '#4C1D95',
        },
        'accent-orange': {
          50: '#FFF8F0',
          100: '#FFEED9',
          200: '#FFD9B3',
          300: '#FFC280',
          400: '#FFB04D',
          500: '#FFA500', // Price
          600: '#FF8C00',
          700: '#FF6F00',
          800: '#FF5A00',
          900: '#FF4A00',
        },
        neutrals: {
          50: '#FBFBFB',
          100: '#F3F3F3',
          200: '#EBEBEB',
          300: '#C3C3C3', // Field
          400: '#7C7C7C',
          500: '#5B5B5B',
          600: '#2B344B',
          700: '#233053',
          800: '#1B1F2B', // Field-Dark
          900: '#000000', // Dark-Mode Background
        },
        error: '#eb1e36',
        'error-le': '#f5c6cb',
        // Based on old design system (dark mode)
        'primary-bg': '#FBFBFB',
        'secondary-bg-color': '#2a3657',
        // Based on old design system (light mode)
        'backdrop-color': '#f2f2f2',
        secondary: '#333',
        'secondary-bg': '#fff',
        // Based on old design system
        'ring-color': '#fbdfd3',
        'email-base': '#F7F7FA',
        'dark-base': '#1A1A1A'
      },
      backgroundImage: {
        'dashed-horizontal': 'linear-gradient(90deg, #DDE0E3, #DDE0E3 75%, transparent 75%, transparent 100%)',
        'dashed-vertical': 'linear-gradient(0deg, #DDE0E3, #DDE0E3 75%, transparent 75%, transparent 100%)',
      },
      transitionDuration: {
        25: '25ms',
      },
      screens: {
        'v1-sm': { min: '576px' },
        'v1-md': { min: '768px' },
        'v1-lg': { min: '992px' },
        'v1-xl': { min: '1200px' },
        'v1-2xl': { min: '1400px' },
        'v1-3xl': { min: '1600px' },
        'v1-4xl': { min: '1800px' },
      },
      typography: ({ theme }) => ({
        neutrals: {
          css: {
            '--tw-prose-body': theme('colors.neutrals[800]'),
            '--tw-prose-headings': theme('colors.neutrals[900]'),
            '--tw-prose-lead': theme('colors.neutrals[700]'),
            '--tw-prose-links': theme('colors.neutrals[900]'),
            '--tw-prose-bold': theme('colors.neutrals[900]'),
            '--tw-prose-counters': theme('colors.neutrals[600]'),
            '--tw-prose-bullets': theme('colors.neutrals[400]'),
            '--tw-prose-hr': theme('colors.neutrals[300]'),
            '--tw-prose-quotes': theme('colors.neutrals[900]'),
            '--tw-prose-quote-borders': theme('colors.neutrals[300]'),
            '--tw-prose-captions': theme('colors.neutrals[700]'),
            '--tw-prose-code': theme('colors.neutrals[900]'),
            '--tw-prose-pre-code': theme('colors.neutrals[100]'),
            '--tw-prose-pre-bg': theme('colors.neutrals[900]'),
            '--tw-prose-th-borders': theme('colors.neutrals[300]'),
            '--tw-prose-td-borders': theme('colors.neutrals[200]'),
            '--tw-prose-invert-body': theme('colors.neutrals[200]'),
            '--tw-prose-invert-headings': theme('colors.white'),
            '--tw-prose-invert-lead': theme('colors.neutrals[300]'),
            '--tw-prose-invert-links': theme('colors.white'),
            '--tw-prose-invert-bold': theme('colors.white'),
            '--tw-prose-invert-counters': theme('colors.neutrals[400]'),
            '--tw-prose-invert-bullets': theme('colors.neutrals[600]'),
            '--tw-prose-invert-hr': theme('colors.neutrals[700]'),
            '--tw-prose-invert-quotes': theme('colors.neutrals[100]'),
            '--tw-prose-invert-quote-borders': theme('colors.neutrals[700]'),
            '--tw-prose-invert-captions': theme('colors.neutrals[400]'),
            '--tw-prose-invert-code': theme('colors.white'),
            '--tw-prose-invert-pre-code': theme('colors.neutrals[300]'),
            '--tw-prose-invert-pre-bg': 'rgb(0 0 0 / 50%)',
            '--tw-prose-invert-th-borders': theme('colors.neutrals[600]'),
            '--tw-prose-invert-td-borders': theme('colors.neutrals[700]'),
          },
        },
      }),
    },
  },
  plugins: [require('@tailwindcss/typography')],
  darkMode: 'class',
}
