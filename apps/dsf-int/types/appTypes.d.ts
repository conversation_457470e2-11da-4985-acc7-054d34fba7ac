interface PaginationType {
  page: number
  perPage: number
  totalPage: number
  offset?: number
  totalItem?: number
}

// Extract the return type of the async function
type AsyncReturnType<T extends (...args: any) => any> = T extends (...args: any) => Promise<infer R> ? R : any

export interface PaginationResponse {
  current: number
  last_page: number
  per_page: number
  total_item: number
}

type AssetType = Record<string, string | number | null | undefined>

interface Pagination {
  lastPage: number
  page: number
  currentPage: number
}

type DataPoint = [timestamp: number, value: number];

export type Graph = DataPoint[];