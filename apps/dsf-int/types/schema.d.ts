/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
  '/nodiens/api/': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get: operations['getNodiensApi']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/nodiens/api/ping': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get: operations['getNodiensApiPing']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/nodiens/api/health': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get: operations['getNodiensApiHealth']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/nodiens/api/v1/account/profile': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get Profile
     * @description Retrieve current user profile
     */
    get: operations['getNodiensApiV1AccountProfile']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/nodiens/api/v1/account/onboarding': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get Onboarding
     * @description Retrieve current user onboarding
     */
    get: operations['getNodiensApiV1AccountOnboarding']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    /**
     * Set Onboarding
     * @description Set current user onboarding
     */
    patch: operations['patchNodiensApiV1AccountOnboarding']
    trace?: never
  }
  '/nodiens/api/v1/asset/top-rank': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get Top Asset Rank
     * @description Retrieve top asset rank based on sentiment rank and topic occurrences
     */
    get: operations['getNodiensApiV1AssetTop-rank']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/nodiens/api/v1/asset/{type}/{slug}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get Asset Metadata
     * @description Retrieve asset metadata based on asset type
     */
    get: operations['getNodiensApiV1AssetByTypeBySlug']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/nodiens/api/v1/asset/feeds': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get Asset Feeds
     * @description Retrieve asset list based on available data or asset preferences of user with recent analytics value
     */
    get: operations['getNodiensApiV1AssetFeeds']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/nodiens/api/v1/community/asset': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get Asset List
     * @description Retrieve list of community assets
     */
    get: operations['getNodiensApiV1CommunityAsset']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/nodiens/api/v1/community/history/{metric}/{slug}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get Community History
     * @description Retrieve community history
     */
    get: operations['getNodiensApiV1CommunityHistoryByMetricBySlug']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/nodiens/api/v1/plan/': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * List plans
     * @description List all the available plans
     */
    get: operations['getNodiensApiV1Plan']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/nodiens/api/v1/plan/current-subscription': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get Current Subscription
     * @description Get the current subscription
     */
    get: operations['getNodiensApiV1PlanCurrent-subscription']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/nodiens/api/v1/plan/manage-subscription': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Manage Subscription
     * @description Manage the current subscription
     */
    post: operations['postNodiensApiV1PlanManage-subscription']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/nodiens/api/v1/plan/subscribe': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Subscribe
     * @description Subscribe to a plan
     */
    post: operations['postNodiensApiV1PlanSubscribe']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/nodiens/api/v1/plan/switch-plan': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Switch Plan
     * @description Switch to a different plan
     */
    post: operations['postNodiensApiV1PlanSwitch-plan']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/nodiens/api/v1/plan/billing-history': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get Billing History
     * @description Get the billing history
     */
    get: operations['getNodiensApiV1PlanBilling-history']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/nodiens/api/v1/plan/webhook-subscription': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /**
     * Webhook Subscription
     * @description Webhook subscription for receiving stripe events
     */
    post: operations['postNodiensApiV1PlanWebhook-subscription']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/nodiens/api/v1/price-market/volume-history/{slug}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get Market Volume History
     * @description Retrieve market volume history
     */
    get: operations['getNodiensApiV1Price-marketVolume-historyBySlug']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/nodiens/api/v1/mood-index/': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get Mood Overview Table
     * @description Retrieve mood overview table
     */
    get: operations['getNodiensApiV1Mood-index']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/nodiens/api/v1/mood-index/{slug}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get Mood Index History
     * @description Retrieve mood index history
     */
    get: operations['getNodiensApiV1Mood-indexBySlug']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/nodiens/api/v1/topic-trend/': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get Topic Trend Feed
     * @description Retrieve topic trend feed
     */
    get: operations['getNodiensApiV1Topic-trend']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/nodiens/api/v1/topic-trend/{topic}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get Topic Trend History
     * @description Retrieve topic trend history
     */
    get: operations['getNodiensApiV1Topic-trendByTopic']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/nodiens/api/v1/trust-index/': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get Trust Overview Table
     * @description Retrieve Trust overview table
     */
    get: operations['getNodiensApiV1Trust-index']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/nodiens/api/v1/trust-index/{slug}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get Trust Index History
     * @description Retrieve trust index history
     */
    get: operations['getNodiensApiV1Trust-indexBySlug']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/nodiens/api/v1/esg/decentralisation/asset': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get Decentralisation Asset
     * @description Retrieve list of decentralisation asset
     */
    get: operations['getNodiensApiV1EsgDecentralisationAsset']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/nodiens/api/v1/esg/decentralisation/daily-comparison': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get Daily Comparison Decentralisation
     * @description Retrieve daily comparison decentralisation for at least 3 assets
     */
    get: operations['getNodiensApiV1EsgDecentralisationDaily-comparison']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/nodiens/api/v1/esg/decentralisation/history-comparison/{metric}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get History Comparison Decentralisation
     * @description Retrieve history comparison decentralisation for at least 3 assets
     */
    get: operations['getNodiensApiV1EsgDecentralisationHistory-comparisonByMetric']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/nodiens/api/v1/esg/decentralisation/tree/{metric}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get Decentralisation Tree
     * @description Retrieve decentralisation tree
     */
    get: operations['getNodiensApiV1EsgDecentralisationTreeByMetric']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/nodiens/api/v1/financial/asset': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get Financial Asset
     * @description Retrieve list of financial asset
     */
    get: operations['getNodiensApiV1FinancialAsset']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/nodiens/api/v1/financial/daily-comparison': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get Daily Comparison Financial
     * @description Retrieve daily comparison financial
     */
    get: operations['getNodiensApiV1FinancialDaily-comparison']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/nodiens/api/v1/financial/history/{slug}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get Financial History
     * @description Retrieve financial history
     */
    get: operations['getNodiensApiV1FinancialHistoryBySlug']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/nodiens/api/v1/financial/history-comparison': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get History Comparison Financial
     * @description Retrieve history comparison financial
     */
    get: operations['getNodiensApiV1FinancialHistory-comparison']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/nodiens/api/v1/esg/energy-consumption/asset': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get Energy Consumption Asset
     * @description Retrieve list of energy consumption asset
     */
    get: operations['getNodiensApiV1EsgEnergy-consumptionAsset']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/nodiens/api/v1/esg/energy-consumption/': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get Energy Consumption Feeds
     * @description Retrieve list of energy consumption feeds
     */
    get: operations['getNodiensApiV1EsgEnergy-consumption']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/nodiens/api/v1/esg/energy-consumption/{slug}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get Energy Consumption History
     * @description Retrieve energy consumption history
     */
    get: operations['getNodiensApiV1EsgEnergy-consumptionBySlug']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/nodiens/api/v1/esg/carbon-emission/asset': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get Carbon Emission Assets
     * @description Retrieve list of carbon emission assets
     */
    get: operations['getNodiensApiV1EsgCarbon-emissionAsset']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/nodiens/api/v1/esg/carbon-emission/': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get Carbon Emission Feeds
     * @description Retrieve list of carbon emission feeds
     */
    get: operations['getNodiensApiV1EsgCarbon-emission']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/nodiens/api/v1/esg/carbon-emission/{slug}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get Carbon Emission Annualized History
     * @description Retrieve annualized value for carbon emission metric
     */
    get: operations['getNodiensApiV1EsgCarbon-emissionBySlug']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
}
export type webhooks = Record<string, never>
export interface components {
  schemas: never
  responses: never
  parameters: never
  requestBodies: never
  headers: never
  pathItems: never
}
export type $defs = Record<string, never>
export interface operations {
  getNodiensApi: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      200: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
    }
  }
  getNodiensApiPing: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      200: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
    }
  }
  getNodiensApiHealth: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      200: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
    }
  }
  getNodiensApiV1AccountProfile: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Success response for getting user profile */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            /** @description Profile */
            payload: {
              /** @description Profile ID */
              id: string
              /** @description User ID */
              userId: string
              /** @description User display name */
              displayName: string
              /** @description DEPRECETED */
              dashboard: Record<string, never> | null
              /** @description DEPRECETED */
              dashboardChange: number
              createdAt: (Record<string, never> | string | number) | null
              suspendedReason: string | null
              suspendedAt: (Record<string, never> | string | number) | null
              deletedAt: (Record<string, never> | string | number) | null
              role: string | null
              status: string | null
              /** @description Onboarding status of the account */
              rawOnboarding: Record<string, never>
              company: string | null
              /** @description User email */
              email: string
              /** @description Excluded paywall of the account */
              excludedPaywall: boolean
            }
            /** @description Request id for reference */
            reqId: number
          }
          'multipart/form-data': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            /** @description Profile */
            payload: {
              /** @description Profile ID */
              id: string
              /** @description User ID */
              userId: string
              /** @description User display name */
              displayName: string
              /** @description DEPRECETED */
              dashboard: Record<string, never> | null
              /** @description DEPRECETED */
              dashboardChange: number
              createdAt: (Record<string, never> | string | number) | null
              suspendedReason: string | null
              suspendedAt: (Record<string, never> | string | number) | null
              deletedAt: (Record<string, never> | string | number) | null
              role: string | null
              status: string | null
              /** @description Onboarding status of the account */
              rawOnboarding: Record<string, never>
              company: string | null
              /** @description User email */
              email: string
              /** @description Excluded paywall of the account */
              excludedPaywall: boolean
            }
            /** @description Request id for reference */
            reqId: number
          }
          'text/plain': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            /** @description Profile */
            payload: {
              /** @description Profile ID */
              id: string
              /** @description User ID */
              userId: string
              /** @description User display name */
              displayName: string
              /** @description DEPRECETED */
              dashboard: Record<string, never> | null
              /** @description DEPRECETED */
              dashboardChange: number
              createdAt: (Record<string, never> | string | number) | null
              suspendedReason: string | null
              suspendedAt: (Record<string, never> | string | number) | null
              deletedAt: (Record<string, never> | string | number) | null
              role: string | null
              status: string | null
              /** @description Onboarding status of the account */
              rawOnboarding: Record<string, never>
              company: string | null
              /** @description User email */
              email: string
              /** @description Excluded paywall of the account */
              excludedPaywall: boolean
            }
            /** @description Request id for reference */
            reqId: number
          }
        }
      }
      /** @description Fail response when user is not authorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when user is forbidden to access API */
      403: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when user profile is not found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when server have an issue */
      500: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
    }
  }
  getNodiensApiV1AccountOnboarding: {
    parameters: {
      query: {
        /** @description Relative path of the page */
        path: string
      }
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Success response for getting user onboarding */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /**
               * @description Relative path of the page
               * @example /slug-page
               */
              path: string
              /**
               * @description Checked status of the page
               * @example true
               */
              isChecked: boolean
            }
            /** @description Request id for reference */
            reqId: number
          }
          'multipart/form-data': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /**
               * @description Relative path of the page
               * @example /slug-page
               */
              path: string
              /**
               * @description Checked status of the page
               * @example true
               */
              isChecked: boolean
            }
            /** @description Request id for reference */
            reqId: number
          }
          'text/plain': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /**
               * @description Relative path of the page
               * @example /slug-page
               */
              path: string
              /**
               * @description Checked status of the page
               * @example true
               */
              isChecked: boolean
            }
            /** @description Request id for reference */
            reqId: number
          }
        }
      }
      /** @description Fail response when user is not authorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when user is forbidden to access API */
      403: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when server have an issue */
      500: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
    }
  }
  patchNodiensApiV1AccountOnboarding: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': {
          /**
           * @description relative path page
           * @example /slug-page
           */
          path: string
        }
        'multipart/form-data': {
          /**
           * @description relative path page
           * @example /slug-page
           */
          path: string
        }
        'text/plain': {
          /**
           * @description relative path page
           * @example /slug-page
           */
          path: string
        }
      }
    }
    responses: {
      /** @description Success response for setting user onboarding */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /**
               * @description Relative path of the page
               * @example /slug-page
               */
              path: string
              /**
               * @description Checked status of the page
               * @example true
               */
              isChecked: boolean
            }
            /** @description Request id for reference */
            reqId: number
          }
          'multipart/form-data': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /**
               * @description Relative path of the page
               * @example /slug-page
               */
              path: string
              /**
               * @description Checked status of the page
               * @example true
               */
              isChecked: boolean
            }
            /** @description Request id for reference */
            reqId: number
          }
          'text/plain': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /**
               * @description Relative path of the page
               * @example /slug-page
               */
              path: string
              /**
               * @description Checked status of the page
               * @example true
               */
              isChecked: boolean
            }
            /** @description Request id for reference */
            reqId: number
          }
        }
      }
      /** @description Fail response when user is not authorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when user is forbidden to access API */
      403: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when server have an issue */
      500: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
    }
  }
  'getNodiensApiV1AssetTop-rank': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Success response for getting top asset rank */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Unix timestamp of last calculation */
              lastCalculation: number
              /** @description List of top asset on mood index */
              mood: {
                /** @description Asset name */
                name: string
                /** @description Asset slug */
                slug: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Asset type (CRYPTOCURRENCY / STABLECOIN) */
                type: 'CRYPTOCURRENCY' | 'STOCK' | 'STABLECOIN' | 'PLATFORM'
                /** @description Rank position of indexes */
                rank: number
                /** @description Percentage of one day change */
                percentage: string
                /** @description Indicator of one day change */
                indicator: '>' | '<' | '-'
              }[]
              /** @description List of top asset on trust index */
              trust: {
                /** @description Asset name */
                name: string
                /** @description Asset slug */
                slug: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Asset type (CRYPTOCURRENCY / STABLECOIN) */
                type: 'CRYPTOCURRENCY' | 'STOCK' | 'STABLECOIN' | 'PLATFORM'
                /** @description Rank position of indexes */
                rank: number
                /** @description Percentage of one day change */
                percentage: string
                /** @description Indicator of one day change */
                indicator: '>' | '<' | '-'
              }[]
              /** @description List of top topics */
              topicAnalytics: {
                rank: number
                name: string
                slug: string
              }[]
            }
            /** @description Request id for reference */
            reqId: number
          }
          'multipart/form-data': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Unix timestamp of last calculation */
              lastCalculation: number
              /** @description List of top asset on mood index */
              mood: {
                /** @description Asset name */
                name: string
                /** @description Asset slug */
                slug: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Asset type (CRYPTOCURRENCY / STABLECOIN) */
                type: 'CRYPTOCURRENCY' | 'STOCK' | 'STABLECOIN' | 'PLATFORM'
                /** @description Rank position of indexes */
                rank: number
                /** @description Percentage of one day change */
                percentage: string
                /** @description Indicator of one day change */
                indicator: '>' | '<' | '-'
              }[]
              /** @description List of top asset on trust index */
              trust: {
                /** @description Asset name */
                name: string
                /** @description Asset slug */
                slug: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Asset type (CRYPTOCURRENCY / STABLECOIN) */
                type: 'CRYPTOCURRENCY' | 'STOCK' | 'STABLECOIN' | 'PLATFORM'
                /** @description Rank position of indexes */
                rank: number
                /** @description Percentage of one day change */
                percentage: string
                /** @description Indicator of one day change */
                indicator: '>' | '<' | '-'
              }[]
              /** @description List of top topics */
              topicAnalytics: {
                rank: number
                name: string
                slug: string
              }[]
            }
            /** @description Request id for reference */
            reqId: number
          }
          'text/plain': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Unix timestamp of last calculation */
              lastCalculation: number
              /** @description List of top asset on mood index */
              mood: {
                /** @description Asset name */
                name: string
                /** @description Asset slug */
                slug: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Asset type (CRYPTOCURRENCY / STABLECOIN) */
                type: 'CRYPTOCURRENCY' | 'STOCK' | 'STABLECOIN' | 'PLATFORM'
                /** @description Rank position of indexes */
                rank: number
                /** @description Percentage of one day change */
                percentage: string
                /** @description Indicator of one day change */
                indicator: '>' | '<' | '-'
              }[]
              /** @description List of top asset on trust index */
              trust: {
                /** @description Asset name */
                name: string
                /** @description Asset slug */
                slug: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Asset type (CRYPTOCURRENCY / STABLECOIN) */
                type: 'CRYPTOCURRENCY' | 'STOCK' | 'STABLECOIN' | 'PLATFORM'
                /** @description Rank position of indexes */
                rank: number
                /** @description Percentage of one day change */
                percentage: string
                /** @description Indicator of one day change */
                indicator: '>' | '<' | '-'
              }[]
              /** @description List of top topics */
              topicAnalytics: {
                rank: number
                name: string
                slug: string
              }[]
            }
            /** @description Request id for reference */
            reqId: number
          }
        }
      }
      /** @description Fail response when server have an issue */
      500: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
    }
  }
  getNodiensApiV1AssetByTypeBySlug: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description Slug of the asset */
        slug: string
        /** @description Type of the asset (crypto / stable) */
        type: 'crypto' | 'stable'
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Success response for getting asset metadata */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Asset name */
              name: string
              logo: string | null
              /** @description Asset type (CRYPTOCURRENCY / STABLECOIN) */
              type: 'CRYPTOCURRENCY' | 'STOCK' | 'STABLECOIN' | 'PLATFORM'
              description: string | null
              website: string | null
              /** @description Asset symbol */
              symbol: string
              moodRankPosition: number | null
              trustRankPosition: number | null
              price: string | null
              /** @description Number of message of the asset */
              messageCount: string
              /** @description Number of bot of the asset */
              botCount: string
              /** @description Number of bad words of the asset */
              badWords: string
              /** @description Timestamp (on unix format) of latest analytics calculation between social metrics and price market */
              latestCalculation: number
              /** @description Slug of ESG of the asset */
              esgSlug: string | null
              /** @description List of URLs of the asset */
              urls: {
                /** @description URL of asset */
                url: string
                /** @description Name of URL asset */
                name: string
                /** @description Type of URL asset */
                type: string
              }[]
            }
            /** @description Request id for reference */
            reqId: number
          }
          'multipart/form-data': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Asset name */
              name: string
              logo: string | null
              /** @description Asset type (CRYPTOCURRENCY / STABLECOIN) */
              type: 'CRYPTOCURRENCY' | 'STOCK' | 'STABLECOIN' | 'PLATFORM'
              description: string | null
              website: string | null
              /** @description Asset symbol */
              symbol: string
              moodRankPosition: number | null
              trustRankPosition: number | null
              price: string | null
              /** @description Number of message of the asset */
              messageCount: string
              /** @description Number of bot of the asset */
              botCount: string
              /** @description Number of bad words of the asset */
              badWords: string
              /** @description Timestamp (on unix format) of latest analytics calculation between social metrics and price market */
              latestCalculation: number
              /** @description Slug of ESG of the asset */
              esgSlug: string | null
              /** @description List of URLs of the asset */
              urls: {
                /** @description URL of asset */
                url: string
                /** @description Name of URL asset */
                name: string
                /** @description Type of URL asset */
                type: string
              }[]
            }
            /** @description Request id for reference */
            reqId: number
          }
          'text/plain': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Asset name */
              name: string
              logo: string | null
              /** @description Asset type (CRYPTOCURRENCY / STABLECOIN) */
              type: 'CRYPTOCURRENCY' | 'STOCK' | 'STABLECOIN' | 'PLATFORM'
              description: string | null
              website: string | null
              /** @description Asset symbol */
              symbol: string
              moodRankPosition: number | null
              trustRankPosition: number | null
              price: string | null
              /** @description Number of message of the asset */
              messageCount: string
              /** @description Number of bot of the asset */
              botCount: string
              /** @description Number of bad words of the asset */
              badWords: string
              /** @description Timestamp (on unix format) of latest analytics calculation between social metrics and price market */
              latestCalculation: number
              /** @description Slug of ESG of the asset */
              esgSlug: string | null
              /** @description List of URLs of the asset */
              urls: {
                /** @description URL of asset */
                url: string
                /** @description Name of URL asset */
                name: string
                /** @description Type of URL asset */
                type: string
              }[]
            }
            /** @description Request id for reference */
            reqId: number
          }
        }
      }
      /** @description Fail response when user is not authorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when user is forbidden to access API */
      403: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when asset is not found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when server have an issue */
      500: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
    }
  }
  getNodiensApiV1AssetFeeds: {
    parameters: {
      query?: {
        /** @description This field was used to search asset */
        terms?: string
        /** @description This field was used to filter assets by type (multiple values should separated by comma). Available values: CRYPTOCURRENCY, STABLECOIN */
        assetType?: ('CRYPTOCURRENCY' | 'STABLECOIN')[]
        /** @description This field was used to sort assets. Available values: energyConsumptionTx, trustIndex, moodIndex, communitySize, nrMessages */
        sortBy?: 'energyConsumptionTx' | 'trustIndex' | 'moodIndex' | 'communitySize' | 'nrMessages'
        /** @description This field was used for pagination */
        page?: string | number
        /** @description This field was used to limit items per page */
        perPage?: string | number
      }
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Success response for getting asset feeds */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Current page number */
              currentPage: number
              /** @description Number of items per page */
              perPage: number
              /** @description Last page number */
              lastPage: number
              data: {
                /** @description Asset name */
                name: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Asset slug */
                slug: string
                /** @description Asset type (CRYPTOCURRENCY / STABLECOIN) */
                type: 'CRYPTOCURRENCY' | 'STOCK' | 'STABLECOIN' | 'PLATFORM'
                /** @description Mood index of the asset */
                moodIndex: string
                /** @description Trust index of the asset */
                trustIndex: string
                /** @description Community size of the asset */
                communitySize: string
                /** @description Number of message of the asset */
                numberOfMessage: string
                /** @description Energy consumption per tx of the asset */
                energyConsumptionPerTx: string
                /** @description Graph of the asset based on user rank selection */
                graph: [number, number][]
              }[]
              /** @description Total of items */
              totalItem: number
            }
            /** @description Request id for reference */
            reqId: number
          }
          'multipart/form-data': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Current page number */
              currentPage: number
              /** @description Number of items per page */
              perPage: number
              /** @description Last page number */
              lastPage: number
              data: {
                /** @description Asset name */
                name: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Asset slug */
                slug: string
                /** @description Asset type (CRYPTOCURRENCY / STABLECOIN) */
                type: 'CRYPTOCURRENCY' | 'STOCK' | 'STABLECOIN' | 'PLATFORM'
                /** @description Mood index of the asset */
                moodIndex: string
                /** @description Trust index of the asset */
                trustIndex: string
                /** @description Community size of the asset */
                communitySize: string
                /** @description Number of message of the asset */
                numberOfMessage: string
                /** @description Energy consumption per tx of the asset */
                energyConsumptionPerTx: string
                /** @description Graph of the asset based on user rank selection */
                graph: [number, number][]
              }[]
              /** @description Total of items */
              totalItem: number
            }
            /** @description Request id for reference */
            reqId: number
          }
          'text/plain': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Current page number */
              currentPage: number
              /** @description Number of items per page */
              perPage: number
              /** @description Last page number */
              lastPage: number
              data: {
                /** @description Asset name */
                name: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Asset slug */
                slug: string
                /** @description Asset type (CRYPTOCURRENCY / STABLECOIN) */
                type: 'CRYPTOCURRENCY' | 'STOCK' | 'STABLECOIN' | 'PLATFORM'
                /** @description Mood index of the asset */
                moodIndex: string
                /** @description Trust index of the asset */
                trustIndex: string
                /** @description Community size of the asset */
                communitySize: string
                /** @description Number of message of the asset */
                numberOfMessage: string
                /** @description Energy consumption per tx of the asset */
                energyConsumptionPerTx: string
                /** @description Graph of the asset based on user rank selection */
                graph: [number, number][]
              }[]
              /** @description Total of items */
              totalItem: number
            }
            /** @description Request id for reference */
            reqId: number
          }
        }
      }
      /** @description Fail response when server have an issue */
      500: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
    }
  }
  getNodiensApiV1CommunityAsset: {
    parameters: {
      query?: {
        /** @description This field was used for pagination */
        page?: string | number
        /** @description This field was used to limit items per page */
        perPage?: string | number
        /** @description This field was used to search asset */
        terms?: string
        /** @description This field was used to filter assets by type */
        type?: ('CRYPTOCURRENCY' | 'STABLECOIN')[]
      }
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Success response for getting community asset list */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Current page number */
              currentPage: number
              /** @description Number of items per page */
              perPage: number
              /** @description Last page number */
              lastPage: number
              data: {
                /** @description Asset name */
                name: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Asset slug */
                slug: string
                /** @description Asset type */
                type: 'CRYPTOCURRENCY' | 'STOCK' | 'STABLECOIN' | 'PLATFORM'
              }[]
              /** @description Total of items */
              totalItem: number
            }
            /** @description Request id for reference */
            reqId: number
          }
          'multipart/form-data': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Current page number */
              currentPage: number
              /** @description Number of items per page */
              perPage: number
              /** @description Last page number */
              lastPage: number
              data: {
                /** @description Asset name */
                name: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Asset slug */
                slug: string
                /** @description Asset type */
                type: 'CRYPTOCURRENCY' | 'STOCK' | 'STABLECOIN' | 'PLATFORM'
              }[]
              /** @description Total of items */
              totalItem: number
            }
            /** @description Request id for reference */
            reqId: number
          }
          'text/plain': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Current page number */
              currentPage: number
              /** @description Number of items per page */
              perPage: number
              /** @description Last page number */
              lastPage: number
              data: {
                /** @description Asset name */
                name: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Asset slug */
                slug: string
                /** @description Asset type */
                type: 'CRYPTOCURRENCY' | 'STOCK' | 'STABLECOIN' | 'PLATFORM'
              }[]
              /** @description Total of items */
              totalItem: number
            }
            /** @description Request id for reference */
            reqId: number
          }
        }
      }
      /** @description Fail response when user is not authorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when server have an issue */
      500: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
    }
  }
  getNodiensApiV1CommunityHistoryByMetricBySlug: {
    parameters: {
      query: {
        /** @description Selected layer 1 for showing whole summarized data from layer 2 */
        layer1: boolean | string
        /** @description Selected layer 2 for showing whole summarized data from layer 1 based on SNS Platform (for multiple platforms, use comma separated) */
        layer2?: ('reddit' | 'telegram')[]
        /** @description Selected layer 3 for showing whole summarized data from layer 1 based on Community ID (for multiple communities, use comma separated) */
        layer3?: (number | string)[]
        /** @description Start date of community history */
        startDate?: string
        /** @description End date of community history */
        endDate?: string
      }
      header?: never
      path: {
        slug: string
        /** @description This field was used to select community metrics */
        metric: 'MESSAGE_COUNT' | 'MEMBER_COUNT'
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Success response for getting community history */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Asset name */
              name: string
              /** @description Asset slug */
              slug: string
              logo: string | null
              /** @description Asset symbol */
              symbol: string
              options: {
                layer1: boolean
                layer2: {
                  label: string
                  value: 'reddit' | 'telegram'
                }[]
                layer3: {
                  platform: 'reddit' | 'telegram'
                  community: {
                    label: string
                    value: string
                  }[]
                }[]
              }
              /** @description Dataset of layer 1 mood index */
              layer1: [number, number][]
              /** @description Dataset of layer 2 mood index separated by sns platform */
              layer2: {
                snsPlatform: 'reddit' | 'telegram'
                history: [number, number][]
              }[]
              /** @description Dataset of layer 3 mood index separated by community ids */
              layer3: {
                communityId: number
                name: string
                platform: 'reddit' | 'telegram'
                history: [number, number][]
              }[]
              startDate: number | null
              endDate: number | null
            }
            /** @description Request id for reference */
            reqId: number
          }
          'multipart/form-data': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Asset name */
              name: string
              /** @description Asset slug */
              slug: string
              logo: string | null
              /** @description Asset symbol */
              symbol: string
              options: {
                layer1: boolean
                layer2: {
                  label: string
                  value: 'reddit' | 'telegram'
                }[]
                layer3: {
                  platform: 'reddit' | 'telegram'
                  community: {
                    label: string
                    value: string
                  }[]
                }[]
              }
              /** @description Dataset of layer 1 mood index */
              layer1: [number, number][]
              /** @description Dataset of layer 2 mood index separated by sns platform */
              layer2: {
                snsPlatform: 'reddit' | 'telegram'
                history: [number, number][]
              }[]
              /** @description Dataset of layer 3 mood index separated by community ids */
              layer3: {
                communityId: number
                name: string
                platform: 'reddit' | 'telegram'
                history: [number, number][]
              }[]
              startDate: number | null
              endDate: number | null
            }
            /** @description Request id for reference */
            reqId: number
          }
          'text/plain': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Asset name */
              name: string
              /** @description Asset slug */
              slug: string
              logo: string | null
              /** @description Asset symbol */
              symbol: string
              options: {
                layer1: boolean
                layer2: {
                  label: string
                  value: 'reddit' | 'telegram'
                }[]
                layer3: {
                  platform: 'reddit' | 'telegram'
                  community: {
                    label: string
                    value: string
                  }[]
                }[]
              }
              /** @description Dataset of layer 1 mood index */
              layer1: [number, number][]
              /** @description Dataset of layer 2 mood index separated by sns platform */
              layer2: {
                snsPlatform: 'reddit' | 'telegram'
                history: [number, number][]
              }[]
              /** @description Dataset of layer 3 mood index separated by community ids */
              layer3: {
                communityId: number
                name: string
                platform: 'reddit' | 'telegram'
                history: [number, number][]
              }[]
              startDate: number | null
              endDate: number | null
            }
            /** @description Request id for reference */
            reqId: number
          }
        }
      }
      /** @description Fail response when user is not authorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when user is forbidden to access */
      403: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when community history is not found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when server have an issue */
      500: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
    }
  }
  getNodiensApiV1Plan: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Success response for listing all the available plans */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Plan ID */
              id: number
              /** @description Plan name */
              name: string
              description: string | null
              /** @description Plan monthly price on USD */
              monthlyPrice: number
              /** @description Plan annual price on USD */
              annualPrice: number
              isEnabled: boolean
              level: number
              /** @description List of available features */
              features: Record<string, never>
            }[]
            /** @description Request id for reference */
            reqId: number
          }
          'multipart/form-data': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Plan ID */
              id: number
              /** @description Plan name */
              name: string
              description: string | null
              /** @description Plan monthly price on USD */
              monthlyPrice: number
              /** @description Plan annual price on USD */
              annualPrice: number
              isEnabled: boolean
              level: number
              /** @description List of available features */
              features: Record<string, never>
            }[]
            /** @description Request id for reference */
            reqId: number
          }
          'text/plain': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Plan ID */
              id: number
              /** @description Plan name */
              name: string
              description: string | null
              /** @description Plan monthly price on USD */
              monthlyPrice: number
              /** @description Plan annual price on USD */
              annualPrice: number
              isEnabled: boolean
              level: number
              /** @description List of available features */
              features: Record<string, never>
            }[]
            /** @description Request id for reference */
            reqId: number
          }
        }
      }
    }
  }
  'getNodiensApiV1PlanCurrent-subscription': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Success response for getting the current subscription */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Subscription ID */
              id: number
              /** @description Member ID */
              memberId: string
              /** @description Plan ID */
              planId: number
              /** @description Subscription status */
              status: string
              /** @description Payment status */
              paymentStatus: string
              activeAt: (Record<string, never> | string | number) | null
              expiredDate: (Record<string, never> | string | number) | null
              switchRawData: Record<string, never> | null
              rawData: Record<string, never> | null
              plan: Record<string, never> | null
              features: Record<string, never> | null
              futurePreferences:
                | {
                    name: string
                    logo: string
                    symbol: string
                  }[]
                | null
            } | null
            /** @description Request id for reference */
            reqId: number
          }
          'multipart/form-data': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Subscription ID */
              id: number
              /** @description Member ID */
              memberId: string
              /** @description Plan ID */
              planId: number
              /** @description Subscription status */
              status: string
              /** @description Payment status */
              paymentStatus: string
              activeAt: (Record<string, never> | string | number) | null
              expiredDate: (Record<string, never> | string | number) | null
              switchRawData: Record<string, never> | null
              rawData: Record<string, never> | null
              plan: Record<string, never> | null
              features: Record<string, never> | null
              futurePreferences:
                | {
                    name: string
                    logo: string
                    symbol: string
                  }[]
                | null
            } | null
            /** @description Request id for reference */
            reqId: number
          }
          'text/plain': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Subscription ID */
              id: number
              /** @description Member ID */
              memberId: string
              /** @description Plan ID */
              planId: number
              /** @description Subscription status */
              status: string
              /** @description Payment status */
              paymentStatus: string
              activeAt: (Record<string, never> | string | number) | null
              expiredDate: (Record<string, never> | string | number) | null
              switchRawData: Record<string, never> | null
              rawData: Record<string, never> | null
              plan: Record<string, never> | null
              features: Record<string, never> | null
              futurePreferences:
                | {
                    name: string
                    logo: string
                    symbol: string
                  }[]
                | null
            } | null
            /** @description Request id for reference */
            reqId: number
          }
        }
      }
      /** @description Fail response when user is not authorized */
      403: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when current subscription is not found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
    }
  }
  'postNodiensApiV1PlanManage-subscription': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Success response for returning manage stripe url */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description URL for managing subscription plan from stripe */
              manageUrl: string
            }
            /** @description Request id for reference */
            reqId: number
          }
          'multipart/form-data': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description URL for managing subscription plan from stripe */
              manageUrl: string
            }
            /** @description Request id for reference */
            reqId: number
          }
          'text/plain': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description URL for managing subscription plan from stripe */
              manageUrl: string
            }
            /** @description Request id for reference */
            reqId: number
          }
        }
      }
      /** @description Fail response when user is not authorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when user is forbidden to access API */
      403: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when subscription is not found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when server have an issue */
      500: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
    }
  }
  postNodiensApiV1PlanSubscribe: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': {
          /** @description Product ID */
          product_id: number
          /** @description Enable annual plan */
          is_annual: boolean
          assets: number[]
        }
        'multipart/form-data': {
          /** @description Product ID */
          product_id: number
          /** @description Enable annual plan */
          is_annual: boolean
          assets: number[]
        }
        'text/plain': {
          /** @description Product ID */
          product_id: number
          /** @description Enable annual plan */
          is_annual: boolean
          assets: number[]
        }
      }
    }
    responses: {
      /** @description Success response for returning checkout url */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              checkoutUrl: string | null
            }
            /** @description Request id for reference */
            reqId: number
          }
          'multipart/form-data': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              checkoutUrl: string | null
            }
            /** @description Request id for reference */
            reqId: number
          }
          'text/plain': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              checkoutUrl: string | null
            }
            /** @description Request id for reference */
            reqId: number
          }
        }
      }
      /** @description Fail response when user is not authorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when subscription is not found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when server have an issue */
      500: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
    }
  }
  'postNodiensApiV1PlanSwitch-plan': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': {
          /** @description Product ID */
          product_id: number
          /** @description Enable annual plan */
          is_annual: boolean
          assets: number[]
        }
        'multipart/form-data': {
          /** @description Product ID */
          product_id: number
          /** @description Enable annual plan */
          is_annual: boolean
          assets: number[]
        }
        'text/plain': {
          /** @description Product ID */
          product_id: number
          /** @description Enable annual plan */
          is_annual: boolean
          assets: number[]
        }
      }
    }
    responses: {
      /** @description Success response for returning upgrade url */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              upgradeUrl: string
            }
            /** @description Request id for reference */
            reqId: number
          }
          'multipart/form-data': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              upgradeUrl: string
            }
            /** @description Request id for reference */
            reqId: number
          }
          'text/plain': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              upgradeUrl: string
            }
            /** @description Request id for reference */
            reqId: number
          }
        }
      }
      /** @description Fail response when user is not authorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when server have an issue */
      500: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
    }
  }
  'getNodiensApiV1PlanBilling-history': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Success response for getting the billing history */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Billing ID */
              id: unknown
              /** @description Billing description */
              description: string
              /** @description Billing amount */
              amount: string
              /** @description Billing creation date */
              date: number
              invoice_url: string | null
            }[]
            /** @description Request id for reference */
            reqId: number
          }
          'multipart/form-data': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Billing ID */
              id: unknown
              /** @description Billing description */
              description: string
              /** @description Billing amount */
              amount: string
              /** @description Billing creation date */
              date: number
              invoice_url: string | null
            }[]
            /** @description Request id for reference */
            reqId: number
          }
          'text/plain': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Billing ID */
              id: unknown
              /** @description Billing description */
              description: string
              /** @description Billing amount */
              amount: string
              /** @description Billing creation date */
              date: number
              invoice_url: string | null
            }[]
            /** @description Request id for reference */
            reqId: number
          }
        }
      }
      /** @description Fail response when user is not authorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when server have an issue */
      500: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
    }
  }
  'postNodiensApiV1PlanWebhook-subscription': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Success response for webhook subscription */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              received: boolean
            }
            /** @description Request id for reference */
            reqId: number
          }
          'multipart/form-data': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              received: boolean
            }
            /** @description Request id for reference */
            reqId: number
          }
          'text/plain': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              received: boolean
            }
            /** @description Request id for reference */
            reqId: number
          }
        }
      }
    }
  }
  'getNodiensApiV1Price-marketVolume-historyBySlug': {
    parameters: {
      query?: {
        /** @description Start date of market volume history using format yyyy-MM-dd */
        startDate?: string
        /** @description Start date of market volume history using format yyyy-MM-dd */
        endDate?: string
      }
      header?: never
      path: {
        /** @description Asset slug */
        slug: string
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Success response for getting market volume history */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Asset name */
              name: string
              logo: string | null
              /** @description Asset symbol */
              symbol: string
              /** @description Asset slug */
              slug: string
              /** @description Timeseries data of market volume */
              entries: [number, number][]
              /** @description Start date of market volume history (unix timestamp) */
              startDate: number | null
              /** @description End date of market volume history (unix timestamp) */
              endDate: number | null
            }
            /** @description Request id for reference */
            reqId: number
          }
          'multipart/form-data': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Asset name */
              name: string
              logo: string | null
              /** @description Asset symbol */
              symbol: string
              /** @description Asset slug */
              slug: string
              /** @description Timeseries data of market volume */
              entries: [number, number][]
              /** @description Start date of market volume history (unix timestamp) */
              startDate: number | null
              /** @description End date of market volume history (unix timestamp) */
              endDate: number | null
            }
            /** @description Request id for reference */
            reqId: number
          }
          'text/plain': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Asset name */
              name: string
              logo: string | null
              /** @description Asset symbol */
              symbol: string
              /** @description Asset slug */
              slug: string
              /** @description Timeseries data of market volume */
              entries: [number, number][]
              /** @description Start date of market volume history (unix timestamp) */
              startDate: number | null
              /** @description End date of market volume history (unix timestamp) */
              endDate: number | null
            }
            /** @description Request id for reference */
            reqId: number
          }
        }
      }
      /** @description Fail response when user is not authorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when user is forbidden to access API */
      403: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when asset is not available */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when server have an issue */
      500: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
    }
  }
  'getNodiensApiV1Mood-index': {
    parameters: {
      query?: {
        /** @description This field was used for pagination */
        page?: number
        /** @description This field was used to limit items per page */
        perPage?: number
        /** @description This field was used to search asset */
        terms?: string
        /** @description The asset type of the asset (multiple values should separated by comma). Available values: CRYPTOCURRENCY, STABLECOIN */
        type?: ('CRYPTOCURRENCY' | 'STABLECOIN')[]
        /** @description The asset type of the asset. Available values: COMMUNITY_SIZE, MESSAGE_COUNT, RANK_INDICATOR */
        rank?: 'COMMUNITY_SIZE' | 'MESSAGE_COUNT' | 'RANK_INDICATOR' | 'A_DAY_CHANGED_PERCENTAGE'
      }
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Success response for getting mood overview table */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Current page number */
              currentPage: number
              /** @description Number of items per page */
              perPage: number
              /** @description Last page number */
              lastPage: number
              data: {
                /** @description Asset name */
                name: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Asset slug */
                slug: string
                /** @description Asset type (CRYPTOCURRENCY / STABLECOIN) */
                type: 'CRYPTOCURRENCY' | 'STOCK' | 'STABLECOIN' | 'PLATFORM'
                /** @description Mood index value of asset */
                value: string
                /** @description Community size of asset */
                communitySize: string
                /** @description Message count of asset based on whole SNS platform */
                numberOfMessages: string
                /** @description One day change percentage of mood index */
                aDayChangePercentage: string
                /** @description Timestamp of calculation */
                timestamp: Record<string, never> | string | number
                /** @description Percentage indicator of day change percentage */
                percentageIndicator: '>' | '<' | '='
                /** @description Graph of mood index */
                graph: [number, number][]
              }[]
              /** @description Total of items */
              totalItem: number
            }
            /** @description Request id for reference */
            reqId: number
          }
          'multipart/form-data': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Current page number */
              currentPage: number
              /** @description Number of items per page */
              perPage: number
              /** @description Last page number */
              lastPage: number
              data: {
                /** @description Asset name */
                name: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Asset slug */
                slug: string
                /** @description Asset type (CRYPTOCURRENCY / STABLECOIN) */
                type: 'CRYPTOCURRENCY' | 'STOCK' | 'STABLECOIN' | 'PLATFORM'
                /** @description Mood index value of asset */
                value: string
                /** @description Community size of asset */
                communitySize: string
                /** @description Message count of asset based on whole SNS platform */
                numberOfMessages: string
                /** @description One day change percentage of mood index */
                aDayChangePercentage: string
                /** @description Timestamp of calculation */
                timestamp: Record<string, never> | string | number
                /** @description Percentage indicator of day change percentage */
                percentageIndicator: '>' | '<' | '='
                /** @description Graph of mood index */
                graph: [number, number][]
              }[]
              /** @description Total of items */
              totalItem: number
            }
            /** @description Request id for reference */
            reqId: number
          }
          'text/plain': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Current page number */
              currentPage: number
              /** @description Number of items per page */
              perPage: number
              /** @description Last page number */
              lastPage: number
              data: {
                /** @description Asset name */
                name: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Asset slug */
                slug: string
                /** @description Asset type (CRYPTOCURRENCY / STABLECOIN) */
                type: 'CRYPTOCURRENCY' | 'STOCK' | 'STABLECOIN' | 'PLATFORM'
                /** @description Mood index value of asset */
                value: string
                /** @description Community size of asset */
                communitySize: string
                /** @description Message count of asset based on whole SNS platform */
                numberOfMessages: string
                /** @description One day change percentage of mood index */
                aDayChangePercentage: string
                /** @description Timestamp of calculation */
                timestamp: Record<string, never> | string | number
                /** @description Percentage indicator of day change percentage */
                percentageIndicator: '>' | '<' | '='
                /** @description Graph of mood index */
                graph: [number, number][]
              }[]
              /** @description Total of items */
              totalItem: number
            }
            /** @description Request id for reference */
            reqId: number
          }
        }
      }
      /** @description Fail response when user is not authorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when user is forbidden to access API */
      403: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when server have an issue */
      500: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
    }
  }
  'getNodiensApiV1Mood-indexBySlug': {
    parameters: {
      query?: {
        /** @description Selected layer 1 for showing mood index data */
        layer1?: boolean
        /** @description Selected layer 2 for showing sns platform data. Available platforms are: reddit, telegram */
        layer2?: ('reddit' | 'telegram')[]
        /** @description Selected layer 3 for showing community data */
        layer3?: number[]
        /** @description Start date of mood index history */
        startDate?: string
        /** @description End date of mood index history */
        endDate?: string
      }
      header?: never
      path: {
        slug: string
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Success response for getting mood index history */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Asset name */
              name: string
              /** @description Asset slug */
              slug: string
              logo: string | null
              /** @description Asset symbol */
              symbol: string
              /** @description Latest value of mood index */
              latestValue: string
              /** @description Latest timestamp of mood index */
              latestTimestamp: number
              /** @description One day change percentage of mood index */
              aDayChangePercentage: string
              /** @description One day change indicator of mood index */
              aDayChangeIndicator: '>' | '<' | '='
              options: {
                /** @description Available options layer 1 for showing mood index data */
                layer1: boolean
                /** @description Available options layer 2 for showing sns platform data */
                layer2: {
                  label: string
                  value: 'reddit' | 'telegram'
                }[]
                /** @description Available options layer 3 for showing community data */
                layer3: {
                  platform: 'reddit' | 'telegram'
                  community: {
                    label: string
                    value: string
                  }[]
                }[]
              }
              /** @description Dataset of layer 1 mood index */
              layer1: [number, number][]
              /** @description Dataset of layer 2 mood index separated by sns platform */
              layer2: {
                snsPlatform: 'reddit' | 'telegram'
                history: [number, number][]
              }[]
              /** @description Dataset of layer 3 mood index separated by community ids */
              layer3: {
                communityId: number
                name: string
                platform: 'reddit' | 'telegram'
                history: [number, number][]
              }[]
              /** @description Start date of mood index history (unix timestamp) */
              startDate: number | null
              /** @description End date of mood index history (unix timestamp) */
              endDate: number | null
            }
            /** @description Request id for reference */
            reqId: number
          }
          'multipart/form-data': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Asset name */
              name: string
              /** @description Asset slug */
              slug: string
              logo: string | null
              /** @description Asset symbol */
              symbol: string
              /** @description Latest value of mood index */
              latestValue: string
              /** @description Latest timestamp of mood index */
              latestTimestamp: number
              /** @description One day change percentage of mood index */
              aDayChangePercentage: string
              /** @description One day change indicator of mood index */
              aDayChangeIndicator: '>' | '<' | '='
              options: {
                /** @description Available options layer 1 for showing mood index data */
                layer1: boolean
                /** @description Available options layer 2 for showing sns platform data */
                layer2: {
                  label: string
                  value: 'reddit' | 'telegram'
                }[]
                /** @description Available options layer 3 for showing community data */
                layer3: {
                  platform: 'reddit' | 'telegram'
                  community: {
                    label: string
                    value: string
                  }[]
                }[]
              }
              /** @description Dataset of layer 1 mood index */
              layer1: [number, number][]
              /** @description Dataset of layer 2 mood index separated by sns platform */
              layer2: {
                snsPlatform: 'reddit' | 'telegram'
                history: [number, number][]
              }[]
              /** @description Dataset of layer 3 mood index separated by community ids */
              layer3: {
                communityId: number
                name: string
                platform: 'reddit' | 'telegram'
                history: [number, number][]
              }[]
              /** @description Start date of mood index history (unix timestamp) */
              startDate: number | null
              /** @description End date of mood index history (unix timestamp) */
              endDate: number | null
            }
            /** @description Request id for reference */
            reqId: number
          }
          'text/plain': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Asset name */
              name: string
              /** @description Asset slug */
              slug: string
              logo: string | null
              /** @description Asset symbol */
              symbol: string
              /** @description Latest value of mood index */
              latestValue: string
              /** @description Latest timestamp of mood index */
              latestTimestamp: number
              /** @description One day change percentage of mood index */
              aDayChangePercentage: string
              /** @description One day change indicator of mood index */
              aDayChangeIndicator: '>' | '<' | '='
              options: {
                /** @description Available options layer 1 for showing mood index data */
                layer1: boolean
                /** @description Available options layer 2 for showing sns platform data */
                layer2: {
                  label: string
                  value: 'reddit' | 'telegram'
                }[]
                /** @description Available options layer 3 for showing community data */
                layer3: {
                  platform: 'reddit' | 'telegram'
                  community: {
                    label: string
                    value: string
                  }[]
                }[]
              }
              /** @description Dataset of layer 1 mood index */
              layer1: [number, number][]
              /** @description Dataset of layer 2 mood index separated by sns platform */
              layer2: {
                snsPlatform: 'reddit' | 'telegram'
                history: [number, number][]
              }[]
              /** @description Dataset of layer 3 mood index separated by community ids */
              layer3: {
                communityId: number
                name: string
                platform: 'reddit' | 'telegram'
                history: [number, number][]
              }[]
              /** @description Start date of mood index history (unix timestamp) */
              startDate: number | null
              /** @description End date of mood index history (unix timestamp) */
              endDate: number | null
            }
            /** @description Request id for reference */
            reqId: number
          }
        }
      }
      /** @description Fail response when user is not authorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when user is forbidden to access API */
      403: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when asset is not found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when server have an issue */
      500: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
    }
  }
  'getNodiensApiV1Topic-trend': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Success response for getting topic trend feed */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Topic name */
              topic: string
              /** @description Number of occurrences */
              occurrences: number
              /** @description Short history of topic trends */
              graph: [number, number][]
              /** @description List of platforms */
              platforms: ('reddit' | 'telegram')[]
            }[]
            /** @description Request id for reference */
            reqId: number
          }
          'multipart/form-data': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Topic name */
              topic: string
              /** @description Number of occurrences */
              occurrences: number
              /** @description Short history of topic trends */
              graph: [number, number][]
              /** @description List of platforms */
              platforms: ('reddit' | 'telegram')[]
            }[]
            /** @description Request id for reference */
            reqId: number
          }
          'text/plain': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Topic name */
              topic: string
              /** @description Number of occurrences */
              occurrences: number
              /** @description Short history of topic trends */
              graph: [number, number][]
              /** @description List of platforms */
              platforms: ('reddit' | 'telegram')[]
            }[]
            /** @description Request id for reference */
            reqId: number
          }
        }
      }
      /** @description Fail response when user does not have plan or does not have metrics under subscription */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when user is not authorized */
      403: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when server have an issue */
      500: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
    }
  }
  'getNodiensApiV1Topic-trendByTopic': {
    parameters: {
      query?: {
        /** @description Start date of topic trend history using format yyyy-MM-dd */
        startDate?: string
        /** @description End date of topic trend history using format yyyy-MM-dd */
        endDate?: string
      }
      header?: never
      path: {
        /** @description Topic name */
        topic: string
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Success response for getting topic trend history */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              analytics: {
                /** @description SNS platform of topic name */
                platform: 'reddit' | 'telegram' | 'all'
                /** @description Full history of topic trends */
                history: [number, number][]
              }[]
              /** @description Start date of topic trend history (on unix timestamp format) */
              startDate: number | null
              /** @description End date of topic trend history (on unix timestamp format) */
              endDate: number | null
            }
            /** @description Request id for reference */
            reqId: number
          }
          'multipart/form-data': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              analytics: {
                /** @description SNS platform of topic name */
                platform: 'reddit' | 'telegram' | 'all'
                /** @description Full history of topic trends */
                history: [number, number][]
              }[]
              /** @description Start date of topic trend history (on unix timestamp format) */
              startDate: number | null
              /** @description End date of topic trend history (on unix timestamp format) */
              endDate: number | null
            }
            /** @description Request id for reference */
            reqId: number
          }
          'text/plain': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              analytics: {
                /** @description SNS platform of topic name */
                platform: 'reddit' | 'telegram' | 'all'
                /** @description Full history of topic trends */
                history: [number, number][]
              }[]
              /** @description Start date of topic trend history (on unix timestamp format) */
              startDate: number | null
              /** @description End date of topic trend history (on unix timestamp format) */
              endDate: number | null
            }
            /** @description Request id for reference */
            reqId: number
          }
        }
      }
      /** @description Fail response when user is not authorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when user is forbidden to access API */
      403: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when topic trend history is not found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when server have an issue */
      500: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
    }
  }
  'getNodiensApiV1Trust-index': {
    parameters: {
      query?: {
        /** @description This field was used for pagination */
        page?: string | number
        /** @description This field was used to limit items per page */
        perPage?: string | number
        /** @description This field was used to search asset */
        terms?: string
        /** @description The asset type of the asset (multiple values should separated by comma). Available options are: CRYPTOCURRENCY, STABLECOIN */
        type?: ('CRYPTOCURRENCY' | 'STABLECOIN')[]
        /** @description The asset type of the asset */
        rank?: 'COMMUNITY_SIZE' | 'MESSAGE_COUNT' | 'RANK_INDICATOR' | 'A_DAY_CHANGED_PERCENTAGE'
      }
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Success response for getting trust overview table */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Current page number */
              currentPage: number
              /** @description Number of items per page */
              perPage: number
              /** @description Last page number */
              lastPage: number
              data: {
                /** @description Asset name */
                name: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Asset slug */
                slug: string
                /** @description Asset type (CRYPTOCURRENCY / STABLECOIN) */
                type: 'CRYPTOCURRENCY' | 'STOCK' | 'STABLECOIN' | 'PLATFORM'
                /** @description Trust index value of asset */
                value: string
                /** @description Community size of asset */
                communitySize: string
                /** @description Message count of asset based on whole SNS platform */
                numberOfMessages: string
                /** @description One day change percentage of trust index */
                aDayChangePercentage: string
                /** @description Timestamp of calculation */
                timestamp: Record<string, never> | string | number
                /** @description Percentage indicator of day change percentage */
                percentageIndicator: '>' | '<' | '='
                /** @description Graph of trust index */
                graph: [number, number][]
              }[]
              /** @description Total of items */
              totalItem: number
            }
            /** @description Request id for reference */
            reqId: number
          }
          'multipart/form-data': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Current page number */
              currentPage: number
              /** @description Number of items per page */
              perPage: number
              /** @description Last page number */
              lastPage: number
              data: {
                /** @description Asset name */
                name: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Asset slug */
                slug: string
                /** @description Asset type (CRYPTOCURRENCY / STABLECOIN) */
                type: 'CRYPTOCURRENCY' | 'STOCK' | 'STABLECOIN' | 'PLATFORM'
                /** @description Trust index value of asset */
                value: string
                /** @description Community size of asset */
                communitySize: string
                /** @description Message count of asset based on whole SNS platform */
                numberOfMessages: string
                /** @description One day change percentage of trust index */
                aDayChangePercentage: string
                /** @description Timestamp of calculation */
                timestamp: Record<string, never> | string | number
                /** @description Percentage indicator of day change percentage */
                percentageIndicator: '>' | '<' | '='
                /** @description Graph of trust index */
                graph: [number, number][]
              }[]
              /** @description Total of items */
              totalItem: number
            }
            /** @description Request id for reference */
            reqId: number
          }
          'text/plain': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Current page number */
              currentPage: number
              /** @description Number of items per page */
              perPage: number
              /** @description Last page number */
              lastPage: number
              data: {
                /** @description Asset name */
                name: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Asset slug */
                slug: string
                /** @description Asset type (CRYPTOCURRENCY / STABLECOIN) */
                type: 'CRYPTOCURRENCY' | 'STOCK' | 'STABLECOIN' | 'PLATFORM'
                /** @description Trust index value of asset */
                value: string
                /** @description Community size of asset */
                communitySize: string
                /** @description Message count of asset based on whole SNS platform */
                numberOfMessages: string
                /** @description One day change percentage of trust index */
                aDayChangePercentage: string
                /** @description Timestamp of calculation */
                timestamp: Record<string, never> | string | number
                /** @description Percentage indicator of day change percentage */
                percentageIndicator: '>' | '<' | '='
                /** @description Graph of trust index */
                graph: [number, number][]
              }[]
              /** @description Total of items */
              totalItem: number
            }
            /** @description Request id for reference */
            reqId: number
          }
        }
      }
      /** @description Fail response when user is not authorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when user is forbidden to access API */
      403: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when server have an issue */
      500: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
    }
  }
  'getNodiensApiV1Trust-indexBySlug': {
    parameters: {
      query?: {
        /** @description This field was used to showing data for layer 1 */
        layer1?: boolean | string
        /** @description Selected layer 2 for showing sns platform data (multiples values are separated by commas). Available options are: reddit, telegram */
        layer2?: ('reddit' | 'telegram')[]
        /** @description Selected layer 3 for showing community data (multiples values are separated by commas) */
        layer3?: (number | string)[]
        /** @description Start date of trust index history */
        startDate?: string
        /** @description End date of trust index history */
        endDate?: string
      }
      header?: never
      path: {
        /** @description Slug of asset */
        slug: string
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Success response for getting trust index history */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Asset name */
              name: string
              /** @description Asset symbol */
              symbol: string
              logo: string | null
              /** @description Asset slug */
              slug: string
              /** @description Latest value of trust index */
              latestValue: string
              /** @description Latest timestamp of trust index */
              latestTimestamp: number
              /** @description One day change percentage of mood index */
              aDayChangePercentage: string
              /** @description One day change indicator of mood index */
              aDayChangeIndicator: '>' | '<' | '='
              options: {
                /** @description Available options layer 1 for showing trust index data */
                layer1: boolean
                /** @description Available options layer 2 for showing sns platform data */
                layer2: {
                  label: string
                  value: 'reddit' | 'telegram'
                }[]
                /** @description Available options layer 3 for showing community data */
                layer3: {
                  platform: 'reddit' | 'telegram'
                  community: {
                    label: string
                    value: string
                  }[]
                }[]
              }
              /** @description Dataset of layer 1 mood index */
              layer1: [number, number][]
              /** @description Dataset of layer 2 mood index separated by sns platform */
              layer2: {
                snsPlatform: 'reddit' | 'telegram'
                history: [number, number][]
              }[]
              /** @description Dataset of layer 3 mood index separated by community ids */
              layer3: {
                communityId: number
                name: string
                platform: 'reddit' | 'telegram'
                history: [number, number][]
              }[]
              /** @description Start date of trust index history (unix timestamp) */
              startDate: number | null
              /** @description End date of trust index history (unix timestamp) */
              endDate: number | null
            }
            /** @description Request id for reference */
            reqId: number
          }
          'multipart/form-data': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Asset name */
              name: string
              /** @description Asset symbol */
              symbol: string
              logo: string | null
              /** @description Asset slug */
              slug: string
              /** @description Latest value of trust index */
              latestValue: string
              /** @description Latest timestamp of trust index */
              latestTimestamp: number
              /** @description One day change percentage of mood index */
              aDayChangePercentage: string
              /** @description One day change indicator of mood index */
              aDayChangeIndicator: '>' | '<' | '='
              options: {
                /** @description Available options layer 1 for showing trust index data */
                layer1: boolean
                /** @description Available options layer 2 for showing sns platform data */
                layer2: {
                  label: string
                  value: 'reddit' | 'telegram'
                }[]
                /** @description Available options layer 3 for showing community data */
                layer3: {
                  platform: 'reddit' | 'telegram'
                  community: {
                    label: string
                    value: string
                  }[]
                }[]
              }
              /** @description Dataset of layer 1 mood index */
              layer1: [number, number][]
              /** @description Dataset of layer 2 mood index separated by sns platform */
              layer2: {
                snsPlatform: 'reddit' | 'telegram'
                history: [number, number][]
              }[]
              /** @description Dataset of layer 3 mood index separated by community ids */
              layer3: {
                communityId: number
                name: string
                platform: 'reddit' | 'telegram'
                history: [number, number][]
              }[]
              /** @description Start date of trust index history (unix timestamp) */
              startDate: number | null
              /** @description End date of trust index history (unix timestamp) */
              endDate: number | null
            }
            /** @description Request id for reference */
            reqId: number
          }
          'text/plain': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Asset name */
              name: string
              /** @description Asset symbol */
              symbol: string
              logo: string | null
              /** @description Asset slug */
              slug: string
              /** @description Latest value of trust index */
              latestValue: string
              /** @description Latest timestamp of trust index */
              latestTimestamp: number
              /** @description One day change percentage of mood index */
              aDayChangePercentage: string
              /** @description One day change indicator of mood index */
              aDayChangeIndicator: '>' | '<' | '='
              options: {
                /** @description Available options layer 1 for showing trust index data */
                layer1: boolean
                /** @description Available options layer 2 for showing sns platform data */
                layer2: {
                  label: string
                  value: 'reddit' | 'telegram'
                }[]
                /** @description Available options layer 3 for showing community data */
                layer3: {
                  platform: 'reddit' | 'telegram'
                  community: {
                    label: string
                    value: string
                  }[]
                }[]
              }
              /** @description Dataset of layer 1 mood index */
              layer1: [number, number][]
              /** @description Dataset of layer 2 mood index separated by sns platform */
              layer2: {
                snsPlatform: 'reddit' | 'telegram'
                history: [number, number][]
              }[]
              /** @description Dataset of layer 3 mood index separated by community ids */
              layer3: {
                communityId: number
                name: string
                platform: 'reddit' | 'telegram'
                history: [number, number][]
              }[]
              /** @description Start date of trust index history (unix timestamp) */
              startDate: number | null
              /** @description End date of trust index history (unix timestamp) */
              endDate: number | null
            }
            /** @description Request id for reference */
            reqId: number
          }
        }
      }
      /** @description Fail response when user is not authorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when user is forbidden to access API */
      403: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when asset is not available */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when server have an issue */
      500: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
    }
  }
  getNodiensApiV1EsgDecentralisationAsset: {
    parameters: {
      query?: {
        /** @description This field was used to search asset */
        terms?: string
        /** @description This field was used for pagination */
        page?: number
        /** @description This field was used to limit items per page */
        perPage?: number
      }
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Success response for getting decentralisation asset */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Current page number */
              currentPage: number
              /** @description Number of items per page */
              perPage: number
              /** @description Last page number */
              lastPage: number
              data: {
                /** @description Asset name */
                name: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Asset slug */
                slug: string
              }[]
              /** @description Total of items */
              totalItem: number
            }
            /** @description Request id for reference */
            reqId: number
          }
          'multipart/form-data': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Current page number */
              currentPage: number
              /** @description Number of items per page */
              perPage: number
              /** @description Last page number */
              lastPage: number
              data: {
                /** @description Asset name */
                name: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Asset slug */
                slug: string
              }[]
              /** @description Total of items */
              totalItem: number
            }
            /** @description Request id for reference */
            reqId: number
          }
          'text/plain': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Current page number */
              currentPage: number
              /** @description Number of items per page */
              perPage: number
              /** @description Last page number */
              lastPage: number
              data: {
                /** @description Asset name */
                name: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Asset slug */
                slug: string
              }[]
              /** @description Total of items */
              totalItem: number
            }
            /** @description Request id for reference */
            reqId: number
          }
        }
      }
      /** @description Fail response when user is not authorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when user is forbidden to access API */
      403: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when server have an issue */
      500: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
    }
  }
  'getNodiensApiV1EsgDecentralisationDaily-comparison': {
    parameters: {
      query: {
        /** @description Slug of decentralisation assets (multiple values are separated by commas) */
        slugs: string[]
      }
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Success response for getting daily comparison decentralisation */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Asset name */
              name: string
              /** @description Asset symbol */
              symbol: string
              /** @description Asset slug */
              slug: string
              /** @description Gini coefficient of IP authority distribution value */
              ipAuthDistrGini: number
              /** @description Gini coefficient of IP participant distribution value */
              ipParticipantDivGini: number
              /** @description HHI of IP author influence value */
              ipAuthorInflConcHHI: number
              /** @description Gini coefficient of RCP development distribution value */
              rcpDevDistrGini: number
              /** @description Shannon entropy of RCP participant distribution value */
              rcpParticipantDivShannon: number
              /** @description HHI of RCP development influence value */
              rcpDevInflConcHHI: number
              /** @description HHI of RCD revenue power value */
              rcdRevrPowerConcHHI: number
              /** @description Gini coefficient of consensus power value */
              consensusPowerNeGini: number
              /** @description Theil coefficient of consensus power value */
              consensusPowerNeTheil: number
              /** @description Nakamoto coefficient of consensus power value */
              consensusPowerConcNakamoto: number
              /** @description HHI of consensus power value */
              consensusPowerConcHHI: number
            }[]
            /** @description Request id for reference */
            reqId: number
          }
          'multipart/form-data': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Asset name */
              name: string
              /** @description Asset symbol */
              symbol: string
              /** @description Asset slug */
              slug: string
              /** @description Gini coefficient of IP authority distribution value */
              ipAuthDistrGini: number
              /** @description Gini coefficient of IP participant distribution value */
              ipParticipantDivGini: number
              /** @description HHI of IP author influence value */
              ipAuthorInflConcHHI: number
              /** @description Gini coefficient of RCP development distribution value */
              rcpDevDistrGini: number
              /** @description Shannon entropy of RCP participant distribution value */
              rcpParticipantDivShannon: number
              /** @description HHI of RCP development influence value */
              rcpDevInflConcHHI: number
              /** @description HHI of RCD revenue power value */
              rcdRevrPowerConcHHI: number
              /** @description Gini coefficient of consensus power value */
              consensusPowerNeGini: number
              /** @description Theil coefficient of consensus power value */
              consensusPowerNeTheil: number
              /** @description Nakamoto coefficient of consensus power value */
              consensusPowerConcNakamoto: number
              /** @description HHI of consensus power value */
              consensusPowerConcHHI: number
            }[]
            /** @description Request id for reference */
            reqId: number
          }
          'text/plain': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Asset name */
              name: string
              /** @description Asset symbol */
              symbol: string
              /** @description Asset slug */
              slug: string
              /** @description Gini coefficient of IP authority distribution value */
              ipAuthDistrGini: number
              /** @description Gini coefficient of IP participant distribution value */
              ipParticipantDivGini: number
              /** @description HHI of IP author influence value */
              ipAuthorInflConcHHI: number
              /** @description Gini coefficient of RCP development distribution value */
              rcpDevDistrGini: number
              /** @description Shannon entropy of RCP participant distribution value */
              rcpParticipantDivShannon: number
              /** @description HHI of RCP development influence value */
              rcpDevInflConcHHI: number
              /** @description HHI of RCD revenue power value */
              rcdRevrPowerConcHHI: number
              /** @description Gini coefficient of consensus power value */
              consensusPowerNeGini: number
              /** @description Theil coefficient of consensus power value */
              consensusPowerNeTheil: number
              /** @description Nakamoto coefficient of consensus power value */
              consensusPowerConcNakamoto: number
              /** @description HHI of consensus power value */
              consensusPowerConcHHI: number
            }[]
            /** @description Request id for reference */
            reqId: number
          }
        }
      }
      /** @description Fail response when user is not authorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when user is forbidden to access API */
      403: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when server have an issue */
      500: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
    }
  }
  'getNodiensApiV1EsgDecentralisationHistory-comparisonByMetric': {
    parameters: {
      query: {
        /** @description Slug of decentralisation assets */
        slugs: string[]
      }
      header?: never
      path: {
        /** @description Decentralisation metric */
        metric:
          | 'ipAuthDistrGini'
          | 'ipParticipantDivGini'
          | 'ipAuthorInflConcHHI'
          | 'rcpDevDistrGini'
          | 'rcpParticipantDivShannon'
          | 'rcpDevInflConcHHI'
          | 'rcdRevrPowerConcHHI'
          | 'consensusPowerNeGini'
          | 'consensusPowerNeTheil'
          | 'consensusPowerConcNakamoto'
          | 'consensusPowerConcHHI'
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Success response for getting history comparison decentralisation */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              comparison: {
                /** @description Asset name */
                name: string
                /** @description Asset symbol */
                symbol: string
                /** @description Asset slug */
                slug: string
                metricName:
                  | 'ipAuthDistrGini'
                  | 'ipParticipantDivGini'
                  | 'ipAuthorInflConcHHI'
                  | 'rcpDevDistrGini'
                  | 'rcpParticipantDivShannon'
                  | 'rcpDevInflConcHHI'
                  | 'rcdRevrPowerConcHHI'
                  | 'consensusPowerNeGini'
                  | 'consensusPowerNeTheil'
                  | 'consensusPowerConcNakamoto'
                  | 'consensusPowerConcHHI'
                /** @description Timeseries data of decentralisation metrics */
                entries: [number, number][]
              }[]
              /** @description Start date of decentralisation history (unix timestamp) */
              startDate: number | null
              /** @description End date of decentralisation history (unix timestamp) */
              endDate: number | null
            }
            /** @description Request id for reference */
            reqId: number
          }
          'multipart/form-data': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              comparison: {
                /** @description Asset name */
                name: string
                /** @description Asset symbol */
                symbol: string
                /** @description Asset slug */
                slug: string
                metricName:
                  | 'ipAuthDistrGini'
                  | 'ipParticipantDivGini'
                  | 'ipAuthorInflConcHHI'
                  | 'rcpDevDistrGini'
                  | 'rcpParticipantDivShannon'
                  | 'rcpDevInflConcHHI'
                  | 'rcdRevrPowerConcHHI'
                  | 'consensusPowerNeGini'
                  | 'consensusPowerNeTheil'
                  | 'consensusPowerConcNakamoto'
                  | 'consensusPowerConcHHI'
                /** @description Timeseries data of decentralisation metrics */
                entries: [number, number][]
              }[]
              /** @description Start date of decentralisation history (unix timestamp) */
              startDate: number | null
              /** @description End date of decentralisation history (unix timestamp) */
              endDate: number | null
            }
            /** @description Request id for reference */
            reqId: number
          }
          'text/plain': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              comparison: {
                /** @description Asset name */
                name: string
                /** @description Asset symbol */
                symbol: string
                /** @description Asset slug */
                slug: string
                metricName:
                  | 'ipAuthDistrGini'
                  | 'ipParticipantDivGini'
                  | 'ipAuthorInflConcHHI'
                  | 'rcpDevDistrGini'
                  | 'rcpParticipantDivShannon'
                  | 'rcpDevInflConcHHI'
                  | 'rcdRevrPowerConcHHI'
                  | 'consensusPowerNeGini'
                  | 'consensusPowerNeTheil'
                  | 'consensusPowerConcNakamoto'
                  | 'consensusPowerConcHHI'
                /** @description Timeseries data of decentralisation metrics */
                entries: [number, number][]
              }[]
              /** @description Start date of decentralisation history (unix timestamp) */
              startDate: number | null
              /** @description End date of decentralisation history (unix timestamp) */
              endDate: number | null
            }
            /** @description Request id for reference */
            reqId: number
          }
        }
      }
      /** @description Fail response when user is not authorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when user is forbidden to access API */
      403: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when server have an issue */
      500: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
    }
  }
  getNodiensApiV1EsgDecentralisationTreeByMetric: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description Decentralisation metric of decision tree */
        metric: 'ipGovOrdinal' | 'coinDistrOrdinal'
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Success response for getting decentralisation tree */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              metric: 'ipGovOrdinal' | 'coinDistrOrdinal'
              decisionTree: Record<string, never>[]
            }
            /** @description Request id for reference */
            reqId: number
          }
          'multipart/form-data': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              metric: 'ipGovOrdinal' | 'coinDistrOrdinal'
              decisionTree: Record<string, never>[]
            }
            /** @description Request id for reference */
            reqId: number
          }
          'text/plain': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              metric: 'ipGovOrdinal' | 'coinDistrOrdinal'
              decisionTree: Record<string, never>[]
            }
            /** @description Request id for reference */
            reqId: number
          }
        }
      }
      /** @description Fail response when user does not have plan or does not have metrics under subscription */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when user is not authorized */
      403: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when asset is not available */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
    }
  }
  getNodiensApiV1FinancialAsset: {
    parameters: {
      query?: {
        /** @description This field was used to search asset */
        terms?: string
        /** @description This field was used for pagination */
        page?: number
        /** @description This field was used to limit items per page */
        perPage?: number
      }
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Success response for getting financial asset */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Current page number */
              currentPage: number
              /** @description Number of items per page */
              perPage: number
              /** @description Last page number */
              lastPage: number
              data: {
                /** @description Asset name */
                name: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Asset slug */
                slug: string
                type: 'CRYPTOCURRENCY' | 'STABLECOIN'
              }[]
              /** @description Total of items */
              totalItem: number
            }
            /** @description Request id for reference */
            reqId: number
          }
          'multipart/form-data': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Current page number */
              currentPage: number
              /** @description Number of items per page */
              perPage: number
              /** @description Last page number */
              lastPage: number
              data: {
                /** @description Asset name */
                name: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Asset slug */
                slug: string
                type: 'CRYPTOCURRENCY' | 'STABLECOIN'
              }[]
              /** @description Total of items */
              totalItem: number
            }
            /** @description Request id for reference */
            reqId: number
          }
          'text/plain': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Current page number */
              currentPage: number
              /** @description Number of items per page */
              perPage: number
              /** @description Last page number */
              lastPage: number
              data: {
                /** @description Asset name */
                name: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Asset slug */
                slug: string
                type: 'CRYPTOCURRENCY' | 'STABLECOIN'
              }[]
              /** @description Total of items */
              totalItem: number
            }
            /** @description Request id for reference */
            reqId: number
          }
        }
      }
      /** @description Fail response when user is not authorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when user is forbidden to access API */
      403: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when server have an issue */
      500: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
    }
  }
  'getNodiensApiV1FinancialDaily-comparison': {
    parameters: {
      query: {
        /** @description Slug of financial assets (multiple values are separated by commas) */
        slugs: string[]
        /** @description This field was used to select cost of liquidity */
        liquidityCost:
          | 'usd_100'
          | 'usd_10_000'
          | 'usd_1m'
          | 'volume_0_001_percent_24h'
          | 'volume_0_1_percent_24h'
          | 'volume_1_percent_24h'
      }
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Success response for getting daily comparison financial */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Name of asset */
              assetName: string
              /** @description Slug of asset */
              slug: string
              symbol: string
              sizeOfCost:
                | 'usd_100'
                | 'usd_10_000'
                | 'usd_1m'
                | 'volume_0_001_percent_24h'
                | 'volume_0_1_percent_24h'
                | 'volume_1_percent_24h'
              costOfLiquidityCex: number
              costOfLiquidityDex: number
              /** @description Timestamp */
              timestamp: Record<string, never> | string | number
              /** @description Asset market cap */
              marketCap: number
              /** @description 30 days Volatility of asset */
              thirtyDayVolatility: number
              /** @description A day trading volume of cex */
              aDayTradingVolCex: number
              /** @description A day trading volume of dex */
              aDayTradingVolDex: number
              /** @description A year maximum depeg rate in days */
              aYearMaxDepegDays: number
              /** @description Number of pairs of cex */
              numberOfPairsCex: number
              /** @description Number of pairs of dex */
              numberOfPairsDex: number
              /** @description Liquidity concentration of dex */
              liquidityConcentrationDex: number
              /** @description Asset price */
              price: number
            }[]
            /** @description Request id for reference */
            reqId: number
          }
          'multipart/form-data': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Name of asset */
              assetName: string
              /** @description Slug of asset */
              slug: string
              symbol: string
              sizeOfCost:
                | 'usd_100'
                | 'usd_10_000'
                | 'usd_1m'
                | 'volume_0_001_percent_24h'
                | 'volume_0_1_percent_24h'
                | 'volume_1_percent_24h'
              costOfLiquidityCex: number
              costOfLiquidityDex: number
              /** @description Timestamp */
              timestamp: Record<string, never> | string | number
              /** @description Asset market cap */
              marketCap: number
              /** @description 30 days Volatility of asset */
              thirtyDayVolatility: number
              /** @description A day trading volume of cex */
              aDayTradingVolCex: number
              /** @description A day trading volume of dex */
              aDayTradingVolDex: number
              /** @description A year maximum depeg rate in days */
              aYearMaxDepegDays: number
              /** @description Number of pairs of cex */
              numberOfPairsCex: number
              /** @description Number of pairs of dex */
              numberOfPairsDex: number
              /** @description Liquidity concentration of dex */
              liquidityConcentrationDex: number
              /** @description Asset price */
              price: number
            }[]
            /** @description Request id for reference */
            reqId: number
          }
          'text/plain': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Name of asset */
              assetName: string
              /** @description Slug of asset */
              slug: string
              symbol: string
              sizeOfCost:
                | 'usd_100'
                | 'usd_10_000'
                | 'usd_1m'
                | 'volume_0_001_percent_24h'
                | 'volume_0_1_percent_24h'
                | 'volume_1_percent_24h'
              costOfLiquidityCex: number
              costOfLiquidityDex: number
              /** @description Timestamp */
              timestamp: Record<string, never> | string | number
              /** @description Asset market cap */
              marketCap: number
              /** @description 30 days Volatility of asset */
              thirtyDayVolatility: number
              /** @description A day trading volume of cex */
              aDayTradingVolCex: number
              /** @description A day trading volume of dex */
              aDayTradingVolDex: number
              /** @description A year maximum depeg rate in days */
              aYearMaxDepegDays: number
              /** @description Number of pairs of cex */
              numberOfPairsCex: number
              /** @description Number of pairs of dex */
              numberOfPairsDex: number
              /** @description Liquidity concentration of dex */
              liquidityConcentrationDex: number
              /** @description Asset price */
              price: number
            }[]
            /** @description Request id for reference */
            reqId: number
          }
        }
      }
      /** @description Fail response when user is not authorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when user is forbidden to access API */
      403: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when server have an issue */
      500: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
    }
  }
  getNodiensApiV1FinancialHistoryBySlug: {
    parameters: {
      query: {
        /** @description Start date of financial history using format yyyy-MM-dd */
        startDate?: string
        /** @description Start date of financial history using format yyyy-MM-dd */
        endDate?: string
        /** @description This field was used to select financial metrics */
        metric:
          | 'marketCap'
          | 'thirtyDayVolatility'
          | 'aDayTradingVolCex'
          | 'aDayTradingVolDex'
          | 'aYearMaxDepegDays'
          | 'numberOfPairsCex'
          | 'numberOfPairsDex'
          | 'costOfLiquidity10ThouPcCex'
          | 'costOfLiquidityHundrethPcCex'
          | 'costofLiquidity1PcCex'
          | 'costofLiquidity100Cex'
          | 'costOfLiquidity10kCex'
          | 'costOfLiquidity1mCex'
          | 'liquidityConcentrationCex'
          | 'costOfLiquidity10ThouPcDex'
          | 'costOfLiquidityHundrethPcDex'
          | 'costOfLiquidity1PcDex'
          | 'costOfLiquidity100Dex'
          | 'costOfLiquidity10kDex'
          | 'costOfLiquidity1mDex'
          | 'liquidityConcentrationDex'
          | 'price'
      }
      header?: never
      path: {
        /** @description Asset slug */
        slug: string
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Success response for getting financial history */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Asset name */
              name: string
              /** @description Asset symbol */
              symbol: string
              /** @description Asset slug */
              slug: string
              metric:
                | 'marketCap'
                | 'thirtyDayVolatility'
                | 'aDayTradingVolCex'
                | 'aDayTradingVolDex'
                | 'aYearMaxDepegDays'
                | 'numberOfPairsCex'
                | 'numberOfPairsDex'
                | 'costOfLiquidity10ThouPcCex'
                | 'costOfLiquidityHundrethPcCex'
                | 'costofLiquidity1PcCex'
                | 'costofLiquidity100Cex'
                | 'costOfLiquidity10kCex'
                | 'costOfLiquidity1mCex'
                | 'liquidityConcentrationCex'
                | 'costOfLiquidity10ThouPcDex'
                | 'costOfLiquidityHundrethPcDex'
                | 'costOfLiquidity1PcDex'
                | 'costOfLiquidity100Dex'
                | 'costOfLiquidity10kDex'
                | 'costOfLiquidity1mDex'
                | 'liquidityConcentrationDex'
                | 'price'
              /** @description Timeseries data of financial metrics */
              entries: [number, number][]
              /** @description Start date of financial history (unix timestamp) */
              startDate: number | null
              /** @description End date of financial history (unix timestamp) */
              endDate: number | null
            }
            /** @description Request id for reference */
            reqId: number
          }
          'multipart/form-data': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Asset name */
              name: string
              /** @description Asset symbol */
              symbol: string
              /** @description Asset slug */
              slug: string
              metric:
                | 'marketCap'
                | 'thirtyDayVolatility'
                | 'aDayTradingVolCex'
                | 'aDayTradingVolDex'
                | 'aYearMaxDepegDays'
                | 'numberOfPairsCex'
                | 'numberOfPairsDex'
                | 'costOfLiquidity10ThouPcCex'
                | 'costOfLiquidityHundrethPcCex'
                | 'costofLiquidity1PcCex'
                | 'costofLiquidity100Cex'
                | 'costOfLiquidity10kCex'
                | 'costOfLiquidity1mCex'
                | 'liquidityConcentrationCex'
                | 'costOfLiquidity10ThouPcDex'
                | 'costOfLiquidityHundrethPcDex'
                | 'costOfLiquidity1PcDex'
                | 'costOfLiquidity100Dex'
                | 'costOfLiquidity10kDex'
                | 'costOfLiquidity1mDex'
                | 'liquidityConcentrationDex'
                | 'price'
              /** @description Timeseries data of financial metrics */
              entries: [number, number][]
              /** @description Start date of financial history (unix timestamp) */
              startDate: number | null
              /** @description End date of financial history (unix timestamp) */
              endDate: number | null
            }
            /** @description Request id for reference */
            reqId: number
          }
          'text/plain': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Asset name */
              name: string
              /** @description Asset symbol */
              symbol: string
              /** @description Asset slug */
              slug: string
              metric:
                | 'marketCap'
                | 'thirtyDayVolatility'
                | 'aDayTradingVolCex'
                | 'aDayTradingVolDex'
                | 'aYearMaxDepegDays'
                | 'numberOfPairsCex'
                | 'numberOfPairsDex'
                | 'costOfLiquidity10ThouPcCex'
                | 'costOfLiquidityHundrethPcCex'
                | 'costofLiquidity1PcCex'
                | 'costofLiquidity100Cex'
                | 'costOfLiquidity10kCex'
                | 'costOfLiquidity1mCex'
                | 'liquidityConcentrationCex'
                | 'costOfLiquidity10ThouPcDex'
                | 'costOfLiquidityHundrethPcDex'
                | 'costOfLiquidity1PcDex'
                | 'costOfLiquidity100Dex'
                | 'costOfLiquidity10kDex'
                | 'costOfLiquidity1mDex'
                | 'liquidityConcentrationDex'
                | 'price'
              /** @description Timeseries data of financial metrics */
              entries: [number, number][]
              /** @description Start date of financial history (unix timestamp) */
              startDate: number | null
              /** @description End date of financial history (unix timestamp) */
              endDate: number | null
            }
            /** @description Request id for reference */
            reqId: number
          }
        }
      }
      /** @description Fail response when user is not authorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when user is forbidden to access API */
      403: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when asset is not available */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when server have an issue */
      500: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
    }
  }
  'getNodiensApiV1FinancialHistory-comparison': {
    parameters: {
      query: {
        /** @description This field was used to select financial metrics */
        metric:
          | 'marketCap'
          | 'thirtyDayVolatility'
          | 'aDayTradingVolCex'
          | 'aDayTradingVolDex'
          | 'aYearMaxDepegDays'
          | 'numberOfPairsCex'
          | 'numberOfPairsDex'
          | 'costOfLiquidity10ThouPcCex'
          | 'costOfLiquidityHundrethPcCex'
          | 'costofLiquidity1PcCex'
          | 'costofLiquidity100Cex'
          | 'costOfLiquidity10kCex'
          | 'costOfLiquidity1mCex'
          | 'liquidityConcentrationCex'
          | 'costOfLiquidity10ThouPcDex'
          | 'costOfLiquidityHundrethPcDex'
          | 'costOfLiquidity1PcDex'
          | 'costOfLiquidity100Dex'
          | 'costOfLiquidity10kDex'
          | 'costOfLiquidity1mDex'
          | 'liquidityConcentrationDex'
          | 'price'
        /** @description Slug of financial assets (multiple values are separated by commas) */
        slugs: string[]
        /** @description Start date of financial history (format: yyyy-MM-dd) */
        startDate?: string
        /** @description Start date of financial history (format: yyyy-MM-dd) */
        endDate?: string
      }
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Success response for getting history comparison financial */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              comparison: {
                /** @description Name of asset */
                assetName: string
                /**
                 * @description This field was used to select financial metrics
                 * @example marketCap
                 */
                metric:
                  | 'marketCap'
                  | 'thirtyDayVolatility'
                  | 'aDayTradingVolCex'
                  | 'aDayTradingVolDex'
                  | 'aYearMaxDepegDays'
                  | 'numberOfPairsCex'
                  | 'numberOfPairsDex'
                  | 'costOfLiquidity10ThouPcCex'
                  | 'costOfLiquidityHundrethPcCex'
                  | 'costofLiquidity1PcCex'
                  | 'costofLiquidity100Cex'
                  | 'costOfLiquidity10kCex'
                  | 'costOfLiquidity1mCex'
                  | 'liquidityConcentrationCex'
                  | 'costOfLiquidity10ThouPcDex'
                  | 'costOfLiquidityHundrethPcDex'
                  | 'costOfLiquidity1PcDex'
                  | 'costOfLiquidity100Dex'
                  | 'costOfLiquidity10kDex'
                  | 'costOfLiquidity1mDex'
                  | 'liquidityConcentrationDex'
                  | 'price'
                /** @description Dataset of time series */
                entries: [number, number][]
              }[]
              /** @description Start date of financial history (unix timestamp) */
              startDate: number | null
              /** @description End date of financial history (unix timestamp) */
              endDate: number | null
            }
            /** @description Request id for reference */
            reqId: number
          }
          'multipart/form-data': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              comparison: {
                /** @description Name of asset */
                assetName: string
                /**
                 * @description This field was used to select financial metrics
                 * @example marketCap
                 */
                metric:
                  | 'marketCap'
                  | 'thirtyDayVolatility'
                  | 'aDayTradingVolCex'
                  | 'aDayTradingVolDex'
                  | 'aYearMaxDepegDays'
                  | 'numberOfPairsCex'
                  | 'numberOfPairsDex'
                  | 'costOfLiquidity10ThouPcCex'
                  | 'costOfLiquidityHundrethPcCex'
                  | 'costofLiquidity1PcCex'
                  | 'costofLiquidity100Cex'
                  | 'costOfLiquidity10kCex'
                  | 'costOfLiquidity1mCex'
                  | 'liquidityConcentrationCex'
                  | 'costOfLiquidity10ThouPcDex'
                  | 'costOfLiquidityHundrethPcDex'
                  | 'costOfLiquidity1PcDex'
                  | 'costOfLiquidity100Dex'
                  | 'costOfLiquidity10kDex'
                  | 'costOfLiquidity1mDex'
                  | 'liquidityConcentrationDex'
                  | 'price'
                /** @description Dataset of time series */
                entries: [number, number][]
              }[]
              /** @description Start date of financial history (unix timestamp) */
              startDate: number | null
              /** @description End date of financial history (unix timestamp) */
              endDate: number | null
            }
            /** @description Request id for reference */
            reqId: number
          }
          'text/plain': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              comparison: {
                /** @description Name of asset */
                assetName: string
                /**
                 * @description This field was used to select financial metrics
                 * @example marketCap
                 */
                metric:
                  | 'marketCap'
                  | 'thirtyDayVolatility'
                  | 'aDayTradingVolCex'
                  | 'aDayTradingVolDex'
                  | 'aYearMaxDepegDays'
                  | 'numberOfPairsCex'
                  | 'numberOfPairsDex'
                  | 'costOfLiquidity10ThouPcCex'
                  | 'costOfLiquidityHundrethPcCex'
                  | 'costofLiquidity1PcCex'
                  | 'costofLiquidity100Cex'
                  | 'costOfLiquidity10kCex'
                  | 'costOfLiquidity1mCex'
                  | 'liquidityConcentrationCex'
                  | 'costOfLiquidity10ThouPcDex'
                  | 'costOfLiquidityHundrethPcDex'
                  | 'costOfLiquidity1PcDex'
                  | 'costOfLiquidity100Dex'
                  | 'costOfLiquidity10kDex'
                  | 'costOfLiquidity1mDex'
                  | 'liquidityConcentrationDex'
                  | 'price'
                /** @description Dataset of time series */
                entries: [number, number][]
              }[]
              /** @description Start date of financial history (unix timestamp) */
              startDate: number | null
              /** @description End date of financial history (unix timestamp) */
              endDate: number | null
            }
            /** @description Request id for reference */
            reqId: number
          }
        }
      }
      /** @description Fail response when user is not authorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when user is forbidden to access API */
      403: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when all of selected asset is not available */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when server have an issue */
      500: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
    }
  }
  'getNodiensApiV1EsgEnergy-consumptionAsset': {
    parameters: {
      query?: {
        /** @description This field was used to search asset */
        terms?: string
        /** @description This field was used for pagination */
        page?: number
        /** @description This field was used to limit items per page */
        perPage?: number
      }
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Success response for getting energy consumption asset */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Current page number */
              currentPage: number
              /** @description Number of items per page */
              perPage: number
              /** @description Last page number */
              lastPage: number
              data: {
                /** @description Asset name */
                name: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Asset slug */
                slug: string
              }[]
              /** @description Total of items */
              totalItem: number
            }
            /** @description Request id for reference */
            reqId: number
          }
          'multipart/form-data': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Current page number */
              currentPage: number
              /** @description Number of items per page */
              perPage: number
              /** @description Last page number */
              lastPage: number
              data: {
                /** @description Asset name */
                name: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Asset slug */
                slug: string
              }[]
              /** @description Total of items */
              totalItem: number
            }
            /** @description Request id for reference */
            reqId: number
          }
          'text/plain': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Current page number */
              currentPage: number
              /** @description Number of items per page */
              perPage: number
              /** @description Last page number */
              lastPage: number
              data: {
                /** @description Asset name */
                name: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Asset slug */
                slug: string
              }[]
              /** @description Total of items */
              totalItem: number
            }
            /** @description Request id for reference */
            reqId: number
          }
        }
      }
      /** @description Fail response when user is not authorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when user is forbidden to access API */
      403: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when server have an issue */
      500: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
    }
  }
  'getNodiensApiV1EsgEnergy-consumption': {
    parameters: {
      query: {
        /** @description Filtering by ESG layer (multiple values should be separated by commas). Available layers: L1, L2 */
        layer?: ('1' | '2')[]
        /** @description Filtering by ESG type (multiple values should be separated by commas). Available types: Platform, Token */
        type?: ('PLATFORM' | 'TOKEN')[]
        /** @description Filtering by ESG consensus group (multiple values should be separated by commas). Available consensus groups: PoS, PoSt, PoW, Other */
        consensusGroup?: ('PoW' | 'PoSt' | 'PoS' | 'other')[]
        /** @description Sorting by Energy Consumption Metric. Available metrics: energyConsumption, energyConsumptionPerTx, energyConsumptionPerTxPerNode, throughput, validators */
        sortBy:
          | 'energyConsumption'
          | 'energyConsumptionPerTx'
          | 'energyConsumptionPerTxPerNode'
          | 'validators'
          | 'throughput'
          | 'name'
        /** @description Sorting flow. Available flows: ASC, DESC */
        sortFlow: 'ASC' | 'DESC'
        /** @description This field was used to search asset */
        terms?: string
        /** @description This field was used for pagination */
        page?: number
        /** @description This field was used to limit items per page */
        perPage?: number
      }
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Success response for getting energy consumption feeds */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Current page number */
              currentPage: number
              /** @description Number of items per page */
              perPage: number
              /** @description Last page number */
              lastPage: number
              data: {
                /** @description Asset name */
                name: string
                /** @description Asset slug */
                slug: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Energy Consumption of asset / platform */
                energyConsumption: string
                /** @description Energy Consumption per transaction of asset / platform */
                energyConsumptionPerTx: string
                /** @description Energy Consumption per transaction per node of asset / platform */
                energyConsumptionPerTxPerNode: string
                /** @description Throughput of asset / platform */
                throughput: string
                /** @description Number of validators of asset / platform */
                validators: string
                /** @description Asset layer (Layer 1 / Layer 2) */
                layer: '1' | '2'
                /** @description Asset type (Token / Platform) */
                type: 'TOKEN' | 'PLATFORM'
                consensusName: string | null
              }[]
              /** @description Total of items */
              totalItem: number
            }
            /** @description Request id for reference */
            reqId: number
          }
          'multipart/form-data': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Current page number */
              currentPage: number
              /** @description Number of items per page */
              perPage: number
              /** @description Last page number */
              lastPage: number
              data: {
                /** @description Asset name */
                name: string
                /** @description Asset slug */
                slug: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Energy Consumption of asset / platform */
                energyConsumption: string
                /** @description Energy Consumption per transaction of asset / platform */
                energyConsumptionPerTx: string
                /** @description Energy Consumption per transaction per node of asset / platform */
                energyConsumptionPerTxPerNode: string
                /** @description Throughput of asset / platform */
                throughput: string
                /** @description Number of validators of asset / platform */
                validators: string
                /** @description Asset layer (Layer 1 / Layer 2) */
                layer: '1' | '2'
                /** @description Asset type (Token / Platform) */
                type: 'TOKEN' | 'PLATFORM'
                consensusName: string | null
              }[]
              /** @description Total of items */
              totalItem: number
            }
            /** @description Request id for reference */
            reqId: number
          }
          'text/plain': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Current page number */
              currentPage: number
              /** @description Number of items per page */
              perPage: number
              /** @description Last page number */
              lastPage: number
              data: {
                /** @description Asset name */
                name: string
                /** @description Asset slug */
                slug: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Energy Consumption of asset / platform */
                energyConsumption: string
                /** @description Energy Consumption per transaction of asset / platform */
                energyConsumptionPerTx: string
                /** @description Energy Consumption per transaction per node of asset / platform */
                energyConsumptionPerTxPerNode: string
                /** @description Throughput of asset / platform */
                throughput: string
                /** @description Number of validators of asset / platform */
                validators: string
                /** @description Asset layer (Layer 1 / Layer 2) */
                layer: '1' | '2'
                /** @description Asset type (Token / Platform) */
                type: 'TOKEN' | 'PLATFORM'
                consensusName: string | null
              }[]
              /** @description Total of items */
              totalItem: number
            }
            /** @description Request id for reference */
            reqId: number
          }
        }
      }
      /** @description Fail response when user is not authorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when user is forbidden to access API */
      403: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when server have an issue */
      500: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
    }
  }
  'getNodiensApiV1EsgEnergy-consumptionBySlug': {
    parameters: {
      query?: {
        /** @description Energy consumption metrics */
        metric?: 'POWER_USE_W' | 'ENG_CONS_TX_WH' | 'ENG_CONS_TX_NODE_WH'
        /** @description Start date of energy consumption history using format yyyy-MM-dd */
        startDate?: string
        /** @description End date of energy consumption history using format yyyy-MM-dd */
        endDate?: string
      }
      header?: never
      path: {
        /** @description Slug of asset */
        slug: string
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Success response for getting energy consumption history */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Asset metadata information */
              feeds: {
                /** @description Asset name */
                name: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Asset slug */
                slug: string
              }
              /** @description Timeseries data of energy consumption metrics */
              analytic_entries: [
                number,
                {
                  upper: number
                  lower: number
                  avg: number
                },
              ][]
              /** @description Timeseries data of daily metrics */
              daily_entries: [
                number,
                {
                  /** @description Throughput of the asset */
                  throughput: number
                  /** @description Number of validators of the asset */
                  validators: number
                },
              ][]
              /** @description List of consensus names */
              consensusNames: string
              /** @description Availability of carbon emission metric */
              availabilityOfCarbonEmission: boolean
              startDate: number | null
              endDate: number | null
            }
            /** @description Request id for reference */
            reqId: number
          }
          'multipart/form-data': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Asset metadata information */
              feeds: {
                /** @description Asset name */
                name: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Asset slug */
                slug: string
              }
              /** @description Timeseries data of energy consumption metrics */
              analytic_entries: [
                number,
                {
                  upper: number
                  lower: number
                  avg: number
                },
              ][]
              /** @description Timeseries data of daily metrics */
              daily_entries: [
                number,
                {
                  /** @description Throughput of the asset */
                  throughput: number
                  /** @description Number of validators of the asset */
                  validators: number
                },
              ][]
              /** @description List of consensus names */
              consensusNames: string
              /** @description Availability of carbon emission metric */
              availabilityOfCarbonEmission: boolean
              startDate: number | null
              endDate: number | null
            }
            /** @description Request id for reference */
            reqId: number
          }
          'text/plain': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Asset metadata information */
              feeds: {
                /** @description Asset name */
                name: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Asset slug */
                slug: string
              }
              /** @description Timeseries data of energy consumption metrics */
              analytic_entries: [
                number,
                {
                  upper: number
                  lower: number
                  avg: number
                },
              ][]
              /** @description Timeseries data of daily metrics */
              daily_entries: [
                number,
                {
                  /** @description Throughput of the asset */
                  throughput: number
                  /** @description Number of validators of the asset */
                  validators: number
                },
              ][]
              /** @description List of consensus names */
              consensusNames: string
              /** @description Availability of carbon emission metric */
              availabilityOfCarbonEmission: boolean
              startDate: number | null
              endDate: number | null
            }
            /** @description Request id for reference */
            reqId: number
          }
        }
      }
      /** @description Fail response when user is not authorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when user is forbidden to access API */
      403: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when asset is not available */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when server have an issue */
      500: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
    }
  }
  'getNodiensApiV1EsgCarbon-emissionAsset': {
    parameters: {
      query?: {
        /** @description This field was used to search asset */
        terms?: string
        /** @description This field was used for pagination */
        page?: number
        /** @description This field was used to limit items per page */
        perPage?: number
      }
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Success response for getting carbon emission assets */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Current page number */
              currentPage: number
              /** @description Number of items per page */
              perPage: number
              /** @description Last page number */
              lastPage: number
              data: {
                /** @description Asset name */
                name: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Asset slug */
                slug: string
              }[]
              /** @description Total of items */
              totalItem: number
            }
            /** @description Request id for reference */
            reqId: number
          }
          'multipart/form-data': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Current page number */
              currentPage: number
              /** @description Number of items per page */
              perPage: number
              /** @description Last page number */
              lastPage: number
              data: {
                /** @description Asset name */
                name: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Asset slug */
                slug: string
              }[]
              /** @description Total of items */
              totalItem: number
            }
            /** @description Request id for reference */
            reqId: number
          }
          'text/plain': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Current page number */
              currentPage: number
              /** @description Number of items per page */
              perPage: number
              /** @description Last page number */
              lastPage: number
              data: {
                /** @description Asset name */
                name: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Asset slug */
                slug: string
              }[]
              /** @description Total of items */
              totalItem: number
            }
            /** @description Request id for reference */
            reqId: number
          }
        }
      }
      /** @description Fail response when user is not authorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when user is forbidden to access API */
      403: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when server have an issue */
      500: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
    }
  }
  'getNodiensApiV1EsgCarbon-emission': {
    parameters: {
      query?: {
        /** @description Filtering by ESG layer (multiple values should be separated by commas). Default: 1,2 */
        layer?: ('1' | '2')[]
        /** @description Filtering by ESG type (multiple values should be separated by commas). Default: PLATFORM,TOKEN */
        type?: ('TOKEN' | 'PLATFORM')[]
        /** @description Filtering by ESG consensus group (multiple values should be separated by commas). Available values: PoS, PoSt, PoW, other */
        consensusGroup?: ('PoW' | 'PoSt' | 'PoS' | 'other')[]
        /** @description This field was used to sort data. Available values: emission, emissionPerTx, emissionPerTxPerNode, throughput, validators */
        sortBy?: 'emission' | 'emissionPerTx' | 'emissionPerTxPerNode' | 'validators' | 'throughput' | 'name'
        /** @description This field was used to sort data. Available values: ASC, DESC */
        sortFlow?: 'ASC' | 'DESC'
        /** @description This field was used to search asset */
        terms?: string
        /** @description This field was used for pagination */
        page?: number
        /** @description This field was used to limit items per page */
        perPage?: number
      }
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Success response for getting carbon emission feeds */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Current page number */
              currentPage: number
              /** @description Number of items per page */
              perPage: number
              /** @description Last page number */
              lastPage: number
              data: {
                /** @description Asset name */
                name: string
                slug: string
                /** @description Asset symbol */
                symbol: string
                logo: string | null
                type: ('TOKEN' | 'PLATFORM') | null
                layer: ('1' | '2') | null
                consensusName: ('PoW' | 'PoSt' | 'PoS' | 'other') | null
                /** @description Total emission of the asset / platform */
                totalEmission: string
                /** @description Emission per transaction of the asset / platform */
                emissionPerTx: string
                /** @description Emission per transaction per node of the asset / platform */
                emissionPerTxPerNode: string
                /** @description Number of validators of the asset / platform */
                validators: string
                /** @description Throughput of the asset / platform */
                throughput: string
                timestamp: (Record<string, never> | string | number) | null
              }[]
              /** @description Total of items */
              totalItem: number
            }
            /** @description Request id for reference */
            reqId: number
          }
          'multipart/form-data': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Current page number */
              currentPage: number
              /** @description Number of items per page */
              perPage: number
              /** @description Last page number */
              lastPage: number
              data: {
                /** @description Asset name */
                name: string
                slug: string
                /** @description Asset symbol */
                symbol: string
                logo: string | null
                type: ('TOKEN' | 'PLATFORM') | null
                layer: ('1' | '2') | null
                consensusName: ('PoW' | 'PoSt' | 'PoS' | 'other') | null
                /** @description Total emission of the asset / platform */
                totalEmission: string
                /** @description Emission per transaction of the asset / platform */
                emissionPerTx: string
                /** @description Emission per transaction per node of the asset / platform */
                emissionPerTxPerNode: string
                /** @description Number of validators of the asset / platform */
                validators: string
                /** @description Throughput of the asset / platform */
                throughput: string
                timestamp: (Record<string, never> | string | number) | null
              }[]
              /** @description Total of items */
              totalItem: number
            }
            /** @description Request id for reference */
            reqId: number
          }
          'text/plain': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Current page number */
              currentPage: number
              /** @description Number of items per page */
              perPage: number
              /** @description Last page number */
              lastPage: number
              data: {
                /** @description Asset name */
                name: string
                slug: string
                /** @description Asset symbol */
                symbol: string
                logo: string | null
                type: ('TOKEN' | 'PLATFORM') | null
                layer: ('1' | '2') | null
                consensusName: ('PoW' | 'PoSt' | 'PoS' | 'other') | null
                /** @description Total emission of the asset / platform */
                totalEmission: string
                /** @description Emission per transaction of the asset / platform */
                emissionPerTx: string
                /** @description Emission per transaction per node of the asset / platform */
                emissionPerTxPerNode: string
                /** @description Number of validators of the asset / platform */
                validators: string
                /** @description Throughput of the asset / platform */
                throughput: string
                timestamp: (Record<string, never> | string | number) | null
              }[]
              /** @description Total of items */
              totalItem: number
            }
            /** @description Request id for reference */
            reqId: number
          }
        }
      }
      /** @description Fail response when user is not authorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when user is forbidden to access API */
      403: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when server have an issue */
      500: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
    }
  }
  'getNodiensApiV1EsgCarbon-emissionBySlug': {
    parameters: {
      query: {
        metric: 'emission' | 'emissionPerTx' | 'emissionPerTxPerNode'
      }
      header?: never
      path: {
        /** @description Slug of asset */
        slug: string
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Success response for getting carbon emission history */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Asset metadata information */
              feeds: {
                /** @description Asset name */
                name: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Asset slug */
                slug: string
              }
              /** @description Timeseries data of carbon emission metrics */
              analytics: [
                number,
                {
                  lowerBound: number
                  bestGuess: number
                  upperBound: number
                },
              ][]
              /** @description Timeseries data of daily metrics */
              dailyMetrics: [
                number,
                {
                  throughput: number
                  validatorCount: number
                },
              ][]
              consensus: 'PoW' | 'PoSt' | 'PoS' | 'other'
              /** @description ESG type */
              type: 'TOKEN' | 'PLATFORM'
              /** @description ESG layer */
              layer: '1' | '2'
              /** @description Availability of energy consumption metric */
              availabilityOfEnergyConsumption: boolean
              /** @description Start date of carbon emission history (unix timestamp) */
              startDate: number | null
              /** @description End date of carbon emission history (unix timestamp) */
              endDate: number | null
            }
            /** @description Request id for reference */
            reqId: number
          }
          'multipart/form-data': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Asset metadata information */
              feeds: {
                /** @description Asset name */
                name: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Asset slug */
                slug: string
              }
              /** @description Timeseries data of carbon emission metrics */
              analytics: [
                number,
                {
                  lowerBound: number
                  bestGuess: number
                  upperBound: number
                },
              ][]
              /** @description Timeseries data of daily metrics */
              dailyMetrics: [
                number,
                {
                  throughput: number
                  validatorCount: number
                },
              ][]
              consensus: 'PoW' | 'PoSt' | 'PoS' | 'other'
              /** @description ESG type */
              type: 'TOKEN' | 'PLATFORM'
              /** @description ESG layer */
              layer: '1' | '2'
              /** @description Availability of energy consumption metric */
              availabilityOfEnergyConsumption: boolean
              /** @description Start date of carbon emission history (unix timestamp) */
              startDate: number | null
              /** @description End date of carbon emission history (unix timestamp) */
              endDate: number | null
            }
            /** @description Request id for reference */
            reqId: number
          }
          'text/plain': {
            /** @description Response message code for frontend to handle with i18n */
            code: string
            payload: {
              /** @description Asset metadata information */
              feeds: {
                /** @description Asset name */
                name: string
                logo: string | null
                /** @description Asset symbol */
                symbol: string
                /** @description Asset slug */
                slug: string
              }
              /** @description Timeseries data of carbon emission metrics */
              analytics: [
                number,
                {
                  lowerBound: number
                  bestGuess: number
                  upperBound: number
                },
              ][]
              /** @description Timeseries data of daily metrics */
              dailyMetrics: [
                number,
                {
                  throughput: number
                  validatorCount: number
                },
              ][]
              consensus: 'PoW' | 'PoSt' | 'PoS' | 'other'
              /** @description ESG type */
              type: 'TOKEN' | 'PLATFORM'
              /** @description ESG layer */
              layer: '1' | '2'
              /** @description Availability of energy consumption metric */
              availabilityOfEnergyConsumption: boolean
              /** @description Start date of carbon emission history (unix timestamp) */
              startDate: number | null
              /** @description End date of carbon emission history (unix timestamp) */
              endDate: number | null
            }
            /** @description Request id for reference */
            reqId: number
          }
        }
      }
      /** @description Fail response when user is not authorized */
      401: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when user is forbidden to access API */
      403: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when asset is not available */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
      /** @description Fail response when server have an issue */
      500: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'multipart/form-data': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
          'text/plain': {
            code: string
            payload: null
            reqId: number
            errors?: string[]
          }
        }
      }
    }
  }
}
