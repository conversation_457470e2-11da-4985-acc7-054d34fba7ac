import { authMiddleware } from '~/server/trpc/middleware/auth'
import { publicProcedure as trpcProcedure } from '~/server/trpc/trpc'
import { guestMiddleware } from '~/server/trpc/middleware/guest'
import { webhookMiddleware } from '../middleware/webhook'
import { csrfMiddleware } from '../middleware/csrf'

export const authorizedProcedure = trpcProcedure.use(authMiddleware).use(csrfMiddleware)
export const publicProcedure = trpcProcedure.use(csrfMiddleware)
export const webhookProcedure = trpcProcedure.use(webhookMiddleware)
export const guestProcedure = trpcProcedure.use(guestMiddleware)
