import { router } from '~/server/trpc/trpc'
import { publicProcedure } from '~/server/trpc/procedure/authorized'
import { z } from 'zod'
import { serverSupabaseClient, serverSupabaseServiceRole } from '#supabase/server'
// eslint-disable-next-line @nx/enforce-module-boundaries
import type { Database } from 'app-types/supabase'
import { TRPCError } from '@trpc/server'

export default router({
  /**
   * Authentication was not required, but someone has authorized logged-in user
   * will be attached into subscriptions
   *
   * @params string (email address)
   */
  subscribeEmail: publicProcedure
    .input(
      z.object({
        email: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const { validateLimiter, increaseLimiter } = await useLimitter('subs-newsletter', ctx.event, {
        expired_in: 30,
        limit: 3,
      })
      const flagLimit = await validateLimiter()
      if (flagLimit === false) {
        throw new TRPCError({
          code: 'TOO_MANY_REQUESTS',
          message: 'Have to many request, please try again later.',
        })
      }

      const client = await serverSupabaseClient<Database>(ctx.event)

      const checkDuplicate = await client
        .from('newsletter_subscriptions')
        .select()
        .eq('email', input.email)
        .maybeSingle()
      if (checkDuplicate.data) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: `${input.email} was registered to newsletters`,
        })
      }

      const { data } = await client
        .from('newsletter_subscriptions')
        .insert({
          email: input.email,
          user_id: ctx.user?.profile?.userId ?? null,
          subscriptions: {},
        })
        .select()
        .single()

      await Promise.all([
        sendEmail({
          to: input.email,
          html: welcomeNewsletterTemplate({
            emailId: data!.uuid,
            id: '123',
            email: input.email,
          }),
          subject: 'Welcome to CoinConsole Newsletters',
        }),
        increaseLimiter(),
      ])

      return {
        message: `Success adding ${input.email} to newsletters`,
        data,
      }
    }),
  getSubscription: publicProcedure.input(z.string().optional()).query<{
    subscribe: boolean | null
    uuid: string | null
  }>(async ({ ctx, input }) => {
    if (!input) {
      return {
        uuid: null,
        subscribe: null,
      }
    }

    const supabaseClient = serverSupabaseServiceRole<Database>(ctx.event)
    const email = await supabaseClient
      .from('newsletter_subscriptions')
      .select('*')
      .eq('uuid', input)
      .limit(1)
      .maybeSingle()

    if (email.data === null) {
      return {
        subscribe: null,
        uuid: input,
      }
    }

    return {
      subscribe: !email.data.unsubscribe,
      uuid: input,
    }
  }),
  setSubscription: publicProcedure
    .input(
      z.object({
        uuid: z.string(),
        subscription: z.boolean().default(false),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { validateLimiter, increaseLimiter } = await useLimitter('setsubs-newsletter', ctx.event, {
        expired_in: 30,
        limit: 3,
      })
      const flagLimit = await validateLimiter()
      if (flagLimit === false) {
        throw new TRPCError({
          code: 'TOO_MANY_REQUESTS',
          message: 'Have to many request, please try again later.',
        })
      }
      const client = serverSupabaseServiceRole<Database>(ctx.event)

      const subscription = await client
        .from('newsletter_subscriptions')
        .select('*')
        .eq('uuid', input.uuid)
        .limit(1)
        .maybeSingle()

      if (subscription.data === null) {
        return false
      }

      await client
        .from('newsletter_subscriptions')
        .update({
          unsubscribe: !input.subscription,
        })
        .eq('uuid', input.uuid)
        .returns()
      await increaseLimiter()

      return true
    }),
})
