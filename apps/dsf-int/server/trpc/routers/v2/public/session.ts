import { publicProcedure, router } from '~/server/trpc/trpc'
import { useCsrf } from '~/server/utils/csrf'
import { generateRandomKey } from '~/utils/generator'
import { z } from 'zod'
import { TRPCError } from '@trpc/server'

const csrfSession = useCsrf()
const guestToken = generateRandomKey()

export default router({
  generate: publicProcedure.query(async ({ ctx }) => {
    const record: Record<string, string> = {}

    if (!ctx.user.profile?.userId) {
      record.guest = guestToken
    } else {
      record.user = ctx.user.profile?.userId
    }

    const token = csrfSession.generateCsrf(record)

    return token
  }),
  refresh: publicProcedure
    .input(
      z.object({
        token: z.string(),
      })
    )
    .mutation(async ({ input }) => {
      const refresh = csrfSession.refreshToken(input.token)

      if (!refresh) {
        throw new TRPCError({
          code: 'UNPROCESSABLE_CONTENT',
          message: 'INVALID_TOKEN',
        })
      }

      return refresh
    }),
})
