import { router } from '@/server/trpc/trpc'
import { publicProcedure } from '@/server/trpc/procedure/authorized'
import { z } from 'zod'
import { cachedTopDaily } from '~/server/utils/trpc/v2/cache/dashboard'
import { displayWattage, numericDisplay } from '~/helper/number-helper'
import { and, eq, sql, inArray, ne, desc, asc, SQL } from 'drizzle-orm'
import { asset, tsdbViewIndicesV2 } from '~/server/utils/chat-source/schema'
import { checkNumber } from '~/helper/utilities-helper'
import { defaultFilter } from '~/server/utils/chat-source/schema/tsdb_vw_indices_v2_feeds'
import { assetNameFilter, exactOrder } from '~/server/utils/chat-source/schema/asset'
import { cachedIndicesHistory } from '~/server/utils/repositories/indices'
import { PER_PAGE } from '~/constants/api'
import AssetSelection from '~/server/utils/asset_selection'

export type DashboardTable = {
  name: string | undefined
  logo: string | null | undefined
  slug: string
  mood_index: number | null
  trust_index: number | null
  energy: number | null
  community_size: number | null
  number_of_message: number | null
  price: number | null
  graph: (number | null)[][] | undefined
  page: number
  totalPage: number
  type: 'cryptocurrency' | 'stablecoin'
}[]

export default router({
  table: publicProcedure
    .input(
      z.object({
        selected: z.array(z.enum(['crypto', 'stable'])).default(['crypto']),
        rank: z.enum(['mood', 'trust', 'energy_trx', 'number_msg', 'community_size']).default('mood'),
        page: z.number().default(1),
        terms: z.string().optional(),
      }),
    )
    .query(async ({ input, ctx }) => {
      const assetSelection = await AssetSelection.initialize(ctx)

      const whereQuery = and(
        inArray(
          asset.type,
          input.selected.map((d) => {
            if (d === 'stable') {
              return 'STABLECOIN'
            }
            if (d === 'crypto') {
              return 'CRYPTOCURRENCY'
            }
            return 'CRYPTOCURRENCY'
          }),
        ),
        input.rank === 'energy_trx' ? ne(tsdbViewIndicesV2.energyConsumptionTx, 0) : defaultFilter,
        input.terms ? assetNameFilter(input.terms) : undefined,
        assetSelection.availableAsset ? inArray(asset.id, assetSelection.availableAsset) : undefined,
      )

      const queriesSq = chatSourceClient.$with('queriesSq').as(
        chatSourceClient
          .select({
            name: asset.name,
            logo: asset.logo,
            slug: asset.slug,
            symbol: asset.symbol,
            type: asset.type,
            assetId: asset.id,
            moodIndex: tsdbViewIndicesV2.moodIndex,
            trustIndex: tsdbViewIndicesV2.trustIndex,
            communitySize: tsdbViewIndicesV2.communitySize,
            energyConsumptionTx: tsdbViewIndicesV2.energyConsumptionTx,
            number_message: tsdbViewIndicesV2.nrMessages,
            vulgarityIndex: tsdbViewIndicesV2.v1VulgarityIndex,
            totalCount: sql<number>`cast (COUNT(*) OVER() as int)`.as('totalCount'),
          })
          .from(tsdbViewIndicesV2)
          .innerJoin(asset, eq(tsdbViewIndicesV2.assetId, asset.id))
          .where(ctx.user ? whereQuery : undefined)
          .orderBy(() => {
            const orderQuery: SQL<unknown>[] = []
            // Ordering query based on rank
            orderQuery.push(sql`
                CASE
                  WHEN ${tsdbViewIndicesV2.timestamp} = CURRENT_DATE then 0
                  ELSE 1
                END
              `)
            if (ctx.user) {
              if (input.rank === 'energy_trx') {
                orderQuery.push(asc(tsdbViewIndicesV2.energyConsumptionTx))
              }

              if (input.rank === 'community_size') {
                orderQuery.push(
                  sql`
                CASE
                  WHEN ${tsdbViewIndicesV2.communitySize} IS NULL THEN 1
                  ELSE 0
                  END`,
                  desc(tsdbViewIndicesV2.communitySize),
                )
              }

              if (input.rank === 'trust') {
                orderQuery.push(
                  sql`
                CASE
                  WHEN ${tsdbViewIndicesV2.trustRank} = 'NaN' THEN 1
                  WHEN ${tsdbViewIndicesV2.trustRank} IS NULL THEN 2
                  ELSE 0
                  END`,
                  desc(tsdbViewIndicesV2.trustRank),
                )
              }

              if (input.rank === 'number_msg') {
                orderQuery.push(desc(tsdbViewIndicesV2.nrMessages))
              }

              if (input.terms) {
                orderQuery.push(...exactOrder(input.terms))
              }
            }

            if (input.rank === 'mood' || orderQuery.length === 0) {
              orderQuery.push(
                sql`
                CASE
                    WHEN ${tsdbViewIndicesV2.moodRank} = 'NaN' THEN 1
                    WHEN ${tsdbViewIndicesV2.moodRank} IS NULL THEN 2
                    ELSE 0
                END`,
                desc(tsdbViewIndicesV2.moodRank),
              )
            }

            return orderQuery
          })
          .limit(!ctx.user.feature.checkSubscription() ? 10 : PER_PAGE)
          .offset(!ctx.user.feature.checkSubscription() ? 0 : input.page ? (input.page - 1) * (PER_PAGE ?? 20) : 0),
      )

      const result = await chatSourceClient.with(queriesSq).select().from(queriesSq)

      const histories = await Promise.all(result.map((d) => cachedIndicesHistory(d.assetId, input.rank)))

      return {
        data: result.map((item) => {
          return {
            name: item.name,
            logo: item.logo,
            symbol: item.symbol,
            type: item.type,
            slug: item.slug,
            mood_index: checkNumber(item.moodIndex) ? numericDisplay(item.moodIndex ?? 0) : '-',
            trust_index: checkNumber(item.trustIndex) ? numericDisplay(item.trustIndex ?? 0) : '-',
            energy: item.energyConsumptionTx ? displayWattage(item.energyConsumptionTx) + 'h' : '-',
            community_size: checkNumber(item.communitySize) ? numericDisplay(item.communitySize ?? 0, 0) : '-',
            number_of_message: checkNumber(item.number_message) ? numericDisplay(item.number_message ?? 0, 0) : '-',
            graph: histories.find((d) => d && d.id === item.assetId)?.ts ?? [],
          }
        }),
        pagination: {
          current: ctx.user.feature.checkSubscription() ? input.page : 1,
          last_page: !ctx.user.feature.checkSubscription() ? (result[0]?.totalCount ?? 0) / PER_PAGE : 1,
          per_page: ctx.user.feature.checkSubscription() ? input.page : 1,
          total_item: ctx.user.feature.checkSubscription() ? (result[0]?.totalCount ?? 0) : 10,
        },
      }
    }),

  topDaily: publicProcedure.query(async () => {
    const result = await cachedTopDaily()
    return {
      last_calculation: result?.timestamp,
      mood: result?.data.mood.map((d, i) => ({
        rank: i + 1,
        name: d.name,
        slug: d.slug,
        logo: d.logo,
        symbol: d.symbol,
        percentage: d.daily_different,
        indicator: d.indicator,
        type: d.type,
      })),
      trust: result?.data.trust.map((d, i) => ({
        rank: i + 1,
        name: d.name,
        slug: d.slug,
        logo: d.logo,
        symbol: d.symbol,
        percentage: d.daily_different,
        indicator: d.indicator,
        type: d.type,
      })),
      topic_analytic: result?.data.topic.map((d, i) => ({
        rank: i + 1,
        name: d.name,
        slug: d.slug,
        type: 'topic',
      })),
    }
  }),
})
