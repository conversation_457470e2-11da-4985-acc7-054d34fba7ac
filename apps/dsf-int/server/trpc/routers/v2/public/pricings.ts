import { publicProcedure } from '~/server/trpc/trpc'
import { router } from '~/server/trpc/trpc'
import { serverSupabaseClient } from '#supabase/server'

// Types
// eslint-disable-next-line @nx/enforce-module-boundaries
import type { Database } from 'app-types/supabase'

export default router({
  getPricing: publicProcedure.query(async ({ ctx }) => {
    const client = await serverSupabaseClient<Database>(ctx.event)
    const { data: pricing } = await client.from('pricings').select('*')

    const serializedPricing = (pricing ?? []).map((pricing) => {
      const serializedItem = { ...pricing }

      delete (serializedItem as Partial<typeof serializedItem>).stripe_price_ids

      return serializedItem as Omit<typeof serializedItem, 'features' | 'stripe_price_ids'> & {
        features: Record<string, unknown>
      }
    })

    return serializedPricing.sort((a, b) => (a?.level ?? 0) - (b?.level ?? 0))
  }),
})
