import { numericDisplay } from '~/helper/number-helper'
import { checkNumber } from '~/helper/utilities-helper'
import { publicProcedure } from '~/server/trpc/procedure/authorized'
import { router } from '~/server/trpc/trpc'

export default router({
  moodPreview: publicProcedure.query(async () => {
    const result = await getIndices('mood', {
      sort: 'indexes',
      page: 1,
      perPage: 5,
      historyLength: 7,
      type: ['STABLECOIN', 'CRYPTOCURRENCY'],
    })

    return result.data.map(mood => {
      return {
        name: mood.name,
        logo: mood.logo,
        symbol: mood.symbol,
        symbol_compare: mood.symbol_compare,
        chart: mood.chart,
        changed: checkNumber(mood.changed)
          ? numericDisplay(mood.changed ? mood.changed * 100 : 0).replace('-', '') + '%'
          : '-',
        mood: checkNumber(mood.indexes) ? numericDisplay(mood.indexes ?? '0') : '-',
      }
    })
  }),
})
