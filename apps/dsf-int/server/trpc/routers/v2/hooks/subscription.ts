/* eslint-disable no-case-declarations */
import { serverSupabaseServiceRole } from '#supabase/server'
import { TRPCError } from '@trpc/server'
import { webhookProcedure } from '~/server/trpc/procedure/authorized'
import { router } from '~/server/trpc/trpc'

// Types
// eslint-disable-next-line @nx/enforce-module-boundaries
import type { Database } from 'app-types/supabase'
import { StripeSession, userAssetPreferences, UserSubscriptions } from '~/server/utils/platform-db/schema'
import { eq, sql } from 'drizzle-orm'
import Stripe from 'stripe'
import { jsonExtract } from '~/server/utils/database'
import { toSnakeCase } from '~/helper/utilities-helper'

const runtimeConfig = useRuntimeConfig()

export default router({
  webhook: webhookProcedure.mutation(async ({ ctx }) => {
    const rawBody = await readRawBody(ctx.event, false)
    const client = serverSupabaseServiceRole<Database>(ctx.event)
    const signature = getHeader(ctx.event, 'stripe-signature')

    if (!rawBody) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'Invalid request body.',
      })
    }

    if (!signature) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'Invalid signature.',
      })
    }

    let event: Stripe.Event | undefined
    try {
      event = ctx.stripe.webhooks.constructEvent(rawBody, signature.toString(), runtimeConfig.stripeWebhookSecretKey)
    } catch (e) {
      console.log('⚠️ Webhook signature verification failed: ', e)
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Cannot verify webhook signature.',
      })
    }

    try {
      switch (event.type) {
        case 'charge.succeeded':
          await client.from('stripe_payment_intents').insert({
            payment_intent_id: event.data.object.payment_intent?.toString(),
            receipt_url: event.data.object.receipt_url,
            stripe_customer_id: event.data.object.customer!.toString(),
          })
          break

        case 'checkout.session.completed':
          const paymentSession = await platformClient.query.StripeSession.findFirst({
            where: (fields, { eq }) => eq(fields.sessionId, event.data.object.id),
          })
          const mainPackage = await platformClient.query.UserSubscriptions.findFirst({
            where: (fields, { eq, and }) =>
              and(
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                // @ts-expect-error
                eq(jsonExtract(fields.rawData, 'session_id'), paymentSession?.productRawData?.main_checkout_id ?? '-'),
                eq(fields.status, 'active'),
              ),
          })

          if (paymentSession && paymentSession.productRawData?.mode === 'main') {
            await platformClient.transaction(async (tx) => {
              // eslint-disable-next-line @typescript-eslint/no-unused-vars
              const [_, product] = await Promise.all([
                tx
                  .update(StripeSession)
                  .set({
                    status: 'active',
                    rawData: sql`${{
                      ...(event.data.object as unknown as Record<string, unknown>),
                      subscription_id: event.data.object.subscription as string,
                    }}::jsonb`,
                    amountCapturable: event.data.object.amount_total,
                  })
                  .where(eq(StripeSession.sessionId, event.data.object.id)),
                tx.query.Pricing.findFirst({
                  where: (fields, { eq }) => eq(fields.id, paymentSession.productRawData!.id as number),
                }),
              ])

              if (event.data.object.payment_status === 'paid' && product) {
                const productRawData = paymentSession.productRawData
                let additionalAsset = 0

                if (typeof product.features?.max_assets === 'number') {
                  if (
                    productRawData &&
                    'preferences' in productRawData &&
                    Array.isArray(productRawData.preferences) &&
                    product.features &&
                    typeof product.features.max_assets === 'number'
                  ) {
                    additionalAsset = productRawData.preferences.length - product.features.max_assets
                  } else {
                    additionalAsset = 0
                  }
                }

                await Promise.all([
                  tx.insert(UserSubscriptions).values({
                    userId: paymentSession.userId ?? '',
                    planId:
                      typeof productRawData === 'object' && productRawData !== null && 'id' in productRawData
                        ? (productRawData.id as string).toString()
                        : '',
                    planName: productRawData?.name ?? 'Unknown Plan',
                    status: 'active',
                    paymentStatus: 'paid',
                    expiredDate: new Date(Date.now() + (productRawData!.is_annual ? 365 : 30) * 24 * 60 * 60 * 1000),
                    activeAt: new Date(),
                    rawData: sql`${JSON.stringify({
                      last_event_creation: event.data.object.created,
                      require_addon: additionalAsset > 0,
                      addon_enable: false,
                      customer_id: event.data.object.customer?.toString() ?? '',
                      session_id: event.data.object.id?.toString() ?? '',
                      subscription_id: event.data.object.subscription?.toString() ?? '',
                      product_id: productRawData!.id,
                      amount: productRawData!.is_annual
                        ? Number(((productRawData as { annual_price: number }).annual_price * 12).toFixed(2))
                        : productRawData!.monthly_price,
                      is_annual: productRawData!.is_annual,
                    })}::jsonb`,
                  } as unknown as (typeof UserSubscriptions)['$inferInsert']),
                  paymentSession.userId && productRawData!.preferences
                    ? client
                        .from('user_asset_preferences')
                        .delete()
                        .eq('user_id', paymentSession.userId)
                        .then(
                          async () =>
                            await client.from('user_asset_preferences').insert(
                              Array.isArray(productRawData?.preferences)
                                ? productRawData.preferences.map((d: number) => ({
                                    asset_id: d,
                                    user_id: paymentSession.userId ?? undefined,
                                  }))
                                : [],
                            ),
                        )
                    : undefined,
                ])
              }
            })
          }

          if (
            paymentSession &&
            paymentSession.productRawData?.mode === 'addon' &&
            mainPackage &&
            !paymentSession.productRawData?.pending_addon
          ) {
            await platformClient.transaction(async (tx) => {
              await tx
                .update(UserSubscriptions)
                .set({
                  rawData: sql`${{
                    ...toSnakeCase(mainPackage.rawData),
                    addon_subscription_id: event.data.object.subscription,
                    addon_enable: false,
                  }}::jsonb`,
                })
                .where(
                  eq(
                    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                    // @ts-expect-error
                    jsonExtract(UserSubscriptions.rawData, 'session_id'),
                    paymentSession.productRawData!.main_checkout_id,
                  ),
                )

              await tx
                .update(StripeSession)
                .set({
                  amountCapturable: event.data.object.amount_total,
                  status: 'complete',
                  rawData: sql`${{
                    customer_id: event.data.object.customer as string,
                    session_id: event.data.object.id as string,
                    subscription_id: event.data.object.subscription as string,
                  }}::jsonb`,
                })
                .where(eq(StripeSession.sessionId, event.data.object.id))
            })
          }

          if (
            paymentSession &&
            paymentSession.productRawData?.mode === 'addon' &&
            paymentSession.productRawData?.pending_addon &&
            mainPackage
          ) {
            await platformClient.transaction(async (tx) => {
              await tx
                .update(UserSubscriptions)
                .set({
                  rawData: sql`${{
                    ...toSnakeCase(mainPackage.rawData),
                    future_addon_subscription_id: event.data.object.subscription,
                    future_addon_enable: true,
                    future_require_addon: true,
                  }}::jsonb`,
                })
                .where(
                  eq(
                    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                    // @ts-expect-error
                    jsonExtract(UserSubscriptions.rawData, 'session_id'),
                    paymentSession.productRawData!.main_checkout_id,
                  ),
                )

              await tx
                .update(StripeSession)
                .set({
                  amountCapturable: event.data.object.amount_total,
                  status: 'complete',
                  rawData: sql`${{
                    customer_id: event.data.object.customer as string,
                    session_id: event.data.object.id as string,
                    subscription_id: event.data.object.subscription as string,
                  }}::jsonb`,
                })
                .where(eq(StripeSession.sessionId, event.data.object.id))
            })
          }
          break

        // Handle subscription updated
        case 'customer.subscription.updated':
          const subsUpdate = await platformClient.query.UserSubscriptions.findFirst({
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-expect-error
            where: (fields, { eq }) => eq(jsonExtract(fields.rawData, 'subscription_id'), event.data.object.id),
          })
          if (!subsUpdate) {
            throw new TRPCError({
              code: 'NOT_FOUND',
              message: 'Subscription not found.',
            })
          }

          if (subsUpdate) {
            const subsRawData = subsUpdate.rawData as Record<string, any>

            // Handle Scheduled Upgrade Plan
            if ((event.data.previous_attributes?.items?.data?.length ?? 0) > 0) {
              const previousSub = event.data.previous_attributes?.items?.data[0]

              if (subsRawData.subscription_id === previousSub?.subscription) {
                await platformClient
                  .update(UserSubscriptions)
                  .set({
                    switchRawData: sql`${{
                      ...(event.data.object.items.data[0] as unknown as Record<string, unknown>),
                      active_at: subsUpdate.expiredDate,
                    }}::jsonb`,
                  })
                  .where(eq(UserSubscriptions.id, subsUpdate.id))
              }
            } else {
              const upgradeRawData = subsUpdate.switchRawData as Record<string, any> | null
              if (event.data.object.status === 'active') {
                // Handle Switching Plan
                if (upgradeRawData) {
                  const getNewPlan = await client
                    .from('pricings')
                    .select('*')
                    .or(
                      `stripe_price_ids->>annual.eq.${upgradeRawData.price.id},stripe_price_ids->>monthly.eq.${upgradeRawData.price.id}`,
                    )
                    .single()

                  if (getNewPlan.data) {
                    const isUpgradeAnnual = upgradeRawData.price.recurring.interval !== 'month'

                    await platformClient.transaction(async (tx) => {
                      await Promise.all([
                        tx.update(UserSubscriptions).set({
                          planId: getNewPlan.data.id.toString(),
                          planName: getNewPlan.data.name,
                          status: 'active',
                          expiredDate: new Date(event.data.object.current_period_end * 1000),
                          activeAt: new Date(),
                          rawData: sql`${{
                            ...{ ...subsRawData, future_preferences: undefined },
                            last_event_creation: event.data.object.created,
                            future_addon_session_id: undefined,
                            is_annual: isUpgradeAnnual,
                            amount: isUpgradeAnnual
                              ? Number((getNewPlan.data.annual_price * 12).toFixed(2))
                              : getNewPlan.data.monthly_price,
                          }}::jsonb`,
                          switchRawData: null,
                        }),
                        subsRawData.future_preferences && subsRawData.future_preferences.length > 0
                          ? tx
                              .delete(userAssetPreferences)
                              .where(eq(userAssetPreferences.userId, subsUpdate.userId))
                              .then(async () => {
                                await tx.insert(userAssetPreferences).values(
                                  (subsUpdate.rawData?.future_preferences as number[]).map((d: number) => ({
                                    assetId: d,
                                    userId: subsUpdate.userId,
                                  })),
                                )
                              })
                          : undefined,
                      ])
                    })
                  }
                } else {
                  // Handle Renewal
                  await platformClient.transaction(async (tx) => {
                    await Promise.all([
                      tx
                        .update(UserSubscriptions)
                        .set({
                          status: 'active',
                          expiredDate: new Date(event.data.object.current_period_end * 1000),
                          rawData: sql`${{
                            ...{ ...subsRawData, future_preferences: undefined },
                            last_event_creation: event.data.object.created,
                            addon_subscription_id: subsRawData.future_addon_subscription_id,
                            addon_session_id: subsRawData.future_addon_session_id,
                            require_addon: subsRawData.future_require_addon,
                            addon_enable: subsRawData.future_addon_enable,
                            future_addon_session_id: undefined,
                            future_addon_subscription_id: undefined,
                            future_require_addon: undefined,
                            future_addon_enable: undefined,
                          }}::jsonb`,
                        })
                        .where(eq(UserSubscriptions.userId, subsUpdate.userId)),
                      Array.isArray(subsUpdate.rawData?.future_preferences) &&
                      subsUpdate.rawData?.future_preferences.length > 0
                        ? tx
                            .delete(userAssetPreferences)
                            .where(eq(userAssetPreferences.userId, subsUpdate.userId))
                            .then(async () => {
                              await tx.insert(userAssetPreferences).values(
                                (subsUpdate.rawData?.future_preferences as number[]).map((d: number) => ({
                                  assetId: d,
                                  userId: subsUpdate.userId,
                                })),
                              )
                            })
                        : undefined,
                    ])
                  })
                }
              }
            }
          }
          break

        // Handle subscription canceled / expired
        case 'customer.subscription.deleted':
          const addon = event.data.object.items.data[0]?.price.lookup_key?.includes('addon') ?? false

          if (addon) {
            const subscription = await platformClient.query.UserSubscriptions.findFirst({
              where: (fields, { eq, and }) =>
                and(
                  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                  // @ts-expect-error
                  eq(jsonExtract(fields.rawData, 'addon_subscription_id'), event.data.object.id),
                  eq(fields.status, 'active'),
                ),
            })

            if (subscription) {
              await platformClient
                .update(UserSubscriptions)
                .set({
                  rawData: sql`${{
                    ...subscription.rawData,
                    addon_subscription_id: undefined,
                    addon_session_id: undefined,
                    addon_enable: false,
                    require_addon: false,
                  }}::jsonb`,
                })
                .where(eq(UserSubscriptions.id, subscription.id))
            }
          } else {
            const subsDelete = await client
              .from('user_subscriptions')
              .select('*')
              .eq('raw_data->>"subscription_id"', event.data.object.id)
              .single()
            if (subsDelete.data) {
              if (event.data.object.status === 'past_due' || event.data.object.status === 'canceled') {
                await platformClient.transaction(async (tx) => {
                  await tx
                    .update(UserSubscriptions)
                    .set({
                      status: 'expired',
                    })
                    .where(eq(UserSubscriptions.id, subsDelete.data.id))
                  await tx.delete(userAssetPreferences).where(eq(userAssetPreferences.userId, subsDelete.data.user_id!))
                })
              }
            }
          }
          break
      }
    } catch (error) {
      console.log('webhook error', error)
    }
    return {
      received: true,
    }
  }),
})
