export type SpiderChartIndice = {
  name?: string
  assetId?: number
  slug: string
  symbol: string
  error_message?: string
  metrics: {
    [key: string]: number
  }
  rawData?: {
    [key: string]: number
  }
}

export type ComparisonChartIndice = {
  assetId?: number
  name?: string
  slug: string
  symbol: string
  firstCalculation: string
  latestCalculation: string
  timeseries: [number, number][]
}
