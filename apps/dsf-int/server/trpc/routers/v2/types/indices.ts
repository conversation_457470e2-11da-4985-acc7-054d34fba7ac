import type { Platform } from '~/server/utils/repositories/topics'
import type { AssetType } from '~/server/utils/chat-source/schema/asset'

export interface IndicesDataTable {
  name: string
  community_size: string
  number_of_message: string
  indexes: string
  changed: string
  changed_symbol: '>' | '<' | '='
  chart: [number, number][]
  type: AssetType['type']
  logo: string
  slug: string
  symbol: string
}

export interface IndicesTopicTable {
  name: string
  platform: Platform[]
  occurrences: string
  chart: [number, number][]
}
