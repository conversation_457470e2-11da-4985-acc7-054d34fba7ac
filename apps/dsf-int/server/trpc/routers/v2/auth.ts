import { publicProcedure, router } from '~/server/trpc/trpc'
import { authorizedProcedure, guestProcedure } from '~/server/trpc/procedure/authorized'
import { z } from 'zod'
import { serverSupabaseClient, serverSupabaseServiceRole } from '#supabase/server'
import { TRPCError } from '@trpc/server'
import { PublicKey } from '@hashgraph/sdk'
import { prefixMessageToSign } from '~/utils/data-transform'
import { hederaPub<PERSON>eyChecker } from '~/utils/hedera-pubkey'

// Types
// eslint-disable-next-line @nx/enforce-module-boundaries
import type { Database } from 'app-types/supabase'

const runtimeConfig = useRuntimeConfig()

export default router({
  register: guestProcedure
    .input(
      z
        .object({
          first_name: z.string(),
          last_name: z.string(),
          password: z.string(),
          confirm_password: z.string(),
          promo: z.string().optional(),
          company: z.string().optional(),
          email: z.string().email(),
        })
        .refine((data) => data.password === data.confirm_password, {
          message: `Password don't match`,
        }),
    )
    .mutation(async ({ input, ctx }) => {
      const client = serverSupabaseServiceRole<Database>(ctx.event)
      const { validateLimiter, increaseLimiter } = await useLimitter('signup', ctx.event, {
        expired_in: 60,
        limit: 3,
      })
      const flagLimit = await validateLimiter()
      if (flagLimit === false) {
        throw new TRPCError({
          code: 'TOO_MANY_REQUESTS',
          message: 'Have to many request, please try again later.',
        })
      }

      if (runtimeConfig.whitelistEmail) {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const [_, domain] = input.email.split('@')

        const checkDomain = await ctx.supabaseClient
          .from('email_whitelisting')
          .select()
          .eq('domain', domain as string)
          .maybeSingle()

        if (checkDomain.data === null) {
          throw new TRPCError({
            code: 'UNPROCESSABLE_CONTENT',
            message: `${input.email} is not allowed to register`,
          })
        }
      }

      const checkUser = await ctx.supabaseClient.from('emails').select().eq('email', input.email).maybeSingle()

      if (checkUser.data) {
        throw new TRPCError({
          code: 'UNPROCESSABLE_CONTENT',
          message: `${input.email} was already registered`,
        })
      }

      const { data, error } = await client.auth.admin.createUser({
        password: input.password,
        email: input.email,
        email_confirm: false,
        user_metadata: {
          display_name: `${input.first_name} ${input.last_name}`,
          company: input.company,
          first_name: input.first_name,
          last_name: input.last_name,
        },
      })

      await client.auth.resend({
        type: 'signup',
        email: input.email,
        options: {
          emailRedirectTo: `${runtimeConfig.public.appUrl}/auth/login?email=${input.email}&registration=success`,
        },
      })

      if (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error.message,
        })
      }

      await increaseLimiter()
      return {
        message: 'Registration success, please check your email inbox',
        data,
      }
    }),
  setPassword: authorizedProcedure
    .input(
      z.object({
        password: z.string(),
        confirmPassword: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const response = await ctx.supabaseClient.auth.updateUser({
        password: input.password,
      })

      if (!response.error) {
        await ctx.supabaseClient.auth.signOut({
          scope: 'local',
        })
      }

      return response
    }),
  signIn: guestProcedure
    .input(
      z.object({
        email: z.string(),
        password: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const { validateLimiter, increaseLimiter } = await useLimitter('signin', ctx.event, {
        limit: 10,
      })
      const flagLimit = await validateLimiter()
      if (flagLimit === false) {
        throw new TRPCError({
          code: 'TOO_MANY_REQUESTS',
          message: 'Have to many request, please try again later.',
        })
      }

      const response = await ctx.supabaseClient.auth.signInWithPassword({
        email: input.email,
        password: input.password,
      })

      if (response.data.user?.user_metadata.suspended) {
        increaseLimiter()
        await ctx.supabaseClient.auth.signOut({
          scope: 'local',
        })
        throw new TRPCError({
          code: 'UNPROCESSABLE_CONTENT',
          message: 'Your account has been suspended',
        })
      }

      if (response.data.user?.confirmed_at === null) {
        throw new TRPCError({
          code: 'UNPROCESSABLE_CONTENT',
          message: 'Your account has not been confirmed',
        })
      }

      if (response.error) {
        increaseLimiter()
        throw new TRPCError({
          code: 'UNPROCESSABLE_CONTENT',
          message: response.error.message,
          cause: response.error.stack,
        })
      }

      return response.data
    }),
  nonce: publicProcedure
    .input(
      z.object({
        accountId: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const { validateLimiter, increaseLimiter } = await useLimitter('nonce', ctx.event, {
        limit: 10,
      })
      const flagLimit = await validateLimiter()

      if (flagLimit === false) {
        throw new TRPCError({
          code: 'TOO_MANY_REQUESTS',
          message: 'Have to many request, please try again later.',
        })
      }

      const client = serverSupabaseServiceRole<Database>(ctx.event)

      const findUser = await ctx.supabaseClient
        .from('users')
        .select('*')
        .not('hashpack_account_id', 'is', null)
        .single()

      if (!findUser) {
        increaseLimiter()
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'No user account is currently linked with this wallet. Please connect your wallet first.',
        })
      }

      const nonce = Math.floor(Math.random() * 1000000)

      await client
        .from('users')
        .update({
          web3_auth: {
            genNonce: nonce,
            lastAuth: new Date().toISOString(),
            lastAuthStatus: 'pending',
          },
        })
        .eq('hashpack_account_id', input.accountId)

      return { nonce }
    }),
  registerNonce: guestProcedure
    .input(
      z.object({
        accountId: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const { validateLimiter, increaseLimiter } = await useLimitter('registerNonce', ctx.event, {
        limit: 10,
      })
      const flagLimit = await validateLimiter()

      if (flagLimit === false) {
        throw new TRPCError({
          code: 'TOO_MANY_REQUESTS',
          message: 'Have to many request, please try again later.',
        })
      }

      const client = serverSupabaseServiceRole<Database>(ctx.event)

      const findUserByAccountId = await client
        .from('users')
        .select('*')
        .eq('hashpack_account_id', input.accountId)
        .single()

      if (findUserByAccountId.data) {
        await increaseLimiter()
        throw new TRPCError({
          code: 'UNPROCESSABLE_CONTENT',
          message:
            'This wallet is already linked to an existing account. Please use a different wallet or login with the linked account.',
        })
      }

      const nonce = Math.floor(Math.random() * 1000000)

      const findGenOnceSession = await client
        .from('register_web3_nonce')
        .select('*')
        .eq('account_id', input.accountId)
        .single()

      if (findGenOnceSession.data) {
        if (findGenOnceSession.data.claimed) {
          await increaseLimiter()
          throw new TRPCError({
            code: 'UNPROCESSABLE_CONTENT',
            message: 'You have already claimed a nonce.',
          })
        } else {
          await client
            .from('register_web3_nonce')
            .update({
              nonce,
              claimed: false,
            })
            .eq('account_id', input.accountId)
        }
      } else {
        await client.from('register_web3_nonce').insert({
          account_id: input.accountId,
          nonce,
          claimed: false,
        })
      }

      return { nonce }
    }),
  signUpWeb3: guestProcedure
    .input(
      z
        .object({
          first_name: z.string(),
          last_name: z.string(),
          password: z.string(),
          confirm_password: z.string(),
          promo: z.string().optional(),
          company: z.string().optional(),
          email: z.string().email(),
          signature: z.array(
            z.object({
              signature: z.any(),
              publicKey: z.any(),
            }),
          ),
          accountId: z.string(),
        })
        .refine((data) => data.password === data.confirm_password, {
          message: `Password don't match`,
        }),
    )
    .mutation(async ({ input, ctx }) => {
      const { validateLimiter, increaseLimiter } = await useLimitter('signUpWeb3', ctx.event, {
        limit: 10,
      })
      const flagLimit = await validateLimiter()

      if (flagLimit === false) {
        throw new TRPCError({
          code: 'TOO_MANY_REQUESTS',
          message: 'Have to many request, please try again later.',
        })
      }

      if (runtimeConfig.whitelistEmail) {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const [_, domain] = input.email.split('@')

        const checkDomain = await ctx.supabaseClient
          .from('email_whitelisting')
          .select()
          .eq('domain', domain as string)
          .maybeSingle()

        if (checkDomain.data === null) {
          await increaseLimiter()
          throw new TRPCError({
            code: 'UNPROCESSABLE_CONTENT',
            message: `${input.email} is not allowed to register`,
          })
        }
      }

      const checkUser = await ctx.supabaseClient.from('emails').select().eq('email', input.email).maybeSingle()

      if (checkUser.data) {
        await increaseLimiter()
        throw new TRPCError({
          code: 'UNPROCESSABLE_CONTENT',
          message: `${input.email} was already registered`,
        })
      }

      const serviceRole = serverSupabaseServiceRole<Database>(ctx.event)

      const findSession = await serviceRole
        .from('register_web3_nonce')
        .select('*')
        .eq('account_id', input.accountId)
        .single()

      if (!findSession.data) {
        await increaseLimiter()
        throw new TRPCError({
          code: 'UNPROCESSABLE_CONTENT',
          message: 'Invalid hashpack session. Please try again.',
        })
      }

      const fetchPubKey = await hederaPubKeyChecker(input.accountId, runtimeConfig.public.hashpackNetwork)

      try {
        const pubKey = await fetchPubKey.json()

        const nonce = findSession.data?.nonce

        const publicKey = PublicKey.fromString(pubKey.key.key)

        const messageBuffer = Buffer.from(prefixMessageToSign(`${nonce}`))
        const signatureBuffer = Buffer.from(input.signature[0]?.signature)

        const isValid = publicKey.verify(messageBuffer, signatureBuffer)

        if (isValid) {
          const { data, error } = await serviceRole.auth.admin.createUser({
            password: input.password,
            email: input.email,
            email_confirm: false,
            user_metadata: {
              display_name: `${input.first_name} ${input.last_name}`,
              company: input.company,
              first_name: input.first_name,
              last_name: input.last_name,
            },
          })

          await serviceRole.auth.resend({
            type: 'signup',
            email: input.email,
            options: {
              emailRedirectTo: `${runtimeConfig.public.appUrl}/auth/login?email=${input.email}&registration=success`,
            },
          })

          if (error) {
            await increaseLimiter()
            throw new TRPCError({
              code: 'INTERNAL_SERVER_ERROR',
              message: error.message,
            })
          }

          await serviceRole
            .from('register_web3_nonce')
            .update({
              claimed: true,
            })
            .eq('account_id', input.accountId)

          await serviceRole
            .from('users')
            .update({
              hashpack_account_id: input.accountId,
            })
            .eq('user_id', data!.user!.id)

          return {
            message: 'Registration success, please check your email inbox',
            data,
          }
        }
      } catch {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: 'Invalid signature.',
        })
      }

      return
    }),
  userNonce: authorizedProcedure.mutation(async ({ ctx }) => {
    const { validateLimiter, increaseLimiter } = await useLimitter('nonce', ctx.event, {
      limit: 10,
    })
    const flagLimit = await validateLimiter()

    if (flagLimit === false) {
      throw new TRPCError({
        code: 'TOO_MANY_REQUESTS',
        message: 'Have to many request, please try again later.',
      })
    }

    const client = serverSupabaseServiceRole<Database>(ctx.event)

    const findUser = await ctx.supabaseClient
      .from('users')
      .select('*')
      .eq('user_id', ctx.user.profile!.userId!)
      .single()

    if (!findUser) {
      increaseLimiter()
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Account not found.',
      })
    }

    const nonce = Math.floor(Math.random() * 1000000)

    await client
      .from('users')
      .update({
        web3_auth: {
          genNonce: nonce,
          lastAuth: new Date().toISOString(),
          lastAuthStatus: 'pending',
        },
      })
      .eq('user_id', ctx.user.profile!.userId!)

    return { nonce }
  }),
  signInWeb3: guestProcedure
    .input(
      z.object({
        signature: z.array(
          z.object({
            signature: z.any(),
            publicKey: z.any(),
          }),
        ),
        accountId: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const { validateLimiter, increaseLimiter } = await useLimitter('signInWeb3', ctx.event, {
        limit: 10,
      })
      const flagLimit = await validateLimiter()

      if (flagLimit === false) {
        throw new TRPCError({
          code: 'TOO_MANY_REQUESTS',
          message: 'Have to many request, please try again later.',
        })
      }

      const client = serverSupabaseServiceRole<Database>(ctx.event)

      const findUser = await client.from('users').select('*').eq('hashpack_account_id', input.accountId).single()

      if (!findUser.data) {
        increaseLimiter()
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'No user account is currently linked with this wallet. Please connect your wallet first.',
        })
      }

      const fetchPubKey = await hederaPubKeyChecker(input.accountId, runtimeConfig.public.hashpackNetwork)
      const web3Session: {
        isValid: boolean
        auth: Record<string, string | number>
      } = {
        isValid: false,
        auth: {},
      }

      try {
        const pubKey = await fetchPubKey.json()

        web3Session.auth = findUser.data?.web3_auth as Record<string, string | number>

        const publicKey = PublicKey.fromString(pubKey.key.key)

        const messageBuffer = Buffer.from(prefixMessageToSign(`${web3Session.auth?.genNonce}`))
        const signatureBuffer = Buffer.from(input.signature[0]?.signature)

        web3Session.isValid = publicKey.verify(messageBuffer, signatureBuffer)
      } catch {
        throw new TRPCError({
          code: 'UNPROCESSABLE_CONTENT',
          message: 'Invalid signature',
        })
      }

      if (web3Session.isValid) {
        const { data: authUser } = await client.auth.admin.getUserById(findUser.data?.user_id ?? '')

        if (!authUser.user?.email_confirmed_at) {
          throw new TRPCError({
            code: 'UNPROCESSABLE_CONTENT',
            message: 'Please confirm your email before signing in.',
          })
        }

        if (authUser.user?.user_metadata.suspended) {
          increaseLimiter()
          throw new TRPCError({
            code: 'UNPROCESSABLE_CONTENT',
            message: 'Your account has been suspended',
          })
        }

        const { data: magicLink } = await client.auth.admin.generateLink({
          type: 'magiclink',
          email: authUser.user?.email ?? '',
        })

        if (magicLink.properties) {
          await client
            .from('users')
            .update({
              web3_auth: {
                ...web3Session.auth,
                lastAuthStatus: 'authenticated',
              },
            })
            .eq('hashpack_account_id', input.accountId)

          const generateSession = await client.auth.verifyOtp({
            type: 'magiclink',
            email: authUser?.user?.email ?? '',
            token: magicLink.properties?.email_otp,
          })

          const authenticatedClient = await serverSupabaseClient<Database>(ctx.event)

          if (generateSession.data.session) {
            await authenticatedClient.auth.setSession({
              access_token: generateSession.data.session.access_token,
              refresh_token: generateSession.data.session.refresh_token,
            })
          }

          return generateSession.data.session
        }
      } else {
        throw new TRPCError({
          code: 'UNPROCESSABLE_CONTENT',
          message: 'Invalid signature',
        })
      }

      return
    }),
  signOut: authorizedProcedure.mutation(async ({ ctx }) => {
    await ctx.supabaseClient.auth.signOut({
      scope: 'local',
    })

    return true
  }),
})
