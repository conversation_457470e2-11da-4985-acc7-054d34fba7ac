import { z } from 'zod'
import { authorizedProcedure } from '~/server/trpc/procedure/authorized'
import { router } from '~/server/trpc/trpc'
import { desc } from 'drizzle-orm'
import { timescaleClient } from '~/server/utils/timescale'
import { decentralisationTree } from '~/server/utils/timescale/schema'

export default router({
  // Get latest decentralisation tree data
  getLatest: authorizedProcedure
    .input(
      z.object({
        limit: z.number().min(1).max(100).default(100),
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        const hasAccess = ctx.user.feature.checkMultipleNonStrict('esg_climate_metrics.decentralisation')

        if (!hasAccess) {
          return {
            data: null,
          }
        }

        const result = await timescaleClient.query.decentralisationTree.findMany({
          orderBy: desc(decentralisationTree.createdAt),
          limit: input.limit,
        })

        return {
          data: result,
        }
      } catch (error) {
        console.error('Error fetching latest decentralisation tree data:', error)
        throw new Error('Failed to fetch latest decentralisation tree data')
      }
    }),
})
