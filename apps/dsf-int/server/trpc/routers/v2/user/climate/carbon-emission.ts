import { eq, inArray, SQL, sql, and, or, isNull, ilike, exists, notExists, asc, isNotNull } from 'drizzle-orm'
import { z } from 'zod'
import { displayWeight, numericDisplay } from '~/helper/number-helper'
import { capitalizeFirstLetter } from '~/helper/string-helper'
import { authorizedProcedure } from '~/server/trpc/procedure/authorized'
import { blankPaginationResponse, router } from '~/server/trpc/trpc'
import { asset, consensus, consensusOnPlatform, platform, tsdbAssetCatalogue } from '~/server/utils/chat-source/schema'
import { tsdbCarbonEmissionFeeds } from '~/server/utils/chat-source/schema/tsdb_carbon_emission_feeds'
import AssetSelection from '~/server/utils/asset_selection'
import { PER_PAGE } from '~/constants/api'
import { assetNameFilter, exactOrder } from '~/server/utils/chat-source/schema/asset'

export default router({
  asset: authorizedProcedure
    .input(
      z.object({
        terms: z.string().optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const assetSelection = await AssetSelection.initialize(ctx)

      let assets: {
        logo: string | null
        symbol: string
        slug: string
        name: string
      }[] = []

      if (assetSelection.availableAsset) {
        assets = await chatSourceClient
          .select({
            assetId: asset.id,
            platformId: platform.id,
            logo: asset.logo,
            symbol: asset.symbol,
            slug: asset.slug,
            name: asset.name,
            platformName: platform.name,
            platformSlug: platform.slug,
            platformLogo: platform.logo,
          })
          .from(asset)
          .fullJoin(tsdbAssetCatalogue, and(eq(tsdbAssetCatalogue.assetId, asset.id)))
          .fullJoin(platform, eq(platform.id, tsdbAssetCatalogue.platformId))
          .where(
            and(
              inArray(asset.id, assetSelection.availableAsset),
              eq(tsdbAssetCatalogue.type, 'carbon_emission_feeds'),
              input.terms ? assetNameFilter(input.terms, { includePlatform: true }) : undefined,
            ),
          )
          .orderBy(() => {
            const orderQuery: SQL<unknown>[] = []

            if (input.terms) {
              orderQuery.push(...exactOrder(input.terms, { includePlatform: true }))
            } else {
              orderQuery.push(asc(asset.name), asc(platform.name))
            }

            return orderQuery
          })
          .limit(10)
          .then((res) =>
            res.map((entry) => {
              const urlIds = ctx.hashids.encode(entry.assetId ?? 0, entry.platformId ?? 0)

              return {
                logo: entry.logo ?? entry.platformLogo ?? null,
                symbol: entry.symbol ?? entry.platformSlug ?? '-',
                slug: `${entry.slug ?? entry.platformSlug}-${urlIds}`,
                name: entry.name ?? entry.platformName ?? '-',
              }
            }),
          )
      } else {
        assets = await chatSourceClient
          .select({
            assetId: asset.id,
            platformId: platform.id,
            logo: asset.logo,
            symbol: asset.symbol,
            slug: asset.slug,
            name: asset.name,
            platformName: platform.name,
            platformSlug: platform.slug,
            platformLogo: platform.logo,
          })
          .from(tsdbAssetCatalogue)
          .innerJoin(asset, eq(asset.id, tsdbAssetCatalogue.assetId))
          .fullJoin(platform, eq(platform.id, tsdbAssetCatalogue.platformId))
          .where(
            and(
              eq(tsdbAssetCatalogue.type, 'carbon_emission_feeds'),
              input.terms ? assetNameFilter(input.terms, { includePlatform: true }) : undefined,
            ),
          )
          .orderBy(() => {
            const orderQuery: SQL<unknown>[] = []

            if (input.terms) {
              orderQuery.push(...exactOrder(input.terms, { includePlatform: true }))
            } else {
              orderQuery.push(asc(asset.name), asc(platform.name))
            }

            return orderQuery
          })
          .limit(10)
          .then((res) =>
            res.map((entry) => {
              const urlIds = ctx.hashids.encode(entry.assetId ?? 0, entry.platformId ?? 0)

              return {
                logo: entry.logo ?? entry.platformLogo ?? null,
                symbol: entry.symbol ?? entry.platformSlug ?? '-',
                slug: `${entry.slug ?? entry.platformSlug}-${urlIds}`,
                name: entry.name ?? entry.platformName ?? '-',
              }
            }),
          )
      }

      return assets
    }),
  list: authorizedProcedure
    .input(
      z.object({
        layer: z.array(z.enum(['L1', 'L2'])).default(['L1', 'L2']),
        type: z.array(z.enum(['Token', 'Platform'])).default(['Platform', 'Token']),
        algorithm: z.array(z.enum(['PoW', 'PoS', 'PoSt', 'other'])).default(['PoS', 'PoSt', 'PoW', 'other']),
        page: z.number().default(1),
        order: z.enum(['emission', 'emissionPerTx', 'emissionPerTxPerNode', 'validators', 'tps']).default('emission'),
        sort: z.enum(['asc', 'desc']).default('desc'),
        terms: z.string().optional(),
      }),
    )
    .query<PaginationResponse<CarbonEmissionRow>>(async ({ input, ctx }) => {
      const assetSelection = await AssetSelection.initialize(ctx)

      if (
        !ctx.user.feature.checkMultipleNonStrict(
          'esg_climate_metrics.power_use',
          'esg_climate_metrics.energy_cons_tx',
          'esg_climate_metrics.energy_cons_tx_node',
        )
      ) {
        return blankPaginationResponse
      }

      const selectedAlgorithm = input.algorithm.filter((algorithm) => algorithm !== 'other')

      const data = await chatSourceClient
        .select({
          assetName: asset.name,
          logo: asset.logo,
          symbol: asset.symbol,
          asset_slug: asset.slug,
          platformName: platform.name,
          platformSlug: platform.slug,
          platformLogo: platform.logo,
          assetId: asset.id,
          platformId: platform.id,
          consensusMechanism: tsdbCarbonEmissionFeeds.consensus_mechanism,
          assetType: tsdbCarbonEmissionFeeds.assetType,
          layerType: tsdbCarbonEmissionFeeds.type,
          emission: tsdbCarbonEmissionFeeds.emission,
          emissionTx: tsdbCarbonEmissionFeeds.emissionPerTx,
          emissionTxNode: tsdbCarbonEmissionFeeds.emissionPerTxPerNode,
          tps: tsdbCarbonEmissionFeeds.tps,
          validators: tsdbCarbonEmissionFeeds.validators,
          totalCount: sql<number>`cast (COUNT(*) OVER() as int)`.as('totalCount'),
        })
        .from(asset)
        .fullJoin(tsdbCarbonEmissionFeeds, eq(asset.id, tsdbCarbonEmissionFeeds.assetId))
        .fullJoin(platform, eq(platform.id, tsdbCarbonEmissionFeeds.platformId))
        .where(
          and(
            assetSelection.availableAsset === undefined
              ? or(isNotNull(tsdbCarbonEmissionFeeds.assetId), isNotNull(tsdbCarbonEmissionFeeds.platformId))
              : inArray(asset.id, assetSelection.availableAsset),
            input.layer.length === 2 ? undefined : inArray(tsdbCarbonEmissionFeeds.type, input.layer),
            input.type.length === 2 ? undefined : inArray(tsdbCarbonEmissionFeeds.assetType, input.type),
            input.terms
              ? or(ilike(asset.name, `%${input.terms}%`), ilike(asset.symbol, `%${input.terms}%`))
              : undefined,
            or(
              selectedAlgorithm.length > 0
                ? sql`
                CASE
                  WHEN ${tsdbCarbonEmissionFeeds.assetType} = 'Platform' THEN
                  ${exists(
                    chatSourceClient
                      .select()
                      .from(consensusOnPlatform)
                      .innerJoin(consensus, eq(consensus.consensusId, consensusOnPlatform.consensusId))
                      .where(
                        and(
                          inArray(consensus.group, selectedAlgorithm),
                          eq(consensusOnPlatform.platformId, tsdbCarbonEmissionFeeds.platformId),
                        ),
                      ),
                  )}
                END`
                : undefined,
              input.algorithm.includes('other')
                ? sql`
              CASE
                WHEN ${tsdbCarbonEmissionFeeds.assetType} = 'Platform' THEN
                ${notExists(
                  chatSourceClient
                    .select()
                    .from(consensusOnPlatform)
                    .fullJoin(consensus, eq(consensus.consensusId, consensusOnPlatform.consensusId))
                    .where(
                      and(
                        inArray(consensus.group, ['PoW', 'PoS', 'PoSt']),
                        eq(consensusOnPlatform.platformId, tsdbCarbonEmissionFeeds.platformId),
                      ),
                    ),
                )} else true
              END`
                : undefined,
            ),
          ),
        )
        .orderBy(() => {
          const orderQuery: SQL<unknown>[] = []

          switch (input.order) {
            case 'emissionPerTx':
              orderQuery.push(
                sql`CASE
                  WHEN ${tsdbCarbonEmissionFeeds.emissionPerTx} = 'NaN' THEN 1
                  WHEN ${tsdbCarbonEmissionFeeds.emissionPerTx} IS NULL THEN 1
                  WHEN ${tsdbCarbonEmissionFeeds.emissionPerTx} = 0 THEN 1
                  ELSE 0
                END`,
                dynamicSort(tsdbCarbonEmissionFeeds.emissionPerTx, input.sort),
              )
              break

            case 'emissionPerTxPerNode':
              orderQuery.push(
                sql`CASE
                    WHEN ${tsdbCarbonEmissionFeeds.emissionPerTxPerNode} = 'NaN' THEN 1
                    WHEN ${tsdbCarbonEmissionFeeds.emissionPerTxPerNode} IS NULL THEN 1
                    WHEN ${tsdbCarbonEmissionFeeds.emissionPerTxPerNode} = 0 THEN 1
                    ELSE 0
                  END`,
                dynamicSort(tsdbCarbonEmissionFeeds.emissionPerTxPerNode, input.sort),
              )
              break

            case 'validators':
              orderQuery.push(
                sql`CASE
                      WHEN ${tsdbCarbonEmissionFeeds.validators} = 'NaN' THEN 1
                      WHEN ${tsdbCarbonEmissionFeeds.validators} IS NULL THEN 1
                      WHEN ${tsdbCarbonEmissionFeeds.validators} = 0 THEN 1
                      ELSE 0
                    END`,
                dynamicSort(tsdbCarbonEmissionFeeds.validators, input.sort),
              )
              break

            case 'tps':
              orderQuery.push(
                sql`CASE
                        WHEN ${tsdbCarbonEmissionFeeds.tps} = 'NaN' THEN 1
                        WHEN ${tsdbCarbonEmissionFeeds.tps} IS NULL THEN 1
                        WHEN ${tsdbCarbonEmissionFeeds.tps} = 0 THEN 1
                        ELSE 0
                      END`,
                dynamicSort(tsdbCarbonEmissionFeeds.tps, input.sort),
              )
              break

            default:
              orderQuery.push(
                sql`CASE
                  WHEN ${tsdbCarbonEmissionFeeds.emission} = 'NaN' THEN 1
                  WHEN ${tsdbCarbonEmissionFeeds.emission} IS NULL THEN 1
                  WHEN ${tsdbCarbonEmissionFeeds.emission} = 0 THEN 1
                  ELSE 0
                END`,
                dynamicSort(tsdbCarbonEmissionFeeds.emission, input.sort),
              )
              break
          }

          return orderQuery
        })
        .limit(PER_PAGE)
        .offset(input.page ? (input.page - 1) * PER_PAGE : 0)

      return {
        data: data.map((d) => {
          const urlIds = ctx.hashids.encode(d.assetId ?? 0, d.platformId ?? 0)

          return {
            name: d.assetName ?? d.platformName,
            logo: d.logo ?? d.platformLogo,
            slug: `${d.asset_slug ?? d.platformSlug}-${urlIds}`,
            asset_type: d.assetType ? capitalizeFirstLetter(d.assetType) : '-',
            layer_type: d.layerType ? capitalizeFirstLetter(d.layerType) : '-',
            consensus_mechanism: d.consensusMechanism?.split('&').map((d) => d.replace(' ', '')) ?? [],
            co_emission: d.emission ? displayWeight(d.emission) : '-',
            co_emission_tx: d.emissionTx ? displayWeight(d.emissionTx) : '-',
            co_emission_tx_node: d.emissionTxNode?.toString() ? displayWeight(d.emissionTxNode) : '-',
            tps: d.tps ? numericDisplay(d.tps) + ' tps' : '-',
            validators: d.validators ? numericDisplay(d.validators, 0) : '-',
          }
        }) as CarbonEmissionRow[],
        page: input.page,
        perPage: PER_PAGE,
        totalItem: data[0]?.totalCount ?? 0,
        totalPage: Math.ceil((data[0]?.totalCount ?? 0) / PER_PAGE),
      }
    }),
  chart: authorizedProcedure
    .input(
      z.object({
        slug: z.string(),
        metric: z.enum(['emission', 'pertx', 'pertxnode', 'source']).default('emission'),
      }),
    )
    .query(async ({ input, ctx }) => {
      const assetSelection = await AssetSelection.initialize(ctx)
      const haveMetrics = ctx.user.feature.checkMultipleNonStrict(
        'esg_climate_metrics.power_use',
        'esg_climate_metrics.energy_cons_tx',
        'esg_climate_metrics.energy_cons_tx_node',
      )

      let annualized = false
      let metric = 'carbon_emission'
      const separateSlug = input.slug.split('-')
      const [assetId, platformId] = ctx.hashids.decode(separateSlug?.[separateSlug.length - 1] || '')

      switch (input.metric) {
        case 'pertx':
          annualized = true
          metric = 'carbon_emission_per_transaction'
          break

        case 'pertxnode':
          annualized = true
          metric = 'carbon_emission_per_transaction_per_node'
          break

        case 'source':
          metric = 'energy_share'
          break

        default:
          annualized = true
          metric = 'carbon_emission'
          break
      }

      const [feeds, entries, energyCons, consensus] = await Promise.all([
        chatSourceClient
          .select()
          .from(tsdbCarbonEmissionFeeds)
          .fullJoin(asset, eq(asset.id, tsdbCarbonEmissionFeeds.assetId))
          .fullJoin(platform, eq(platform.id, tsdbCarbonEmissionFeeds.platformId))
          .where(
            and(
              typeof assetId === 'number' && assetId > 0 ? eq(tsdbCarbonEmissionFeeds.assetId, assetId) : undefined,
              typeof platformId === 'number' && platformId > 0
                ? eq(tsdbCarbonEmissionFeeds.platformId, platformId)
                : undefined,
            ),
          )
          .then((res) => res[0])
          .then((res) => {
            if (res?.mvw_esg_co2_metrics?.assetType?.toLowerCase() === 'token') {
              return {
                id: res?.asset?.id,
                name: res?.asset?.name,
                slug: res?.asset?.slug,
                symbol: res?.asset?.symbol,
                logo: res?.asset?.logo,
                type: 'Token',
                layer: res?.mvw_esg_co2_metrics?.type,
              }
            }

            if (res?.mvw_esg_co2_metrics?.assetType?.toLowerCase() === 'platform') {
              return {
                id: res?.platforms?.id,
                name: res?.platforms?.name,
                slug: res?.platforms?.slug,
                symbol: res?.platforms?.symbol,
                logo: res?.platforms?.logo,
                type: 'Platform',
                layer: res?.mvw_esg_co2_metrics?.type,
              }
            }

            return null
          }),
        haveMetrics
          ? timescaleClient.query.co2DailyMetrics
              .findMany({
                where: (fields, { and, eq }) =>
                  and(
                    assetSelection.availableAsset ? inArray(fields.assetId, assetSelection.availableAsset) : undefined,
                    typeof assetId === 'number' && assetId > 0 ? eq(fields.assetId, assetId) : isNull(fields.assetId),
                    typeof platformId === 'number' && platformId > 0
                      ? eq(fields.platformId, platformId)
                      : isNull(fields.platformId),
                    eq(fields.metric, metric),
                  ),
                orderBy: (fields, { asc }) => asc(fields.date),
              })
              .then((data) =>
                data.reduce<CarbonEmissionChartEntries>(
                  (res: CarbonEmissionChartEntries, val) => {
                    if (res.mode === 'annualized') {
                      if (
                        val.bestGuessValue &&
                        val.lowerBoundValue &&
                        val.upperBoundValue &&
                        val.tps &&
                        val.validators
                      ) {
                        res.entries.push([
                          new Date(val.date ?? '').getTime(),
                          {
                            avg: val.bestGuessValue,
                            lower: val.lowerBoundValue,
                            top: val.upperBoundValue,
                            tps: val.tps,
                            validators: val.validators,
                          },
                        ])
                      }
                    }

                    if (res.mode === 'source') {
                      let groupIndex = res.entries.findIndex((d) => d.type === (val.energyType ?? '-'))

                      if (groupIndex < 0) {
                        res.entries.push({
                          timeseries: [],
                          type: val.energyType ?? '-',
                        })
                        groupIndex = res.entries.length - 1
                      }

                      if (val.bestGuessValue) {
                        res.entries[groupIndex]?.timeseries?.push([
                          new Date(val.date ?? '').getTime(),
                          val.bestGuessValue,
                        ])
                      }
                    }

                    return res
                  },
                  {
                    mode: annualized ? 'annualized' : 'source',
                    entries: [],
                  } as CarbonEmissionChartEntries,
                ),
              )
          : undefined,
        timescaleClient.query.energyConsFeeds.findFirst({
          where: (fields, { and, eq }) =>
            and(eq(fields.assetId, assetId as number), eq(fields.platformId, platformId as number)),
        }),
        platformId !== 0
          ? chatSourceClient.query.consensusOnPlatform.findMany({
              where: (fields, { eq }) => eq(fields.platformId, platformId as number),
              with: {
                consensus: {
                  columns: {
                    group: true,
                  },
                },
              },
            })
          : undefined,
      ])

      return {
        feeds,
        metric: input.metric,
        entries: entries ?? [],
        group: consensus?.map((d) => d.consensus.group),
        available_climate: {
          carbon_emission: true,
          energy_consumption: energyCons !== undefined,
        },
      }
    }),
})
