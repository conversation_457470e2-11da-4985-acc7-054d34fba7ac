import { eq, inArray, or, and, SQL, sql, ilike, asc, isNotNull, exists, notExists } from 'drizzle-orm'
import { z } from 'zod'
import { displayWattage, numericDisplay } from '~/helper/number-helper'
import { capitalizeFirstLetter, titleCase } from '~/helper/string-helper'
import { authorizedProcedure } from '~/server/trpc/procedure/authorized'
import { blankPaginationResponse, router } from '~/server/trpc/trpc'
import { asset, consensus, consensusOnPlatform, platform, tsdbAssetCatalogue } from '~/server/utils/chat-source/schema'
import { tsdbEnergyConsFeeds } from '~/server/utils/chat-source/schema/tsdb_energy_cons_feeds'
import { analyticsEnergyCons } from '~/server/utils/timescale/schema'
import { PgColumn } from 'drizzle-orm/pg-core'
import { PER_PAGE } from '~/constants/api'
import AssetSelection from '~/server/utils/asset_selection'
import { assetNameFilter, exactOrder } from '~/server/utils/chat-source/schema/asset'
import type { SQLWrapper } from 'drizzle-orm'

export default router({
  asset: authorizedProcedure
    .input(
      z.object({
        terms: z.string().optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const assetSelection = await AssetSelection.initialize(ctx)

      let assets: {
        logo: string | null
        symbol: string
        slug: string
        name: string
      }[] = []

      if (assetSelection.availableAsset) {
        assets = await chatSourceClient
          .select({
            assetId: asset.id,
            platformId: platform.id,
            logo: asset.logo,
            symbol: asset.symbol,
            slug: asset.slug,
            name: asset.name,
            platformName: platform.name,
            platformSlug: platform.slug,
            platformLogo: platform.logo,
          })
          .from(asset)
          .fullJoin(
            tsdbAssetCatalogue,
            and(eq(tsdbAssetCatalogue.assetId, asset.id), eq(tsdbAssetCatalogue.type, 'energy_cons_feeds')),
          )
          .fullJoin(platform, eq(platform.id, tsdbAssetCatalogue.platformId))
          .where(
            and(
              inArray(asset.id, assetSelection.availableAsset),
              input.terms ? assetNameFilter(input.terms, { includePlatform: true }) : undefined,
            ),
          )
          .orderBy(() => {
            const orderQuery: SQL<unknown>[] = []

            if (input.terms) {
              orderQuery.push(...exactOrder(input.terms, { includePlatform: true }))
            } else {
              orderQuery.push(asc(asset.name), asc(platform.name))
            }

            return orderQuery
          })
          .limit(10)
          .then((res) =>
            res.map((entry) => {
              const urlIds = ctx.hashids.encode(entry.assetId ?? 0, entry.platformId ?? 0)

              return {
                logo: entry.logo ?? entry.platformLogo ?? null,
                symbol: entry.symbol ?? entry.platformSlug ?? '-',
                slug: `${entry.slug ?? entry.platformSlug}-${urlIds}`,
                name: entry.name ?? entry.platformName ?? '-',
              }
            }),
          )
      } else {
        assets = await chatSourceClient
          .select({
            assetId: asset.id,
            platformId: platform.id,
            logo: asset.logo,
            symbol: asset.symbol,
            slug: asset.slug,
            name: asset.name,
            platformName: platform.name,
            platformSlug: platform.slug,
            platformLogo: platform.logo,
          })
          .from(tsdbAssetCatalogue)
          .innerJoin(asset, eq(asset.id, tsdbAssetCatalogue.assetId))
          .fullJoin(platform, eq(platform.id, tsdbAssetCatalogue.platformId))
          .where(
            and(
              eq(tsdbAssetCatalogue.type, 'energy_cons_feeds'),
              input.terms ? assetNameFilter(input.terms, { includePlatform: true }) : undefined,
            ),
          )
          .orderBy(() => {
            const orderQuery: SQL<unknown>[] = []

            if (input.terms) {
              orderQuery.push(...exactOrder(input.terms, { includePlatform: true }))
            } else {
              orderQuery.push(asc(asset.name), asc(platform.name))
            }

            return orderQuery
          })
          .limit(10)
          .then((res) =>
            res.map((entry) => {
              const urlIds = ctx.hashids.encode(entry.assetId ?? 0, entry.platformId ?? 0)

              return {
                logo: entry.logo ?? entry.platformLogo ?? null,
                symbol: entry.symbol ?? entry.platformSlug ?? '-',
                slug: `${entry.slug ?? entry.platformSlug}-${urlIds}`,
                name: entry.name ?? entry.platformName ?? '-',
              }
            }),
          )
      }

      return assets
    }),
  list: authorizedProcedure
    .input(
      z.object({
        layer: z.array(z.enum(['L1', 'L2'])).default(['L1', 'L2']),
        type: z.array(z.enum(['Token', 'Platform'])).default(['Token', 'Platform']),
        algorithm: z.array(z.enum(['PoW', 'PoS', 'PoSt', 'other'])).default(['PoS', 'PoSt', 'PoW', 'other']),
        page: z.number().default(1),
        order: z
          .enum(['powerUse', 'energyConsPerTx', 'energyConsPerTxPerNode', 'validators', 'tps'])
          .default('powerUse'),
        sort: z.enum(['asc', 'desc']).default('desc'),
        terms: z.string().optional(),
      }),
    )
    .query<PaginationResponse<EnergyConsumptionRow>>(async ({ input, ctx }) => {
      const assetSelection = await AssetSelection.initialize(ctx)

      if (
        !ctx.user.feature.checkMultipleNonStrict(
          'esg_climate_metrics.waste_footprint',
          'esg_climate_metrics.energy_cons_tx',
          'esg_climate_metrics.energy_cons_tx_node',
        )
      ) {
        return blankPaginationResponse
      }

      const selectedAlgorithm = input.algorithm.filter((algorithm) => algorithm !== 'other')

      const whereQuery: (SQL<unknown> | undefined)[] = [
        assetSelection.availableAsset
          ? inArray(asset.id, assetSelection.availableAsset)
          : or(isNotNull(tsdbEnergyConsFeeds.assetId), isNotNull(tsdbEnergyConsFeeds.platformId)),
        input.layer.length === 2 ? undefined : inArray(tsdbEnergyConsFeeds.layer, input.layer),
        input.type.length === 2
          ? undefined
          : inArray(
              tsdbEnergyConsFeeds.type,
              input.type.map((d) => d.toUpperCase()),
            ),
        input.terms ? or(ilike(asset.name, `%${input.terms}%`), ilike(asset.symbol, `%${input.terms}%`)) : undefined,

        or(
          selectedAlgorithm.length > 0
            ? sql`
              CASE
                WHEN ${tsdbEnergyConsFeeds.type} = 'PLATFORM' THEN
                ${exists(
                  chatSourceClient
                    .select()
                    .from(consensusOnPlatform)
                    .innerJoin(consensus, eq(consensus.consensusId, consensusOnPlatform.consensusId))
                    .where(
                      and(
                        inArray(consensus.group, selectedAlgorithm),
                        eq(consensusOnPlatform.platformId, tsdbEnergyConsFeeds.platformId),
                      ),
                    ),
                )}
              END`
            : undefined,
          input.algorithm.includes('other')
            ? sql`
              CASE
                WHEN ${tsdbEnergyConsFeeds.type} = 'PLATFORM' THEN
                ${notExists(
                  chatSourceClient
                    .select()
                    .from(consensusOnPlatform)
                    .fullJoin(consensus, eq(consensus.consensusId, consensusOnPlatform.consensusId))
                    .where(
                      and(
                        inArray(consensus.group, ['PoW', 'PoS', 'PoSt']),
                        eq(consensusOnPlatform.platformId, tsdbEnergyConsFeeds.platformId),
                      ),
                    ),
                )} else true
              END`
            : undefined,
        ),
      ]

      const orderQuery = () => {
        const orderQuery: SQL<unknown>[] = []

        switch (input.order) {
          case 'energyConsPerTx':
            orderQuery.push(
              sql`CASE
              WHEN ${tsdbEnergyConsFeeds.energyConsumptionTx} = 'NaN' THEN 1
              WHEN ${tsdbEnergyConsFeeds.energyConsumptionTx} IS NULL THEN 1
              WHEN ${tsdbEnergyConsFeeds.energyConsumptionTx} = 0 THEN 1
              ELSE 0
            END`,
              dynamicSort(tsdbEnergyConsFeeds.energyConsumptionTx, input.sort),
            )
            break

          case 'energyConsPerTxPerNode':
            orderQuery.push(
              sql`CASE
                WHEN ${tsdbEnergyConsFeeds.energyConsumptionTxNode} = 'NaN' THEN 1
                WHEN ${tsdbEnergyConsFeeds.energyConsumptionTxNode} IS NULL THEN 1
                WHEN ${tsdbEnergyConsFeeds.energyConsumptionTxNode} = 0 THEN 1
                ELSE 0
              END`,
              dynamicSort(tsdbEnergyConsFeeds.energyConsumptionTxNode, input.sort),
            )
            break

          case 'tps':
            orderQuery.push(
              sql`CASE
                  WHEN ${tsdbEnergyConsFeeds.throughputTps} = 'NaN' THEN 1
                  WHEN ${tsdbEnergyConsFeeds.throughputTps} IS NULL THEN 1
                  WHEN ${tsdbEnergyConsFeeds.throughputTps} = 0 THEN 1
                  ELSE 0
                END`,
              dynamicSort(tsdbEnergyConsFeeds.throughputTps, input.sort),
            )
            break

          case 'validators':
            orderQuery.push(
              sql`CASE
                    WHEN ${tsdbEnergyConsFeeds.validators} = 'NaN' THEN 1
                    WHEN ${tsdbEnergyConsFeeds.validators} IS NULL THEN 1
                    WHEN ${tsdbEnergyConsFeeds.validators} = 0 THEN 1
                    ELSE 0
                  END`,
              dynamicSort(tsdbEnergyConsFeeds.validators, input.sort),
            )
            break

          default:
            orderQuery.push(
              sql`CASE
              WHEN ${tsdbEnergyConsFeeds.powerUse} = 'NaN' THEN 1
              WHEN ${tsdbEnergyConsFeeds.powerUse} IS NULL THEN 1
              WHEN ${tsdbEnergyConsFeeds.powerUse} = 0 THEN 1
              ELSE 0
            END`,
              dynamicSort(tsdbEnergyConsFeeds.powerUse, input.sort),
            )
            break
        }

        return orderQuery
      }

      const selectQuery = chatSourceClient.select({
        assetName: asset.name,
        assetSymbol: asset.symbol,
        assetSlug: asset.slug,
        assetLogo: asset.logo,
        platformName: platform.name,
        platformSymbol: platform.symbol,
        platformSlug: platform.slug,
        platformLogo: platform.logo,
        layer: tsdbEnergyConsFeeds.layer,
        consensusMechanism: tsdbEnergyConsFeeds.consensusMechanism,
        type: tsdbEnergyConsFeeds.type,
        powerUse: tsdbEnergyConsFeeds.powerUse,
        energyConsPerTx: tsdbEnergyConsFeeds.energyConsumptionTx,
        energyConsPerTxPerNode: tsdbEnergyConsFeeds.energyConsumptionTxNode,
        validators: tsdbEnergyConsFeeds.validators,
        tps: tsdbEnergyConsFeeds.throughputTps,
        assetId: tsdbEnergyConsFeeds.assetId,
        platformId: tsdbEnergyConsFeeds.platformId,
        totalCount: sql<number>`cast (COUNT(*) OVER() as int)`.as('totalCount'),
      })

      let data: EnergyConsumptionSqlRow[] = []

      if (assetSelection.availableAsset) {
        data = await selectQuery
          .from(asset)
          .fullJoin(tsdbEnergyConsFeeds, eq(tsdbEnergyConsFeeds.assetId, asset.id))
          .fullJoin(platform, eq(tsdbEnergyConsFeeds.platformId, platform.id))
          .where(and(...whereQuery))
          .orderBy(orderQuery)
          .limit(PER_PAGE)
          .offset((input.page - 1) * PER_PAGE)
      } else {
        data = await selectQuery
          .from(tsdbEnergyConsFeeds)
          .fullJoin(asset, eq(tsdbEnergyConsFeeds.assetId, asset.id))
          .fullJoin(platform, eq(tsdbEnergyConsFeeds.platformId, platform.id))
          .where(and(...whereQuery))
          .orderBy(orderQuery)
          .limit(PER_PAGE)
          .offset((input.page - 1) * PER_PAGE)
      }

      return {
        page: input.page,
        perPage: PER_PAGE,
        totalItem: data[0]?.totalCount ?? 0,
        totalPage: Math.ceil((data[0]?.totalCount ?? 0) / PER_PAGE),
        data: data.map((d) => {
          const hashIds = ctx.hashids.encode(d.assetId ?? 0, d.platformId ?? 0)
          const slug = `${d.assetSlug ?? d.platformSlug}-${hashIds}`

          return {
            name: d.assetName ?? d.platformName,
            asset_type: d.type ? capitalizeFirstLetter(d.type) : '-',
            layer_type: d.layer ? capitalizeFirstLetter(d.layer) : '-',
            logo: d.assetLogo ?? d.platformLogo,
            slug,
            consensus_mechanism: d.consensusMechanism?.split('&').map((d) => d.replace(' ', '')) ?? [],
            energy_cons: d.powerUse ? displayWattage(d.powerUse) : '-',
            energy_cons_tx: d.energyConsPerTx ? displayWattage(d.energyConsPerTx) + 'h' : '-',
            energy_cons_tx_node: d.energyConsPerTxPerNode ? displayWattage(d.energyConsPerTxPerNode) + 'h' : '-',
            validators: d.validators ? numericDisplay(d.validators, 0) : '-',
            tps: d.tps?.toString() ? numericDisplay(d.tps) + ' tps' : '-',
          }
        }) as EnergyConsumptionRow[],
      }
    }),
  chart: authorizedProcedure
    .input(
      z.object({
        slug: z.string(),
        metric: z.enum(['powerUse', 'powerUsePerTx', 'powerUsePerTxNode']).default('powerUse'),
      }),
    )
    .query(async ({ input, ctx }) => {
      const assetSelection = await AssetSelection.initialize(ctx)
      const haveMetrics = ctx.user.feature.checkMultipleNonStrict(
        'esg_climate_metrics.power_use',
        'esg_climate_metrics.energy_cons_tx',
        'esg_climate_metrics.energy_cons_tx_node',
      )

      const separatedSlug = input.slug.split('-')
      const encodedSlug = separatedSlug[separatedSlug.length - 1]
      const [assetId, platformId] = ctx.hashids.decode(encodedSlug as string)

      const [topColumn, avgColumn, bottomColumn] = ((metric: typeof input.metric): PgColumn[] => {
        switch (metric) {
          case 'powerUsePerTxNode':
            return [
              analyticsEnergyCons.energyConsTxNodeUpperBoundWh,
              analyticsEnergyCons.energyConsTxNodeBestGuessWh,
              analyticsEnergyCons.energyConsTxNodeLowerBoundWh,
            ]

          case 'powerUsePerTx':
            return [
              analyticsEnergyCons.energyConsTxUpperBoundWh,
              analyticsEnergyCons.energyConsTxBestGuessWh,
              analyticsEnergyCons.energyConsTxLowerBoundWh,
            ]

          default:
            return [
              analyticsEnergyCons.powerUseUpperBoundW,
              analyticsEnergyCons.powerUseBestGuessW,
              analyticsEnergyCons.powerUseLowerBoundW,
            ]
        }
      })(input.metric)

      const [daily, analytics, asset, platform] = await Promise.all([
        haveMetrics
          ? timescaleClient.query.dailyEnergyCons.findMany({
              where: (fields, { and, eq, inArray }) =>
                and(
                  eq(fields.assetId, assetId as number),
                  eq(fields.platformId, platformId as number),
                  assetSelection.availableAsset ? inArray(fields.assetId, assetSelection.availableAsset) : undefined,
                ),
              orderBy: (fields, { asc }) => asc(fields.date),
            })
          : undefined,
        haveMetrics
          ? timescaleClient
              .select({
                date: analyticsEnergyCons.date,
                upper: sql<number | null>`${topColumn}`,
                avg: sql<number | null>`${avgColumn}`,
                lower: sql<number | null>`${bottomColumn}`,
              })
              .from(analyticsEnergyCons)
              .where(
                and(
                  assetSelection.availableAsset
                    ? inArray(analyticsEnergyCons.assetId, assetSelection.availableAsset)
                    : undefined,
                  eq(analyticsEnergyCons.assetId, assetId as number),
                  eq(analyticsEnergyCons.platformId, platformId as number),
                  isNotNull(topColumn as SQLWrapper),
                  isNotNull(avgColumn as SQLWrapper),
                  isNotNull(bottomColumn as SQLWrapper),
                ),
              )
              .orderBy(asc(analyticsEnergyCons.date))
          : undefined,
        chatSourceClient.query.asset.findFirst({
          where: (fields, { eq }) => eq(fields.id, assetId as number),
        }),
        chatSourceClient.query.platform.findFirst({
          where: (fields, { eq }) => eq(fields.id, platformId as number),
        }),
      ])

      const [feeds, carbonEmission, consensus] = await Promise.all([
        timescaleClient.query.energyConsFeeds
          .findFirst({
            where: (fields, { and, eq }) =>
              and(eq(fields.assetId, assetId as number), eq(fields.platformId, platformId as number)),
          })
          .then((res) => {
            if (res?.type === 'PLATFORM') {
              return {
                name: platform?.name ?? asset?.name,
                slug: `${platform?.slug ?? asset?.slug}-${encodedSlug}`,
                symbol: platform?.symbol ?? asset?.symbol,
                logo: platform?.logo ?? asset?.logo,
                type: titleCase(platform?.type ?? asset?.type ?? ''),
                layer: res.layer,
              }
            }

            if (res?.type === 'TOKEN') {
              return {
                name: asset?.name ?? platform?.name,
                slug: `${asset?.slug ?? platform?.slug}-${encodedSlug}`,
                symbol: asset?.symbol ?? platform?.symbol,
                logo: asset?.logo ?? platform?.logo,
                type: titleCase(asset?.type ?? platform?.type ?? ''),
                layer: res.layer,
              }
            }

            return null
          }),
        timescaleClient.query.carbonEmissionFeeds.findFirst({
          columns: {
            assetId: true,
            platformId: true,
          },
          where: (fields, { and, eq }) =>
            and(eq(fields.assetId, assetId as number), eq(fields.platformId, platformId as number)),
        }),
        platformId !== 0
          ? chatSourceClient.query.consensusOnPlatform.findMany({
              where: (fields, { and, eq }) => and(eq(fields.platformId, platformId as number)),
              with: {
                consensus: true,
              },
            })
          : undefined,
      ])

      return {
        feeds,
        available_metric: {
          energy_consumption: true,
          carbon_emission: carbonEmission !== undefined,
        },
        group: consensus?.map((d) => d.consensus.group) ?? [],
        metric_timeseries:
          analytics?.map((data) => [
            new Date(data.date ?? '').getTime(),
            {
              upper: data.upper ?? 0,
              avg: data.avg ?? 0,
              lower: data.lower ?? 0,
            },
          ]) ?? [],
        daily_timeseries:
          daily?.map((data) => [
            new Date(data.date ?? '').getTime(),
            {
              tps: data.throughputTps ?? 0,
              validator: data.validators ?? 0,
            },
          ]) ?? [],
      }
    }),
})
