import { z } from 'zod'
import { authorizedProcedure } from '~/server/trpc/procedure/authorized'
import { router } from '~/server/trpc/trpc'
import { serverSupabaseServiceRole } from '#supabase/server'
import { TRPCError } from '@trpc/server'
import { PublicKey } from '@hashgraph/sdk'
import { prefixMessageToSign } from '~/utils/data-transform'
import { hederaPubKeyChecker } from '~/utils/hedera-pubkey'

// Types
// eslint-disable-next-line @nx/enforce-module-boundaries
import type { Database } from 'app-types/supabase'
import { asset } from '~/server/utils/chat-source/schema'
import { eq, ilike, or, SQL, sql, and, inArray } from 'drizzle-orm'
import { StripeSession, UserSubscriptions } from '~/server/utils/platform-db/schema'
import Stripe from 'stripe'
import { tsdbAssetCatalogue } from '~/server/utils/chat-source/schema/tsdb_asset_catalogue'

const runtimeConfig = useRuntimeConfig()

export default router({
  checkProfile: authorizedProcedure.query(async ({ ctx }) => {
    const [profile, subscription] = await Promise.all([
      ctx.supabaseClient.from('users').select('*').eq('user_id', ctx.user.profile!.userId!).single(),
      ctx.supabaseClient
        .from('user_subscriptions')
        .select('*')
        .eq('user_id', ctx.user.profile!.userId!)
        .eq('status', 'active')
        .single(),
    ])

    return {
      user: {
        ...profile.data,
        display_name: `${profile.data?.first_name ?? ''} ${profile.data?.last_name ?? ''}`,
        email: ctx.user?.session?.user.email,
        phone: ctx.user?.session?.user.phone,
      },
      subscription: subscription.data,
    }
  }),
  onboardingStatus: authorizedProcedure.input(z.string()).query(async ({ input, ctx }) => {
    const { data: user } = await ctx.supabaseClient
      .from('users')
      .select('*')
      .eq('user_id', ctx.user.profile!.userId!)
      .single()

    if (user === null) {
      throw new TRPCError({
        code: 'UNAUTHORIZED',
        message: 'UNAUTHORIZED_ACCESS',
      })
    }

    if (user?.raw_onboarding) {
      const rawOnboarding = user?.raw_onboarding as Record<string, boolean>
      if (rawOnboarding[input]) {
        return true
      }
      return false
    }
    return false
  }),

  setOnboardingStatus: authorizedProcedure
    .input(
      z.object({
        path: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const { data: user } = await ctx.supabaseClient
        .from('users')
        .select('*')
        .eq('user_id', ctx.user.profile!.userId!)
        .single()

      if (user === null) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: 'UNAUTHORIZED_ACCESS',
        })
      }

      const currentOnboarding = user.raw_onboarding as Record<string, boolean>

      if (currentOnboarding[input.path] === true) {
        return {
          [input.path]: true,
          message: 'Already assigned !',
        }
      }

      currentOnboarding[input.path] = true
      await ctx.supabaseClient
        .from('users')
        .update({
          raw_onboarding: {
            ...currentOnboarding,
          },
        })
        .eq('user_id', ctx.user.profile!.userId!)

      return {
        [input.path]: true,
      }
    }),
  changePassword: authorizedProcedure
    .input(
      z.object({
        current_password: z.string(),
        password: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const checkPassword = await ctx.supabaseClient.rpc('authenticate_user', {
        p_user_id: ctx.user.profile!.userId!,
        p_password: input.current_password,
      })

      if (checkPassword.data === null) {
        throw new TRPCError({
          code: 'UNPROCESSABLE_CONTENT',
          message: 'Your current password is wrong !',
        })
      }

      const response = await ctx.supabaseClient.auth.updateUser({
        password: input.password,
      })

      if (response.error) {
        throw new TRPCError({
          code: 'UNPROCESSABLE_CONTENT',
          cause: response.error.cause,
          message: response.error.message,
        })
      }

      return true
    }),
  linkWallet: authorizedProcedure
    .input(
      z.object({
        signature: z.array(
          z.object({
            signature: z.any(),
            publicKey: z.any(),
          }),
        ),
        accountId: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { validateLimiter, increaseLimiter } = await useLimitter('signInWeb3', ctx.event, {
        limit: 10,
      })
      const flagLimit = await validateLimiter()

      if (flagLimit === false) {
        throw new TRPCError({
          code: 'TOO_MANY_REQUESTS',
          message: 'Have to many request, please try again later.',
        })
      }

      const findUser = await ctx.supabaseClient
        .from('users')
        .select('*')
        .eq('user_id', ctx.user.profile!.userId!)
        .single()

      if (!findUser) {
        increaseLimiter()
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'No user account is found.',
        })
      }

      const fetchPubKey = await hederaPubKeyChecker(input.accountId, runtimeConfig.public.hashpackNetwork)

      try {
        const pubKey = await fetchPubKey.json()

        const web3auth = findUser.data?.web3_auth as Record<string, string | number>

        const publicKey = PublicKey.fromString(pubKey.key.key)

        const messageBuffer = Buffer.from(prefixMessageToSign(`${web3auth?.genNonce}`))
        const signatureBuffer = Buffer.from(input.signature[0]?.signature)

        const isValid = publicKey.verify(messageBuffer, signatureBuffer)

        if (isValid) {
          const client = serverSupabaseServiceRole<Database>(ctx.event)

          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          const [_, { data }] = await Promise.all([
            ctx.supabaseClient.auth.updateUser({
              data: {
                hashpack_account_id: input.accountId,
              },
            }),
            client
              .from('users')
              .update({
                hashpack_account_id: input.accountId,
                web3_auth: {
                  ...web3auth,
                  lastAuthStatus: 'authenticated',
                },
              })
              .eq('user_id', ctx.user.profile!.userId!)
              .select()
              .single(),
          ])

          return data
        } else {
          throw new TRPCError({
            code: 'UNAUTHORIZED',
            message: 'Invalid signature',
          })
        }
      } catch (e) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: 'Invalid signature',
        })
      }
    }),
  unlinkWallet: authorizedProcedure
    .input(
      z.object({
        signature: z.array(
          z.object({
            signature: z.any(),
            publicKey: z.any(),
          }),
        ),
        accountId: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { validateLimiter, increaseLimiter } = await useLimitter('signInWeb3', ctx.event, {
        limit: 10,
      })
      const flagLimit = await validateLimiter()

      if (flagLimit === false) {
        throw new TRPCError({
          code: 'TOO_MANY_REQUESTS',
          message: 'Have to many request, please try again later.',
        })
      }

      const client = serverSupabaseServiceRole<Database>(ctx.event)

      const findUser = await client
        .from('users')
        .select('*')
        .eq('hashpack_account_id', input.accountId)
        .eq('user_id', ctx.user.profile!.userId!)
        .single()

      if (!findUser) {
        increaseLimiter()
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'No user account is currently linked with this wallet. Please connect your wallet first.',
        })
      }

      const fetchPubKey = await hederaPubKeyChecker(input.accountId, runtimeConfig.public.hashpackNetwork)

      try {
        const pubKey = await fetchPubKey.json()

        const web3auth = findUser.data?.web3_auth as Record<string, string | number>

        const publicKey = PublicKey.fromString(pubKey.key.key)

        const messageBuffer = Buffer.from(prefixMessageToSign(`${web3auth?.genNonce}`))
        const signatureBuffer = Buffer.from(input.signature[0]?.signature)

        const isValid = publicKey.verify(messageBuffer, signatureBuffer)

        if (isValid) {
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          const [_, { data }] = await Promise.all([
            ctx.supabaseClient.auth.updateUser({
              data: {
                hashpack_account_id: null,
                web3auth: null,
              },
            }),
            client
              .from('users')
              .update({
                hashpack_account_id: null,
                web3_auth: null,
              })
              .eq('user_id', ctx.user.profile!.userId!)
              .select()
              .single(),
          ])

          return data
        } else {
          throw new TRPCError({
            code: 'UNAUTHORIZED',
            message: 'Invalid signature',
          })
        }
      } catch (e) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: 'Invalid signature',
        })
      }
    }),

  assetPreferences: authorizedProcedure.query(async ({ ctx }) => {
    const assetPreferences = await platformClient.query.userAssetPreferences.findMany({
      where: (fields, { eq }) => {
        if (!ctx.user.profile?.userId) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'User ID is missing.',
          })
        }
        return eq(fields.userId, ctx.user.profile.userId)
      },
    })

    const assets = await getCachedAsset(
      assetPreferences.map((d) => d.assetId),
      'id',
    )

    return assetPreferences.map((d) => ({
      name: assets[d.assetId].name,
      logo: assets[d.assetId].logo,
      slug: assets[d.assetId].slug,
      symbol: assets[d.assetId].symbol,
    }))
  }),

  availableAssets: authorizedProcedure
    .input(
      z.object({
        terms: z.string().optional(),
        product_id: z.number().optional(),
      }),
    )
    .query(async ({ input, ctx }) => {
      const availableMetrics: string[] = []
      const [currentSubscription, preferences] = await Promise.all([
        platformClient.query.UserSubscriptions.findFirst({
          where: (fields, { and, eq }) =>
            and(eq(fields.userId, ctx.user.profile!.userId!), eq(fields.status, 'active')),
          orderBy: (fields, { desc }) => desc(fields.activeAt),
        }),
        platformClient.query.userAssetPreferences.findMany({
          where: (fields, { eq }) => eq(fields.userId, ctx.user.profile!.userId!),
        }),
      ])

      const product = await platformClient.query.Pricing.findFirst({
        where: (fields, { eq }) =>
          eq(fields.id, Number(input.product_id ?? currentSubscription?.rawData?.product_id ?? -1)),
      })

      if (!product) {
        return []
      }

      for (const key in product.features) {
        if (typeof product.features[key] === 'object') {
          const objFeature = product.features[key]

          let enableCounter = 0
          for (const keyFeature in objFeature) {
            if (typeof objFeature[keyFeature] === 'boolean') {
              if (objFeature[keyFeature] === true) {
                enableCounter += 1
              }
            }
          }

          if (enableCounter > 0) {
            switch (key) {
              case 'financial_metrics':
                availableMetrics.push('financial_cryptocurrency')
                availableMetrics.push('financial_stablecoin')
                break

              case 'sentiment_metrics':
                availableMetrics.push('community')
                break

              case 'esg_climate_metrics':
                availableMetrics.push('carbon_emission_feeds', 'energy_cons_feeds')
                break

              default:
                break
            }
          }
        }
      }

      const assets = await chatSourceClient
        .select({
          id: tsdbAssetCatalogue.assetId,
          name: asset.name,
          symbol: asset.symbol,
          logo: asset.logo,
          slug: asset.slug,
          selected: sql.raw(`
          CASE
            when ${tsdbAssetCatalogue.assetId.name} in (${preferences?.length ? preferences?.map((d) => d.assetId).join(',') : -1}) then true
            else false
          END
          `),
        })
        .from(tsdbAssetCatalogue)
        .innerJoin(asset, eq(asset.id, tsdbAssetCatalogue.assetId))
        .where(
          and(
            input.terms
              ? or(ilike(asset.name, `%${input.terms}%`), ilike(asset.symbol, `%${input.terms}%`))
              : undefined,
            inArray(tsdbAssetCatalogue.type, availableMetrics),
          ),
        )
        .groupBy(tsdbAssetCatalogue.assetId, asset.name, asset.symbol, asset.slug, asset.logo, asset.id)
        .orderBy(() => {
          const orderQuery: SQL<unknown>[] = []

          if (preferences && !input.terms) {
            orderQuery.push(
              sql.raw(`
              CASE
                when ${tsdbAssetCatalogue.assetId.name} IN (${preferences?.length ? preferences?.map((d) => d.assetId).join(',') : -1}) then 0
                else 1
              END
            `),
            )
          }

          if (input.terms) {
            orderQuery.push(sql`
            CASE
                WHEN ${asset.name} ILIKE ${input.terms} THEN 0
                WHEN ${asset.symbol} ILIKE ${input.terms} THEN 0
                ELSE 1
            END`)
          }

          return orderQuery
        })
        .limit(20)

      return assets
    }),
  updateAsset: authorizedProcedure
    .input(
      z.object({
        assets: z.array(z.number()).min(1),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const [subscription, assets] = await Promise.all([
        platformClient.query.UserSubscriptions.findFirst({
          orderBy: (fields, { desc }) => desc(fields.id),
          where: (fields, { eq, and }) => and(eq(fields.userId, ctx.user.profile!.userId), eq(fields.status, 'active')),
        }),
        chatSourceClient.query.asset.findMany({
          columns: {
            id: true,
          },
          where: (fields, { inArray }) => inArray(fields.id, input.assets),
        }),
      ])

      if (!subscription) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Subscription not found.',
        })
      }

      if (assets.length !== input.assets.length) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Some of asset was not found',
        })
      }

      const adonSubscriptionId =
        subscription.rawData?.addon_subscription_id ?? subscription.rawData?.future_addon_subscription_id

      const [product, retrieveAddonSubscription] = await Promise.all([
        platformClient.query.Pricing.findFirst({
          where: (fields, { eq }) => eq(fields.id, Number(subscription.rawData?.product_id ?? -1)),
        }),
        typeof adonSubscriptionId === 'string' ? ctx.stripe.subscriptions.retrieve(adonSubscriptionId) : undefined,
      ])

      if (!product) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Product not found',
        })
      }

      let additionalAsset = 0
      let addonSession:
        | Stripe.Response<Stripe.BillingPortal.Session>
        | Stripe.Response<Stripe.Checkout.Session>
        | undefined

      if (typeof product.features?.max_assets === 'number') {
        additionalAsset = input.assets.length - product.features?.max_assets
      }

      let newAddon = false

      // Handling existing addon subscription
      if (retrieveAddonSubscription && subscription.rawData) {
        const portalConfig = await ctx.stripe.billingPortal.configurations.create({
          features: {
            payment_method_update: {
              enabled: true,
            },
            subscription_cancel: {
              enabled:
                subscription.rawData?.addon_subscription_id !== undefined
                  ? additionalAsset <= 0
                  : retrieveAddonSubscription?.id
                    ? true
                    : false,
              mode: 'at_period_end',
            },
            subscription_update: {
              enabled: true,
              default_allowed_updates: ['price', 'quantity'],
              proration_behavior: 'none',
              schedule_at_period_end: {},
              products: [
                {
                  prices: [product.stripePriceIds.addon_price_id as string],
                  product: product.stripePriceIds.addon_product_id as string,
                },
              ],
            },
          },
        })

        // If additional asset is less than or equal to 0, cancel existing addon
        if (additionalAsset <= 0) {
          addonSession = await ctx.stripe.billingPortal.sessions.create({
            customer: subscription.rawData!.customer_id as string,
            configuration: portalConfig.id,
            flow_data: {
              type: 'subscription_cancel',
              subscription_cancel: {
                subscription:
                  (subscription.rawData.addon_subscription_id as string) ?? (retrieveAddonSubscription.id as string),
              },
              after_completion: {
                type: 'redirect',
                redirect: {
                  return_url: `${runtimeConfig.public.appUrl}/settings/billing`,
                },
              },
            },
          })
        }

        // If additional asset is greater than 0, update existing addon
        if (
          additionalAsset > 0 &&
          retrieveAddonSubscription &&
          retrieveAddonSubscription.items.data[0]?.quantity !== additionalAsset &&
          retrieveAddonSubscription?.status !== 'canceled'
        ) {
          addonSession = await ctx.stripe.billingPortal.sessions.create({
            customer: subscription.rawData.customer_id as string,
            configuration: portalConfig.id,
            flow_data: {
              type: 'subscription_update_confirm',
              subscription_update_confirm: {
                subscription: String(subscription.rawData!.addon_subscription_id ?? retrieveAddonSubscription.id),
                items: [
                  {
                    id: retrieveAddonSubscription.items.data[0]?.id || '',
                    price: product.stripePriceIds.addon_price_id as string,
                    quantity: additionalAsset,
                  },
                ],
              },
              after_completion: {
                type: 'redirect',
                redirect: {
                  return_url: `${runtimeConfig.public.appUrl}/settings/billing`,
                },
              },
            },
          })
        }
      }

      // But if user doesn't have addon subscription and they need addon, just create a new subscription
      if (additionalAsset > 0 && (!retrieveAddonSubscription || retrieveAddonSubscription?.status === 'canceled')) {
        newAddon = true
        addonSession = await ctx.stripe.checkout.sessions.create({
          // @ts-expect-error - ignore this error
          mode: 'subscription',
          client_reference_id: ctx.user.profile!.userId!,
          line_items: [
            {
              quantity: additionalAsset,
              price: product.stripePriceIds.addon_price_id,
            },
          ],
          subscription_data: {
            billing_cycle_anchor:
              (subscription.rawData as { last_event_creation: number })?.last_event_creation + 30 * 24 * 60 * 60,
            proration_behavior: 'none',
          },
          allow_promotion_codes: true,
          customer: subscription.rawData?.customer_id,
          billing_address_collection: 'required',
          success_url: `${runtimeConfig.public.appUrl}/settings/billing`,
          cancel_url: `${runtimeConfig.public.appUrl}/api/subscription/cancel-addon?session_id={CHECKOUT_SESSION_ID}`,
        })

        await platformClient.insert(StripeSession).values([
          ...(addonSession?.id
            ? [
                {
                  userId: ctx.user.profile!.userId!,
                  sessionId: addonSession.id,
                  productRawData: sql`${{
                    mode: 'addon',
                    name: `Additional asset ${additionalAsset}x for ${product.name}`,
                    addon_subscription_price: product.stripePriceIds.addon_price_id,
                    addon_product_id: product.stripePriceIds.addon_product_id,
                    main_checkout_id: subscription.rawData?.session_id,
                    pending_addon: true,
                    ...(subscription.rawData?.future_preferences
                      ? {
                          prev_future_preferences: subscription.rawData?.future_preferences,
                        }
                      : undefined),
                  }}::jsonb`,
                  status: 'pending',
                },
              ]
            : []),
        ])
      }

      await platformClient
        .update(UserSubscriptions)
        .set({
          rawData: sql`${{
            ...(subscription.rawData ? subscription.rawData : {}),
            future_preferences: input.assets,
            future_addon_session_id: newAddon ? addonSession!.id : undefined,
          }}::jsonb`,
        })
        .where(eq(UserSubscriptions.id, subscription.id))

      return {
        message: addonSession
          ? 'You have to complete update your subscription'
          : 'Asset selection will applied in next billing cycle.',
        redirect_uri: addonSession ? addonSession.url : null,
      }
    }),
})
