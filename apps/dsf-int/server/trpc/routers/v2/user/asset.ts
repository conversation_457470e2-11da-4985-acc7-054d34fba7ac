import { z } from 'zod'
import { router } from '~/server/trpc/trpc'
import { and, asc, eq, inArray, SQL, sql } from 'drizzle-orm'
import { asset, tsdbCmcPriceDaily } from '~/server/utils/chat-source/schema'
import { getActiveStablecoin } from '~/server/utils/cache/asset'
import { authorizedProcedure } from '~/server/trpc/procedure/authorized'
import { assetNameFilter, exactOrder } from '~/server/utils/chat-source/schema/asset'
import { PER_PAGE } from '~/constants/api'
import AssetSelection from '~/server/utils/asset_selection'
import { tsdbAssetCatalogue } from '~/server/utils/chat-source/schema/tsdb_asset_catalogue'

export default router({
  financialCheck: authorizedProcedure
    .input(
      z.object({
        slug: z.array(z.string()),
      }),
    )
    .query(async ({ input, ctx }) => {
      const assetSelection = await AssetSelection.initialize(ctx)

      const assets = await chatSourceClient
        .select()
        .from(tsdbAssetCatalogue)
        .innerJoin(
          asset,
          and(
            eq(tsdbAssetCatalogue.assetId, asset.id),
            inArray(tsdbAssetCatalogue.type, ['financial_cryptocurrency', 'financial_stablecoin']),
          ),
        )
        .where(
          and(
            inArray(asset.slug, input.slug),
            assetSelection.availableAsset ? inArray(asset.id, assetSelection.availableAsset) : undefined,
          ),
        )

      return assets.map((d) => ({
        name: d.asset.name,
        logo: d.asset.logo ?? '',
        slug: d.asset.slug,
        symbol: d.asset.symbol,
        type: d.asset.type,
      }))
    }),
  list: authorizedProcedure
    .input(
      z.object({
        terms: z.string().default(''),
        page: z.number().default(1),
        filter: z.enum(['crypto', 'stable']).default('crypto'),
      }),
    )
    .query(async ({ input, ctx }) => {
      const assetSelection = await AssetSelection.initialize(ctx)
      const activeStablecoin = await getActiveStablecoin()

      const assets: {
        name: string
        logo: string
        slug: string
        symbol: string
        type: string
      }[] = []

      if (input.filter === 'crypto') {
        await chatSourceClient
          .select({
            name: asset.name,
            logo: asset.logo,
            slug: asset.slug,
            symbol: asset.symbol,
            type: asset.type,
          })
          .from(tsdbAssetCatalogue)
          .innerJoin(asset, eq(asset.id, tsdbAssetCatalogue.assetId))
          .where(
            and(
              input.terms !== '' ? assetNameFilter(input.terms) : undefined,
              assetSelection.availableAsset
                ? inArray(tsdbAssetCatalogue.assetId, assetSelection.availableAsset)
                : undefined,
              eq(tsdbAssetCatalogue.type, 'community'),
            ),
          )
          .orderBy(() => {
            const sqlOrder: SQL<unknown>[] = []
            if (input.terms !== '') {
              sqlOrder.push(...exactOrder(input.terms))
            }

            sqlOrder.push(asc(asset.name))

            return sqlOrder
          })
          .limit(PER_PAGE)
          .then((res) =>
            assets.push(
              ...res.map((d) => ({
                name: d.name,
                logo: d.logo ?? '',
                slug: d.slug,
                symbol: d.symbol,
                type: d.type,
              })),
            ),
          )
      }

      if (input.filter === 'stable') {
        await chatSourceClient
          .select({
            name: asset.name,
            logo: asset.logo,
            slug: asset.slug,
            symbol: asset.symbol,
            type: asset.type,
          })
          .from(tsdbCmcPriceDaily)
          .innerJoin(asset, eq(asset.cmcId, tsdbCmcPriceDaily.cmc_id))
          .where(
            and(
              assetSelection.availableAsset ? inArray(asset.id, assetSelection.availableAsset) : undefined,
              sql`${tsdbCmcPriceDaily.timestamp} = (select max(${tsdbCmcPriceDaily.timestamp}) from ${tsdbCmcPriceDaily}) - interval '1 day'`,
              input.terms !== '' ? assetNameFilter(input.terms) : undefined,
              input.filter === 'stable'
                ? inArray(
                    asset.id,
                    (activeStablecoin ?? []).map((d) => d.id),
                  )
                : undefined,
            ),
          )
          .orderBy(() => {
            const sqlOrder: SQL<unknown>[] = []
            if (input.terms !== '') {
              sqlOrder.push(...exactOrder(input.terms))
            }

            sqlOrder.push(asc(asset.name))

            return sqlOrder
          })
          .limit(PER_PAGE)
          .then((res) =>
            assets.push(
              ...res.map((d) => ({
                name: d.name,
                logo: d.logo ?? '',
                slug: d.slug,
                symbol: d.symbol,
                type: d.type,
              })),
            ),
          )
      }

      return {
        data: assets,
      }
    }),

  finance: authorizedProcedure
    .input(
      z.object({
        terms: z.string().default(''),
        page: z.number().default(1),
        filter: z
          .array(z.enum(['CRYPTOCURRENCY', 'STABLECOIN']).default('CRYPTOCURRENCY'))
          .default(['CRYPTOCURRENCY', 'STABLECOIN']),
        topSlug: z.string().optional(),
      }),
    )
    .query(async ({ input, ctx }) => {
      const assetSelection = await AssetSelection.initialize(ctx)

      let assets: {
        name: string
        symbol: string
        slug: string
        logo: string | null
        type: string
      }[] = []

      if (assetSelection.availableAsset) {
        assets = await chatSourceClient
          .select()
          .from(asset)
          .where(
            and(
              inArray(asset.id, assetSelection.availableAsset),
              input.terms ? assetNameFilter(input.terms) : undefined,
            ),
          )
          .fullJoin(
            tsdbAssetCatalogue,
            and(
              eq(asset.id, tsdbAssetCatalogue.assetId),
              inArray(tsdbAssetCatalogue.type, ['financial_cryptocurrency', 'financial_stablecoin']),
            ),
          )
          .orderBy(() => {
            const query: SQL<unknown>[] = []

            if (input.terms) {
              query.push(...exactOrder(input.terms))
            }

            if (input.topSlug) {
              query.push(sql`
                CASE
                    WHEN ${asset.slug} ILIKE ${input.topSlug} THEN 0
                    ELSE 1
                END
              `)
            }

            query.push(asc(asset.name))

            return query
          })
          .limit(PER_PAGE)
          .then((res) =>
            res.map((entry) => ({
              name: entry.asset?.name ?? '-',
              symbol: entry.asset?.symbol ?? '-',
              slug: entry.asset?.slug ?? '-',
              logo: entry.asset?.logo ?? null,
              type: entry.assets_catalogue?.type === 'financial_stablecoin' ? 'STABLECOIN' : 'CRYPTOCURRENCY',
            })),
          )
      } else {
        assets = await chatSourceClient
          .select()
          .from(tsdbAssetCatalogue)
          .innerJoin(asset, eq(asset.id, tsdbAssetCatalogue.assetId))
          .where(
            and(
              inArray(tsdbAssetCatalogue.type, ['financial_cryptocurrency', 'financial_stablecoin']),
              inArray(asset.type, input.filter),
              input.terms ? assetNameFilter(input.terms) : undefined,
              assetSelection.availableAsset ? inArray(asset.id, assetSelection.availableAsset) : undefined,
            ),
          )
          .orderBy(() => {
            const query: SQL<unknown>[] = []

            if (input.terms) {
              query.push(...exactOrder(input.terms))
            }

            if (input.topSlug) {
              query.push(sql`
              CASE
                  WHEN ${asset.slug} ILIKE ${input.topSlug} THEN 0
                  ELSE 1
              END
            `)
            }

            query.push(asc(asset.name))

            return query
          })
          .limit(PER_PAGE)
          .then((res) =>
            res.map((entry) => ({
              name: entry.asset.name,
              symbol: entry.asset.symbol,
              slug: entry.asset.slug,
              logo: entry.asset.logo,
              type: entry.assets_catalogue?.type === 'financial_stablecoin' ? 'STABLECOIN' : 'CRYPTOCURRENCY',
            })),
          )
      }

      return {
        assets,
      }
    }),
})
