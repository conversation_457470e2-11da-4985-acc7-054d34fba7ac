import { router } from '~/server/trpc/trpc'
import { z } from 'zod'
import { and, inArray, sql, asc, ne, gte } from 'drizzle-orm'
import { stablecoinIndices } from '~/server/utils/timescale/schema/stablecoin-indices'
import { authorizedProcedure } from '~/server/trpc/procedure/authorized'
import { cryptoIndices, cryptoTimeseries, stablecoinTimeseries } from '~/server/utils/timescale/schema'
import { set } from 'date-fns'
import type { ComparisonChartIndice, SpiderChartIndice } from '../types/comparison'
import AssetSelection from '~/server/utils/asset_selection'

export type financeComparisonAPI = {
  name: string
  symbol: string
  timeseries: [number, number][]
}

type timeserieRow = {
  slug?: string
  symbol: string
  marketCap: number | null
  price: number | null
  assetId: number
  createdAt: Date
  thirtyDayVolatility: number | null
  '24hrTradingVolCex': number | null
  '24hrTradingVolDex': number | null
  aYearMaxDepegDays: number
  numberOfPairsCex: number | null
  numberOfPairsDex: number | null
  costOfLiquidity10ThouPcCex: number | null
  costOfLiquidityHundrethPcCex: number | null
  costofLiquidity1PcCex: number | null
  costofLiquidity100Cex: number | null
  costOfLiquidity10kCex: number | null
  costOfLiquidity1mCex: number | null
  liquidityConcentrationCex: number | null
  costOfLiquidity10ThouPcDex: number | null
  costOfLiquidityHundrethPcDex: number | null
  costOfLiquidity1PcDex: number | null
  costOfLiquidity100Dex: number | null
  costOfLiquidity10kDex: number | null
  costOfLiquidity1mDex: number | null
  liquidityConcentrationDex: number | null
}

enum filterFinancial {
  cexLiqCost10k = 'costOfLiquidity10kCex',
  cexLiqCost100 = 'costofLiquidity100Cex',
  cexLiqCost1m = 'costOfLiquidity1mCex',
  'cexLiqCost0.001_24h_vol' = 'costOfLiquidity10ThouPcCex',
  'cexLiqCost0.1_24h_vol' = 'costOfLiquidityHundrethPcCex',
  'cexLiqCost1_24h_vol' = 'costofLiquidity1PcCex',
  'dexLiqCost10k' = 'costOfLiquidity10kDex',
  dexLiqCost100 = 'costOfLiquidity100Dex',
  dexLiqCost1m = 'costOfLiquidity1mDex',
  'dexLiqCost0.001_24h_vol' = 'costOfLiquidity10ThouPcDex',
  'dexLiqCost0.1_24h_vol' = 'costOfLiquidityHundrethPcDex',
  'dexLiqCost1_24h_vol' = 'costOfLiquidity1PcDex',
  dayVolumeCex = '24hrTradingVolCex',
  monthVolatility = 'thirtyDayVolatility',
  cexPairs = 'numberOfPairsCex',
  depegDays = 'aYearMaxDepegDays',
  marketCap = 'marketCap',
  dexLiqConcentrate = 'liquidityConcentrationDex',
  dexPairs = 'numberOfPairsDex',
  dayVolumeDex = '24hrTradingVolDex',
  price = 'price',
}

const spiderStableValidity = z.object({
  metrics: z.object({
    cexLiqCost: z.number().finite(),
    dexLiqCost: z.number().finite(),
    cexPairs: z.number().finite(),
    dexPairs: z.number().finite(),
    dayVolumeCex: z.number().finite(),
    dayVolumeDex: z.number().finite(),
    depegDays: z.number().finite(),
    dexLiqConcentrate: z.number().finite(),
    marketCap: z.number().finite(),
    monthVolatility: z.number().finite(),
  }),
  slug: z.any().optional(),
  symbol: z.string().optional(),
})

/**
 *
 * @param assets
 * @returns [Stable, Crypto]
 */
function filterAssetsByTypes(
  assets: Awaited<ReturnType<typeof getCachedAsset>>,
): [Record<string, any>[], Record<string, any>[]] {
  return [
    Object.keys(assets).reduce((res: Record<string, unknown>[], key) => {
      if (assets[key].type === 'STABLECOIN') {
        res.push(assets[key])
      }
      return res
    }, []),
    Object.keys(assets).reduce((res: Record<string, unknown>[], key) => {
      if (assets[key].type === 'CRYPTOCURRENCY') {
        res.push(assets[key])
      }
      return res
    }, []),
  ]
}

function mapAssets(timeseries: timeserieRow[], assets: { id: number; slug?: string }[]): timeserieRow[] {
  return timeseries.map((timeserie) => {
    const asset = assets.find((d) => d.id === timeserie.assetId)

    return {
      ...timeserie,
      slug: asset?.slug,
    }
  })
}

function mapFinancialRow(timeseries: timeserieRow[], sizes: string): [SpiderChartIndice[], string | undefined] {
  let errMessage: string | undefined
  const result = timeseries.map((timeserie) => {
    let [cexLiqCost, dexLiqCost] = [0, 0]

    // Process CEX Liq Cost
    if (sizes === '0.001_24h_vol') {
      cexLiqCost = timeserie.costOfLiquidity10ThouPcCex ?? 0
    }
    if (sizes === '0.1_24h_vol') {
      cexLiqCost = timeserie.costOfLiquidityHundrethPcCex ?? 0
    }
    if (sizes === '1_24h_vol') {
      cexLiqCost = timeserie.costofLiquidity1PcCex ?? 0
    }
    if (sizes === '100') {
      cexLiqCost = timeserie.costofLiquidity100Cex ?? 0
    }
    if (sizes === '10k') {
      cexLiqCost = timeserie.costOfLiquidity10kCex ?? 0
    }
    if (sizes === '1m') {
      cexLiqCost = timeserie.costOfLiquidity1mCex ?? 0
    }
    // Process DEX Liq Cost
    if (sizes === '0.001_24h_vol') {
      dexLiqCost = timeserie.costOfLiquidity10ThouPcDex ?? 0
    }
    if (sizes === '0.1_24h_vol') {
      dexLiqCost = timeserie.costOfLiquidityHundrethPcDex ?? 0
    }
    if (sizes === '1_24h_vol') {
      dexLiqCost = timeserie.costOfLiquidity1PcDex ?? 0
    }
    if (sizes === '100') {
      dexLiqCost = timeserie.costOfLiquidity100Dex ?? 0
    }
    if (sizes === '10k') {
      dexLiqCost = timeserie.costOfLiquidity10kDex ?? 0
    }
    if (sizes === '1m') {
      dexLiqCost = timeserie.costOfLiquidity1mDex ?? 0
    }

    const result: SpiderChartIndice = {
      metrics: {
        cexLiqCost,
        dexLiqCost,
        cexPairs: timeserie.numberOfPairsCex ?? 0,
        dexPairs: timeserie.numberOfPairsDex ?? 0,
        dayVolumeCex: timeserie['24hrTradingVolCex'] ?? 0,
        dayVolumeDex: timeserie['24hrTradingVolDex'] ?? 0,
        depegDays: timeserie.aYearMaxDepegDays ?? 0,
        dexLiqConcentrate: timeserie.liquidityConcentrationDex ?? 0,
        marketCap: timeserie.marketCap ?? 0,
        monthVolatility: timeserie.thirtyDayVolatility ?? 0,
        price: timeserie.price ?? 0,
      },
      slug: timeserie.slug ?? '-',
      symbol: timeserie.symbol,
    }

    const check = spiderStableValidity.safeParse(result)

    if (!check.success) {
      result.error_message = `We've identified that some parameter inside spider chart is unavailable`
    }

    return result
  })

  return [result, errMessage]
}

function mapComparisonRow(
  timeseries: {
    createdAt: Date
    target: string | number | null
    symbol: string
  }[],
  assets: { id: number; slug?: string; symbol: string; name: string }[],
) {
  return timeseries.reduce((_res: ComparisonChartIndice[], data) => {
    let key = _res.findIndex((d) => d.symbol === data.symbol)
    const asset = assets.find((d) => d.symbol === data.symbol)

    if (key === -1) {
      _res.push({
        firstCalculation: data.createdAt.toISOString(),
        latestCalculation: data.createdAt.toISOString(),
        slug: asset?.slug ?? '-',
        symbol: asset?.symbol ?? '-',
        name: asset?.name,
        timeseries: [],
        assetId: asset?.id,
      })
      key = _res.length - 1
    }

    const dataRecord: [number, number] = [data.createdAt.getTime(), Number(data.target)]

    _res[key]?.timeseries?.push(dataRecord)

    return _res
  }, [])
}

function selectedFinancialMetric(target: 'crypto' | 'stable', compareUnit: keyof typeof filterFinancial) {
  if (target === 'stable') {
    return stablecoinTimeseries[filterFinancial[compareUnit]]
  } else {
    return cryptoTimeseries[filterFinancial[compareUnit]]
  }
}

const authorizedFeatureMaps = {
  price: ['price'],
  cex_pairs: ['cexPairs'],
  dex_pairs: ['dexPairs'],
  depeg_rata: ['depegDays'],
  market_cap: ['marketCap'],
  cex_volume_24h: ['dayVolumeCex'],
  dex_volume_24h: ['dayVolumeDex'],
  cex_liquidity_cost: [
    'cexLiqCost10k',
    'cexLiqCost100',
    'cexLiqCost1m',
    'cexLiqCost0.001_24h_vol',
    'cexLiqCost0.1_24h_vol',
    'cexLiqCost1_24h_vol',
  ],
  dex_liquidity_cost: [
    'dexLiqCost10k',
    'dexLiqCost100',
    'dexLiqCost1m',
    'dexLiqCost0.001_24h_vol',
    'dexLiqCost0.1_24h_vol',
    'dexLiqCost1_24h_vol',
  ],
  volatility_30_days: ['monthVolatility'],
  cex_liquidity_concentration: ['dexLiqConcentrate'],
  dex_liquidity_concentration: ['cexLiqConcentrate'],
}

export default router({
  chart: authorizedProcedure
    .input(
      z.object({
        slugs: z.array(z.string()).max(3),
        sizes: z.enum(['10k', '100', '1m', '0.001_24h_vol', '0.1_24h_vol', '1_24h_vol']).default('100'),
      }),
    )
    .query<SpiderChartIndice[]>(async ({ input, ctx }) => {
      const assetSelection = await AssetSelection.initialize(ctx)
      let errMessage: string | undefined

      const assets = await getCachedAsset(input.slugs, 'slug')

      const [stable, crypto] = filterAssetsByTypes(assets)

      const timeseries: SpiderChartIndice[] = []
      const rawTimeseries: SpiderChartIndice[] = []

      await Promise.all([
        // Stablecoin - Normalized Query
        stable.length > 0
          ? timescaleClient
              .select()
              .from(stablecoinIndices)
              .where(
                and(
                  sql`${stablecoinIndices.createdAt} = (select max(${stablecoinIndices.createdAt}) from ${stablecoinIndices})`,
                  inArray(
                    stablecoinIndices.assetId,
                    stable.map((d) => d.id),
                  ),
                  assetSelection.availableAsset
                    ? inArray(stablecoinIndices.assetId, assetSelection.availableAsset)
                    : undefined,
                ),
              )
              .then((res) => mapAssets(res, stable as { id: number; slug?: string }[]))
              .then((res) => {
                const [result, error] = mapFinancialRow(res, input.sizes)
                if (error && errMessage === undefined) {
                  errMessage = error
                }
                timeseries.push(...result)
              })
          : undefined,

        // Stablecoin - Raw Query
        stable.length > 0
          ? timescaleClient
              .select()
              .from(stablecoinTimeseries)
              .where(
                and(
                  sql`${stablecoinTimeseries.createdAt} = (select max(${stablecoinTimeseries.createdAt}) from ${stablecoinTimeseries})`,
                  inArray(
                    stablecoinTimeseries.assetId,
                    stable.map((d) => d.id),
                  ),
                ),
              )
              .then((res) => mapAssets(res, stable as { id: number; slug?: string }[]))
              .then((res) => {
                const [result, error] = mapFinancialRow(res, input.sizes)
                if (error && errMessage === undefined) {
                  errMessage = error
                }
                rawTimeseries.push(...result)
              })
          : undefined,

        // Normalized - Cryptocurrency Process
        crypto.length > 0
          ? timescaleClient
              .select()
              .from(cryptoIndices)
              .where(
                and(
                  sql`${cryptoIndices.createdAt} = (select max(${cryptoIndices.createdAt}) from ${cryptoIndices})`,
                  inArray(
                    cryptoIndices.assetId,
                    crypto.map((d) => d.id),
                  ),
                ),
              )
              .then((res) => mapAssets(res, crypto as { id: number; slug?: string }[]))
              .then((res) => {
                const [result, error] = mapFinancialRow(res, input.sizes)
                if (error && errMessage === undefined) {
                  errMessage = error
                }
                timeseries.push(...result)
              })
          : undefined,

        // Raw Query - Cryptocurrency Process
        crypto.length > 0
          ? timescaleClient
              .select()
              .from(cryptoTimeseries)
              .where(
                and(
                  assetSelection.availableAsset
                    ? inArray(cryptoTimeseries.assetId, assetSelection.availableAsset)
                    : undefined,
                  sql`${cryptoTimeseries.createdAt} = (select max(${cryptoTimeseries.createdAt}) from ${cryptoTimeseries})`,
                  inArray(
                    cryptoTimeseries.assetId,
                    crypto.map((d) => d.id),
                  ),
                ),
              )
              .then((res) => mapAssets(res, crypto as { id: number; slug?: string }[]))
              .then((res) => {
                const [result, error] = mapFinancialRow(res, input.sizes)
                if (error && errMessage === undefined) {
                  errMessage = error
                }
                rawTimeseries.push(...result)
              })
          : undefined,
      ])

      for (const rawTimeserie of rawTimeseries) {
        const normalizeTimeserieIndex = timeseries.findIndex((d) => rawTimeserie.slug === d.slug)
        if (normalizeTimeserieIndex > -1 && timeseries[normalizeTimeserieIndex]) {
          timeseries[normalizeTimeserieIndex].rawData = rawTimeserie.metrics
        }
      }

      return timeseries
    }),
  comparison: authorizedProcedure
    .input(
      z.object({
        slugs: z.array(z.string()).max(3).optional(),
        compareUnit: z.enum(['', ...Object.keys(filterFinancial)]),
      }),
    )
    .query<ComparisonChartIndice[]>(async ({ input, ctx }) => {
      const assetSelection = await AssetSelection.initialize(ctx)
      let metricsKey: string | undefined

      for (const key in authorizedFeatureMaps) {
        const _key = key as keyof typeof authorizedFeatureMaps
        if (authorizedFeatureMaps[_key] && metricsKey === undefined) {
          if (authorizedFeatureMaps[_key].findIndex((d) => d === input.compareUnit) > -1) {
            metricsKey = `financial_metrics.${_key}`
          }
        }
      }

      if (!metricsKey) {
        return []
      }

      if (!ctx.user.feature.checkFeature(metricsKey)) {
        return []
      }

      const result: ComparisonChartIndice[] = []

      if (input.slugs === undefined || input.slugs.length === 0) {
        return []
      }

      const assets = await getCachedAsset(input.slugs, 'slug')

      const [stable, crypto] = filterAssetsByTypes(assets)

      await Promise.all([
        stable.length > 0
          ? timescaleClient
              .select({
                createdAt: stablecoinTimeseries.createdAt,
                target: selectedFinancialMetric('stable', input.compareUnit as keyof typeof filterFinancial),
                symbol: stablecoinTimeseries.symbol,
              })
              .from(stablecoinTimeseries)
              .where(
                and(
                  assetSelection.availableAsset
                    ? inArray(stablecoinTimeseries.assetId, assetSelection.availableAsset)
                    : undefined,
                  inArray(
                    stablecoinTimeseries.assetId,
                    stable.map((d) => d.id),
                  ),
                  input.compareUnit.toLowerCase().includes('cex')
                    ? gte(
                        stablecoinTimeseries.createdAt,
                        set(new Date(), {
                          date: 3,
                          month: 4,
                          year: 2024,
                        }),
                      )
                    : undefined,
                  ne(
                    selectedFinancialMetric('stable', input.compareUnit as keyof typeof filterFinancial),
                    sql`'Infinity'::float`,
                  ),
                  ne(
                    selectedFinancialMetric('stable', input.compareUnit as keyof typeof filterFinancial),
                    sql`'-Infinity'::float`,
                  ),
                ),
              )
              .orderBy(asc(stablecoinTimeseries.createdAt))
              .then((res) => {
                const _res = mapComparisonRow(
                  res,
                  stable as { id: number; slug?: string; symbol: string; name: string }[],
                )
                result.push(..._res)
              })
          : undefined,
        crypto.length > 0
          ? timescaleClient
              .select({
                createdAt: cryptoTimeseries.createdAt,
                target: selectedFinancialMetric('crypto', input.compareUnit as keyof typeof filterFinancial),
                symbol: cryptoTimeseries.symbol,
              })
              .from(cryptoTimeseries)
              .where(
                and(
                  assetSelection.availableAsset
                    ? inArray(cryptoTimeseries.assetId, assetSelection.availableAsset)
                    : undefined,

                  inArray(
                    cryptoTimeseries.assetId,
                    crypto.map((d) => d.id),
                  ),
                  input.compareUnit.toLowerCase().includes('cex')
                    ? gte(
                        cryptoTimeseries.createdAt,
                        set(new Date(), {
                          date: 3,
                          month: 4,
                          year: 2024,
                        }),
                      )
                    : undefined,
                  ne(
                    selectedFinancialMetric('crypto', input.compareUnit as keyof typeof filterFinancial),
                    sql`'Infinity'::float`,
                  ),
                  ne(
                    selectedFinancialMetric('crypto', input.compareUnit as keyof typeof filterFinancial),
                    sql`'-Infinity'::float`,
                  ),
                ),
              )
              .orderBy(asc(cryptoTimeseries.createdAt))
              .then((res) => {
                const _res = mapComparisonRow(
                  res,
                  crypto as { id: number; slug?: string; symbol: string; name: string }[],
                )
                result.push(..._res)
              })
          : undefined,
      ])

      return result
    }),
})
