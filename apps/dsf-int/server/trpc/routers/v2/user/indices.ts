import { z } from 'zod'
import { and, asc, between, eq, inArray, isNotNull, sql } from 'drizzle-orm'
import { TRPCError } from '@trpc/server'
import { format, parseISO } from 'date-fns'
import { router } from '@/server/trpc/trpc'
import { timescaleClient } from '~/server/utils/timescale'
import { cmcPriceDaily, indice, indiceV2, stablecoinTimeseries } from '~/server/utils/timescale/schema'
import { lastIndexOfArray, numericDisplay } from '~/helper/number-helper'
import { titleCase } from '~/helper/string-helper'
import { ytyIndicesV2Cached, ytyPricesCached } from '~/server/utils/trpc/v2/cache/indices'
import { ytyEsgOverview } from '~/server/utils/trpc/v2/cache/esg'
import type { IndicesDataTable, IndicesTopicTable } from '../types/indices'
import { cachedOverviewData, getIndices } from '~/server/utils/repositories/indices'
import { getTopicTrend } from '~/server/utils/repositories/topics'
import { checkNumber } from '~/helper/utilities-helper'
import { authorizedProcedure } from '../../../procedure/authorized'
import { PER_PAGE } from '~/constants/api'
import AssetSelection from '~/server/utils/asset_selection'
import { asset } from '~/server/utils/chat-source/schema'

const { now, yty } = yearToYear()

export interface ChartLayerPayload {
  options: {
    id: string
    label: string
    group?: string
    username?: string
  }[]
  graph: {
    [key: string]: [string, number][]
  }
}

export default router({
  /**
   * Getting all indices data (mood, trust and topic trend)
   *
   * Need authentication to access this API
   */
  table: authorizedProcedure
    .input(
      z.object({
        selector: z.enum(['mood', 'trust', 'topic_analytic']).default('mood'),
        rank: z.enum(['community', 'message', 'indexes', 'changed']).optional(),
        type: z.array(z.enum(['crypto', 'stable'])).default(['crypto', 'stable']),
        page: z.number().default(1),
        terms: z.string().optional(),
      }),
    )
    .query(async ({ input, ctx }) => {
      const assetSelection = await AssetSelection.initialize(ctx)

      const record: {
        indice: IndicesDataTable[] | null
        topic_analytic: IndicesTopicTable[] | null
      } = {
        indice: null,
        topic_analytic: null,
      }

      let pagination: Partial<PaginationResponse<Awaited<ReturnType<typeof getIndices>>>> | undefined

      if (input.selector === 'topic_analytic' && input.rank) {
        throw new TRPCError({
          code: 'UNPROCESSABLE_CONTENT',
          message: 'Topic analytic cannot use rank field',
        })
      }

      if (input.selector === 'mood' || input.selector === 'trust') {
        if (!input.rank) {
          input.rank = 'indexes'
        }
        const flag: ('CRYPTOCURRENCY' | 'STABLECOIN')[] = []

        for (const type of input.type) {
          if (type === 'crypto') {
            flag.push('CRYPTOCURRENCY')
          } else {
            flag.push('STABLECOIN')
          }
        }

        const result = await getIndices(input.selector, {
          sort: input.rank,
          page: ctx.user.feature.checkSubscription() ? input.page : 1,
          perPage: PER_PAGE,
          type: flag,
          terms: input.terms,
          assetSelection: assetSelection.availableAsset ? inArray(asset.id, assetSelection.availableAsset) : undefined,
        })

        record.indice = []
        result.data.forEach((d) =>
          record.indice?.push({
            changed: d.changed ? numericDisplay(d.changed * 100).replace('-', '') + '%' : '-',
            changed_symbol: d.symbol_compare as IndicesDataTable['changed_symbol'],
            chart: d.chart,
            community_size: checkNumber(d.community_size) ? numericDisplay(d.community_size ?? 0, 0) : '-',
            indexes: checkNumber(d.indexes) ? numericDisplay(d.indexes ?? 0) : '-',
            name: d.name,
            number_of_message: checkNumber(d.number_message) ? numericDisplay(d.number_message ?? 0, 0) : '-',
            type: d.type,
            logo: d.logo ?? '-',
            slug: d.slug,
            symbol: d.symbol,
          }),
        )

        pagination = {
          current: ctx.user.feature.checkSubscription() ? result.pagination?.current : 1,
          last_page: ctx.user.feature.checkSubscription() ? result.pagination?.totalItem / PER_PAGE : 1,
          total_item: ctx.user.feature.checkSubscription() ? result.pagination?.totalItem : 10,
        }
      }

      if (input.selector === 'topic_analytic') {
        const result = await getTopicTrend()

        record.topic_analytic = []
        result.data.forEach((topic) => {
          record.topic_analytic?.push({
            name: topic.topic ?? '',
            chart: topic.history,
            occurrences: numericDisplay(topic.occurrences ?? 0, 0),
            platform: topic.platform,
          })
        })

        pagination = result.pagination
      }

      return {
        ...record,
        pagination,
      }
    }),
  /**
   *
   * Getting summary (year to year) data for cryptocurrency
   *
   * Need authentication to access this API
   */
  overview: authorizedProcedure.input(z.string()).query(async ({ input, ctx }) => {
    try {
      const assetSelection = await AssetSelection.initialize(ctx)
      const feeds = (await getCachedAsset([input], 'slug'))[input]

      if (feeds === null) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'ASSET_NOT_FOUND',
        })
      }

      if (assetSelection.availableAsset && !assetSelection.availableAsset.some((d) => d === feeds.id)) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Asset not found',
        })
      }

      const [indices, prices, esg, overviews] = await Promise.all([
        ctx.user.feature.checkMultipleNonStrict(
          'sentiment_metrics.messages',
          'sentiment_metrics.mood_index_level_1',
          'sentiment_metrics.trust_index_level_1',
        )
          ? ytyIndicesV2Cached(feeds.id)
          : null,
        feeds.cmcId ? ytyPricesCached([feeds.cmcId]) : null,

        feeds.esgSlug ? ytyEsgOverview(feeds.esgSlug) : null,
        ctx.user.feature.checkMultipleNonStrict(
          'sentiment_metrics.mood_ranking',
          'sentiment_metrics.trust_ranking',
          'sentiment_metrics.bots_tracker',
          'sentiment_metrics.messages',
          'sentiment_metrics.vulgarity_index',
        )
          ? cachedOverviewData(feeds.id)
          : null,
      ])

      const overview = (() => {
        const stats = {
          price: 0,
          mood_rank: 0,
          trust_rank: 0,
          bots: 0,
        }

        return {
          price: prices ? '$' + numericDisplay(prices?.[prices.length - 1]?.close ?? 0) : '$0',
          mood_rank: ctx.user.feature.checkFeature('sentiment_metrics.mood_ranking')
            ? overviews?.moodRankOrder
              ? numericDisplay(overviews.moodRankOrder, 0)
              : '-'
            : '-',
          trust_rank: ctx.user.feature.checkFeature('sentiment_metrics.trust_ranking')
            ? overviews?.trustRankOrder
              ? numericDisplay(overviews.trustRankOrder, 0)
              : '-'
            : '-',
          bots: ctx.user.feature.checkFeature('sentiment_metrics.bots_tracker') ? numericDisplay(stats.bots, 0) : '-',
        }
      })()

      return {
        last_calculation_date: indices?.all?.[lastIndexOfArray(indices?.all || [])]?.numericTimestamp ?? 0,
        asset: {
          logo: feeds.logo,
          name: feeds.name,
          slug: feeds.slug,
          symbol: feeds.symbol,
          type: feeds.type,
          esg_slug: feeds.esgSlug,
        },
        name: feeds.name,
        url: feeds.website,
        logo: feeds.logo,
        description: feeds.description ?? '-',
        overview: {
          ...overview,
          messages: ctx.user.feature.checkFeature('sentiment_metrics.messages')
            ? checkNumber(overviews?.nrMessages)
              ? numericDisplay(overviews!.nrMessages!, 0)
              : '-'
            : '-',
          bad_words: ctx.user.feature.checkFeature('sentiment_metrics.vulgarity_index')
            ? checkNumber(overviews?.v1VulgarityIndex)
              ? numericDisplay(overviews!.v1VulgarityIndex!)
              : '-'
            : '-',
        },
        urls: [],
        global_kwh_power:
          esg
            ?.filter((item) => {
              if (item.avg_consumption_kwh === null || item.avg_consumption_kwh === 0) {
                return false
              } else {
                return true
              }
            })
            .map((item) => [
              item.measured_at,
              {
                average: item.avg_consumption_kwh,
                range: [item.lower_consumption_kwh, item.upper_consumption_kwh],
              },
            ]) ?? [],
        power_wh_transaction:
          esg
            ?.filter((item) => {
              if (item.avg_consumption_wh_tx === null || item.avg_consumption_wh_tx === 0) {
                return false
              } else {
                return true
              }
            })
            .map((item) => [
              item.measured_at,
              {
                average: item.avg_consumption_wh_tx,
                range: [item.lower_consumption_wh_tx, item.upper_consumption_wh_tx],
              },
            ]) ?? [],
        messages: ctx.user.feature.checkFeature('sentiment_metrics.messages')
          ? indices
            ? Object.keys(indices)
                .filter((key) => key !== '_all')
                .reduce((res: { [key: string]: [number, number][] }, key) => {
                  if (res[key] === undefined && indices[key]) {
                    res[key] = indices[key].map((indice) => [
                      typeof indice.timestamp === 'string'
                        ? parseISO(indice.timestamp as string).getTime()
                        : indice.timestamp.getTime(),
                      indice.nrMessages ?? 0,
                    ])
                  }

                  return res
                }, {})
            : {}
          : {},
        price: prices?.map((price) => [price.timestamp, price.close ?? 0]) ?? [],
        volume: prices?.map((price) => [price.timestamp, price.volume ?? 0]) ?? [],
        mood: ctx.user.feature.checkFeature('sentiment_metrics.mood_index_level_1')
          ? indices
            ? (indices?.all || [])
                .filter((indice) => {
                  if (indice.moodIndex === null) {
                    return false
                  } else {
                    return true
                  }
                })
                .map((indice) => [
                  typeof indice.timestamp === 'string'
                    ? parseISO(indice.timestamp as string).getTime()
                    : indice.timestamp.getTime(),
                  indice.moodIndex,
                ])
            : []
          : [],
        trust: ctx.user.feature.checkFeature('sentiment_metrics.trust_index_level_1')
          ? indices
            ? (indices?.all || [])
                .filter((indice) => {
                  if (indice.trustIndex === null) {
                    return false
                  } else {
                    return true
                  }
                })
                .map((indice) => [
                  typeof indice.timestamp === 'string'
                    ? parseISO(indice.timestamp as string).getTime()
                    : indice.timestamp.getTime(),
                  indice.trustIndex,
                ])
            : []
          : [],
        community: ctx.user.feature.checkFeature('sentiment_metrics.community_size')
          ? indices
            ? Object.keys(indices).reduce((res: Record<string, [number, number][]>, key) => {
                if (key === 'all') {
                  return res
                }

                if (res[key] === undefined && indices[key]) {
                  res[key] = indices[key]
                    .filter((indice) => {
                      if (indice.communitySize === null) {
                        return false
                      } else {
                        return true
                      }
                    })
                    .map((indice) => [
                      typeof indice.timestamp === 'string'
                        ? parseISO(indice.timestamp as string).getTime()
                        : indice.timestamp.getTime(),
                      indice.communitySize ?? 0,
                    ])
                }
                return res
              }, {})
            : {}
          : {},
      }
    } catch (error: unknown) {
      if (error instanceof Error && error.message === 'FEED_NOT_FOUND') {
        throw new TRPCError({
          code: 'NOT_FOUND',
        })
      }

      throw error
    }
  }),
  overviewStablecoin: authorizedProcedure.input(z.string()).query(async ({ input, ctx }) => {
    const assetSelection = await AssetSelection.initialize(ctx)
    const asset = (await getCachedAsset([input], 'slug'))[input]

    if (assetSelection.availableAsset && !assetSelection.availableAsset.some((d) => d === asset.id)) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Asset not found',
      })
    }

    const [cmcPrices, indices, stableSeries, overviews] = await Promise.all([
      asset.cmcId
        ? timescaleClient
            .select()
            .from(cmcPriceDaily)
            .where(and(between(cmcPriceDaily.timestamp, yty, now), eq(cmcPriceDaily.cmc_id, asset.cmcId)))
            .orderBy(asc(cmcPriceDaily.timestamp))
        : [],
      ctx.user.feature.checkMultipleNonStrict(
        'sentiment_metrics.mood_index_level_1',
        'sentiment_metrics.trust_index_level_1',
      )
        ? timescaleClient
            .select()
            .from(indiceV2)
            .where(
              and(
                sql`${indiceV2.timestamp} >= (select max(${indiceV2.timestamp}) from ${indiceV2}) - interval '365 day'`,
                sql`${indiceV2.timestamp} <= (select max(${indiceV2.timestamp}) from ${indiceV2})`,
                eq(indiceV2.assetId, asset.id),
              ),
            )
            .orderBy(asc(indiceV2.timestamp))
        : [],
      timescaleClient
        .select()
        .from(stablecoinTimeseries)
        .where(
          and(
            eq(stablecoinTimeseries.symbol, asset.symbol),
            sql`${stablecoinTimeseries.createdAt} >= (select max(${stablecoinTimeseries.createdAt}) from ${stablecoinTimeseries}) - interval '365 day'`,
            sql`${stablecoinTimeseries.createdAt} <= (select max(${stablecoinTimeseries.createdAt}) from ${stablecoinTimeseries})`,
          ),
        )
        .orderBy(asc(stablecoinTimeseries.createdAt)),
      cachedOverviewData(asset.id),
    ])

    const allIndices = indices.filter((d) => d.platform === '_all')

    const statistics: {
      bots: number | null
    } = {
      bots: null,
    }

    return {
      name: asset.name,
      description: asset.description,
      website: asset.website,
      logo: asset.logo,
      last_calculation_date: indices[lastIndexOfArray(indices)]?.timestamp.getTime() ?? new Date().getTime(),
      links: [],
      asset: {
        logo: asset.logo,
        name: asset.name,
        slug: asset.slug,
        symbol: asset.symbol,
        type: asset.type,
      },
      overview: {
        price:
          '$' +
          numericDisplay(lastIndexOfArray(cmcPrices) > -1 ? (cmcPrices?.[lastIndexOfArray(cmcPrices)]?.close ?? 0) : 0),
        mood_rank: ctx.user.feature.checkFeature('sentiment_metrics.mood_ranking')
          ? overviews?.moodRankOrder
            ? numericDisplay(overviews.moodRankOrder, 0)
            : '-'
          : '-',
        trust_rank: ctx.user.feature.checkFeature('sentiment_metrics.trust_ranking')
          ? overviews?.trustRankOrder
            ? numericDisplay(overviews.trustRankOrder, 0)
            : '-'
          : '-',
        number_messages: ctx.user.feature.checkFeature('sentiment_metrics.messages')
          ? overviews?.nrMessages
            ? numericDisplay(overviews?.nrMessages, 0)
            : '-'
          : '-',
        number_bots: ctx.user.feature.checkFeature('sentiment_metrics.bots_tracker')
          ? statistics.bots
            ? numericDisplay(statistics.bots, 0)
            : '-'
          : '-',
        number_bad_word: ctx.user.feature.checkFeature('sentiment_metrics.vulgarity_index')
          ? overviews?.v1VulgarityIndex
            ? numericDisplay(overviews.v1VulgarityIndex ?? 0)
            : '-'
          : '-',
      },
      mood: ctx.user.feature.checkFeature('sentiment_metrics.mood_index_level_1')
        ? allIndices
            .filter((indice) => {
              if (indice.moodIndex === null) {
                return false
              } else {
                return true
              }
            })
            .map((indice) => [indice.timestamp.getTime(), indice.moodIndex ?? 0])
        : [],
      trust: ctx.user.feature.checkFeature('sentiment_metrics.trust_index_level_1')
        ? allIndices
            .filter((indice) => {
              if (indice.trustIndex === null) {
                return false
              } else {
                return true
              }
            })
            .map((indice) => [indice.timestamp.getTime(), indice.trustIndex ?? 0])
        : [],
      messages: ctx.user.feature.checkFeature('sentiment_metrics.messages')
        ? indices.reduce((res: { [key: string]: [number, number | number][] }, data) => {
            if (data.platform === '_all') {
              return res
            }

            if (res[data.platform] === undefined) {
              res[data.platform] = [[data.timestamp.getTime(), data.nrMessages ?? 0]]
            } else {
              ;(res?.[data?.platform || ''] || []).push([data.timestamp.getTime(), data.nrMessages ?? 0])
            }

            return res
          }, {})
        : {},
      community: ctx.user.feature.checkFeature('sentiment_metrics.community_size')
        ? indices.reduce((res: { [key: string]: [number, number | number][] }, data) => {
            if (data.platform === '_all') {
              return res
            }

            if (res[data.platform] === undefined) {
              res[data.platform] = [[data.timestamp.getTime(), data.communitySize ?? 0]]
            } else {
              ;(res?.[data?.platform || ''] || []).push([data.timestamp.getTime(), data.communitySize ?? 0])
            }

            return res
          }, {})
        : {},
      marketcap: cmcPrices
        .filter((cmcPrice) => cmcPrice.marketcap !== null)
        .map((cmcPrice) => [cmcPrice.timestamp.getTime(), cmcPrice.marketcap]),
      volume: cmcPrices
        .filter((cmcPrice) => cmcPrice.volume !== null)
        .map((cmcPrice) => [cmcPrice.timestamp.getTime(), cmcPrice.volume ?? 0]),
      price: cmcPrices
        .filter((cmcPrice) => cmcPrice.close !== null)
        .map((cmcPrice) => [cmcPrice.timestamp.getTime(), cmcPrice.close ?? 0]),
      avg_liqudity: {
        cex: stableSeries.map((stable) => [
          stable.createdAt?.getTime(),
          stable.costOfLiquidity100Dex ? Number(stable.costOfLiquidity100Dex) : null,
        ]),
        dex: stableSeries.map((stable) => [
          stable.createdAt?.getTime(),
          stable.costofLiquidity100Cex ? Number(stable.costofLiquidity100Cex) : null,
        ]),
      },
    }
  }),
  /**
   *
   *
   * Getting all timeseries data related with mood & trust for standalone chart
   *
   * Need authentication to access this API
   */
  charts: authorizedProcedure
    .input(
      z.object({
        slug: z.string(),
        selected_1_layer: z.enum(['mood', 'trust']),
        selected_2_layer: z.array(z.string()).optional(),
        selected_3_layer: z
          .array(z.string())
          .refine((items) => new Set(items).size === items.length, {
            message: 'Must be an array of unique strings',
          })
          .optional(),
      }),
    )
    .query(async ({ input, ctx }) => {
      const assetSelection = await AssetSelection.initialize(ctx)
      let layers: {
        'l-1'?: ChartLayerPayload
        'l-2'?: ChartLayerPayload
        'l-3'?: ChartLayerPayload
      } = {}
      const communityFlag: Record<string, [string, string][]> = {}

      const newFeeds = (await getCachedAsset([input.slug], 'slug'))[input.slug]
      if (newFeeds === undefined) {
        throw new TRPCError({
          message: 'Asset not found',
          code: 'NOT_FOUND',
        })
      }

      if (assetSelection.availableAsset && !assetSelection.availableAsset.some((d) => d === newFeeds.id)) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Asset not found',
        })
      }

      newFeeds.communities.forEach((community: { communityId: string; community: { name: string } }) => {
        const [platform, platformId] = community.communityId.split('::')
        const key = platform?.toLowerCase() ?? ''
        if (!communityFlag[key]) {
          communityFlag[key] = []
        }
        communityFlag[key].push([platformId as string, community.community.name])
      })

      layers = {
        'l-1': {
          graph: {},
          options: [
            {
              id: input.selected_1_layer,
              label: input.selected_1_layer === 'mood' ? 'Mood' : 'Trust',
            },
          ],
        },
        'l-2': {
          graph: {},
          options: Object.keys(communityFlag).map((cf) => ({
            id: cf,
            label: titleCase(cf),
          })),
        },
        'l-3': {
          graph: {},
          options: [],
        },
      }

      Object.keys(layers).forEach((key) => {
        const _key = key as keyof typeof layers
        if (layers[_key]) {
          const assignedKey = layers[_key]?.options.map((opt) => opt.id)
          assignedKey?.forEach((key) => {
            layers[_key]!.graph[key] = []
          })
        }
      })

      const [stats, topLayer, groupLayer, availablePlatform] = await Promise.all([
        timescaleClient.query.indicesV2Feeds.findFirst({
          where: (fields, { eq }) => eq(fields.assetId, newFeeds.id),
          orderBy: (fields, { desc }) => desc(fields.timestamp),
        }),
        ctx.user?.feature.checkFeature(`sentiment_metrics.${input.selected_1_layer}_index_level_1`)
          ? timescaleClient
              .select()
              .from(indiceV2)
              .where(
                and(
                  isNotNull(indiceV2.moodIndex),
                  isNotNull(indiceV2.trustIndex),
                  inArray(indiceV2.platform, [
                    '_all',
                    ...(input.selected_2_layer &&
                    ctx.user?.feature.checkFeature(`sentiment_metrics.${input.selected_1_layer}_index_level_2`)
                      ? input.selected_2_layer
                      : []),
                  ]),
                  eq(indiceV2.assetId, newFeeds.id),
                ),
              )
              .orderBy(asc(indiceV2.timestamp))
          : [],
        ctx.user?.feature.checkFeature(`sentiment_metrics.${input.selected_1_layer}_index_level_3`)
          ? timescaleClient
              .select()
              .from(indice)
              .where(
                and(
                  input.selected_3_layer
                    ? input.selected_3_layer.length > 0
                      ? inArray(indice.platform_id, input.selected_3_layer)
                      : eq(indice.platform_id, 'null')
                    : eq(indice.platform, 'null'),
                ),
              )
              .orderBy(asc(indice.timestamp))
          : [],
        timescaleClient
          .select({
            platformId: indice.platform_id,
          })
          .from(indice)
          .where(
            inArray(
              indice.platform_id,
              newFeeds.communities.reduce(
                (res: string[], data: { communityId: string; community: { name: string } }) => {
                  const platformId = data.communityId.split('::')
                  res.push(platformId[1] as string)
                  return res
                },
                [],
              ),
            ),
          )
          .groupBy(indice.platform_id),
      ])

      if (layers['l-3']?.options) {
        layers['l-3'].options = availablePlatform.reduce((res: ChartLayerPayload['options'], data) => {
          const key = newFeeds.communities.findIndex(
            (d: { community: { platformId: string } }) => d.community.platformId === data.platformId,
          )

          if (key > -1) {
            res.push({
              id: data.platformId,
              label: newFeeds.communities[key].community.name,
              group: titleCase(newFeeds.communities[key].communityId.split('::')[0]),
              username: newFeeds.communities[key].community.url.split('/').pop(),
            })
          }
          return res
        }, [])
      }

      await Promise.all([
        topLayer.forEach((layer) => {
          const value = input.selected_1_layer === 'mood' ? layer.moodIndex : layer.trustIndex
          if (layer.platform === '_all') {
            if (layers['l-1']?.graph[input.selected_1_layer]) {
              ;(layers['l-1'].graph[input?.selected_1_layer || 'mood'] || []).push([
                format(layer.timestamp, 'yyyy-MM-dd'),
                value ?? 0,
              ])
            }
          } else {
            if (layers['l-2']?.graph[layer.platform]) {
              ;(layers['l-2'].graph[layer?.platform || ''] || []).push([
                format(layer.timestamp, 'yyyy-MM-dd'),
                value ?? 0,
              ])
            }
          }
        }),
        groupLayer.forEach((layer) => {
          const value = input.selected_1_layer === 'mood' ? layer.mood_index : layer.trust_index
          if (layers['l-3']) {
            if (layers['l-3']?.graph[layer.platform_id]) {
              ;(layers['l-3'].graph[layer?.platform_id || ''] || []).push([
                format(layer.timestamp!, 'yyyy-MM-dd'),
                value ?? 0,
              ])
            } else {
              layers['l-3'].graph[layer.platform_id] = [[format(layer.timestamp!, 'yyyy-MM-dd'), value ?? 0]]
            }
          }
        }),
      ])

      const startcalculation = topLayer[0]
      const latestCalculation = topLayer[topLayer.length - 1]

      const latestValue = input.selected_1_layer === 'mood' ? (stats?.moodIndex ?? 0) : (stats?.trustIndex ?? 0)
      const percentageValue =
        input.selected_1_layer === 'mood' ? (stats?.dayChangedMood ?? 0) : (stats?.dayChangedTrust ?? 0)

      return {
        layers,
        latest: {
          value: numericDisplay(latestValue ?? 0),
          percentage: numericDisplay(latestValue).replace('-', '') + ' %',
          indicator: (percentageValue ?? 0) > 0 ? '>' : percentageValue < 0 ? '<' : '=',
        },
        info: {
          logo: newFeeds.logo,
          domain: newFeeds.domain,
          website: newFeeds.website,
          symbol: newFeeds.symbol,
          name: newFeeds.name,
          slug: newFeeds.slug,
          type: newFeeds.type,
        },
        latest_calculation: latestCalculation?.timestamp,
        start_calculation: startcalculation?.timestamp,
      }
    }),
})
