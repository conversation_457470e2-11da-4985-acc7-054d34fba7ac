import { router } from '~/server/trpc/trpc'
import { authorizedProcedure } from '~/server/trpc/procedure/authorized'
import { asset, tsdbAssetCatalogue } from '~/server/utils/chat-source/schema'
import { and, asc, eq, inArray, sql } from 'drizzle-orm'
import { z } from 'zod'
import { decentralisationNormalised, DecentralizeFilter } from '~/server/utils/timescale/schema'
import { TRPCError } from '@trpc/server'
import { format } from 'date-fns'
import type { ComparisonChartIndice, SpiderChartIndice } from '../types/comparison'
import AssetSelection from '~/server/utils/asset_selection'

const decentralizeMetricsProperties = [
  'ipAuthDistrGini',
  'ipParticipantDivGini',
  'ipAuthorInflConcHHI',
  'ipGovOrdinal',
  'rcpDevDistrGini',
  'rcpParticipantDivShannon',
  'rcpDevInflConcHHI',
  'rcdRevrPowerConcHHI',
  'consensusPowerNeGini',
  'consensusPowerNeTheil',
  'consensusPowerConcNakamoto',
  'consensusPowerConcHHI',
  'coinDistrOrdinal',
] as const

export default router({
  asset: authorizedProcedure.query(async ({ ctx }) => {
    const assetSelection = await AssetSelection.initialize(ctx)

    let assets: {
      name: string | null
      symbol: string | null
      slug: string | null
      logo: string | null
      type: 'CRYPTOCURRENCY' | 'STOCK' | 'STABLECOIN' | null
    }[] = []

    if (assetSelection.availableAsset) {
      assets = await chatSourceClient
        .select({
          name: asset.name,
          symbol: asset.symbol,
          slug: asset.slug,
          logo: asset.logo,
          type: asset.type,
        })
        .from(asset)
        .fullJoin(
          tsdbAssetCatalogue,
          and(eq(asset.id, tsdbAssetCatalogue.assetId), eq(tsdbAssetCatalogue.type, 'decentralisation_feeds')),
        )
        .where(and(inArray(asset.id, assetSelection.availableAsset)))
    } else {
      assets = await chatSourceClient
        .select({
          name: asset.name,
          symbol: asset.symbol,
          slug: asset.slug,
          logo: asset.logo,
          type: asset.type,
        })
        .from(tsdbAssetCatalogue)
        .innerJoin(asset, eq(asset.id, tsdbAssetCatalogue.assetId))
        .where(and(eq(tsdbAssetCatalogue.type, 'decentralisation_feeds')))
    }

    return {
      assets: assets.map((d) => ({
        symbol: d.symbol,
        type: d.type as string,
        slug: d.slug,
        logo: d.logo ?? '',
        name: d.name,
      })),
    }
  }),
  chart: authorizedProcedure
    .input(
      z.object({
        slugs: z.array(z.string()),
      }),
    )
    .query<SpiderChartIndice[]>(async ({ input, ctx }) => {
      const assetSelection = await AssetSelection.initialize(ctx)

      const payload: SpiderChartIndice[] = []

      const assets = await getCachedAsset(input.slugs, 'slug')

      for (const key in assets) {
        const assetData = assets[key]
        if (assetData) {
          const payloadMetrics: SpiderChartIndice['metrics'] = {}
          const result = await Promise.all(
            Object.keys(DecentralizeFilter).map((key) => {
              if (DecentralizeFilter[key]) {
                return timescaleClient
                  .select()
                  .from(decentralisationNormalised)
                  .where(
                    and(
                      assetSelection.availableAsset
                        ? inArray(decentralisationNormalised.assetId, assetSelection.availableAsset)
                        : undefined,
                      eq(decentralisationNormalised.metricName, DecentralizeFilter[key][0]),
                      eq(decentralisationNormalised.metricCluster, DecentralizeFilter[key][1]),
                      eq(decentralisationNormalised.assetId, assetData.id),
                      sql`${decentralisationNormalised.date} = (
                        select max(${decentralisationNormalised.date}) from ${decentralisationNormalised}
                        where (${decentralisationNormalised.assetId} = ${assetData.id})
                        and (${decentralisationNormalised.metricName} = ${DecentralizeFilter[key][0]})
                        and (${decentralisationNormalised.metricCluster} = ${DecentralizeFilter[key][1]})
                      )`,
                    ),
                  )
                  .then((res) => {
                    return [key, res[0]?.value ?? 0] as [string, number]
                  })
              }

              return
            }),
          )

          for (const item of result) {
            if (item) {
              payloadMetrics[item[0]] = item[1]
            }
          }

          payload.push({
            assetId: assetData.id,
            name: assetData.name,
            slug: assetData.slug,
            symbol: assetData.symbol,
            metrics: payloadMetrics,
          })
        }
      }

      return payload
    }),
  comparison: authorizedProcedure
    .input(
      z.object({
        compareUnit: z.enum(decentralizeMetricsProperties),
        slugs: z.array(z.string()),
      }),
    )
    .query<ComparisonChartIndice[]>(async ({ input, ctx }) => {
      const assetSelection = await AssetSelection.initialize(ctx)

      if (input.compareUnit === 'ipGovOrdinal' || input.compareUnit === 'coinDistrOrdinal') {
        return []
      }
      const assets = await getCachedAsset(input.slugs, 'slug')

      const decentralizeFilter = DecentralizeFilter[input.compareUnit]

      if (decentralizeFilter === null) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Uppss, something wrong',
          cause: 'D_FILTER',
        })
      }

      const timeseries = await timescaleClient
        .select({
          assetId: decentralisationNormalised.assetId,
          value: decentralisationNormalised.value,
          date: decentralisationNormalised.date,
        })
        .from(decentralisationNormalised)
        .where(
          and(
            and(
              eq(decentralisationNormalised.metricName, decentralizeFilter?.[0] ?? ''),
              eq(decentralisationNormalised.metricCluster, decentralizeFilter?.[1] ?? ''),
            ),
            inArray(
              decentralisationNormalised.assetId,
              Object.keys(assets).map((key) => assets[key].id),
            ),
            assetSelection.availableAsset
              ? inArray(decentralisationNormalised.assetId, assetSelection.availableAsset)
              : undefined,
          ),
        )
        .orderBy(asc(decentralisationNormalised.date))

      const [firstCalculation, latestCalculation] = [
        format(timeseries[0]?.date?.getTime() ?? 0, 'yyyy-MM-dd'),
        format(timeseries[timeseries.length - 1]?.date?.getTime() ?? 0, 'yyyy-MM-dd'),
      ]

      return Object.keys(assets)
        .map((key) => {
          if (assets[key] === undefined) {
            return null
          }

          return {
            assetId: assets[key].id,
            name: assets[key].name,
            slug: assets[key].slug,
            symbol: assets[key].symbol,
            firstCalculation: firstCalculation ?? 0,
            latestCalculation: latestCalculation ?? 0,
            timeseries: timeseries
              .filter((d) => d.assetId === assets[key].id)
              .map((d) => [d.date?.getTime() ?? 0, d.value ?? 0] as [number, number]),
          }
        })
        .filter((d) => d !== null)
    }),
})
