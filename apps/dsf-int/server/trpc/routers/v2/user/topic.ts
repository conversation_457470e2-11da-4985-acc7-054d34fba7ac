import { z } from 'zod'
import { router } from '~/server/trpc/trpc'
import { asc, desc, eq } from 'drizzle-orm'
import { wcTopics } from '~/server/utils/timescale/schema'
import { TRPCError } from '@trpc/server'
import type { TopicTrendChartQuery } from '../types/topic'
import { titleCase } from '~/helper/string-helper'
import { authorizedProcedure } from '~/server/trpc/procedure/authorized'

export default router({
  chart: authorizedProcedure.input(z.string()).query<TopicEntries>(async ({ input, ctx }) => {
    if (!ctx.user?.feature.checkFeature(`sentiment_metrics.topic_trends`)) {
      return {
        available_platform: [],
        latest_calculation: 0,
        start_calculation: 0,
        name: '',
        timestamp: {},
      }
    }

    const checkTopic = await timescaleClient.query.wcTopics.findFirst({
      columns: {
        date: true,
      },
      where: eq(wcTopics.topic, input),
      orderBy: desc(wcTopics.date),
    })

    if (!checkTopic) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: `${input} topic is not found`,
      })
    }

    const result: TopicTrendChartQuery = await timescaleClient
      .select()
      .from(wcTopics)
      .where(eq(wcTopics.topic, input))
      .orderBy(asc(wcTopics.date))
      .then((res) =>
        res.reduce(
          (e: TopicTrendChartQuery, value) => {
            const platform = value.platform.toLowerCase()
            if (e.platform.findIndex((data) => data === platform) === -1) {
              e.platform.push(platform)
            }

            if (e.timestamp[platform] === undefined) {
              e.timestamp[platform] = []
            }

            const timestamp = value.date.getTime()

            e.timestamp[platform].push([timestamp, value.occurrences])

            e.timestamp.all = e.timestamp.all || []
            if (e.timestamp.all.length > 0) {
              const target = e.timestamp.all.length - 1
              if (e.timestamp.all[target] && e.timestamp.all[target][0] === timestamp) {
                e.timestamp.all[target][1] = (e.timestamp.all?.[target]?.[1] ?? 0) + (value.occurrences ?? 0)
              } else {
                e.timestamp.all.push([timestamp, value.occurrences])
              }
            } else {
              e.timestamp.all.push([timestamp, value.occurrences])
            }

            return e
          },
          {
            platform: ['all'],
            timestamp: {
              all: [],
            },
          },
        ),
      )

    return {
      name: input,
      available_platform: result.platform.map((platform) => ({
        key: platform,
        label: titleCase(platform),
      })),
      timestamp: result.timestamp,
      start_calculation: result?.timestamp?.all?.[0]?.[0] || 0,
      latest_calculation: result?.timestamp?.all?.[result.timestamp.all.length - 1]?.[0] || 0,
    }
  }),
})
