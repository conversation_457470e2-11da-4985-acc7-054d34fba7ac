import { z } from 'zod'
import { router } from '~/server/trpc/trpc'
import { getEsgCached } from '~/server/utils/trpc/v2/cache/esg'
import { and, eq, ilike, inArray, or, sql, asc, gte, ne, isNotNull, SQL } from 'drizzle-orm'
import { asset, esgPairV2, tsdbVwEsgv3Feeds } from '~/server/utils/chat-source/schema'
import { TRPCError } from '@trpc/server'
import { authorizedProcedure } from '~/server/trpc/procedure/authorized'
import { vwEsgv2 } from '~/server/utils/timescale/schema/vw_esgv3'
import { assetNameFilter, exactOrder } from '~/server/utils/chat-source/schema/asset'
import { dynamicSort } from '~/server/utils/database'
import { displayWattage, numericDisplay } from '~/helper/number-helper'
import type { PaginationResponse } from '~/types/appTypes'

type esgIndice = [number, { min: number; max: number; avg: number }][]

interface esgIndices {
  mechanism: string
  type: string
  kwh: esgIndice
  tps: [number, number][]
  validator: [number, number][]
  wh_trx: esgIndice
  wh_trx_node: esgIndice
}

export default router({
  table: authorizedProcedure
    .input(
      z.object({
        page: z.number().default(1),
        terms: z.string().optional(),
        sort: z
          .tuple([
            z.enum(['power_cons', 'energy_tx', 'energy_tx_node', 'validators', 'tps', 'name']).default('power_cons'),
            z.enum(['desc', 'asc']).default('desc'),
          ])
          .default(['power_cons', 'desc']),
      }),
    )
    .query(async ({ input }) => {
      const whereQuery = and(input.terms ? assetNameFilter(input.terms) : undefined)

      const queriesSq = chatSourceClient.$with('queriesSq').as(
        chatSourceClient
          .select({
            assetName: asset.name,
            logo: asset.logo,
            symbol: asset.symbol,
            blockchainId: sql<string>`${tsdbVwEsgv3Feeds.name}`.as('blockchainId'),
            consumptionKwh: tsdbVwEsgv3Feeds.poweruseBestGuessW,
            consumptionWhTx: tsdbVwEsgv3Feeds.energyConsumptionTxBestGuessWh,
            consumptionTxNode: tsdbVwEsgv3Feeds.energyConsumptionTxNodeBestGuessWh,
            validator: tsdbVwEsgv3Feeds.validators,
            tps: tsdbVwEsgv3Feeds.throughputTps,
            totalCount: sql<number>`cast (COUNT(*) OVER() as int)`.as('totalCount'),
          })
          .from(tsdbVwEsgv3Feeds)
          .innerJoin(esgPairV2, eq(esgPairV2.name, tsdbVwEsgv3Feeds.name))
          .innerJoin(asset, eq(asset.id, esgPairV2.assetId))
          .where(whereQuery)
          .limit(10)
          .offset(input.page ? (input.page - 1) * 10 : 0)
          .orderBy(() => {
            const orderQuery: SQL<unknown>[] = []

            switch (input.sort[0]) {
              case 'validators':
                orderQuery.push(
                  sql`CASE
                    WHEN ${tsdbVwEsgv3Feeds.validators} = 'NaN' THEN 1
                    WHEN ${tsdbVwEsgv3Feeds.validators} IS NULL THEN 1
                    WHEN ${tsdbVwEsgv3Feeds.validators} = 0 THEN 1
                    ELSE 0
                  END`,
                  dynamicSort(tsdbVwEsgv3Feeds.validators, input.sort[1]),
                )
                break

              case 'tps':
                orderQuery.push(
                  sql`CASE
                    WHEN ${tsdbVwEsgv3Feeds.throughputTps} = 'NaN' THEN 1
                    WHEN ${tsdbVwEsgv3Feeds.throughputTps} IS NULL THEN 1
                    WHEN ${tsdbVwEsgv3Feeds.throughputTps} = 0 THEN 1
                    ELSE 0
                  END`,
                  dynamicSort(tsdbVwEsgv3Feeds.throughputTps, input.sort[1]),
                )
                break

              case 'energy_tx':
                orderQuery.push(
                  sql`CASE
                    WHEN ${tsdbVwEsgv3Feeds.energyConsumptionTxBestGuessWh} = 'NaN' THEN 1
                    WHEN ${tsdbVwEsgv3Feeds.energyConsumptionTxBestGuessWh} IS NULL THEN 1
                    WHEN ${tsdbVwEsgv3Feeds.energyConsumptionTxBestGuessWh} = 0 THEN 1
                    ELSE 0
                  END`,
                  dynamicSort(tsdbVwEsgv3Feeds.energyConsumptionTxBestGuessWh, input.sort[1]),
                )
                break

              case 'energy_tx_node':
                orderQuery.push(
                  sql`CASE
                    WHEN ${tsdbVwEsgv3Feeds.energyConsumptionTxNodeBestGuessWh} = 'NaN' THEN 1
                    WHEN ${tsdbVwEsgv3Feeds.energyConsumptionTxNodeBestGuessWh} IS NULL THEN 1
                    WHEN ${tsdbVwEsgv3Feeds.energyConsumptionTxNodeBestGuessWh} = 0 THEN 1
                    ELSE 0
                  END`,
                  dynamicSort(tsdbVwEsgv3Feeds.energyConsumptionTxNodeBestGuessWh, input.sort[1]),
                )
                break

              case 'name':
                orderQuery.push(dynamicSort(asset.name, input.sort[1]))
                break

              default:
                orderQuery.push(
                  sql`CASE
                    WHEN ${tsdbVwEsgv3Feeds.poweruseBestGuessW} = 'NaN' THEN 1
                    WHEN ${tsdbVwEsgv3Feeds.poweruseBestGuessW} IS NULL THEN 1
                    WHEN ${tsdbVwEsgv3Feeds.poweruseBestGuessW} = 0 THEN 1
                    ELSE 0
                  END`,
                  dynamicSort(tsdbVwEsgv3Feeds.poweruseBestGuessW, input.sort[1]),
                )
                break
            }

            return orderQuery
          }),
      )

      const latest = await chatSourceClient.with(queriesSq).select().from(queriesSq)

      return {
        data: latest.map((esg) => ({
          kwh: esg.consumptionKwh ? displayWattage(esg.consumptionKwh) : '-',
          wh_transaction: esg.consumptionWhTx ? displayWattage(esg.consumptionWhTx) + 'h' : '-',
          wh_node_trx: esg.consumptionTxNode ? displayWattage(esg.consumptionTxNode) + 'h' : '-',
          validator: esg.validator ? numericDisplay(esg.validator, 0) : '-',
          tps: esg.tps ? numericDisplay(esg.tps) + ' tps' : '-',
          slug: esg.blockchainId,
          name: esg.assetName,
          logo: esg.logo,
          symbol: esg.symbol,
        })),
        pagination: {
          current: input.page,
          last_page: Math.ceil((latest[0]?.totalCount ?? 0) / 10),
          per_page: 10,
          total_item: latest[0]?.totalCount ?? 0,
        } as PaginationResponse,
      }
    }),
  assets: authorizedProcedure
    .input(
      z.object({
        page: z.number().default(1),
        search: z.string().optional(),
      }),
    )
    .query(async ({ input }) => {
      const pairs = await getEsgCached()
      const whereQuery = and(
        inArray(
          asset.id,
          (pairs ?? []).map((pair) => pair.assetId),
        ),
        input.search ? or(ilike(asset.name, `%${input.search}%`), ilike(asset.symbol, `%${input.search}%`)) : undefined,
      )

      const pagination: PaginationResponse = await chatSourceClient
        .select({
          total: sql<number>`cast(count (*) as int)`,
        })
        .from(asset)
        .where(whereQuery)
        .then((res) => ({
          current: input.page,
          per_page: 10,
          total_item: res[0]?.total ?? 0,
          last_page: Math.ceil(res[0]?.total ?? 0 / 10),
        }))

      const result = await chatSourceClient
        .select()
        .from(asset)
        .innerJoin(esgPairV2, eq(asset.id, esgPairV2.assetId))
        .where(whereQuery)
        .limit(pagination.per_page)
        .offset(pagination.current > 1 ? pagination.current * pagination.per_page : 0)
        .orderBy(...exactOrder(input.search ?? ''), asc(asset.name))

      return {
        data: result.map((dt) => ({
          name: dt.asset.name,
          slug: dt.esg_pair_v2.name,
          symbol: dt.asset.symbol,
          logo: dt.asset.logo,
        })),
        pagination,
      }
    }),
  chart: authorizedProcedure.input(z.string()).query(async ({ input }) => {
    let errMsg: string | undefined

    const pairs = await getEsgCached()
    const target = pairs?.find((data) => data.name === input)
    if (!target) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Target asset not found',
      })
    }

    const feeds = await chatSourceClient.query.asset.findFirst({
      where: and(
        inArray(
          asset.id,
          (pairs ?? []).map((pair) => pair.assetId),
        ),
        eq(asset.id, target?.assetId ?? 0),
      ),
    })

    if (feeds === undefined) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Asset not found',
      })
    }

    const result = await timescaleClient
      .select()
      .from(vwEsgv2)
      .where(
        and(
          eq(vwEsgv2.name, input),
          gte(vwEsgv2.date, '2019-01-01'),
          ne(vwEsgv2.energyConsumptionTxBestGuessWh, 0),
          isNotNull(vwEsgv2.energyConsumptionTxBestGuessWh),
          sql`${vwEsgv2.energyConsumptionTxBestGuessWh} != 'NaN'`,
        ),
      )
      .orderBy(asc(vwEsgv2.date))
      .then((res) =>
        res.reduce(
          (res: esgIndices, data) => {
            const date = new Date(data.date)

            if (res.mechanism === '') {
              res.mechanism = data.source
            }

            if (res.type === '') {
              res.type = data.type
            }

            res.kwh.push([
              date.getTime(),
              { min: data.poweruseLowerW ?? 0, avg: data.poweruseBestGuessW ?? 0, max: data.poweruseUpperW ?? 0 },
            ])
            res.tps.push([date.getTime(), data.throughputTps ?? 0])
            res.validator.push([date.getTime(), data.validators ?? 0])
            res.wh_trx.push([
              date.getTime(),
              {
                min: data.energyConsumptionTxLowerWh ?? 0,
                avg: data.energyConsumptionTxBestGuessWh ?? 0,
                max: data.energyConsumptionTxUpperWh ?? 0,
              },
            ])
            res.wh_trx_node.push([
              date.getTime(),
              {
                min: data.energyConsumptionTxNodeLowerWh ?? 0,
                avg: data.energyConsumptionTxNodeBestGuessWh ?? 0,
                max: data.energyConsumptionTxNodeUpperWh ?? 0,
              },
            ])

            return res
          },
          {
            mechanism: '',
            type: '',
            kwh: [],
            tps: [],
            validator: [],
            wh_trx: [],
            wh_trx_node: [],
          },
        ),
      )

    return {
      warning_message: errMsg,
      info: {
        name: feeds.name,
        slug: target!.name,
        logo: feeds.logo,
        symbol: feeds.symbol,
        consensusMechanism: result.mechanism,
        type: result.type,
      },
      ...result,
    }
  }),
})
