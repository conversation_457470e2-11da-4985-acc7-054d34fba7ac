import { router } from '~/server/trpc/trpc'
import { authorizedProcedure } from '~/server/trpc/procedure/authorized'
import { z } from 'zod'
import { TRPCError } from '@trpc/server'
import { serverSupabaseServiceRole } from '#supabase/server'

// Types
// eslint-disable-next-line @nx/enforce-module-boundaries
import type { Database } from 'app-types/supabase'
import { StripeSession, UserSubscriptions } from '~/server/utils/platform-db/schema'
import { eq, sql } from 'drizzle-orm'
import { jsonExtract } from '~/server/utils/database'
import Stripe from 'stripe'

const runtimeConfig = useRuntimeConfig()

export default router({
  currentSubscription: authorizedProcedure.query(async ({ ctx }) => {
    const client = serverSupabaseServiceRole<Database>(ctx.event)

    const activeSubscriptions = await client
      .from('user_subscriptions')
      .select('*')
      .eq('user_id', ctx.user.profile!.userId!)
      .eq('status', 'active')
      .maybeSingle()

    if (activeSubscriptions.data === null) {
      return null
    }

    const rawData = activeSubscriptions.data?.raw_data as Record<string, any> | null

    const [activePlan, upgradePlan, currentPlan, futurePreferences] = await Promise.all([
      platformClient.query.Pricing.findFirst({
        where: (fields, { eq }) => eq(fields.id, Number(activeSubscriptions.data?.plan_id)),
      }),
      activeSubscriptions.data?.switch_raw_data
        ? platformClient.query.Pricing.findFirst({
            where: (fields, { eq }) =>
              eq(
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                // @ts-expect-error
                jsonExtract(fields.stripePriceIds, 'product_id'),
                (activeSubscriptions.data?.switch_raw_data as unknown as Record<string, any>)?.plan?.product,
              ),
          })
        : undefined,
      activeSubscriptions.data
        ? client.from('pricings').select('*').eq('id', Number(activeSubscriptions.data.plan_id)).single()
        : undefined,
      rawData?.future_preferences
        ? chatSourceClient.query.asset.findMany({
            columns: {
              name: true,
              logo: true,
              symbol: true,
            },
            where: (fields, { inArray }) => inArray(fields.id, rawData.future_preferences),
          })
        : undefined,
    ])

    return {
      ...activeSubscriptions.data,
      plan: activePlan
        ? {
            id: activePlan.id,
            name: activePlan.name,
            description: activePlan.description,
            monthly_price: activePlan.monthlyPrice,
            annual_price: activePlan.annualPrice,
            stripe_price_ids: activePlan.stripePriceIds,
            features: activePlan.features,
            created_at: activePlan.createdAt,
            is_enabled: activePlan.isEnabled,
            level: activePlan.level,
          }
        : null,
      raw_data: rawData,
      switch_raw_data: activeSubscriptions.data.switch_raw_data
        ? {
            ...(activeSubscriptions.data.switch_raw_data as unknown as { plan: { product: string } }),
            tier_id: upgradePlan?.id ?? null,
          }
        : null,
      features: currentPlan?.data?.features as Record<string, Record<string, string | number | boolean>>,
      future_preferences: futurePreferences,
    }
  }),
  manage: authorizedProcedure.mutation(async ({ ctx }) => {
    const client = serverSupabaseServiceRole<Database>(ctx.event)

    const subscription = await client
      .from('user_subscriptions')
      .select('*')
      .eq('user_id', ctx.user.profile!.userId!)
      .eq('status', 'active')
      .single()

    if (!subscription.data) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Subscription not found.',
      })
    }

    const portalSession = await ctx.stripe.billingPortal.sessions.create({
      customer: (subscription.data.raw_data as { customer_id: string }).customer_id,
      return_url: `${runtimeConfig.public.appUrl}/settings/billing`,
    })

    return {
      manage_url: portalSession.url,
    }
  }),
  subscribe: authorizedProcedure
    .input(
      z.object({
        product_id: z.number(),
        is_annual: z.boolean(),
        asset_id: z.array(z.number()),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      try {
        const product = await platformClient.query.Pricing.findFirst({
          where: (fields, { eq }) => eq(fields.id, input.product_id),
        })

        if (!product) {
          throw new TRPCError({
            code: 'UNPROCESSABLE_CONTENT',
            message: 'Invalid product id.',
          })
        }

        const customer = await ctx.stripe.customers.create({
          email:
            ctx.user.session?.user?.email ??
            (() => {
              throw new TRPCError({ code: 'BAD_REQUEST', message: 'User email is required.' })
            })(),
          test_clock: ctx.stripeTestClock,
          name: ctx.user.profile?.displayName,
        })

        let additionalAsset = 0
        const productRawData: Record<string, unknown> = {
          ...product,
          additional_asset: additionalAsset,
          preferences: input.asset_id,
          is_annual: input.is_annual,
        }
        const stripeProductIds = product.stripePriceIds as unknown as Record<string, string>

        let addonSession: Stripe.Checkout.Session | undefined

        if (typeof product.features?.max_assets === 'number') {
          if (input.asset_id.length > product.features?.max_assets) {
            additionalAsset = input.asset_id.length - product.features?.max_assets

            addonSession = await ctx.stripe.checkout.sessions.create({
              mode: 'subscription',
              client_reference_id: ctx.user.profile!.userId!,
              line_items: [
                {
                  quantity: additionalAsset,
                  price: product.stripePriceIds.addon_price_id as string,
                },
              ],
              allow_promotion_codes: true,
              customer: customer.id,
              billing_address_collection: 'required',
              success_url: `${runtimeConfig.public.appUrl}/settings/billing/subscription-success`,
              cancel_url: `${runtimeConfig.public.appUrl}/api/subscription/cancel?session_id={CHECKOUT_SESSION_ID}&product_id=${input.product_id}`,
            })

            productRawData.addon_session_id = addonSession.id
          }
        }

        const stripeSession = await ctx.stripe.checkout.sessions.create({
          mode: 'subscription',
          client_reference_id: ctx.user.profile!.userId!,
          line_items: [
            {
              price: input.is_annual ? stripeProductIds.annual : stripeProductIds.monthly,
              quantity: 1,
            },
          ],
          allow_promotion_codes: true,
          customer: customer.id,
          billing_address_collection: 'required',
          success_url: addonSession?.url ?? `${runtimeConfig.public.appUrl}/settings/billing/subscription-success`,
          cancel_url: `${runtimeConfig.public.appUrl}/api/subscription/cancel?session_id={CHECKOUT_SESSION_ID}&product_id=${input.product_id}`,
        })

        if (stripeSession) {
          await platformClient.insert(StripeSession).values([
            {
              userId: ctx.user.profile!.userId!,
              sessionId: stripeSession.id,
              productRawData: sql`${{
                ...productRawData,
                mode: 'main',
              }}::jsonb`,
              status: 'pending',
            },
            ...(addonSession?.id
              ? [
                  {
                    userId: ctx.user.profile!.userId!,
                    sessionId: addonSession.id,
                    productRawData: sql`${{
                      mode: 'addon',
                      name: `Additional asset ${additionalAsset}x for ${product.name}`,
                      addon_subscription_price: product.stripePriceIds.addon_price_id,
                      addon_product_id: product.stripePriceIds.addon_product_id,
                      main_checkout_id: stripeSession.id,
                    }}::jsonb`,
                    status: 'pending',
                  },
                ]
              : []),
          ])

          return {
            checkout_url: stripeSession.url,
          }
        } else {
          throw new TRPCError({
            code: 'INTERNAL_SERVER_ERROR',
            message: 'Cannot create checkout session.',
          })
        }
      } catch (e) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Cannot create checkout session.',
        })
      }
    }),
  switchPlan: authorizedProcedure
    .input(
      z.object({
        product_id: z.number(),
        is_annual: z.boolean(),
        assets: z.array(z.number()).default([]),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const product = await platformClient.query.Pricing.findFirst({
        where: (fields, { eq }) => eq(fields.id, input.product_id),
      })

      if (!product) {
        throw new TRPCError({
          code: 'UNPROCESSABLE_CONTENT',
          message: 'Invalid product id.',
        })
      }

      const subscription = await platformClient.query.UserSubscriptions.findFirst({
        where: (fields, { and, eq }) => and(eq(fields.status, 'active'), eq(fields.userId, ctx.user.profile!.userId!)),
      })

      if (!subscription) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Subscription not found.',
        })
      }

      interface SubscriptionRawData {
        subscription_id: string
        customer_id: string
        addon_subscription_id?: string
        future_addon_subscription_id?: string
        last_event_creation?: number
        future_preferences?: number[]
      }

      const subsRawData = subscription.rawData as unknown as SubscriptionRawData

      const addonSubscriptionid = subsRawData.future_addon_subscription_id ?? subsRawData.addon_subscription_id

      const productStripePriceIds = product.stripePriceIds as Record<string, string>
      const [retrieveSubscription, retrieveAddonSubscription] = await Promise.all([
        ctx.stripe.subscriptions.retrieve(subsRawData.subscription_id),
        addonSubscriptionid ? ctx.stripe.subscriptions.retrieve(addonSubscriptionid) : undefined,
      ])

      if (!retrieveSubscription) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Subscription not found.',
        })
      }

      if (input.assets.length > 0) {
        await platformClient
          .update(UserSubscriptions)
          .set({
            rawData: sql`${{
              ...subscription.rawData,
              future_preferences: input.assets,
            }}::jsonb`,
          })
          .where(eq(UserSubscriptions.id, subscription.id))
      }

      let additionalAsset = 0

      if (typeof product.features?.max_assets === 'number') {
        additionalAsset = input.assets.length - product.features?.max_assets
      }

      const portalConfig = await ctx.stripe.billingPortal.configurations.create({
        features: {
          payment_method_update: {
            enabled: true,
          },
          subscription_cancel: {
            enabled: retrieveAddonSubscription?.id !== undefined && additionalAsset <= 0,
            mode: 'at_period_end',
          },
          subscription_update: {
            enabled: true,
            default_allowed_updates: ['price', 'quantity'],
            proration_behavior: 'none',
            schedule_at_period_end: {},
            products: [
              {
                prices: [input.is_annual ? productStripePriceIds.annual : productStripePriceIds.monthly] as string[],
                product: productStripePriceIds.product_id as string,
              },
              {
                prices: [product.stripePriceIds.addon_price_id] as string[],
                product: product.stripePriceIds.addon_product_id as string,
              },
            ],
          },
        },
      })

      if (!portalConfig.id) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Cannot create billing portal configuration.',
        })
      }

      let addonSession: Stripe.Response<Stripe.BillingPortal.Session> | Stripe.Checkout.Session | undefined
      // If user doesn't have addon subscription and need additional asset, just create a new subscription
      if (additionalAsset > 0 && !retrieveAddonSubscription) {
        addonSession = await ctx.stripe.checkout.sessions.create({
          mode: 'subscription',
          client_reference_id: ctx.user.profile!.userId!,
          line_items: [
            {
              quantity: additionalAsset,
              price: product.stripePriceIds.addon_price_id as string,
            },
          ],
          subscription_data: {
            billing_cycle_anchor: (() => {
              const rawData = subscription.rawData as SubscriptionRawData | null
              if (rawData?.last_event_creation !== undefined) {
                return rawData.last_event_creation + 30 * 24 * 60 * 60
              }
              throw new TRPCError({
                code: 'BAD_REQUEST',
                message: 'Invalid subscription raw data.',
              })
            })(),
            proration_behavior: 'none',
          },
          allow_promotion_codes: true,
          customer: subsRawData.customer_id as string,
          billing_address_collection: 'required',
          success_url: `${runtimeConfig.public.appUrl}/settings/billing`,
          cancel_url: `${runtimeConfig.public.appUrl}/settings/billing?checkout=canceled`,
        } as Stripe.Checkout.SessionCreateParams)
      }

      // If user need addon and they have additional asset, just update the subscription
      // Remember quantity should never same like subscription, skip update if quantity still same like previous
      if (
        additionalAsset > 0 &&
        retrieveAddonSubscription &&
        retrieveAddonSubscription.items.data[0]?.quantity !== additionalAsset &&
        retrieveAddonSubscription.canceled_at === null
      ) {
        addonSession = await ctx.stripe.billingPortal.sessions.create({
          customer: subsRawData.customer_id,
          configuration: portalConfig.id,
          flow_data: {
            type: 'subscription_update_confirm',
            subscription_update_confirm: {
              subscription: subsRawData.addon_subscription_id ?? '',
              items: [
                {
                  id: retrieveAddonSubscription.items.data[0]?.id || '',
                  price: product.stripePriceIds.addon_price_id as string,
                  quantity: additionalAsset,
                },
              ],
            },
            after_completion: {
              type: 'redirect',
              redirect: {
                return_url: `${runtimeConfig.public.appUrl}/settings/billing`,
              },
            },
          },
        })
      }

      // Delete existing addon if they don't need additional asset
      if (additionalAsset <= 0 && retrieveAddonSubscription && retrieveAddonSubscription.canceled_at === null) {
        addonSession = await ctx.stripe.billingPortal.sessions.create({
          customer: subsRawData.customer_id,
          configuration: portalConfig.id,
          flow_data: {
            type: 'subscription_cancel',
            subscription_cancel: {
              subscription: retrieveAddonSubscription.id,
            },
            after_completion: {
              type: 'redirect',
              redirect: {
                return_url: `${runtimeConfig.public.appUrl}/settings/billing`,
              },
            },
          },
        })
      }

      const portalSession = await ctx.stripe.billingPortal.sessions.create({
        customer: subsRawData.customer_id,
        configuration: portalConfig.id,
        flow_data: {
          type: 'subscription_update_confirm',
          subscription_update_confirm: {
            subscription: subsRawData.subscription_id,
            items: [
              {
                id: retrieveSubscription.items.data[0]?.id || '',
                price: input.is_annual ? productStripePriceIds.annual : productStripePriceIds.monthly,
                quantity: 1,
              },
            ],
          },
          after_completion: {
            type: 'redirect',
            redirect: {
              return_url: addonSession?.url ? addonSession.url : `${runtimeConfig.public.appUrl}/settings/billing`,
            },
          },
        },
        return_url: addonSession?.url ? addonSession.url : `${runtimeConfig.public.appUrl}/settings/billing`,
      })

      return {
        upgrade_url: portalSession.url,
      }
    }),
  billingHistory: authorizedProcedure.query(async ({ ctx }) => {
    const client = serverSupabaseServiceRole<Database>(ctx.event)

    const { data: billingHistory } = await client
      .from('user_subscriptions')
      .select('*')
      .eq('user_id', ctx.user.profile!.userId!)
      .eq('payment_status', 'paid')

    if (!billingHistory) {
      return []
    }

    const stripeInvoices = await Promise.all(
      billingHistory.map((billing) => {
        const rawData = billing.raw_data as Record<string, unknown>

        return ctx.stripe.invoices.list({
          subscription: rawData.subscription_id as string,
          status: 'paid',
        })
      }),
    )

    const billingList = await Promise.all(
      stripeInvoices.flatMap((invoice) =>
        invoice.data.map(async (subsInvoice) => {
          const charge = await ctx.stripe.charges.retrieve(subsInvoice.charge!.toString())
          const periodLabel = subsInvoice.lines.data[0]?.plan?.interval === 'month' ? 'Monthly' : 'Annual'

          return {
            id: subsInvoice.lines.data[0]?.subscription || null,
            description: `Nodiens ${subsInvoice.lines.data[0]?.price?.metadata?.plan_name} (${periodLabel})`,
            amount: `${(subsInvoice.amount_paid / 100).toFixed(2)}/${periodLabel}`,
            date: subsInvoice.status_transitions.paid_at! * 1000,
            invoice_url: charge.receipt_url,
          }
        }),
      ),
    )

    return billingList
  }),
})
