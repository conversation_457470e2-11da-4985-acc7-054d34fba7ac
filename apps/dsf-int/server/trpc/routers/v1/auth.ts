// eslint-disable-next-line @nx/enforce-module-boundaries
import type { Database } from 'app-types/supabase'
import { z } from 'zod'
import { serverSupabaseClient } from '#supabase/server'

import { router } from '@/server/trpc/trpc'
import { authorizedProcedure } from '~/server/trpc/procedure/authorized'
import { toSnakeCase } from '~/helper/utilities-helper'

export default router({
  profile: authorizedProcedure.query(async ({ ctx }) => {
    return {
      ...toSnakeCase(ctx.user.profile),
      excluded_paywall: ctx.excludedPaywall,
    }
  }),
  updateProfile: authorizedProcedure
    .input(
      z.object({
        firstName: z.string().optional(),
        lastName: z.string().optional(),
        company: z.string().optional(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const client = await serverSupabaseClient<Database>(ctx.event)

      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const [_, { data }] = await Promise.all([
        client.auth.updateUser({
          data: {
            display_name: `${input.firstName} ${input.lastName}`,
            first_name: input.firstName,
            last_name: input.lastName,
            company: input.company,
          },
        }),
        client
          .from('users')
          .update({
            display_name: `${input.firstName} ${input.lastName}`,
            company: input.company,
            first_name: input.firstName,
            last_name: input.lastName,
          })
          .eq('user_id', ctx.user.profile?.userId ?? '-1')
          .select()
          .single(),
      ])

      return data
    }),
})
