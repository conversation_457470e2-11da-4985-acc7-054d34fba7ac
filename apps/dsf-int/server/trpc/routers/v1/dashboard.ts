import { z } from 'zod'
import { and, asc, eq, ilike, inArray, lte, or, sql } from 'drizzle-orm'
// eslint-disable-next-line @nx/enforce-module-boundaries
import type { Database } from 'app-types/supabase'
import { TRPCError } from '@trpc/server'
import { format, sub } from 'date-fns'
import { authorizedProcedure } from '@/server/trpc/procedure/authorized'
import { router } from '@/server/trpc/trpc'
import { timescaleClient } from '~/server/utils/timescale'
import { v1Feeds } from '~/server/utils/timescale/schema/v1-feeds'
import { serverSupabaseClient } from '#supabase/server'
import { indice } from '~/server/utils/timescale/schema/indicies'
import { cachedGraphIndice } from '~/server/utils/trpc/v1/timeseries/summary'
import { setFloatMaxFractional } from '~/helper/number-helper'

export default router({
  searchFeeds: authorizedProcedure
    .input(
      z.object({
        platforms: z.array(z.string()).optional().default(['telegram', 'reddit']),
        searchTerm: z.string(),
        page: z.number().default(1),
      }),
    )
    .query(async ({ input }) => {
      if (input.platforms.length === 0) {
        input.platforms = ['telegram', 'reddit']
      }

      const count = await timescaleClient
        .select({
          total: sql<number>`count(*)`,
        })
        .from(v1Feeds)
        .where(
          and(
            or(ilike(v1Feeds.name, `%${input.searchTerm}%`), ilike(v1Feeds.symbol, `%${input.searchTerm}%`)),
            inArray(v1Feeds.platform, input.platforms),
            eq(v1Feeds.is_publishable, true),
          ),
        )

      const perPage = 20
      const totalPage = Math.ceil(count[0]?.total || 0 / perPage)
      const offsetItem = (input.page - 1) * perPage

      const feeds = await timescaleClient.query.v1Feeds.findMany({
        columns: {
          is_publishable: false,
        },
        where: and(
          or(ilike(v1Feeds.name, `%${input.searchTerm}%`), ilike(v1Feeds.symbol, `%${input.searchTerm}%`)),
          inArray(v1Feeds.platform, input.platforms),
          eq(v1Feeds.is_publishable, true),
        ),
        orderBy: asc(v1Feeds.rank),
        offset: offsetItem,
        limit: perPage,
      })

      return {
        feeds,
        pagination: {
          current: input.page,
          lastPage: totalPage,
          perPage,
        },
      }
    }),
  updateFilter: authorizedProcedure
    .input(
      z.object({
        indicators: z.array(z.string()),
        platforms: z.array(z.string()),
        feeds: z.array(
          z.object({
            id: z.string(),
            name: z.string(),
            platform: z.string(),
            platform_id: z.string(),
          }),
        ),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const config = useRuntimeConfig()

      const client = await serverSupabaseClient<Database>(ctx.event)
      const user = await client
        .from('users')
        .select('*')
        .eq('user_id', ctx.user.profile?.id as string)
        .single()

      if (user.data === null) {
        throw new TRPCError({
          message: 'Profile not found',
          code: 'NOT_FOUND',
        })
      }

      if (user.data?.dashboard_changes !== null) {
        if (user.data.dashboard_changes >= config.dashboardChangePerMonth) {
          throw new TRPCError({
            message: 'LIMIT_REACHED',
            code: 'UNPROCESSABLE_CONTENT',
          })
        }
      }

      const increaseCounter = (user.data.dashboard_changes ?? 0) + 1

      const { error } = await client
        .from('users')
        .update({
          dashboard: input,
          dashboard_changes: increaseCounter,
        })
        .eq('user_id', ctx.user.profile?.id as string)

      if (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Error unexpected',
          cause: error,
        })
      }

      return {
        isChanged: true,
      }
    }),
  filterOptions: authorizedProcedure.query(async ({ ctx }) => {
    const client = await serverSupabaseClient<Database>(ctx.event)
    const config = useRuntimeConfig()

    const { data } = await client
      .from('users')
      .select('*')
      .eq('user_id', ctx.user.profile?.id as string)
      .single()

    if (data === null) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'User not found',
      })
    }

    return {
      filterOptions: {
        platforms: ['telegram', 'reddit'],
        indicators: ['trust_index', 'mood_index'],
      },
      maxDashboardChanges: config.dashboardChangePerMonth,
      availableDashboardChanges: data.dashboard_changes ? config.dashboardChangePerMonth - data.dashboard_changes : 0,
    }
  }),
  feeds: authorizedProcedure.query(async ({ ctx }) => {
    const client = await serverSupabaseClient<Database>(ctx.event)
    const { data } = await client
      .from('users')
      .select('*')
      .eq('user_id', ctx.user.profile?.id as string)
      .single()

    if (data === null) {
      throw new TRPCError({
        message: 'User not found',
        code: 'NOT_FOUND',
      })
    }

    const maxTimestampResult = await timescaleClient
      .select({
        timestamp: sql<Date>`max(timestamp)`,
      })
      .from(indice)
    const maxTimestamp = maxTimestampResult[0] ? maxTimestampResult[0].timestamp.toISOString().substring(0, 10) : ''
    const twoWeekEarlier = maxTimestampResult[0]
      ? sub(maxTimestampResult[0].timestamp, { days: 60 }).toISOString().substring(0, 10)
      : ''

    const latestCalculation = {
      dateCalculation: maxTimestamp,
      updatedAt: format(maxTimestampResult[0]?.timestamp ?? new Date(), 'yyyy-MM-dd'),
    }
    interface Dashboard {
      feeds: { id: string; name: string; platform: string; platform_id: string }[]
    }
    const dashboard = data.dashboard as unknown as Dashboard
    const chatGroups = dashboard.feeds.map((feed: { id: string }) => feed.id) as string[]

    const feedIndices = await timescaleClient
      .select({
        platform: v1Feeds.platform,
        platform_id: v1Feeds.platform_id,
        cmc_id: v1Feeds.cmc_id,
        name: v1Feeds.name,
        symbol: v1Feeds.symbol,
        logo: v1Feeds.logo,
        rank: v1Feeds.rank,
        asset_type: v1Feeds.asset_type,
        slug: v1Feeds.slug,
      })
      .from(v1Feeds)
      .where(
        and(
          chatGroups.length > 0 ? inArray(v1Feeds.platform_id, chatGroups) : lte(v1Feeds.rank, 10),
          eq(v1Feeds.is_publishable, true),
        ),
      )
      .orderBy(asc(v1Feeds.rank))

    const indices = await cachedGraphIndice(
      twoWeekEarlier,
      maxTimestamp,
      feedIndices.map((data) => data.platform_id),
    )

    const feeds = feedIndices.map((feed) => {
      const _indices = (indices ?? []).filter((data) => data.platform_id === feed.platform_id)

      if (_indices.length > 0) {
        const todayIndex = _indices.length - 1
        const yesterdayIndex = _indices.length - 2

        return {
          ...feed,
          graph: _indices,
          total_message: setFloatMaxFractional(
            _indices.slice(0, 13).reduce((sum, data) => (sum = sum + (data.avg_msgs ?? 0)), 0),
            0,
          ).toLocaleString(),
          changed_trust: `${setFloatMaxFractional((((_indices[todayIndex]?.trust_index ?? 0) - (_indices[yesterdayIndex]?.trust_index ?? 0)) / (_indices[yesterdayIndex]?.trust_index ?? 1)) * 100, 3).toLocaleString()} %`,
          changed_mood: `${setFloatMaxFractional((((_indices[todayIndex]?.mood_index ?? 0) - (_indices[yesterdayIndex]?.mood_index ?? 0)) / (_indices[yesterdayIndex]?.mood_index ?? 1)) * 100, 3).toLocaleString()} %`,
          community_size: _indices[todayIndex]?.community_size?.toLocaleString() || undefined,
          mood_index: setFloatMaxFractional(_indices[todayIndex]?.mood_index ?? 0, 3).toLocaleString() || '',
          trust_index: setFloatMaxFractional(_indices[todayIndex]?.trust_index ?? 0, 3).toLocaleString() || '',
        }
      } else {
        return {
          ...feed,
          graph: [],
          total_message: '-',
          changed_trust: '-',
          changed_mood: '-',
          community_size: '-',
          mood_index: '-',
          trust_index: '-',
        }
      }
    })

    return {
      latestCalculation,
      feeds,
    }
  }),
})
