import { TRPCError } from '@trpc/server'
import { z } from 'zod'
import { router } from '@/server/trpc/trpc'
import { authorizedProcedure } from '@/server/trpc/procedure/authorized'
import {
  cachedRedditStatsSummary,
  cachedSumIndice,
  cachedTelegramStatsSummary,
} from '~/server/utils/trpc/v1/timeseries/summary'

export default router({
  summary: authorizedProcedure.input(z.string()).query(async ({ input }) => {
    try {
      const timeseries = await cachedSumIndice(input)
      return timeseries
    } catch (error) {
      if (error instanceof Error) {
        if (error.message === 'FEEDS_NOT_FOUND') {
          throw new TRPCError({ code: 'NOT_FOUND' })
        } else {
          throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR' })
        }
      }

      return
    }
  }),
  redditStatistic: authorizedProcedure.input(z.string()).query(async ({ input }) => {
    const statistic = await cachedRedditStatsSummary(input)
    return statistic
  }),
  telegramStatistic: authorizedProcedure.input(z.string()).query(async ({ input }) => {
    const statistic = await cachedTelegramStatsSummary(input)
    return statistic
  }),
})
