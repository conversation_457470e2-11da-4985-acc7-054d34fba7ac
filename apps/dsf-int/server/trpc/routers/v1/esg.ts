import { and, asc, eq, sql, desc, ilike, or, SQL } from 'drizzle-orm'
import { z } from 'zod'
import { TRPCError } from '@trpc/server'
import { router } from '@/server/trpc/trpc'
import { authorizedProcedure } from '@/server/trpc/procedure/authorized'
import { timescaleClient } from '~/server/utils/timescale'
import { esgTimeseries } from '~/server/utils/timescale/schema'
import { titleCase } from '~/helper/string-helper'
import { displayWattage, numericDisplay, setFloatMaxFractional } from '~/helper/number-helper'
import { asset } from '~/server/utils/chat-source/schema'
import type { PaginationResponse } from '~/types/appTypes'
import { tsdbVwEsgv2 } from '~/server/utils/chat-source/schema/tsdb_vw_esgv2'
import { esgPairV2 } from '~/server/utils/chat-source/schema/esg_pair_v2'

interface GraphTimestamp {
  [key: number]: {
    upper_kwh?: number | null
    upper_wh_tx?: number | null
    upper_wh_node?: number | null
    upper_wh_node_trx?: number | null
    lower_kwh?: number | null
    lower_wh_tx?: number | null
    lower_wh_node?: number | null
    lower_wh_node_trx?: number | null
    avg_wh_tx?: number | null
    avg_wh_node?: number | null
    avg_wh_node_trx?: number | null
    avg_kwh?: number | null
    tps: number | null
    validator: number | null
  }
}

export default router({
  latest: authorizedProcedure
    .input(
      z.object({
        page: z.number().optional().default(1),
        rank: z.enum(['power_cons', 'energy_tx', 'energy_tx_node', 'validators', 'tps']).default('power_cons'),
        terms: z.string().optional(),
      }),
    )
    .query(async ({ input }) => {
      const whereQuery = and(
        sql`${tsdbVwEsgv2.date} = (select max(${tsdbVwEsgv2.date}) from ${tsdbVwEsgv2})`,
        input.terms ? or(ilike(asset.name, `%${input.terms}%`), ilike(asset.symbol, `%${input.terms}%`)) : undefined,
      )

      const queriesSq = chatSourceClient.$with('queriesSq').as(
        chatSourceClient
          .select({
            assetName: asset.name,
            logo: asset.logo,
            symbol: asset.symbol,
            blockchainId: sql<string>`${tsdbVwEsgv2.name}`.as('blockchainId'),
            consumptionKwh: tsdbVwEsgv2.poweruseBestGuessW,
            consumptionWhTx: tsdbVwEsgv2.energyConsumptionTxBestGuessWh,
            consumptionTxNode: tsdbVwEsgv2.energyConsumptionTxNodeBestGuessWh,
            validator: tsdbVwEsgv2.validators,
            tps: tsdbVwEsgv2.throughputTps,
            totalCount: sql<number>`cast (COUNT(*) OVER() as int)`.as('totalCount'),
          })
          .from(tsdbVwEsgv2)
          .innerJoin(esgPairV2, eq(esgPairV2.name, tsdbVwEsgv2.name))
          .innerJoin(asset, eq(asset.id, esgPairV2.assetId))
          .where(whereQuery)
          .limit(10)
          .offset(input.page ? (input.page - 1) * 10 : 0)
          .orderBy(() => {
            const orderQuery: SQL<unknown>[] = []

            if (input.rank === 'power_cons') {
              orderQuery.push(
                sql`
                      CASE
                        WHEN ${tsdbVwEsgv2.poweruseBestGuessW} = 'NaN' THEN 1
                        WHEN ${tsdbVwEsgv2.poweruseBestGuessW} IS NULL THEN 1
                        WHEN ${tsdbVwEsgv2.poweruseBestGuessW} = 0 THEN 1
                        ELSE 0
                        END`,
                asc(tsdbVwEsgv2.poweruseBestGuessW),
              )
            }

            if (input.rank === 'energy_tx') {
              orderQuery.push(
                sql`
                      CASE
                        WHEN ${tsdbVwEsgv2.energyConsumptionTxBestGuessWh} = 'NaN' THEN 1
                        WHEN ${tsdbVwEsgv2.energyConsumptionTxBestGuessWh} IS NULL THEN 1
                        WHEN ${tsdbVwEsgv2.energyConsumptionTxBestGuessWh} = 0 THEN 1
                        ELSE 0
                        END`,
                asc(tsdbVwEsgv2.energyConsumptionTxBestGuessWh),
              )
            }

            if (input.rank === 'energy_tx_node') {
              orderQuery.push(
                sql`
                      CASE
                        WHEN ${tsdbVwEsgv2.energyConsumptionTxNodeBestGuessWh} = 'NaN' THEN 1
                        WHEN ${tsdbVwEsgv2.energyConsumptionTxNodeBestGuessWh} IS NULL THEN 1
                        WHEN ${tsdbVwEsgv2.energyConsumptionTxNodeBestGuessWh} = 0 THEN 1
                        ELSE 0
                        END`,
                asc(tsdbVwEsgv2.energyConsumptionTxNodeBestGuessWh),
              )
            }

            if (input.rank === 'tps') {
              orderQuery.push(
                sql`
                      CASE
                        WHEN ${tsdbVwEsgv2.throughputTps} = 'NaN' THEN 1
                        WHEN ${tsdbVwEsgv2.throughputTps} IS NULL THEN 1
                        WHEN ${tsdbVwEsgv2.throughputTps} = 0 THEN 1
                        ELSE 0
                        END`,
                desc(tsdbVwEsgv2.throughputTps),
              )
            }

            if (input.rank === 'validators') {
              orderQuery.push(
                sql`
                      CASE
                        WHEN ${tsdbVwEsgv2.validators} = 'NaN' THEN 1
                        WHEN ${tsdbVwEsgv2.validators} IS NULL THEN 1
                        WHEN ${tsdbVwEsgv2.validators} = 0 THEN 1
                        ELSE 0
                        END`,
                desc(tsdbVwEsgv2.validators),
              )
            }

            return orderQuery
          }),
      )

      const latest = await chatSourceClient.with(queriesSq).select().from(queriesSq)

      return {
        data: latest.map((esg) => ({
          kwh: esg.consumptionKwh ? displayWattage(esg.consumptionKwh) : '-',
          wh_transaction: esg.consumptionWhTx ? displayWattage(esg.consumptionWhTx) + 'h' : '-',
          wh_node_trx: esg.consumptionTxNode ? displayWattage(esg.consumptionTxNode) + 'h' : '-',
          validator: esg.validator ? numericDisplay(esg.validator, 0) : '-',
          tps: esg.tps ? numericDisplay(esg.tps) + ' tps' : '-',
          slug: esg.blockchainId,
          name: esg.assetName,
          logo: esg.logo,
          symbol: esg.symbol,
        })),
        pagination: {
          current: input.page,
          last_page: latest[0]?.totalCount ?? 0 / 10,
          per_page: 10,
          total_item: latest[0]?.totalCount ?? 0,
        } as PaginationResponse,
      }
    }),
  powerConsumptionGraph: authorizedProcedure.input(z.string()).query(async ({ input }) => {
    // TODO: Collect coin information

    const timeseries = await timescaleClient.query.esgTimeseries.findMany({
      where: eq(esgTimeseries.blockchain_id, input),
      orderBy: asc(esgTimeseries.measured_at),
    })

    if (timeseries.length === 0) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Coin not found',
      })
    }

    const timestamps: GraphTimestamp = {}
    timeseries.forEach((esg) => {
      const key = esg.measured_at.getTime()
      if (timestamps[key] === undefined) {
        timestamps[key] = {
          tps: setFloatMaxFractional(esg.tps ?? 0, 2),
          validator: setFloatMaxFractional(esg.nodeCount ?? 0, 2),
        }
      }

      switch (esg.index_group) {
        case 'avg_consumption':
          timestamps[key].avg_kwh = esg.totalPowerDemand ?? 0
          timestamps[key].avg_wh_node = (esg.consumptionTxNode ?? 0) / (esg.node_power ?? 0)
          timestamps[key].avg_wh_node_trx = (esg.consumptionTxNode ?? 0) / (esg.node_power ?? 0)
          timestamps[key].avg_wh_tx = esg.consumptionTx ?? 0
          break
        case 'lower_consumption':
          timestamps[key].lower_kwh = esg.totalPowerDemand ?? 0
          timestamps[key].lower_wh_node = (esg.consumptionTxNode ?? 0) / (esg.node_power ?? 0)
          timestamps[key].lower_wh_node_trx = (esg.consumptionTxNode ?? 0) / (esg.node_power ?? 0)
          timestamps[key].lower_wh_tx = esg.consumptionTx ?? 0
          break
        case 'upper_consumption':
          timestamps[key].upper_kwh = esg.totalPowerDemand ?? 0
          timestamps[key].upper_wh_node = (esg.consumptionTxNode ?? 0) / (esg.node_power ?? 0)
          timestamps[key].upper_wh_node_trx = (esg.consumptionTxNode ?? 0) / (esg.node_power ?? 0)
          timestamps[key].upper_wh_tx = esg.consumptionTx ?? 0
      }
    })

    return {
      timestamps,
      name: titleCase(timeseries[0]?.blockchain_id ?? 'Unknown'),
    }
  }),
})
