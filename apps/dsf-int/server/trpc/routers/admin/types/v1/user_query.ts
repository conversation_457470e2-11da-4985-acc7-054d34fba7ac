export interface UserQuery {
  [key: string]: unknown
  id: string
  user_id: string
  display_name: string | null
  first_name: string | null
  last_name: string | null
  dashboard: {
    feeds: unknown[] // Adjust type based on expected feed objects
    platforms: string[]
    indicators: string[]
  }
  dashboard_changes: number
  suspended_reason: string | null
  suspended_until: string | null
  deleted_at: string | null
  profile_role: 'admin' | 'super_admin' | 'user'
  role: string
  status: string
  raw_onboarding: Record<string, unknown> // Adjust type based on expected structure
  company: string | null
  hashpack_account_id: string | null
  web3_auth: string | null
  updated_at: string
  instance_id: string
  aud: string
  email: string
  encrypted_password: string
  email_confirmed_at: string | null
  invited_at: string | null
  confirmation_token: string | null
  confirmation_sent_at: string | null
  recovery_token: string | null
  recovery_sent_at: string | null
  email_change_token_new: string
  email_change: string
  email_change_sent_at: string | null
  last_sign_in_at: string
  raw_app_meta_data?: {
    provider: string
    providers: string[]
  }
  raw_user_meta_data?: {
    sub: string
    email: string
    display_name: string
    email_verified: boolean
    phone_verified: boolean
  }
  is_super_admin: boolean | null
  created_at: string
  phone: string | null
  phone_confirmed_at: string | null
  phone_change: string
  phone_change_token: string
  phone_change_sent_at: string | null
  confirmed_at: string
  email_change_token_current: string
  email_change_confirm_status: number
  banned_until: string | null
  reauthentication_token: string
  reauthentication_sent_at: string | null
  is_sso_user: boolean
  is_anonymous: boolean
  total: number
}
