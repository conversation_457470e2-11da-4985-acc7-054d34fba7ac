import { add, format, sub } from 'date-fns'
import { and, eq, sql } from 'drizzle-orm'
import { z } from 'zod'
import { generateRandomData } from '~/helper/utilities-helper'
import { adminProcedure } from '~/server/trpc/procedure/admin'
import { router } from '~/server/trpc/trpc'
import { users, UserSubscriptions } from '~/server/utils/platform-db/schema'

const pagination = 5

export default router({
  userSummary: adminProcedure.query<UserSummary>(async () => {
    const [userPlan, userStatus] = await Promise.all([
      platformClient
        .select({
          user_count: sql<number>`cast(count(${users.userId}) as int)`.as('user_count'),
          user_plan: UserSubscriptions.planName,
        })
        .from(users)
        .fullJoin(
          UserSubscriptions,
          and(eq(users.userId, UserSubscriptions.userId), sql`${UserSubscriptions.expiredDate} > CURRENT_DATE`),
        )
        .groupBy(UserSubscriptions.planName),
      platformClient
        .select({
          user_count: sql<number>`cast(count(${users.userId}) as int)`.as('user_count'),
          status: users.status,
        })
        .from(users)
        .groupBy(users.status),
    ])

    return {
      revenue_history: generateRandomData(100).map((d, i) => {
        // Increment dummy data timestamps to simulate historical daily data
        const incrementedTs = Math.floor(sub(new Date(d.time * 1000), { days: 100 - (i + 1) }).getTime() / 1000)
        return [incrementedTs, d.value]
      }),
      total_revenue: 0,
      total_user: userPlan.reduce((res, val) => res + val.user_count, 0),
      user_plans: {
        entry: userPlan.map((d) => ({
          count: d.user_count,
          name: d.user_plan ?? 'Free Tier',
        })),
        total_record: userPlan.reduce((res, val) => res + val.user_count, 0),
      },
      user_status: {
        entry: userStatus.map((d) => ({
          name: d.status,
          count: d.user_count,
        })),
        total_record: userStatus.reduce((res, val) => res + val.user_count, 0),
      },
    }
  }),
  revenueHistory: adminProcedure
    .input(
      z.object({
        page: z.number().default(1),
      }),
    )
    .query<PaginationResponse<UserRevenueHistory>>(async () => {
      return {
        data: [
          {
            name: 'Michael Chen',
            plan: 'Enterprise',
            total_revenue: '$15,000.00',
          },
          {
            name: 'Sophia Rodriguez',
            plan: 'Pro',
            total_revenue: '$490.00',
          },
          {
            name: 'Emma Wilson',
            plan: 'Basic',
            total_revenue: '$147.00',
          },
          {
            name: 'Amanda Lee',
            plan: 'Free Trial',
            total_revenue: '$0.00',
          },
        ],
        page: 1,
        perPage: pagination,
        totalItem: 50,
        totalPage: 5,
      }
    }),
  assetPopularity: adminProcedure
    .input(
      z.object({
        terms: z.string().optional(),
        page: z.number().default(1),
        startDate: z.string().default(format(new Date(), 'yyyy-MM-dd')),
        endDate: z.string().default(format(add(new Date(), { days: 7 }), 'yyyy-MM-dd')),
      }),
    )
    .query<PaginationResponse<AssetPopularity>>(async () => {
      return {
        data: [
          {
            name: 'Bitcoin',
            view: '10k',
            percentage: '10%',
          },
          {
            name: 'Ethereum',
            view: '5k',
            percentage: '5%',
          },
        ],
        page: 1,
        perPage: pagination,
        totalItem: 50,
        totalPage: 5,
      }
    }),
  metricPopularity: adminProcedure.query<MetricPopularity[]>(async () => {
    return [
      {
        group_name: 'Community Index',
        metric: [
          {
            metric_name: 'Mood Index',
            view_count: 100,
            percentage: 0.4,
          },
          {
            metric_name: 'Trust Index',
            view_count: 200,
            percentage: 0.6,
          },
          {
            metric_name: 'Topic Trends',
            view_count: 300,
            percentage: 0.2,
          },
        ],
      },
      {
        group_name: 'Climate Index',
        metric: [
          {
            metric_name: 'Power Use',
            view_count: 100,
            percentage: 0.4,
          },
          {
            metric_name: 'Energy Cons/Tx',
            view_count: 200,
            percentage: 0.6,
          },
          {
            metric_name: 'Energy Const/Tx/Node',
            view_count: 300,
            percentage: 0.2,
          },
        ],
      },
      {
        group_name: 'Financial Indices',
        metric: [
          {
            metric_name: 'Price',
            view_count: 100,
            percentage: 0.4,
          },
          {
            metric_name: 'DEX Volume (24h)',
            view_count: 200,
            percentage: 0.6,
          },
        ],
      },
      {
        group_name: 'Decentralisation Indices',
        metric: [
          {
            metric_name: 'IP Auth Distr (GINI)',
            view_count: 100,
            percentage: 0.4,
          },
          {
            metric_name: 'Consensus Power Conc (HHI)',
            view_count: 200,
            percentage: 0.6,
          },
        ],
      },
    ]
  }),
})
