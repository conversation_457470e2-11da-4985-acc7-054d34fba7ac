import { TRPCError } from '@trpc/server'
import { and, eq, ilike, inArray, or, SQL, sql } from 'drizzle-orm'
import { z } from 'zod'
import { adminProcedure } from '~/server/trpc/procedure/admin'
import { router } from '~/server/trpc/trpc'
import { users, vwUser } from '~/server/utils/platform-db/schema'

const PER_PAGE = 10

export default router({
  list: adminProcedure
    .input(
      z.object({
        page: z.number().default(1),
        column: z.enum(['userId', 'role', 'name', 'company', 'email']).default('userId'),
        order: z.enum(['asc', 'desc']).default('desc'),
        searchTerm: z.string().optional(),
        excludedStatus: z.boolean().default(true),
      })
    )
    .query<PaginationResponse<ExcludedPaywalls>>(async ({ input }) => {
      const user = await platformClient
        .select({
          userId: vwUser.userId,
          role: vwUser.role,
          firstName: vwUser.firstName,
          lastName: vwUser.lastName,
          company: vwUser.company,
          email: vwUser.email,
          totalCount: sql<number>`cast (COUNT(*) OVER() as int)`.as('totalCount'),
        })
        .from(vwUser)
        .where(
          and(
            input.searchTerm
              ? or(ilike(vwUser.displayName, `%${input.searchTerm}%`), ilike(vwUser.email, `%${input.searchTerm}%`))
              : undefined,
            eq(vwUser.excludedPaywall, input.excludedStatus)
          )
        )
        .orderBy(() => {
          const orderQuery: SQL<unknown>[] = []

          switch (input.column) {
            case 'company':
              orderQuery.push(dynamicSort(vwUser.company, input.order))
              break

            case 'email':
              orderQuery.push(dynamicSort(vwUser.email, input.order))
              break

            case 'role':
              orderQuery.push(dynamicSort(vwUser.role, input.order))
              break

            case 'name':
              orderQuery.push(dynamicSort(vwUser.firstName, input.order))
              break

            default:
              orderQuery.push(dynamicSort(vwUser.userId, input.order))
              break
          }

          return orderQuery
        })
        .limit(PER_PAGE)
        .offset((input.page - 1) * PER_PAGE)

      return {
        page: input.page,
        perPage: PER_PAGE,
        totalItem: user[0]?.totalCount ?? 0,
        totalPage: Math.ceil((user[0]?.totalCount ?? 0) / PER_PAGE),
        data: user.map(d => ({
          company: d.company,
          email: d.email,
          name: `${d.firstName} ${d.lastName}`,
          role: d.role,
          userId: d.userId,
        })),
      }
    }),

  assign: adminProcedure.input(z.array(z.string())).mutation(async ({ input }) => {
    const checkUsers = await platformClient.query.vwUser.findMany({
      where: (fields, { inArray, and, eq }) => and(inArray(fields.userId, input), eq(fields.excludedPaywall, false)),
    })

    if (checkUsers.length !== input.length) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Some of user was not found',
      })
    }

    await platformClient
      .update(users)
      .set({
        excludedPaywall: true,
      })
      .where(inArray(users.userId, input))

    return {
      success: 'All user has excluded from paywall',
    }
  }),

  unassign: adminProcedure.input(z.array(z.string())).mutation(async ({ input }) => {
    const user = await platformClient.query.vwUser.findMany({
      where: (fields, { and, inArray }) => and(inArray(fields.userId, input), eq(fields.excludedPaywall, true)),
    })

    if (user.length === 0) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'User not found',
      })
    }

    await platformClient
      .update(users)
      .set({
        excludedPaywall: false,
      })
      .where(inArray(users.userId, input))

    return {
      success: 'User has unexcluded from paywall',
    }
  }),
})
