import { z } from 'zod'
import { sql } from 'drizzle-orm'
import { adminProcedure } from '~/server/trpc/procedure/admin'
import { router } from '~/server/trpc/trpc'
import type { User, UserProfile } from '~/server/types/admin/user'
import { platformClient } from '~/server/utils/platform-db'
import type { UserQuery } from '../types/v1/user_query'
import { TRPCError } from '@trpc/server'
import { serverSupabaseServiceRole } from '#supabase/server'
// eslint-disable-next-line @nx/enforce-module-boundaries
import type { Database } from 'app-types/supabase'
import { format } from 'date-fns'

const PAGINATION = 10

const runtimeConfig = useRuntimeConfig()

export default router({
  list: adminProcedure
    .input(
      z.object({
        userType: z.array(z.enum(['user', 'admin'])).default(['user', 'admin']),
        authProvider: z.array(z.string()).default([]),
        company: z.array(z.string()).default([]),
        emailDomain: z.array(z.string()).default([]),
        registrationDate: z.string().optional(),
        status: z.array(z.enum(['active', 'inactive', 'suspend'])).default([]),
        plan: z.array(z.string()).default([]),
        page: z.number().default(1),
      }),
    )
    .query<PaginationResponse<User>>(async ({ input }) => {
      // TODO: Need to refactor to ORM
      let query = `select *, public.users.role as profile_role, (COUNT(*) OVER()) as total from public.users inner join auth.users on auth.users.id = public.users.user_id`

      if (input.userType.length > 0) {
        query = `${query} where public.users.role in (${input.userType.reduce((r, val, i) => {
          if (i === 0) {
            r = `'${val}'`
          } else {
            r = `${r}, '${val}'`
          }

          return r
        }, '')})`
      }

      if (input.status.length > 0) {
        query = `${query} and public.users.status in (${input.status.reduce((r, val, i) => {
          if (i === 0) {
            r = `'${val}'`
          } else {
            r = `${r}, '${val}'`
          }

          return r
        }, '')})`
      }

      const data = await platformClient.execute<UserQuery>(
        sql.raw(`${query} order by auth.users.created_at desc limit 10 offset ${(input.page - 1) * PAGINATION}`),
      )

      return {
        data: data.map((d) => ({
          auth_providers: d.raw_app_meta_data?.providers ?? [],
          email: d.email,
          first_name: d.first_name,
          last_name: d.last_name,
          id: d.id,
          registration_date: d.created_at,
          role: d.profile_role,
          activation_plan_date: null,
          company: d.company,
          expired_plan_date: null,
          hashpack_account: null,
          last_login: null,
          onboarding_status: null,
          plan: null,
          suspended_reason: null,
        })) as User[],
        page: input.page,
        perPage: PAGINATION,
        totalPage: Math.ceil((data[0]?.total ?? 0) / PAGINATION),
        totalItem: Number(data[0]?.total ?? 0),
      }
    }),
  profile: adminProcedure.input(z.string()).query<UserProfile>(async ({ input }) => {
    const data = await platformClient.execute<UserQuery>(sql`
      select *, public.users.role as profile_role from public.users
      inner join auth.users on auth.users.id = public.users.user_id
      where auth.users.id = ${input} limit 1
    `)

    if (data.length > 1 || data.length === 0 || !data[0]) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'USER_NOT_FOUND',
      })
    }

    return {
      auth_providers: data[0].raw_app_meta_data?.providers ?? [],
      email: data[0].email,
      first_name: data[0].first_name,
      last_name: data[0].last_name,
      id: data[0].id,
      registration_date: data[0].created_at,
      role: data[0].profile_role,
      activation_plan_date: null,
      company: data[0].company,
      expired_plan_date: null,
      hashpack_account: null,
      last_login: null,
      onboarding_status: null,
      plan: null,
      suspended_reason: null,
      discounts: [],
      plan_history: [],
      support_logs: [],
    } as UserProfile
  }),
  sendResetPassword: adminProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .mutation<MessageResponse>(async ({ input, ctx }) => {
      const user = await platformClient.query.authUsers.findFirst({
        where: (fields, opts) => opts.eq(fields.id, input.id),
      })

      if (user === undefined) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'USER_NOT_FOUND',
        })
      }

      if (user.email === null) {
        throw new TRPCError({
          code: 'UNPROCESSABLE_CONTENT',
          message: 'EMAIL_NOT_FOUND',
        })
      }

      const serviceRole = serverSupabaseServiceRole(ctx.event)

      const { error } = await serviceRole.auth.resetPasswordForEmail(user.email)

      if (error) {
        throw new TRPCError({
          code: 'UNPROCESSABLE_CONTENT',
          message: error.message,
        })
      }

      return {
        message: 'Reset password success, email will delivered to user email',
      }
    }),
  sendMagicLink: adminProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .mutation<MessageResponse>(async ({ input, ctx }) => {
      const user = await platformClient.query.authUsers.findFirst({
        where: (fields, opts) => opts.eq(fields.id, input.id),
      })

      if (user === undefined) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'USER_NOT_FOUND',
        })
      }

      if (user.email === null) {
        throw new TRPCError({
          code: 'UNPROCESSABLE_CONTENT',
          message: 'EMAIL_NOT_FOUND',
        })
      }

      const serviceRole = serverSupabaseServiceRole(ctx.event)

      const { error } = await serviceRole.auth.signInWithOtp({
        email: user.email,
        options: {
          shouldCreateUser: true,
        },
      })

      if (error) {
        throw new TRPCError({
          code: 'UNPROCESSABLE_CONTENT',
          message: error.message,
        })
      }

      return {
        message: 'Magic link success, email will delivered to user email',
      }
    }),
  deleteUser: adminProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .mutation<MessageResponse>(async ({ input, ctx }) => {
      const user = await platformClient.query.authUsers.findFirst({
        where: (fields, opts) => opts.eq(fields.id, input.id),
      })

      if (user === undefined) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'USER_NOT_FOUND',
        })
      }

      const serviceRole = serverSupabaseServiceRole(ctx.event)
      const { error } = await serviceRole.auth.admin.deleteUser(user.id)

      if (error) {
        throw new TRPCError({
          code: 'UNPROCESSABLE_CONTENT',
          message: error.message,
        })
      }

      return {
        message: 'User deleted',
      }
    }),
  createUser: adminProcedure
    .input(
      z.object({
        role: z.string(),
        first_name: z.string(),
        last_name: z.string(),
        company: z.string(),
        email: z.string().email(),
      }),
    )
    .mutation<MessageResponse>(async ({ input, ctx }) => {
      const serviceRole = serverSupabaseServiceRole<Database>(ctx.event)

      if (runtimeConfig.whitelistEmail) {
        const { data: domains } = await serviceRole.from('email_whitelisting').select()

        if (!domains?.some((record) => input.email.endsWith(record.domain))) {
          throw new TRPCError({
            code: 'UNPROCESSABLE_CONTENT',
            message: 'EMAIL_NOT_WHITELISTED',
          })
        }
      }

      const { error } = await serviceRole.auth.admin.createUser({
        email: input.email,
        user_metadata: {
          display_name: `${input.first_name} ${input.last_name}`,
          first_name: input.first_name,
          last_name: input.last_name,
          company: input.company,
        },
      })

      if (error) {
        throw new TRPCError({
          code: 'UNPROCESSABLE_CONTENT',
          message: error.message,
        })
      }

      return {
        message: 'User created',
      }
    }),
  suspendUser: adminProcedure
    .input(
      z.object({
        userId: z.string(),
        reason: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const serviceRole = serverSupabaseServiceRole<Database>(ctx.event)

      const { data: userProfile } = await serviceRole
        .from('users')
        .select('*')
        .eq('user_id', input.userId)
        .maybeSingle()

      if (userProfile === null) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'USER_NOT_FOUND',
        })
      }

      await Promise.all([
        serviceRole
          .from('users')
          .update({
            suspended_reason: input.reason,
            suspended_at: format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
          })
          .eq('user_id', input.userId)
          .returns(),
        serviceRole.auth.admin.updateUserById(input.userId, {
          user_metadata: {
            suspended: true,
          },
        }),
      ])

      return {
        message: 'User suspended',
      }
    }),
  updateUser: adminProcedure
    .input(
      z.object({
        userId: z.string(),
        firstName: z.string().optional(),
        lastName: z.string().optional(),
        company: z.string().optional(),
        email: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const serviceRole = serverSupabaseServiceRole<Database>(ctx.event)

      const { data: userProfile } = await serviceRole
        .from('users')
        .select('*')
        .eq('user_id', input.userId)
        .maybeSingle()

      if (userProfile === null) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'USER_NOT_FOUND',
        })
      }

      await Promise.all([
        serviceRole
          .from('users')
          .update({
            first_name: input.firstName,
            last_name: input.lastName,
            display_name: `${input.firstName ?? ''} ${input.lastName ?? ''}`,
            company: input.company,
          })
          .eq('user_id', input.userId),
        serviceRole.auth.admin.updateUserById(input.userId, {
          email: input.email,
          user_metadata: {
            display_name: `${input.firstName} ${input.lastName}`,
            company: input.company,
            first_name: input.firstName,
            last_name: input.lastName,
          },
        }),
      ])

      return {
        message: 'User updated',
      }
    }),
})
