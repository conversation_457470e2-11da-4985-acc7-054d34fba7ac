import { TRPCError } from '@trpc/server'
import { middleware } from '~/server/trpc/trpc'

export const adminMiddleware = middleware(async ({ ctx, next }) => {
  if (ctx.user.session === null) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Invalid access, please login.',
    })
  }

  if (ctx.user.profile === null) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Invalid access, please login.',
    })
  }

  if (ctx.user.profile?.role === 'user') {
    throw new TRPCError({
      code: 'UNAUTHORI<PERSON>ED',
      message: 'Invalid access, please try again later.',
    })
  }

  return next()
})
