import { TRPCError } from '@trpc/server'
import { middleware } from '~/server/trpc/trpc'
import { useCsrf } from '~/server/utils/csrf'

export const csrfMiddleware = middleware(async ({ ctx, next }) => {
  const { validateCsrf } = useCsrf()
  const { disableCsrf } = useRuntimeConfig()

  if (ctx.user.profile?.role !== 'user') {
    return next()
  }

  if (ctx.csrfIsServer === 'true') {
    return next()
  }

  if (disableCsrf) {
    return next()
  }

  if (ctx.csrfToken === undefined) {
    throw new TRPCError({
      code: 'UNPROCESSABLE_CONTENT',
      message: 'Invalid CSRF token',
    })
  }

  const verify = validateCsrf(ctx.csrfToken)

  if (verify.is_expired) {
    throw new TRPCError({
      code: 'UNPROCESSABLE_CONTENT',
      message: 'EXPIRED_TOKEN',
    })
  }

  if (verify.error) {
    throw new TRPCError({
      code: 'UNPROCESSABLE_CONTENT',
      message: 'Invalid CSRF token',
      cause: verify.error,
    })
  }

  return next()
})
