// Types
import type { inferRouterInputs, inferRouterOutputs } from '@trpc/server'

/**
 * This is your entry point to setup the root configuration for tRPC on the server.
 * - `initTRPC` should only be used once per app.
 * - We export only the functionality that we use so we can enforce which base procedures should be used
 *
 * Learn how to create protected base procedures and other things below:
 * @see https://trpc.io/docs/v10/router
 * @see https://trpc.io/docs/v10/procedures
 */
import { initTRPC } from '@trpc/server'
import type { Context } from './context'
import type { AppRouter } from './routers'
import type { SnakeCaseKeys } from '~/helper/utilities-helper'

const t = initTRPC.context<Context>().create()

/**
 * Unprotected procedure
 **/
export const publicProcedure = t.procedure
export const router = t.router
export const middleware = t.middleware

export type RouterOutput = inferRouterOutputs<AppRouter>
export type RouterInput = inferRouterInputs<AppRouter>
export type v1AuthProfileType = RouterOutput['v1']['auth']['profile'] & {
  email: string
}
// type v1AuthProfileType with snake_case keys
export type SnakeCaseV1AuthProfileType = SnakeCaseKeys<v1AuthProfileType>

export type v1SearchFeedType = RouterOutput['v1']['dashboard']['searchFeeds']
export type v1FilterOptionsType = RouterOutput['v1']['dashboard']['filterOptions']
export type v1TableFeedsType = RouterOutput['v1']['dashboard']['feeds']
export type v1SummaryData = RouterOutput['v1']['timeSeries']['summary']
export type v1SummaryStatisticReddit = RouterOutput['v1']['timeSeries']['redditStatistic']
export type v1SummaryStatisticTelegram = RouterOutput['v1']['timeSeries']['telegramStatistic']
export type v1PowerConsumptionGraph = RouterOutput['v1']['esg']['powerConsumptionGraph']
export type v1EsgLatest = RouterOutput['v1']['esg']['latest']
export type v1SummaryStatistic = v1SummaryStatisticReddit | v1SummaryStatisticTelegram

export type v2IndicesCharts = RouterOutput['v2']['user']['indices']['charts']
export type v2IndicesTable = RouterOutput['v2']['user']['indices']['table']
export type v2IndicesTableQueryInput = RouterInput['v2']['user']['indices']['table']
export type v2TableDataType = RouterOutput['v2']['public']['dashboard']['table']
export type v2DashboardTableQueryInput = RouterInput['v2']['public']['dashboard']['table']
export type v2TopDailyType = RouterOutput['v2']['public']['dashboard']['topDaily']
export type v2EsgTableQueryInput = RouterInput['v1']['esg']['latest']
export type v2EsgChart = RouterOutput['v2']['user']['esg']['chart']
export type v2TopicTrendChart = RouterOutput['v2']['user']['topic']['chart']
export type v2AssetList = RouterOutput['v2']['user']['asset']['list']
export type v2AssetCoin = RouterOutput['v2']['user']['asset']['list']['data'][0]
export type v2FinanceChartQueryInputSizesEnum = RouterInput['v2']['user']['finance']['chart']['sizes']
export type v2FinanceLineChart = RouterOutput['v2']['user']['finance']['comparison']
// export type v2FinanceLinChartSeriesData = RouterOutput['v2']['user']['finance']['comparison']['data']
export type v2NewsletterGetSubscription = RouterOutput['v2']['public']['newsletter']['getSubscription']
export type v2DecentraliseComparison = RouterOutput['v2']['user']['decentralize']['comparison']
export type v2Pricing = RouterOutput['v2']['public']['pricings']['getPricing'][0]
export type v2CurrentSubscription = RouterOutput['v2']['user']['subscription']['currentSubscription']
export type v2BillingHistory = RouterOutput['v2']['user']['subscription']['billingHistory'][0]
export type v2AssetWithId = RouterOutput['v2']['user']['account']['availableAssets'][0]

export const blankPaginationResponse: PaginationResponse<any> = {
  data: [],
  page: 1,
  perPage: 1,
  totalItem: 0,
  totalPage: 0,
}
