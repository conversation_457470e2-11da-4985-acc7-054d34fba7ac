import type { inferAsyncReturnType } from '@trpc/server'
import type { H3Event } from 'h3'
import { parseStringToObject } from '../utils/string'
import type { FeatureFlag } from '~/stores/feature-flags'
// eslint-disable-next-line @nx/enforce-module-boundaries
import type { Database } from 'app-types/supabase'
import { serverSupabaseClient } from '#supabase/server'
import stripeSdk from 'stripe'
import { Pricing, UserSubscriptions } from '../utils/platform-db/schema'
import { sql, eq, desc, and } from 'drizzle-orm'
import FeatureTier from '../utils/feature'
import { generateStripeTestClock } from '../utils/config'
import Hashids from 'hashids'

/**
 * Creates context for an incoming request
 * @link https://trpc.io/docs/context
 */
export async function createContext(event: H3Event) {
  const { csrfServerHeader, stripeSecretKey, stripeEnableTestClock } = useRuntimeConfig()
  const headers = getRequestHeaders(event)
  const cookies = parseStringToObject(headers.cookie ?? '')
  const stripe = new stripeSdk(stripeSecretKey)
  const client = await serverSupabaseClient<Database>(event)

  const featureFlags: FeatureFlag[] = []

  if (cookies['platform-ff']) {
    featureFlags.push(...JSON.parse(decodeURIComponent(cookies['platform-ff'])))
  }

  const { data: userSession } = await client.auth.getSession()
  const [userProfile, userSubscriptions, stripeTestClock] = await Promise.all([
    userSession.session?.user
      ? platformClient.query.users.findFirst({
          where: (fields, { eq }) =>
            userSession.session?.user?.id ? eq(fields.userId, userSession.session?.user?.id) : undefined,
        })
      : undefined,
    userSession.session?.user?.id
      ? platformClient
          .select()
          .from(UserSubscriptions)
          .innerJoin(Pricing, eq(Pricing.id, sql`(${UserSubscriptions.rawData}->>'product_id')::int`))
          .orderBy(desc(UserSubscriptions.createdAt))
          .where(
            and(eq(UserSubscriptions.status, 'active'), eq(UserSubscriptions.userId, userSession.session?.user?.id)),
          )
          .limit(1)
      : [],
    stripeEnableTestClock ? generateStripeTestClock(stripe) : undefined,
  ])

  const csrfToken: string | undefined = headers['x-csrf']
  const csrfIsServer: string | undefined = headers[csrfServerHeader]

  const excludedPaywall = userProfile?.role === 'admin' || userProfile?.excludedPaywall || false

  return {
    user: {
      ...userSession,
      profile: userProfile,
      feature: new FeatureTier(userSubscriptions[0]?.pricings.features, excludedPaywall),
      subscription: userSubscriptions[0],
    },
    hashids: new Hashids(),
    excludedPaywall,
    stripeTestClock,
    event,
    userData: null as Database['public']['Tables']['users']['Row'] | null,
    featureFlags,
    csrfToken,
    csrfIsServer,
    isAdmin: false,
    supabaseClient: client,
    stripe,
  }
}

export type Context = inferAsyncReturnType<typeof createContext>
