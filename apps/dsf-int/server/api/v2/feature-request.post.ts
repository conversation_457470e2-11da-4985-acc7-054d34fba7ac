import { z } from 'zod'

export default defineEventHandler(async (event) => {
  const { validateLimiter, increaseLimiter } = await useLimitter('feature-request', event, {
    expired_in: 60,
    limit: 5,
  })
  const flagLimit = await validateLimiter()
  if (flagLimit === false) {
    throw createError({
      statusCode: 429,
      message: 'Have to many request, please try again later.',
    })
  }

  const rules = z.object({
    first_name: z.string(),
    last_name: z.string(),
    email: z.string().email(),
    company: z.string().optional(),
    title: z.string(),
    details: z.string(),
  })
  const attachmentRules = z.array(
    z
      .custom<File>()
      .refine(
        (file) =>
          !file ||
          (!!file &&
            ['image/jpeg', 'image/png', 'application/pdf', 'video/mp4', 'video/quicktime'].includes(file.type)),
        {
          message: 'File format not allowed !',
        },
      ),
  )
  const formData = await readFormData(event)
  const payload: Record<string, unknown> = {}
  formData.forEach((value, key) => {
    payload[key] = value
  })

  const form = rules.safeParse(payload)
  const attachments = attachmentRules.safeParse(formData.getAll('files'))

  if (form.success === false) {
    throw createError({
      status: 422,
      statusMessage: 'VALIDATION_FAIL',
      message: 'Validation form fail, please check your submission',
      cause: form.error.issues,
    })
  }

  if (attachments.success === false) {
    throw createError({
      status: 422,
      statusMessage: 'VALIDATION_FAIL',
      message: 'Validation attachments fail, please check your submission',
      cause: attachments.error.issues,
    })
  }

  await Promise.all([
    createSupportMessage(
      {
        title: `Bug Report - ${form.data.first_name} ${form.data.last_name}`,
        browser: null,
        company: form.data.company ?? null,
        email: form.data.email,
        first_name: form.data.first_name,
        last_name: form.data.last_name,
        message: form.data.details,
        os_info: null,
        type: 'feature',
        attachments: attachments.data,
        callback: async () => {
          await sendEmail({
            subject: 'Thank You for Contacting Horizon Risk Labs',
            to: form.data.email,
            html: contactUsTemplate({
              email: form.data.email,
              firstName: form.data.first_name,
              lastName: form.data.last_name,
              message: form.data.details,
              topic: `Feature Request - ${form.data.title}`,
              company: form.data.company ?? '',
            }),
          })
        },
      },
      event,
    ),
    increaseLimiter(),
  ])

  return {
    message:
      'Thank you for your suggestion! Our team will review your feature request and consider it for future updates.',
  }
})
