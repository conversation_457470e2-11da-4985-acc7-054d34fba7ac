import { and, asc, between, eq } from 'drizzle-orm'
import { sub } from 'date-fns'
import { timescaleClient } from '~/server/utils/timescale'
import { cmcPriceDaily } from '~/server/utils/timescale/schema/cmc-price-daily'
import { v1Feeds } from '~/server/utils/timescale/schema/v1-feeds'
import { indice } from '~/server/utils/timescale/schema/indicies'

export default defineEventHandler(async (event) => {
  const feeds = await timescaleClient
    .select()
    .from(v1Feeds)
    .where(eq(v1Feeds.platform_id, event.context.params?.id ?? ''))

  if (feeds.length === 0) {
    return createError({
      statusCode: 404,
      message: 'Platform id not found',
    })
  }

  const timeNow = new Date()
  const minTime = sub(timeNow, { years: 1 })

  const indices = await timescaleClient
    .select({
      community_size: indice.community_size,
      avg_msgs: indice.avg_msgs,
      mood_index: indice.mood_index,
      trust_index: indice.trust_index,
      timestamp: indice.timestamp,
    })
    .from(indice)
    .where(and(eq(indice.platform_id, feeds[0]?.platform_id ?? ''), between(indice.timestamp, minTime, timeNow)))
    .orderBy(asc(indice.timestamp))
  const startTimestamp = indices.length > 0 && indices[0] && indices[0].timestamp ? indices[0].timestamp : new Date(0)
  const endTimestamp =
    indices.length > 0 && indices?.[indices.length - 1] && indices[indices.length - 1]?.timestamp
      ? indices?.[indices.length - 1]?.timestamp
      : new Date()
  const feed = feeds[0]?.cmc_id ?? 0

  const prices = await timescaleClient
    .select({
      close: cmcPriceDaily.close,
      volume: cmcPriceDaily.volume,
      marketcap: cmcPriceDaily.marketcap,
      timestamp: cmcPriceDaily.timestamp,
    })
    .from(cmcPriceDaily)
    .where(
      and(
        eq(cmcPriceDaily.cmc_id, feed ?? null),
        between(cmcPriceDaily.timestamp, startTimestamp ?? new Date(0), endTimestamp ?? new Date(0)),
      ),
    )
    .orderBy(asc(cmcPriceDaily.timestamp))

  const series: any = {}
  const keySeries = feeds[0]?.name ?? '_'
  series[keySeries] = prices.map((price) => {
    const indiceIndex = indices.findIndex((ind) => ind.timestamp?.toDateString() === price.timestamp.toDateString())
    return {
      ...indices[indiceIndex],
      ...price,
    }
  })

  return {
    message: 'success',
    series,
  }
})
