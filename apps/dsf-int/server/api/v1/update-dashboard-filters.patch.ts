// eslint-disable-next-line @nx/enforce-module-boundaries
import type { Database } from 'app-types/supabase'
import { serverSupabaseClient } from '#supabase/server'

export default defineEventHandler(async (event) => {
  const config = useRuntimeConfig()

  const client = await serverSupabaseClient<Database>(event)
  const user = await client.from('users').select('*').eq('user_id', event.context.user.id).single()

  if (user.data === null) {
    throw createError({
      statusCode: 404,
      message: 'User profile not found',
    })
  }

  if (user.data?.dashboard_changes !== null) {
    if (user.data.dashboard_changes >= config.dashboardChangePerMonth) {
      throw createError({
        statusCode: 422,
        message: 'Limit has reached',
      })
    }

    const body = await readBody(event)

    const increaseCounter = user.data.dashboard_changes + 1

    const { error } = await client
      .from('users')
      .update({
        dashboard: body.dashboard,
        dashboard_changes: increaseCounter,
      })
      .match({ id: user.data.id })
      .select()

    if (error) {
      throw createError({
        statusCode: 500,
        message: 'Error unexpected',
      })
    }

    return {
      message: 'success',
      isChanged: true,
    }
  } else {
    return {}
  }
})
