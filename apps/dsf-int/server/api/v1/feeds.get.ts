import { and, eq, inArray, sql } from 'drizzle-orm'
import { format, parse } from 'date-fns'
// eslint-disable-next-line @nx/enforce-module-boundaries
import type { Database } from 'app-types/supabase'
import { v1Feeds } from '~/server/utils/timescale/schema/v1-feeds'
import { indice } from '~/server/utils/timescale/schema/indicies'
import { timescaleClient } from '~/server/utils/timescale'
import { serverSupabaseClient } from '#supabase/server'

export default defineEventHandler(async (event) => {
  const client = await serverSupabaseClient<Database>(event)
  const { data } = await client.from('users').select('*').eq('user_id', event.context.user.id).single()

  if (data === null) {
    throw createError({
      statusCode: 404,
      message: 'User data not found',
    })
  }

  const maxTimestampResult = await timescaleClient
    .select({
      timestamp: sql<Date>`max(timestamp)`,
    })
    .from(indice)
  const maxTimestamp = maxTimestampResult[0]?.timestamp.toISOString().substring(0, 10) || ''

  if (data.dashboard) {
    const latestCalculation = {
      date_calculation: maxTimestampResult[0]?.timestamp.toISOString().substring(0, 10) || '',
      updated_at: format(parse(maxTimestamp, 'yyyy-MM-dd', new Date()), 'T').toString(),
    }
    const chatGroups: string[] = []
    interface Dashboard {
      feeds: { id: string }[]
      // Add other properties of the dashboard object here if needed
    }
    const dashboard = data.dashboard as unknown as Dashboard
    for (const chatGroup of dashboard.feeds) {
      chatGroups.push(chatGroup.id)
    }

    if (chatGroups.length === 0) {
      return {
        message: 'success',
        feeds: [],
        latest_calculation_info: latestCalculation,
      }
    }

    const feedIndices = await timescaleClient
      .select()
      .from(v1Feeds)
      .innerJoin(indice, eq(v1Feeds.platform_id, indice.platform_id))
      .where(and(inArray(v1Feeds.platform_id, chatGroups), sql`${indice.timestamp} = ${maxTimestamp}`))

    const feeds: unknown[] = []
    for (const feedIndice of feedIndices) {
      feeds.push({
        ...feedIndice.indices,
        ...feedIndice.v1_feeds,
      })
    }

    return {
      message: 'success',
      feeds,
      latest_calculation_info: {
        date_calculation: maxTimestampResult[0]?.timestamp.toISOString().substring(0, 10) || '',
        updated_at: format(parse(maxTimestamp, 'yyyy-MM-dd', new Date()), 'T').toString(),
      },
    }
  } else {
    return {
      message: 'success',
      feeds: [],
    }
  }
})
