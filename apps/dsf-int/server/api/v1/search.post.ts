import { and, desc, ilike, inArray } from 'drizzle-orm'
import { v1Feeds } from '~/server/utils/timescale/schema/v1-feeds'
import { timescaleClient } from '~/server/utils/timescale'

export default defineEventHandler(async (event) => {
  const body = await readBody(event)

  if (body.platforms === undefined || body.platforms.length === 0)
    {
      body.platforms = ['telegram', 'reddit']
    }

  const query = timescaleClient.select().from(v1Feeds)
    .where(and(
      ilike(v1Feeds.name, `%${body.search_term}%`),
      inArray(v1Feeds.platform, body.platforms),
    ))
    .limit(body.limit)
    .offset(body.offset)

  const results = await query.orderBy(desc(v1Feeds.cmc_id))

  return {
    message: 'success',
    results,
  }
})
