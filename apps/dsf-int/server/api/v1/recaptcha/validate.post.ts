interface RecaptchaPayload {
  'success': true | false
  'challenge_ts': string // timestamp of the challenge load (ISO format yyyy-MM-dd'T'HH:mm:ssZZ)
  'hostname': string // the hostname of the site where the reCAPTCHA was solved
  'error-codes': [] // optional
}

export default defineEventHandler(async (event) => {
  const config = useRuntimeConfig()

  const body = await readBody(event)

  if (body.recaptcha_response === undefined || body.recaptcha_response.length === 0) {
    throw createError({
      statusCode: 200,
      message: 'Error while validating client recaptcha response.',
    })
  }

  const result = await $fetch<RecaptchaPayload>('https://www.google.com/recaptcha/api/siteverify', {
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    body: `secret=${config.recaptchaSecret}&response=${body.recaptcha_response}`,
  })

  if (result.success === true) {
    return {
      message: 'success',
    }
  }
  else {
    return {
      error: {
        message: 'Error while validating client recaptcha response.',
      },
    }
  }
})
