// eslint-disable-next-line @nx/enforce-module-boundaries
import type { Database } from 'app-types/supabase'
import { serverSupabaseClient } from '#supabase/server'

export default defineEventHandler(async (event) => {
  const client = await serverSupabaseClient<Database>(event)
  const body = await readBody(event)

  const validate = await client.from('users').select().eq('user_id', event.context.user.id).single()
  if (validate.data === null) {
    throw createError({
      message: 'Profile not exists',
      statusCode: 422,
    })
  }

  const { error, data } = await client
    .from('users')
    .update({
      display_name: body.display_name,
    })
    .eq('id', validate.data.id)
    .select()

  if (error) {
    throw createError({
      message: 'Insert fail',
      statusCode: 422,
    })
  }

  return {
    message: 'success',
    user: data,
  }
})
