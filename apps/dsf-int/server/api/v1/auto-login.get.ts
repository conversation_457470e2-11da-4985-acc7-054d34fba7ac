// eslint-disable-next-line @nx/enforce-module-boundaries
import type { Database } from 'app-types/supabase'
import { serverSupabaseClient } from '#supabase/server'

export default defineEventHandler(async (event) => {
  const client = await serverSupabaseClient<Database>(event)

  const { data } = await client.from('users').select('*').eq('user_id', event.context.user.id).single()

  return {
    message: 'success',
    user: data,
  }
})
