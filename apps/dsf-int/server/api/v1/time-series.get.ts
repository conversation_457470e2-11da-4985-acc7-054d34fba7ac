// eslint-disable-next-line @nx/enforce-module-boundaries
import type { Database } from 'app-types/supabase'
import { inArray, sql } from 'drizzle-orm'
import { format, subDays } from 'date-fns'
import { v1Feeds } from '~/server/utils/timescale/schema/v1-feeds'
import { indice } from '~/server/utils/timescale/schema/indicies'
import { timescaleClient } from '~/server/utils/timescale'
import { serverSupabaseClient } from '#supabase/server'

export default defineEventHandler(async (event) => {
  const client = await serverSupabaseClient<Database>(event)
  const { data } = await client.from('users').select('*').eq('user_id', event.context.user.id).single()

  if (data === null) {
    throw createError({
      statusCode: 404,
      message: 'User data not found',
    })
  }

  if (data.dashboard) {
    const chatGroups: string[] = []
    interface Dashboard {
      feeds: { id: string }[]
    }
    const dashboard = data.dashboard as unknown as Dashboard
    for (const chatGroup of dashboard.feeds) {
      chatGroups.push(chatGroup.id)
    }

    if (chatGroups.length === 0) {
      return {
        message: 'success',
        series: [],
      }
    }

    const query = timescaleClient.select().from(v1Feeds).where(inArray(v1Feeds.platform_id, chatGroups))

    const feeds = await query
    const series: { [key: string]: unknown[] } = {}

    const systemDate = new Date()

    const endDate = format(systemDate, 'yyyy-MM-dd')
    const startDate = format(subDays(systemDate, 7), 'yyyy-MM-dd')

    for (const feed of feeds) {
      const indices = await timescaleClient
        .select()
        .from(indice)
        .where(sql`timestamp >= ${startDate} AND timestamp <= ${endDate} AND platform_id = ${feed.platform_id}`)
      series[feed.platform_id] = indices
    }

    return {
      message: 'success',
      series,
    }
  } else {
    return {
      message: 'success',
      series: [],
    }
  }
})
