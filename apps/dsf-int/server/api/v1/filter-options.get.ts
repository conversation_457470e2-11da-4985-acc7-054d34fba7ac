// eslint-disable-next-line @nx/enforce-module-boundaries
import type { Database } from 'app-types/supabase'
import { serverSupabaseClient } from '#supabase/server'

export default defineEventHandler(async (event) => {
  const client = await serverSupabaseClient<Database>(event)
  const config = useRuntimeConfig()

  const { data } = await client.from('users').select('*').eq('user_id', event.context.user.id).single()

  if (data === null) {
    throw createError({
      statusCode: 404,
      message: 'User data not found',
    })
  }

  return {
    message: 'success',
    filter_options: {
      platforms: ['telegram', 'reddit'],
      indicators: ['trust_index', 'mood_index'],
    },
    max_dashboard_changes: config.dashboardChangePerMonth,
    available_dashboard_changes: data.dashboard_changes ? config.dashboardChangePerMonth - data.dashboard_changes : 0,
  }
})
