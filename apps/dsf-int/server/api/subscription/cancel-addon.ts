import { platformClient } from '~/server/utils/platform-db'
import { StripeSession, UserSubscriptions } from '~/server/utils/platform-db/schema'
import { and, eq, sql } from 'drizzle-orm'
import { jsonExtract } from '~/server/utils/database'

const runtimeConfig = useRuntimeConfig()

export default defineEventHandler(async (event) => {
  const query = getQuery(event)
  const user = event.context.user

  if (!query.session_id) {
    throw createError({
      statusCode: 400,
      message: 'Invalid session id',
    })
  }

  if (!user || !user.profile) {
    throw createError({
      statusCode: 401,
      message: 'Unauthorized',
    })
  }

  const session = await platformClient.query.StripeSession.findFirst({
    where: (fields, { eq, and }) =>
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-expect-error
      and(eq(jsonExtract(fields.productRawData, 'mode'), 'addon'), eq(fields.sessionId, query.session_id!.toString())),
  })

  if (!session) {
    throw createError({
      statusCode: 404,
      message: 'Session not found',
    })
  }

  await platformClient.transaction(async (tx) => {
    const subscription = await tx.query.UserSubscriptions.findFirst({
      where: (fields, { eq, and }) => and(eq(fields.userId, user.profile!.userId), eq(fields.status, 'active')),
    })

    if (!subscription) {
      throw createError({
        statusCode: 400,
        message: "User doesn't have subscription",
      })
    }

    await tx
      .update(UserSubscriptions)
      .set({
        rawData: sql`${{
          ...subscription.rawData,
          future_preferences: session.productRawData?.prev_future_preferences ?? undefined,
          future_addon_session_id: undefined,
        }}::jsonb`,
      })
      .where(
        and(
          eq(UserSubscriptions.id, subscription.id),
          eq(UserSubscriptions.userId, user.profile!.userId),
          eq(UserSubscriptions.status, 'active'),
        ),
      )

    await tx
      .update(StripeSession)
      .set({
        status: 'cancelled',
      })
      .where(eq(StripeSession.id, session.id))
  })

  // Redirect to billing page
  await sendRedirect(event, `${runtimeConfig.public.appUrl}/settings/billing`)
})
