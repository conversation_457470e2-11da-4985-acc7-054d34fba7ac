import { serverSupabaseServiceRole } from '#supabase/server'
import { platformClient } from '~/server/utils/platform-db'

const runtimeConfig = useRuntimeConfig()

export default defineEventHandler(async event => {
  const client = serverSupabaseServiceRole(event)
  const query = getQuery(event)

  if (!query.session_id) {
    throw createError({
      statusCode: 400,
      message: 'Invalid session id.',
    })
  }

  const session = await platformClient.query.StripeSession.findFirst({
    where: (fields, { eq }) => eq(fields.sessionId, query.session_id?.toString() ?? '-'),
  })

  if (!session) {
    throw createError({
      statusCode: 500,
      message: 'Cannot find session.',
    })
  }

  // Update the session canceled_at with date now
  try {
    await client
      .from('stripe_session')
      .update({
        canceled_at: new Date().toISOString(),
        status: 'canceled',
      })
      .eq('session_id', query.session_id.toString())
  } catch (error) {
    console.error('Failed to update session:', error)
    throw createError({
      statusCode: 500,
      message: 'Failed to update session status.',
    })
  }

  // Redirect to billing page
  await sendRedirect(event, `${runtimeConfig.public.appUrl}/settings/billing?checkout=canceled`)
})
