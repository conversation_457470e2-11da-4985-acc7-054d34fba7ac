import { serverSupabaseClient } from '#supabase/server'

export default defineEventHandler(async (event) => {
  if (getRequestURL(event).pathname.startsWith('/api/v1/recaptcha'))
    {return}
  if (getRequestURL(event).pathname.startsWith('/api/v1/auth') || getRequestURL(event).pathname.startsWith('/api/v1/')) {
    const authHeader = event.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('bearer ')) {
      throw createError({
        statusCode: 401,
        message: 'Please sign in',
      })
    }

    const token = authHeader.split(' ')[1]
    const client = await serverSupabaseClient(event)
    const { data, error } = await client.auth.getUser(token)

    if (error) {
      throw createError({
        statusCode: 401,
        message: 'Please sign in',
      })
    }

    if (data === null) {
      throw createError({
        statusCode: 404,
        message: 'Profile not found',
      })
    }
    event.context.user = data.user
  }
})
