export function parseStringToObject(input: string): { [key: string]: string } {
  const result: { [key: string]: string } = {}

  // Remove any trailing semicolon and trim the string
  input = input.trim().replace(/;$/, '')

  // Split the input string by semicolon to get key-value pairs
  const pairs = input.split(';')

  // Iterate over each pair
  pairs.forEach(pair => {
    // Trim the pair to remove any surrounding white spaces
    pair = pair.trim()

    // Split each pair by the equal sign
    const [key, value] = pair.split('=')

    // Add the key-value pair to the result object, after trimming
    if (key && value) {
      result[key.trim()] = value.trim()
    }
  })

  return result
}

export function removeHtmlTags(str: string) {
  return str.replace(/<\/?[^>]+(>|$)/g, '')
}

export function generateRandomString(length: number): string {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''

  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length)
    result += characters.charAt(randomIndex)
  }

  return result
}

export function generateRandomFilename(file: File): string | null {
  const fileName = file.name
  const lastDotIndex = fileName.lastIndexOf('.')

  if (lastDotIndex !== -1 && lastDotIndex < fileName.length - 1) {
    return generateRandomString(32) + '.' + fileName.substring(lastDotIndex + 1)
  } else {
    return null // No extension found or the dot is at the end of the filename
  }
}

export function removeTrailingSlash(url: string) {
  if (url === '/') {
    return url
  }

  return url.replace(/\/$/, '')
}