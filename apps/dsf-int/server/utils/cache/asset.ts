import { add } from 'date-fns'
import { and, eq, inArray } from 'drizzle-orm'
import { asset } from '../chat-source/schema'
import Hashids from 'hashids'

const cacheStorage = useStorage('cache')
const hashids = new Hashids()

function getSelector(source: 'id' | 'price' | 'slug' | 'symbol') {
  switch (source) {
    case 'slug':
      return asset.slug

    case 'price':
      return asset.cmcId

    case 'symbol':
      return asset.symbol

    default:
      return asset.id
  }
}

export async function getCachedAsset(
  keys: number[] | string[],
  source: 'id' | 'price' | 'slug' | 'symbol',
  opt?: {
    inIds: number[]
  },
) {
  const nullKeys: string[] = []
  const res: Record<string, any> = {}
  const currentDate = new Date()

  for (const key of keys) {
    const _key = `asset-${key}-${source}`
    const __key = typeof key === 'number' ? key.toString() : key
    const value = await cacheStorage.getItem<Record<string, any>>(_key)

    if (value) {
      if (currentDate > value.expired_at) {
        res[__key] = null
        await cacheStorage.removeItem(_key)
        nullKeys.push(__key)
      } else {
        res[__key] = value
      }
    } else {
      res[__key] = null
      nullKeys.push(__key)
    }
  }

  if (nullKeys.length > 0) {
    const expiredDate = add(currentDate, { seconds: cacheMaxAgeConfig })

    const assets = await chatSourceClient.query.asset.findMany({
      where: and(
        inArray(getSelector(source), nullKeys),
        opt?.inIds && opt?.inIds.length > 0 ? inArray(asset.id, opt?.inIds) : undefined,
      ),
      with: {
        communities: {
          with: {
            community: true,
          },
        },
      },
    })

    const esgs = await timescaleClient.query.energyConsFeeds.findMany({
      columns: {
        assetId: true,
        platformId: true,
      },
      where: (fields, { inArray }) =>
        inArray(
          fields.assetId,
          assets.map((d) => d.id),
        ),
    })

    for (const asset of assets) {
      const energyConsSlug = esgs.find((entry) => entry.assetId === asset.id)
      let esgSlug: string | null = null

      if (energyConsSlug) {
        const urlIds = hashids.encode(energyConsSlug?.assetId ?? 0, energyConsSlug?.platformId ?? 0)

        esgSlug = `${asset.slug}-${urlIds}`
      }

      const payload = {
        ...asset,
        esgSlug,
      }

      const selector = (() => {
        switch (source) {
          case 'slug':
            return asset.slug

          case 'price':
            return asset.cmcId

          default:
            return asset.id
        }
      })()

      if (source === 'id') {
        res[asset.id] = payload
      }
      if (source === 'price') {
        res[asset.cmcId!] = payload
      }
      if (source === 'slug') {
        res[asset.slug] = payload
      }
      if (source === 'symbol') {
        res[asset.symbol] = payload
      }
      await cacheStorage.setItem(`asset-${selector}-${source}`, {
        ...payload,
        expired_at: expiredDate.getTime(),
      })
    }
  }

  return res
}

export const getStablecoinAsset = cachedFunction(
  async () => {
    const assets = await chatSourceClient.query.asset.findMany({
      where: eq(asset.type, 'STABLECOIN'),
    })

    return assets
  },
  {
    getKey: () => 'stablecoin-asset',
  },
)

export const getActiveStablecoin = cachedFunction(
  async () => {
    const stablecoinPairs = await chatSourceClient.query.stablecoinPair.findMany({
      with: {
        asset: true,
      },
    })

    return stablecoinPairs.map((data) => ({
      id: data.assetId,
      symbol: data.asset.symbol,
      slug: data.asset.slug,
    }))
  },
  {
    getKey: () => 'active-stablecoin',
    maxAge: cacheMaxAgeConfig,
  },
)
