import type { Context } from '~/server/trpc/context'

export default class AssetSelection {
  private requireAddon = false
  private addonEnable = false
  private bypass = false
  private assets: number[] = []

  public static async initialize(trpcContext: Context) {
    const instance = new AssetSelection()

    if (trpcContext) {
      if (trpcContext.user.subscription?.user_subscriptions?.rawData?.require_addon === true) {
        instance.requireAddon = true
      }

      if (trpcContext.user.subscription?.user_subscriptions?.rawData?.addon_enable === true) {
        instance.addonEnable = true
      }
      instance.bypass = trpcContext.excludedPaywall

      if (instance.bypass) {
        return instance
      }
    }

    if (trpcContext?.user.subscription) {
      instance.assets = await platformClient.query.userAssetPreferences
        .findMany({
          where: (fields, { eq }) => eq(fields.userId, String(trpcContext.user.profile?.userId ?? '0')),
        })
        .then((res) => res.map((d) => d.assetId))
    }

    if (trpcContext?.excludedPaywall) {
      instance.assets = []
    }

    return instance
  }

  public get availableAsset() {
    if (this.assets.length > 0) {
      if (this.requireAddon) {
        return this.addonEnable ? this.assets : [-1]
      }
      return this.assets
    } else {
      return undefined
    }
  }
}
