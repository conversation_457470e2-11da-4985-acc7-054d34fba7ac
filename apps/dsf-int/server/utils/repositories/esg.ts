import { and, desc, eq, inArray, sql } from 'drizzle-orm'
import { cmcPriceDaily, esgTimeseries, indiceV2, redditIndicator, telegramIndicator } from '../timescale/schema'
import type { FirstLayerData } from './types/first_layer'
import type { DashboardTable } from '~/server/trpc/routers/v2/public/dashboard'
import { esgPair } from '../chat-source/schema'
import type { PaginationResponse, PaginationType } from '~/types/appTypes'

export async function GetFirstEsg(opt?: { page?: number; perPage?: number; onlyStable?: boolean }) {
  const stablecoinAssets = await getActiveStablecoin()

  let pagination: PaginationType | undefined
  const esgStable: string[] = []

  if (opt?.onlyStable) {
    const pair = await chatSourceClient
      .select()
      .from(esgPair)
      .where(inArray(esgPair.assetId, stablecoinAssets?.map((d) => d.id) ?? []))
    pair.forEach((d) => esgStable.push(d.id))
  }

  if (opt?.onlyStable && esgStable.length === 0) {
    return {
      data: [],
      pagination: {
        total_item: pagination?.totalItem ?? 0,
        last_page: pagination?.totalPage ?? 0,
        current: opt?.page,
        per_page: opt?.perPage,
      } as PaginationResponse,
    }
  }

  if (opt?.page && opt?.perPage) {
    const count = await timescaleClient
      .select({
        total: sql<number>`cast(count(*) as int)`,
      })
      .from(esgTimeseries)
      .where(
        and(
          eq(esgTimeseries.measured_at, sql`(select max(${esgTimeseries.measured_at}) from ${esgTimeseries})`),
          eq(esgTimeseries.index_group, 'avg_consumption'),
          esgStable.length > 0 ? inArray(esgTimeseries.blockchain_id, esgStable) : undefined,
        ),
      )

    pagination = {
      perPage: opt.perPage,
      page: opt.page,
      totalPage: Math.ceil(count[0]?.total || 0 / opt.perPage),
      offset: (opt.page - 1) * opt.perPage,
      totalItem: count[0]?.total || 0,
    }
  }

  const data: FirstLayerData[] = []

  const esg = await timescaleClient
    .select()
    .from(esgTimeseries)
    .where(
      and(
        eq(esgTimeseries.measured_at, sql`(select max(${esgTimeseries.measured_at}) from ${esgTimeseries})`),
        eq(esgTimeseries.index_group, 'avg_consumption'),
        esgStable.length > 0 ? inArray(esgTimeseries.blockchain_id, esgStable) : undefined,
      ),
    )
    .orderBy(desc(esgTimeseries.consumptionTx))
    .offset(pagination?.offset ?? 0)
    .limit(pagination?.perPage ?? 20)
    .then((res) => {
      res.forEach((d) => {
        data.push({
          esg: d,
        })
      })
      return res
    })

  await chatSourceClient.query.esgPair
    .findMany({
      where: inArray(
        esgPair.id,
        esg.map((d) => d.blockchain_id!),
      ),
    })
    .then((pairs) =>
      pairs.forEach((pair) => {
        const findKey = data.findIndex((d) => d.esg?.blockchain_id === pair.id)
        if (findKey > -1) {
          if (data[findKey]) {
            data[findKey].esgPair = pair
          }
        }
      }),
    )

  const communityFlag: Record<string, [string, number][]> = {}
  const assets = await getCachedAsset(
    data.map((d) => d.esgPair?.assetId ?? 0),
    'id',
  )

  for (const key in assets) {
    const findKey = data.findIndex((d) => d.esgPair?.assetId === parseInt(key))
    if (findKey > -1) {
      data[findKey] = {
        ...data[findKey],
        asset: assets[key],
      }
    }

    if (assets[key]) {
      assets[key].communities.forEach((community: any) => {
        const [platform, id] = community.communityId.split('::')
        if (!communityFlag[platform]) {
          communityFlag[platform] = []
        }

        communityFlag[platform].push([id, findKey])
      })
    }
  }

  await Promise.all([
    // Get graph 7 day
    timescaleClient
      .select({
        blockchainId: esgTimeseries.blockchain_id,
        consumption_wh_tx: esgTimeseries.consumptionTx,
        measured_at: esgTimeseries.measured_at,
      })
      .from(esgTimeseries)
      .where(
        and(
          inArray(
            esgTimeseries.blockchain_id,
            data.map((d) => d.asset?.slug ?? ''),
          ),
          sql`(${esgTimeseries.measured_at} <= (select max(${esgTimeseries.measured_at}) from ${esgTimeseries}) and ${esgTimeseries.measured_at} >= ((select max(${esgTimeseries.measured_at}) from ${esgTimeseries}) - interval '7 day'))`,
        ),
      )
      // Grouping graph
      .then((res) => {
        const graph: Record<string, typeof res> = {}

        res.forEach((d) => {
          if (!graph[d.blockchainId!]) {
            graph[d.blockchainId!] = []
          }

          graph[d.blockchainId!]?.push(d)
        })

        return graph
      })
      .then((res) => {
        Object.keys(res).forEach((d) => {
          const findKey = data.findIndex((r) => r.asset?.slug === d)
          if (findKey > -1) {
            data[findKey] = {
              ...data[findKey],
              graph: res[d] && res[d].map((data) => [data.measured_at.getTime(), data.consumption_wh_tx!]),
            }
          }
        })
      }),
    // Get Indices
    timescaleClient
      .select()
      .from(indiceV2)
      .where(
        and(
          inArray(
            indiceV2.assetId,
            data.map((d) => d.asset?.id ?? 0),
          ),
          eq(indiceV2.platform, '_all'),
          sql`${indiceV2.timestamp} = (select max(${indiceV2.timestamp}) from ${indiceV2})`,
        ),
      )
      .then((res) =>
        res.forEach((d) => {
          const findKey = data.findIndex((r) => r.asset?.id === d.assetId)
          if (findKey > -1) {
            data[findKey] = {
              ...data[findKey],
              indice: d,
            }
          }
        }),
      ),
    // Get Prices
    timescaleClient
      .select()
      .from(cmcPriceDaily)
      .where(
        and(
          inArray(
            cmcPriceDaily.cmc_id,
            data.map((d) => d.asset?.cmcId ?? 0),
          ),
          sql`${cmcPriceDaily.timestamp} = (select max(${cmcPriceDaily.timestamp}) from ${cmcPriceDaily})`,
        ),
      )
      .then((res) =>
        res.forEach((d) => {
          const findKey = data.findIndex((r) => r.asset?.cmcId === d.cmc_id)
          if (findKey > -1) {
            data[findKey] = {
              ...data[findKey],
              price: d,
            }
          }
        }),
      ),
    // Get Platform Indicator
    (async () => {
      for (const key in communityFlag) {
        if (key === 'REDDIT') {
          const redditMsg = await timescaleClient
            .select({
              id: redditIndicator.subreddit_id,
              message: redditIndicator.nr_posts,
            })
            .from(redditIndicator)
            .where(
              and(
                sql`${redditIndicator.timestamp} = (select max(${redditIndicator.timestamp}) from ${redditIndicator})`,
                inArray(redditIndicator.subreddit_id, communityFlag[key] ? communityFlag[key].map((d) => d[0]) : []),
              ),
            )

          for (const msg of redditMsg) {
            const findKey = data.findIndex((dp: any) => {
              const t = dp.asset?.communities?.findIndex((dc: any) => dc.communityId.includes(msg.id))
              return t > -1
            })
            if (data[findKey]) {
              data[findKey].message = msg.message ?? undefined
            }
          }
        }
        if (key === 'TELEGRAM') {
          const telegramMsg = await timescaleClient
            .select({
              id: telegramIndicator.tg_chat_id,
              message: telegramIndicator.nr_messages,
            })
            .from(telegramIndicator)
            .where(
              and(
                sql`${telegramIndicator.timestamp} = CURRENT_DATE`,
                inArray(
                  telegramIndicator.tg_chat_id,
                  (communityFlag[key] ?? []).map((d) => parseInt(d[0])),
                ),
              ),
            )

          for (const msg of telegramMsg) {
            const findKey = data.findIndex((dp: any) => {
              const t = dp.asset?.communities?.findIndex((dc: any) => dc.communityId.includes(msg.id))
              return t > -1
            })
            if (data[findKey]) {
              data[findKey].message = msg.message ?? undefined
            }
          }
        }
      }
    })(),
  ])

  return {
    data: data.map((d) => ({
      name: d.asset?.name,
      logo: d.asset?.logo,
      slug: d.asset?.slug,
      price: d.price?.close,
      mood_index: d.indice?.moodIndex,
      trust_index: d.indice?.trustIndex ?? null,
      community_size: d.indice?.communitySize ?? null,
      energy: d.esg?.consumptionTx ?? null,
      number_of_message: d.message ?? null,
      graph: d.graph,
      type: (stablecoinAssets ?? []).reduce((res: DashboardTable[0]['type'], data) => {
        if (res === 'stablecoin') {
          return res
        }

        if (data.id === d.asset?.id) {
          return 'stablecoin'
        }
        return 'cryptocurrency'
      }, 'cryptocurrency'),
    })) as DashboardTable,
    pagination: {
      total_item: pagination?.totalItem ?? 0,
      last_page: pagination?.totalPage,
      current: opt?.page,
      per_page: opt?.perPage,
    } as PaginationResponse,
  }
}
