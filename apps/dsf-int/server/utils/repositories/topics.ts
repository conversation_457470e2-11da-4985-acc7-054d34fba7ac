import { and, asc, desc, inArray, sql } from 'drizzle-orm'
import { wcTopics } from '../timescale/schema'
import type { PaginationResponse } from '~/types/appTypes'

export interface Platform {
  name: string
  value: number
}

function findHighestPlatform(input: string) {
  // Split the input string by comma and remove unnecessary characters
  const platformsString = input.split(',').map((str) => str.replace(/[()]/g, ''))

  // Parse platform objects
  const platforms: Platform[] = platformsString.map((platform) => {
    const parsed = platform.match(/([a-zA-Z]+)(\d+)/)
    const value = parseInt(parsed && parsed[2] ? parsed[2] : '0')
    return { name: parsed && parsed[1] ? parsed[1] : '0', value }
  })

  return platforms.sort((a, b) => b.value - a.value)
}

export async function getTopicTrend() {
  const topics = await timescaleClient
    .select({
      topic: wcTopics.topic,
      platform: sql<string>`STRING_AGG(${wcTopics.platform} ||'(' ||cast(${wcTopics.occurrences} as text)||')' ,',') as platform`,
      occurrences: sql<number>`sum(${wcTopics.occurrences})`,
    })
    .from(wcTopics)
    .where(sql`${wcTopics.date} = (select max(${wcTopics.date}) from ${wcTopics})`)
    .orderBy(desc(sql`sum(${wcTopics.occurrences})`))
    .groupBy(wcTopics.topic)
    .limit(10)

  const history = await timescaleClient
    .select({
      occurrences: sql<number>`cast(sum(${wcTopics.occurrences}) as int) as occurrences`,
      date: wcTopics.date,
      topic: sql<string>`min(${wcTopics.topic})`,
    })
    .from(wcTopics)
    .where(
      and(
        inArray(
          wcTopics.topic,
          topics.map((topic) => topic.topic!),
        ),
        sql`${wcTopics.date} >= (select max(${wcTopics.date}) from ${wcTopics}) - interval '90 day'`,
        sql`${wcTopics.date} <= (select max(${wcTopics.date}) from ${wcTopics})`,
      ),
    )
    .groupBy(wcTopics.date, wcTopics.topic)
    .orderBy(asc(wcTopics.date))

  return {
    data: topics.map((topic) => ({
      topic: topic.topic,
      platform: findHighestPlatform(topic.platform),
      occurrences: topic.occurrences,
      history: history.reduce((res: [number, number][], data) => {
        if (data.topic === topic.topic) {
          res.push([data.date.getTime(), data.occurrences ?? 0])
        }

        return res
      }, []),
    })),
    pagination: {
      current: 0,
      last_page: 0,
      per_page: 0,
      total_item: 0,
    } as PaginationResponse,
  }
}
