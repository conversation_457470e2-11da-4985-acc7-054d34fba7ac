import { sql, and, eq, desc, inArray } from 'drizzle-orm'
import { cmcPriceDaily, esgTimeseries, indiceV2, redditIndicator, telegramIndicator } from '../timescale/schema'
import type { FirstLayerData } from './types/first_layer'
import type { AssetType } from '../chat-source/schema/asset'
import type { DashboardTable } from '~/server/trpc/routers/v2/public/dashboard'
import type { PaginationType, PaginationResponse } from '~/types/appTypes'

export async function GetFirstPrices(opt?: { page?: number; perPage?: number; onlyStable?: boolean }) {
  const stablecoinAssets = await getActiveStablecoin()

  let pagination: PaginationType | undefined
  const cmcStable: number[] = []

  if (opt?.onlyStable) {
    const assets = await getCachedAsset(stablecoinAssets?.map((d) => d.id) ?? [], 'id')
    Object.keys(assets).forEach((key) => {
      if (assets[key]) {
        if (assets[key].cmcId) {
          cmcStable.push(assets[key].cmcId)
        }
      }
    })
  }

  if (opt?.page && opt?.perPage) {
    const count = await timescaleClient
      .select({
        total: sql<number>`cast(count(*) as int)`,
      })
      .from(cmcPriceDaily)
      .where(
        and(
          eq(cmcPriceDaily.timestamp, sql`(select max(${cmcPriceDaily.timestamp}) from ${cmcPriceDaily})`),
          cmcStable.length > 0 ? inArray(cmcPriceDaily.cmc_id, cmcStable) : undefined,
        ),
      )

    pagination = {
      perPage: opt.perPage,
      page: opt.page,
      totalPage: Math.ceil(count[0]?.total || 0 / opt.perPage),
      offset: (opt.page - 1) * opt.perPage,
      totalItem: count[0]?.total || 0,
    }
  }

  const data: FirstLayerData[] = []

  const prices = await timescaleClient
    .select()
    .from(cmcPriceDaily)
    .where(
      and(
        eq(cmcPriceDaily.timestamp, sql`(select max(${cmcPriceDaily.timestamp}) from ${cmcPriceDaily})`),
        cmcStable.length > 0 ? inArray(cmcPriceDaily.cmc_id, cmcStable) : undefined,
      ),
    )
    .orderBy(desc(cmcPriceDaily.close))
    .limit(pagination?.perPage ?? 20)
    .offset(pagination?.offset ?? 0)
    .then((prices) => {
      prices.forEach((price) => {
        data.push({
          price,
        })
      })
      return prices
    })

  const assets = await getCachedAsset(
    data.filter((d) => d.price?.cmc_id !== undefined).map((d) => d.price!.cmc_id),
    'price',
  )

  const communityFlag: Record<string, [string, number][]> = {}
  for (const key in assets) {
    const findKey = data.findIndex((d) => d.price?.cmc_id === parseInt(key))
    if (findKey > -1) {
      data[findKey] = {
        ...data[findKey],
        asset: assets[key] as AssetType,
      }

      if (assets[key]) {
        assets[key].communities.forEach((community: any) => {
          const [platform, id] = community.communityId.split('::')
          if (!communityFlag[platform]) {
            communityFlag[platform] = []
          }

          communityFlag[platform].push([id, findKey])
        })
      }
    }
  }

  await Promise.all([
    // Get graph 7 day
    timescaleClient
      .select({
        close: cmcPriceDaily.close,
        timestamp: cmcPriceDaily.timestamp,
        cmcId: cmcPriceDaily.cmc_id,
      })
      .from(cmcPriceDaily)
      .where(
        and(
          inArray(
            cmcPriceDaily.cmc_id,
            prices.map((cmc) => cmc.cmc_id),
          ),
          sql`(timestamp <= (select max(${cmcPriceDaily.timestamp}) from ${cmcPriceDaily}) and timestamp >= ((select max(${cmcPriceDaily.timestamp}) from ${cmcPriceDaily}) - interval '7 day'))`,
        ),
      )
      // Grouping graph
      .then((res) => {
        const graph: Record<string, typeof res> = {}

        res.forEach((d) => {
          if (!graph[d.cmcId]) {
            graph[d.cmcId] = []
          }

          graph[d.cmcId]?.push(d)
        })

        return graph
      })
      .then((res) =>
        Object.keys(res).forEach((d) => {
          const findKey = data.findIndex((r) => r.price?.cmc_id === parseInt(d ?? '0'))
          if (findKey > -1) {
            data[findKey] = {
              ...data[findKey],
              graph: res[d] && res[d].map((data) => [data.timestamp.getTime(), data.close!]),
            }
          }
        }),
      ),
    // Get Indices
    timescaleClient
      .select()
      .from(indiceV2)
      .where(
        and(
          inArray(
            indiceV2.assetId,
            data.map((d) => d.asset!.id),
          ),
          eq(indiceV2.platform, '_all'),
          sql`${indiceV2.timestamp} = (select max(${indiceV2.timestamp}) from ${indiceV2})`,
        ),
      )
      .then((res) =>
        res.forEach((d) => {
          const findKey = data.findIndex((r) => r.asset?.id === d.assetId)
          if (findKey > -1) {
            data[findKey] = {
              ...data[findKey],
              indice: d,
            }
          }
        }),
      ),
    // Get ESG
    timescaleClient
      .select()
      .from(esgTimeseries)
      .where(
        and(
          inArray(
            esgTimeseries.blockchain_id,
            data.map((d) => d.asset!.slug),
          ),
          sql`${esgTimeseries.measured_at} = (select max(${esgTimeseries.measured_at}) from ${esgTimeseries})`,
          eq(esgTimeseries.index_group, 'avg_consumption'),
        ),
      )
      .then((res) =>
        res.forEach((d) => {
          const findKey = data.findIndex((r) => r.asset!.slug === d.blockchain_id)
          if (findKey > -1) {
            data[findKey] = {
              ...data[findKey],
              esg: d,
            }
          }
        }),
      ),
    // Get Platform Indicator
    (async () => {
      for (const key in communityFlag) {
        if (key === 'REDDIT') {
          const redditMsg = await timescaleClient
            .select({
              id: redditIndicator.subreddit_id,
              message: redditIndicator.nr_posts,
            })
            .from(redditIndicator)
            .where(
              and(
                sql`${redditIndicator.timestamp} = (select max(${redditIndicator.timestamp}) from ${redditIndicator})`,
                inArray(redditIndicator.subreddit_id, communityFlag[key] ? communityFlag[key].map((d) => d[0]) : []),
              ),
            )

          for (const msg of redditMsg) {
            const findKey = data.findIndex((dp: any) => {
              const t = dp.asset?.communities.findIndex((dc: any) => dc.communityId.includes(msg.id))
              return t > -1
            })
            if (data[findKey]) {
              data[findKey].message = msg.message ?? undefined
            }
          }
        }
        if (key === 'TELEGRAM') {
          const telegramMsg = await timescaleClient
            .select({
              id: telegramIndicator.tg_chat_id,
              message: telegramIndicator.nr_messages,
            })
            .from(telegramIndicator)
            .where(
              and(
                sql`${telegramIndicator.timestamp} = CURRENT_DATE`,
                inArray(
                  telegramIndicator.tg_chat_id,
                  communityFlag[key] ? communityFlag[key].map((d) => parseInt(d[0])) : [],
                ),
              ),
            )

          for (const msg of telegramMsg) {
            const findKey = data.findIndex((dp: any) => {
              const t = dp.asset?.communities.findIndex((dc: any) => dc.communityId.includes(msg.id))
              return t > -1
            })
            if (data[findKey]) {
              data[findKey].message = msg.message ?? undefined
            }
          }
        }
      }
    })(),
  ])

  return {
    data: data.map((d) => ({
      name: d.asset?.name,
      logo: d.asset?.logo,
      slug: d.asset?.slug,
      price: d.price?.close,
      mood_index: d.indice?.moodIndex,
      trust_index: d.indice?.trustIndex ?? null,
      community_size: d.indice?.communitySize ?? null,
      energy: d.esg?.consumptionTx ?? null,
      number_of_message: d.message ?? null,
      graph: d.graph,
      page: pagination?.page,
      totalPage: pagination?.totalPage,
      type: (stablecoinAssets ?? []).reduce((res: DashboardTable[0]['type'], data) => {
        if (res === 'stablecoin') {
          return res
        }

        if (data.id === d.asset?.id) {
          return 'stablecoin'
        }
        return 'cryptocurrency'
      }, 'cryptocurrency'),
    })) as DashboardTable,
    pagination: {
      total_item: pagination?.totalItem ?? 0,
      last_page: pagination?.totalPage,
      current: opt?.page,
      per_page: opt?.perPage,
    } as PaginationResponse,
  }
}
