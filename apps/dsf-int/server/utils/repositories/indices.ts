import { and, desc, eq, inArray, ne, sql, asc, isNotNull, notInArray, SQL } from 'drizzle-orm'
import { indiceV2 } from '../timescale/schema'
import { getCachedAsset } from '../cache/asset'
import type { DashboardTable } from '~/server/trpc/routers/v2/public/dashboard'
import { displayWattage, numericDisplay } from '~/helper/number-helper'
import { checkNumber } from '~/helper/utilities-helper'
import type { PaginationResponse, PaginationType } from '~/types/appTypes'
import { asset, tsdbViewIndicesV2 } from '~/server/utils/chat-source/schema'
import { assetNameFilter, exactOrder } from '../chat-source/schema/asset'
import { defaultFilter } from '../chat-source/schema/tsdb_vw_indices_v2_feeds'
import { PgColumn } from 'drizzle-orm/pg-core'
type indiceTarget = 'community_size' | 'number_msg' | 'energy_trx' | 'trust' | 'mood'

export async function getFullIndices(opt?: {
  sort?: 'mood' | 'trust' | 'energy_trx' | 'number_msg' | 'community_size'
  page?: number
  perPage?: number
  only?: 'crypto' | 'stable'
  disablePagination?: boolean
}) {
  const stablecoinAssets = await getActiveStablecoin()

  let pagination: PaginationType | undefined
  const whereQuery = and(
    opt?.only === 'stable' ? inArray(indiceV2.assetId, stablecoinAssets?.map((asset) => asset.id) ?? []) : undefined,
    opt?.only === 'crypto' ? notInArray(indiceV2.assetId, stablecoinAssets?.map((asset) => asset.id) ?? []) : undefined,
    sql`${indiceV2.timestamp} = (select max(${indiceV2.timestamp}) from ${indiceV2})`,
    opt?.sort === 'number_msg' ? and(isNotNull(indiceV2.nrMessages), sql`${indiceV2.nrMessages} != 'NaN'`) : undefined,
    opt?.sort === 'energy_trx'
      ? and(
          ne(indiceV2.energyConsumptionTx, 0),
          isNotNull(indiceV2.energyConsumptionTx),
          sql`${indiceV2.energyConsumptionTx} != 'NaN'`,
        )
      : undefined,
    eq(indiceV2.platform, '_all'),
    and(
      isNotNull(indiceV2.moodRank),
      isNotNull(indiceV2.trustRank),
      sql`${indiceV2.moodRank} != 'NaN'`,
      sql`${indiceV2.trustRank} != 'NaN'`,
    ),
  )

  if (opt?.page && opt?.perPage) {
    const count = await timescaleClient
      .select({
        total: sql<number>`cast (count(*) as int)`,
      })
      .from(indiceV2)
      .where(whereQuery)

    pagination = {
      perPage: opt.perPage,
      page: opt.page,
      totalPage: Math.ceil(count[0]?.total || 0 / opt.perPage),
      offset: (opt.page - 1) * opt.perPage,
      totalItem: count[0]?.total || 0,
    }
  }

  if (pagination?.totalItem === 0) {
    return {
      data: [],
    }
  }

  const result = await timescaleClient
    .select({
      assetId: indiceV2.assetId,
      moodIndex: indiceV2.moodIndex,
      trustIndex: indiceV2.trustIndex,
      communitySize: indiceV2.communitySize,
      moodRank: indiceV2.moodRank,
      trustRank: indiceV2.trustRank,
      energyConsumption: indiceV2.energyConsumptionTx,
      timestamp: indiceV2.timestamp,
      number_message: sql<number>`(
        select sum(nr_messages) from indices_v2 iv where iv.platform = '_all' and iv.asset_id = indices_v2.asset_id and
        (iv.timestamp > (select max(timestamp) from indices_v2) - '15 day'::INTERVAL and iv.timestamp <= (select max(timestamp) from indices_v2) - interval '1 day')
      )`.as('number_message'),
    })
    .from(indiceV2)
    .where(whereQuery)
    .orderBy(
      (() => {
        switch (opt?.sort) {
          case 'trust':
            return desc(indiceV2.trustRank)

          case 'community_size':
            return sql`${indiceV2.communitySize} desc nulls last`

          case 'energy_trx':
            return asc(indiceV2.energyConsumptionTx)

          case 'number_msg':
            return desc(sql`number_message`)

          default:
            return desc(indiceV2.moodRank)
        }
      })(),
    )
    .limit(pagination?.perPage ?? 20)
    .offset(pagination?.offset ?? 0)

  if (result.length === 0) {
    return {
      data: [],
    }
  }

  const [assets, history] = await Promise.all([
    getCachedAsset(
      result.map((indice) => indice.assetId),
      'id',
    ),
    timescaleClient
      .select({
        assetId: indiceV2.assetId,
        value: (() => {
          switch (opt?.sort) {
            case 'trust':
              return indiceV2.trustIndex

            case 'energy_trx':
              return indiceV2.energyConsumptionTx

            case 'community_size':
              return indiceV2.communitySize

            case 'number_msg':
              return indiceV2.nrMessages

            default:
              return indiceV2.moodIndex
          }
        })(),
        timestamp: indiceV2.timestamp,
      })
      .from(indiceV2)
      .where(
        and(
          eq(indiceV2.platform, '_all'),
          sql`${indiceV2.timestamp} > (select max(${indiceV2.timestamp}) from ${indiceV2}) - '8 day'::INTERVAL`,
          sql`${indiceV2.timestamp} <= (select max(${indiceV2.timestamp}) from ${indiceV2})`,
          inArray(
            indiceV2.assetId,
            result.map((indice) => indice.assetId),
          ),
        ),
      )
      .orderBy(asc(indiceV2.timestamp)),
  ])

  return {
    data: result.map((data) => ({
      name: assets[data.assetId].name,
      logo: assets[data.assetId].logo,
      slug: assets[data.assetId].slug,
      mood_index: checkNumber(data.moodIndex) ? numericDisplay(data.moodIndex ?? 0) : '-',
      trust_index: checkNumber(data.trustIndex) ? numericDisplay(data.trustIndex ?? 0) : '-',
      energy: data.energyConsumption ? displayWattage(data.energyConsumption ?? 0) + 'h' : '-',
      community_size: checkNumber(data.communitySize) ? numericDisplay(data.communitySize ?? 0) : '-',
      number_of_message: checkNumber(data.number_message) ? numericDisplay(data.number_message ?? 0, 0) : '-',
      timetime: data.timestamp,
      type: (stablecoinAssets ?? []).reduce((res: DashboardTable[0]['type'], stable) => {
        if (res === 'stablecoin') {
          return res
        }

        if (data.assetId === stable.id) {
          return 'stablecoin'
        }
        return 'cryptocurrency'
      }, 'cryptocurrency'),
      graph: history.reduce((res: [number, number][], indice) => {
        if (indice.assetId === data.assetId) {
          res.push([indice.timestamp.getTime(), indice.value ?? 0])
        }

        return res
      }, []),
    })),
    pagination: opt?.disablePagination
      ? null
      : ({
          current: pagination?.page ?? 1,
          last_page: pagination?.totalPage ?? 1,
          per_page: pagination?.perPage ?? 10,
          total_item: pagination?.totalItem ?? 1,
        } as PaginationResponse),
  }
}

export async function getIndices(
  indexes: 'mood' | 'trust',
  opt?: {
    sort?: 'community' | 'message' | 'indexes' | 'changed'
    page?: number
    perPage?: number
    historyLength?: number
    type?: ('CRYPTOCURRENCY' | 'STABLECOIN')[]
    messageLength?: number
    terms?: string
    assetSelection?: SQL<unknown>
  },
) {
  const whereQuery = and(
    defaultFilter,
    inArray(asset.type, opt?.type ?? ['CRYPTOCURRENCY']),
    opt?.terms ? assetNameFilter(opt.terms) : undefined,
    opt?.assetSelection,
  )

  const queriesSq = chatSourceClient.$with('queriesQs').as(
    chatSourceClient
      .select({
        name: asset.name,
        logo: asset.logo,
        slug: asset.slug,
        symbol: asset.symbol,
        type: asset.type,
        assetId: asset.id,
        indexes: indexes === 'mood' ? tsdbViewIndicesV2.moodIndex : tsdbViewIndicesV2.trustIndex,
        changed: indexes === 'mood' ? tsdbViewIndicesV2.dayChangedMood : tsdbViewIndicesV2.dayChangedTrust,
        communitySize: tsdbViewIndicesV2.communitySize,
        number_message: tsdbViewIndicesV2.nrMessages,
        totalCount: sql<number>`cast (COUNT(*) OVER() as int)`.as('totalCount'),
      })
      .from(tsdbViewIndicesV2)
      .innerJoin(asset, eq(tsdbViewIndicesV2.assetId, asset.id))
      .where(whereQuery)
      .orderBy(() => {
        const orderQuery: SQL<unknown>[] = []
        orderQuery.push(sql`
          CASE
            WHEN ${tsdbViewIndicesV2.timestamp} = CURRENT_DATE then 0
            ELSE 1
          END
        `)

        // Ordering query based on rank

        if (indexes === 'mood' && opt?.sort === 'indexes') {
          orderQuery.push(
            sql`
                CASE
                  WHEN ${tsdbViewIndicesV2.moodRank} = 'NaN' THEN 1
                  WHEN ${tsdbViewIndicesV2.moodRank} IS NULL THEN 2
                  ELSE 0
                  END`,
            desc(tsdbViewIndicesV2.moodRank),
          )
        }

        if (indexes === 'trust' && opt?.sort === 'indexes') {
          orderQuery.push(
            sql`
                CASE
                  WHEN ${tsdbViewIndicesV2.trustRank} = 'NaN' THEN 1
                  WHEN ${tsdbViewIndicesV2.trustRank} IS NULL THEN 2
                  ELSE 0
                  END`,
            desc(tsdbViewIndicesV2.trustRank),
          )
        }

        if (indexes === 'mood' && opt?.sort === 'changed') {
          orderQuery.push(
            sql`
                CASE
                  WHEN ${tsdbViewIndicesV2.dayChangedMood} = 'NaN' THEN 1
                  WHEN ${tsdbViewIndicesV2.dayChangedMood} IS NULL THEN 2
                  ELSE 0
                  END`,
            desc(tsdbViewIndicesV2.dayChangedMood),
          )
        }

        if (indexes === 'trust' && opt?.sort === 'changed') {
          orderQuery.push(
            sql`
                CASE
                  WHEN ${tsdbViewIndicesV2.dayChangedTrust} = 'NaN' THEN 1
                  WHEN ${tsdbViewIndicesV2.dayChangedTrust} IS NULL THEN 2
                  ELSE 0
                  END`,
            desc(tsdbViewIndicesV2.dayChangedTrust),
          )
        }

        if (opt?.sort === 'message') {
          orderQuery.push(
            sql`
                CASE
                  WHEN ${tsdbViewIndicesV2.nrMessages} IS NULL THEN 1
                  ELSE 0
                END`,
            desc(tsdbViewIndicesV2.nrMessages),
          )
        }

        if (opt?.sort === 'community') {
          orderQuery.push(
            sql`
                CASE
                  WHEN ${tsdbViewIndicesV2.communitySize} IS NULL THEN 1
                  ELSE 0
                END`,
            desc(tsdbViewIndicesV2.communitySize),
          )
        }

        if (opt?.terms) {
          orderQuery.push(...exactOrder(opt?.terms))
        }
        return orderQuery
      })
      .limit(opt?.perPage ?? 20)
      .offset(opt?.page ? (opt.page - 1) * (opt.perPage ?? 20) : 0),
  )

  const result = await chatSourceClient.with(queriesSq).select().from(queriesSq)
  const histories = await Promise.all(result.map((d) => cachedIndicesHistory(d.assetId, indexes)))

  return {
    data: result.map((item) => ({
      name: item.name,
      logo: item.logo,
      slug: item.slug,
      symbol: item.symbol,
      indexes: item.indexes,
      community_size: item.communitySize,
      changed: item.changed,
      symbol_compare: (() => {
        if (item.changed! > 0) {
          return '>'
        }
        if (item.changed! < 0) {
          return '<'
        }
        return '-'
      })(),
      number_message: item.number_message,
      chart: (histories.find((d) => d?.id === item.assetId) ?? { ts: [] }).ts,
      type: item.type,
    })),
    pagination: {
      current: opt?.page ?? 0,
      totalItem: result[0]?.totalCount ?? 0,
      lastPage: (result[0]?.totalCount ?? 0) / (opt?.perPage ?? 20),
      perPage: opt?.perPage ?? 20,
    },
  }
}

export const cachedIndicesHistory = cachedFunction(
  async (id: number, target: indiceTarget) => {
    let targetColumn: PgColumn<any>

    switch (target) {
      case 'community_size':
        targetColumn = indiceV2.communitySize
        break

      case 'number_msg':
        targetColumn = indiceV2.nrMessages
        break

      case 'energy_trx':
        targetColumn = indiceV2.energyConsumptionTx
        break

      case 'trust':
        targetColumn = indiceV2.trustIndex
        break

      default:
        targetColumn = indiceV2.moodIndex
        break
    }

    const queries = await timescaleClient
      .select({
        timestamp: indiceV2.timestamp,
        target: targetColumn,
      })
      .from(indiceV2)
      .where(
        and(
          eq(indiceV2.platform, '_all'),
          eq(indiceV2.assetId, id),
          and(
            isNotNull(targetColumn),
            targetColumn !== indiceV2.communitySize ? sql`${targetColumn} != 'NaN'` : undefined,
          ),
        ),
      )
      .orderBy(desc(indiceV2.timestamp))
      .limit(90)

    return {
      ts: queries.reverse().map((d) => [d.timestamp.getTime(), d.target as number]) as [number, number][],
      id,
    }
  },
  {
    maxAge: cacheMaxAgeConfig,
    getKey: (id: number, target: indiceTarget) => `hin-${id}-${target}`,
  },
)

export const cachedOverviewData = cachedFunction(
  async (assetId: number) => {
    const overview = await chatSourceClient.query.tsdbViewIndicesV2.findFirst({
      columns: {
        assetId: true,
        nrMessages: true,
        v1VulgarityIndex: true,
        moodRankOrder: true,
        trustRankOrder: true,
      },
      where: (fields, { eq }) => eq(fields.assetId, assetId),
    })
    return overview
  },
  {
    maxAge: cacheMaxAgeConfig,
    getKey: (assetId: number) => `covdata-${assetId}`,
  },
)
