import type { AssetType } from '../../chat-source/schema/asset'
import type { EsgPairType } from '../../chat-source/schema/esg-pair'
import type { CmcPriceDaily } from '../../timescale/schema/cmc-price-daily'
import type { EsgTimeseries } from '../../timescale/schema/esg-timeseries'
import type { IndiceV2 } from '../../timescale/schema/indices-v2'
import type { Indice } from '../../timescale/schema/indicies'

export interface FirstLayerData {
  indice?: IndiceV2
  indiceV1?: Indice
  asset?: AssetType
  price?: CmcPriceDaily
  message?: number
  esg?: EsgTimeseries
  esgPair?: EsgPairType
  graph?: [number, number][]
}
