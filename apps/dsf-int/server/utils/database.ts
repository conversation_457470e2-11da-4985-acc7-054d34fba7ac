import { asc, desc, sql } from 'drizzle-orm'
import type { AnyC<PERSON>umn, SQLWrapper } from 'drizzle-orm'
import { PgColumn } from 'drizzle-orm/pg-core'

// Type to get nested path types
type PathsToStringProperty<T> = T extends object
  ? {
      [K in keyof T & string]: T[K] extends string
        ? K
        : T[K] extends object
          ? `${K & string}.${PathsToStringProperty<T[K]>}`
          : never
    }[keyof T & string]
  : never

// Helper type to extract the data type from a PgColumn
type ExtractColumnData<T> =
  T extends PgColumn<infer Config, any, any> ? (Config extends { data: any } ? Config['data'] : never) : never

export function jsonExtract<
  TColumn extends PgColumn<any, any, any>,
  TPath extends PathsToStringProperty<NonNullable<ExtractColumnData<TColumn>>>,
>(column: TColumn, path: TPath) {
  const parts = path.split('.')
  const lastPart = parts.pop()!
  const pathParts = parts.length ? parts.map((p) => `'${p}'`).join('->') + `->'${lastPart}'` : `'${lastPart}'`
  return sql`${column}->>${sql.raw(pathParts)}`
}

export function dynamicSort(sql: AnyColumn | SQLWrapper, sort: 'asc' | 'desc') {
  switch (sort) {
    case 'asc':
      return asc(sql)

    default:
      return desc(sql)
  }
}
