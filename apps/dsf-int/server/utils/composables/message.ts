import { serverSupabaseServiceRole } from '#supabase/server'
import type { H3Event } from 'h3'

export interface SupportMessagePayload {
  browser: string | null
  os_info: string | null
  company: string | null
  title: string
  first_name: string
  last_name: string
  message: string
  type: string
  email: string
  attachments?: File[]
  callback?: () => Promise<void>
}

export interface SupportMessage {
  browser: string | null
  os_info: string | null
  company: string | null
  title: string
  first_name: string
  last_name: string
  message: string
  type: string
  email: string
  attachments?: {
    base64: string
    filename: string
  }[]
}

const supportData = useStorage<SupportMessage>('support_data')
import schedule from 'node-schedule'
import { add } from 'date-fns'
// eslint-disable-next-line @nx/enforce-module-boundaries
import type { Database } from 'app-types/supabase'

export async function createSupportMessage(payload: SupportMessagePayload, event?: H3Event) {
  let attachments: { base64: string; filename: string }[] | undefined
  const datePrefix = new Date().toISOString()
  const keyStorage = `${payload.type}-${payload.email}-${datePrefix}`

  if (payload.attachments && payload.attachments.length > 0) {
    // await fs.promises.mkdir(join(pathSupportMessage, datePrefix), { recursive: true })
    await Promise.all(
      payload.attachments.map(async (d) => {
        if (attachments === undefined) {
          attachments = []
        }
        const buffer = await d.arrayBuffer()
        attachments.push({
          base64: Buffer.from(buffer).toString('base64'),
          filename: d.name,
        })
      }),
    )
  }

  await supportData.setItem(keyStorage, {
    title: payload.title,
    browser: payload.browser,
    company: payload.company,
    email: payload.email,
    first_name: payload.first_name,
    last_name: payload.last_name,
    message: payload.message,
    os_info: payload.os_info,
    type: payload.type,
    attachments,
  })

  schedule.scheduleJob(
    add(new Date(datePrefix), {
      seconds: 5,
    }),
    async () => {
      const supportMessageData = await supportData.getItem(keyStorage)
      const date = new Date()

      if (supportMessageData) {
        if (payload.callback) {
          await payload.callback()
        }

        if (event) {
          const sbClient = serverSupabaseServiceRole<Database>(event)
          const supportMessage = await sbClient.rpc('insert_support_message_transaction', {
            p_browser: supportMessageData.browser ?? '-',
            p_company: supportMessageData.company ?? '-',
            p_email: supportMessageData.email,
            p_first_name: supportMessageData.first_name,
            p_last_name: supportMessageData.last_name,
            p_message: supportMessageData.message,
            p_os_info: supportMessageData.os_info ?? '-',
            p_title: supportMessageData.title,
            p_type: supportMessageData.type as 'bug' | 'feature' | 'business' | 'media' | 'other',
          })

          if (supportMessage.error) {
            console.error(supportMessage.error)
          }

          if (supportMessageData.attachments && supportMessageData.attachments.length > 0) {
            const uploads = await Promise.all(
              supportMessageData.attachments.map(async (attachment) => {
                const filePath = `${date.getFullYear()}/${date.getMonth() + 1}/${attachment.filename}`
                const { error } = await sbClient.storage
                  .from('supports')
                  .upload(filePath, Buffer.from(attachment.base64, 'base64'))

                if (error) {
                  return null
                }

                return filePath
              }),
            )

            const insertResult = await sbClient.from('support_messages_attachment').insert(
              uploads
                .filter((d) => d !== null)
                .map((d) => {
                  const splitFilename = d.split('/')

                  return {
                    file_name: splitFilename[splitFilename.length - 1],
                    location: d,
                    support_message_id: supportMessage.data![0]?.support_message_id,
                  }
                })
                .filter((item) => item.file_name !== undefined && item.support_message_id !== undefined) as {
                file_name: string
                location: string
                support_message_id: number
              }[],
            )
            if (insertResult.error) {
              console.error(insertResult.error)
            }
          }
        }
      }
    },
  )
}
