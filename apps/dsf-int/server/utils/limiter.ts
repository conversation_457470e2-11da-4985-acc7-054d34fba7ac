import { add } from 'date-fns'
import type { H3Event, EventHandlerRequest } from 'h3'
import schedule from 'node-schedule'

interface limiterIndex {
  ip: string
  key: string
  limit: number
  expires_at: number
  occurrences: number
}

const cacheStorage = useStorage('cache')

/**
 * By default it will expired in 5 minute and limit occurrences at 5 times.
 *
 */
export async function useLimitter(
  key: string,
  event: H3Event<EventHandlerRequest>,
  opt?: {
    expired_in?: number
    limit?: number
  }
) {
  const ip = event.node.req.socket.remoteAddress || event.node.req.headers['x-forwarded-for']
  const fullKey = `${ip}-${key}`
  const indice = await cacheStorage.getItem<limiterIndex>(fullKey)

  async function setLimiter(expired_at?: number) {
    if (ip) {
      const expiredAt = expired_at ?? add(new Date(), { minutes: opt?.expired_in ?? 5 }).getTime()
      await cacheStorage.setItem<limiterIndex>(fullKey, {
        expires_at: expiredAt,
        ip: ip.toString(),
        key,
        limit: opt?.limit ?? 5,
        occurrences: 1,
      })

      schedule.scheduleJob(expiredAt, async () => {
        await cacheStorage.removeItem(fullKey)
      })
    }
  }

  function validateExpiration(date?: Date) {
    if (date === undefined) {
      date = new Date()
    }

    if (indice?.expires_at === undefined) {
      return false
    }

    return date.getTime() > indice?.expires_at
  }

  /**
   *
   * It will return false if validation was fail
   *
   * @returns boolean
   */
  async function validateLimiter() {
    if (indice === null) {
      // Create a new limit
      setLimiter()
      return true
    } else {
      // Check the occurrences
      if (validateExpiration()) {
        setLimiter()
        return true
      }

      if (indice.limit > indice.occurrences) {
        return true
      }

      return false
    }
  }

  async function increaseLimiter() {
    if (indice) {
      await cacheStorage.setItem<limiterIndex>(fullKey, {
        ...indice,
        occurrences: indice.occurrences + 1,
      })
    }
  }

  async function decreaseLimiter() {
    if (indice === null) {
      // Create a new limit
      setLimiter()
    } else {
      // Check the occurrences
      if (validateExpiration()) {
        setLimiter()
      }
      await cacheStorage.setItem<limiterIndex>(fullKey, {
        ...indice,
        occurrences: indice.occurrences - 1,
      })
    }
  }

  async function clearLimiter() {
    await cacheStorage.removeItem(fullKey)
  }

  return {
    indice,
    validateLimiter,
    clearLimiter,
    decreaseLimiter,
    increaseLimiter,
  }
}
