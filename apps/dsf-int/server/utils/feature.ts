export default class FeatureTier {
  private features: Feature | null
  private bypass: boolean

  constructor(data?: Feature, bypass = false) {
    this.features = data ?? null
    this.bypass = bypass
  }

  public checkSubscription() {
    if (this.bypass) {
      return true
    }

    return this.features !== null
  }

  public checkFeature(featurePath: string) {
    if (this.bypass === true) {
      return true
    }

    if (this.features === null) {
      return false
    }

    try {
      const [feature, metrics] = featurePath.split('.')
      if (!metrics || !feature || typeof this.features?.[feature]?.[metrics] === 'string') {
        return false
      }

      return (this.features?.[feature]?.[metrics] as boolean) ?? false
    } catch (error) {
      return false
    }
  }

  public checkMultipleNonStrict(...featurePath: string[]) {
    const result = featurePath.map((d) => this.checkFeature(d)).filter((d) => d === true)

    return result.length > 0
  }
}
