import { format } from 'date-fns'
import process from 'node:process'
import Stripe from 'stripe'

export const cacheMaxAgeConfig = Number.parseInt(process.env.NUXT_APP_CACHE_MAX_AGE ?? '0')

export const generateStripeTestClock = async (stripeSdk: Stripe) => {
  const { csrfServerHeader } = useRuntimeConfig()
  const cacheStorage = useStorage('cache')
  const date = new Date()

  const testClockEnable = process.env.NUXT_APP_STRIPE_ENABLE_TEST_CLOCK
  if (testClockEnable !== 'true') {
    return undefined
  }

  let testClock = await cacheStorage.getItem<{
    key: string
    value: string
  }>('stripeTestClock')

  if (testClock?.key !== csrfServerHeader) {
    testClock = null
  }

  if (testClock) {
    return testClock.value
  }

  const testClockId = await stripeSdk.testHelpers.testClocks.create({
    frozen_time: Math.ceil(new Date().getTime() / 1000),
    name: `Test Clock at ${format(date, 'yyyy-MM-dd')}`,
  })

  await cacheStorage.setItem('stripeTestClock', {
    key: csrfServerHeader,
    value: testClockId.id,
  })

  return testClockId.id
}
