import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import * as schema from './schema'

const runtimeConfig = useRuntimeConfig()

const client = postgres({
  host: runtimeConfig.chatSourceHost,
  port: runtimeConfig.chatSourcePort,
  user: runtimeConfig.chatSourceUser,
  password: runtimeConfig.chatSourcePassword,
  database: runtimeConfig.chatSourceDbName,
  ssl: false,
})

export const chatSourceClient = drizzle(client, {
  schema,
})