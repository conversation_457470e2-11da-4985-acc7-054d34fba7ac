import { varchar, date, text, doublePrecision, boolean } from 'drizzle-orm/pg-core'
import { tsdbSchema } from './tsdb'

export const tsdbVwEsgv2 = tsdbSchema.table('esgv3', {
  name: varchar('name'),
  date: date('date'),
  source: text('source'),
  validators: doublePrecision('validators'),
  throughputTps: doublePrecision('throughput_tps'),
  poweruseLowerW: doublePrecision('poweruse_lowerbound_w'),
  poweruseBestGuessW: doublePrecision('poweruse_bestguess_w'),
  poweruseUpperW: doublePrecision('poweruse_upperbound_w'),
  energyConsumptionTxLowerWh: doublePrecision('energyconstx_lowerbound_w'),
  energyConsumptionTxBestGuessWh: doublePrecision('energyconstx_bestguess_wh'),
  energyConsumptionTxUpperWh: doublePrecision('energyconstx_upperbound_wh'),
  energyConsumptionTxNodeLowerWh: doublePrecision('energyconstxnode_lowerbound_w'),
  energyConsumptionTxNodeBestGuessWh: doublePrecision('energyconstxnode_bestguess_wh'),
  energyConsumptionTxNodeUpperWh: doublePrecision('energyconstxnode_upperbound_wh'),
  isForwardFilled: boolean('isforwardfilled'),
})
