import { tsdbSchema } from '~/server/utils/chat-source/schema/tsdb'
import { integer } from 'drizzle-orm/pg-core'
import { doublePrecision, timestamp, varchar } from 'drizzle-orm/pg-core'

export const tsdbIndicesV2 = tsdbSchema.table('indices_v2', {
  assetId: integer('asset_id').notNull(),
  platform: varchar('platform').notNull(),
  timestamp: timestamp('timestamp').notNull(),
  moodIndex: doublePrecision('mood_index'),
  trustIndex: doublePrecision('trust_index'),
  communitySize: integer('community_size'),
  nrMessages: doublePrecision('nr_messages'),
  moodRank: doublePrecision('mood_rank'),
  trustRank: doublePrecision('trust_rank'),
  energyConsumptionTx: doublePrecision('energy_consumption_tx'),
  v1VulgarityIndex: doublePrecision('v1_vulgarity_index'),
})

export const tsdbViewIndicesV2 = tsdbSchema.table('vw_indices_v2', {
  assetId: integer('asset_id').notNull(),
  platform: varchar('platform').notNull(),
  timestamp: timestamp('timestamp').notNull(),
  moodIndex: doublePrecision('mood_index'),
  trustIndex: doublePrecision('trust_index'),
  communitySize: integer('community_size'),
  nrMessages: doublePrecision('nr_messages'),
  moodRank: doublePrecision('mood_rank'),
  trustRank: doublePrecision('trust_rank'),
  energyConsumptionTx: doublePrecision('energy_consumption_tx'),
  v1VulgarityIndex: doublePrecision('v1_vulgarity_index'),
  twoWeekMessage: doublePrecision('2week_message'),
  dayChangedMood: doublePrecision('1d_changed_mood'),
  dayChangedTrust: doublePrecision('1d_changed_trust'),
  moodRankOrder: integer('mood_rank_order'),
  trustRankOrder: integer('trust_rank_order'),
  marketcap: doublePrecision('marketcap'),
})
