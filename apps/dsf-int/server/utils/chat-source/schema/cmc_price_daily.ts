import { tsdbSchema } from '~/server/utils/chat-source/schema/tsdb'
import { doublePrecision, integer, timestamp } from 'drizzle-orm/pg-core'

export const tsdbCmcPriceDaily = tsdbSchema.table('cmc_price_daily', {
  cmc_id: integer('cmc_id').notNull(),
  timestamp: timestamp('timestamp').notNull(),
  open: doublePrecision('open'),
  high: doublePrecision('high'),
  low: doublePrecision('low'),
  close: doublePrecision('close'),
  volume: doublePrecision('volume'),
  marketcap: doublePrecision('marketcap'),
})
