import { doublePrecision, integer, timestamp, varchar } from 'drizzle-orm/pg-core'
import { tsdbSchema } from './tsdb'

export const tsdbDecentralise = tsdbSchema.table('decentralisation_normalised', {
  metricName: varchar('metric_name'),
  metricCluster: varchar('metric_cluster'),
  date: timestamp('date'),
  processedAt: timestamp('processed_at'),
  assetId: integer('asset_id'),
  value: doublePrecision('value'),
})
