import { pgTable, serial, text, timestamp, varchar } from 'drizzle-orm/pg-core'

export const platform = pgTable('platforms', {
  id: serial('id').primaryKey(),
  name: text('name').notNull(),
  symbol: varchar('symbol').notNull(),
  type: varchar('type'),
  layer: varchar('layer'),
  cmcWebpage: text('cmc_webpage'),
  coingeckoWebpage: text('coingecko_webpage'),
  website: text('website'),
  logo: text('logo'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').notNull(),
  slug: text('slug'),
})
