import { ilike, or, relations, sql } from 'drizzle-orm'
import { integer, pgEnum, pgTable, serial, text, timestamp, varchar } from 'drizzle-orm/pg-core'
import { communityOnAsset } from './community-on-asset'
import { esgPair } from './esg-pair'
import { url } from './url'
import { esgPairV2 } from './esg_pair_v2'
import { platform } from './platform'

export const typeEnum = pgEnum('type', ['CRYPTOCURRENCY', 'STOCK', 'STABLECOIN'])
export const statusEnum = pgEnum('status', ['ACTIVE', 'INACTIVE', 'NEW'])
export const layerEnum = pgEnum('LayersTypes', ['0', '1', '2'])

export type AssetTypeEnum = (typeof typeEnum.enumValues)[number]

export const asset = pgTable('asset', {
  id: serial('id').notNull().primaryKey(),
  slug: text('slug').notNull(),
  name: text('name').notNull(),
  symbol: varchar('symbol').notNull(),
  type: typeEnum('type').notNull(),
  status: statusEnum('status').notNull().default('NEW'),
  website: text('website'),
  domain: text('domain'),
  logo: text('logo'),
  tokenAddress: text('token_address'),
  cmcId: integer('cmc_id'),
  lcwId: text('lcw_id'),
  parentId: integer('parent_id'),
  createdAt: timestamp('created_at').notNull(),
  updatedAt: timestamp('updated_at').notNull(),
  description: text('description'),
  cmcWebpage: text('cmc_webpage'),
  coingeckoWebpage: text('coingecko_webpage'),
  coingeckoId: text('coingecko_id'),
  layer: layerEnum('layer'),
})

export const assetRelations = relations(asset, ({ many, one }) => ({
  communities: many(communityOnAsset),
  esg: one(esgPair, {
    fields: [asset.id],
    references: [esgPair.assetId],
  }),
  esgV3: one(esgPairV2, {
    fields: [asset.id],
    references: [esgPairV2.assetId],
  }),
  urls: many(url),
}))

export const exactOrder = (value: string, opt?: { includePlatform?: boolean }) => [
  sql`
    CASE
        WHEN ${asset.name} ILIKE ${value} THEN 0
        WHEN ${asset.symbol} ILIKE ${value} THEN 0
        ELSE 1
    END
  `,
  ...(opt?.includePlatform
    ? [
        sql`
          CASE
              WHEN ${platform.name} ILIKE ${value} THEN 0
              WHEN ${platform.symbol} ILIKE ${value} THEN 0
              ELSE 1
          END    
        `,
      ]
    : []),
]

export const assetNameFilter = (value: string, opt?: { includePlatform?: boolean }) =>
  or(
    ilike(asset.name, `%${value}%`),
    ilike(asset.symbol, `%${value}%`),
    opt?.includePlatform ? ilike(platform.name, `%${value}%`) : undefined,
    opt?.includePlatform ? ilike(platform.symbol, `%${value}%`) : undefined
  )

export type AssetType = typeof asset.$inferSelect
