import { integer, pgTable, serial, text, timestamp, varchar } from 'drizzle-orm/pg-core'
import { layerEnum, statusEnum, typeEnum } from './asset'

export const overallAsset = pgTable('overall_assets', {
  id: serial('id').notNull().primaryKey(),
  slug: text('slug').notNull(),
  name: text('name').notNull(),
  symbol: varchar('symbol').notNull(),
  type: typeEnum('type').notNull(),
  status: statusEnum('status').notNull().default('NEW'),
  website: text('website'),
  domain: text('domain'),
  logo: text('logo'),
  tokenAddress: text('token_address'),
  cmcId: integer('cmc_id'),
  lcwId: text('lcw_id'),
  parentId: integer('parent_id'),
  createdAt: timestamp('created_at').notNull(),
  updatedAt: timestamp('updated_at').notNull(),
  description: text('description'),
  cmcWebpage: text('cmc_webpage'),
  coingeckoWebpage: text('coingecko_webpage'),
  coingeckoId: text('coingecko_id'),
  layer: layerEnum('layer'),
  assetId: integer('asset_id'),
})
