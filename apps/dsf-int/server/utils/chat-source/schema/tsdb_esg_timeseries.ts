import { tsdbSchema } from '~/server/utils/chat-source/schema/tsdb'
import { varchar, timestamp, doublePrecision, integer } from 'drizzle-orm/pg-core'

export const tsdbEsgTimeseries = tsdbSchema.table('esg_timeseries', {
  blockchainId: varchar('blockchain_id').notNull(),
  indexGroup: varchar('index_group').notNull(),
  measuredAt:  timestamp('measured_at').notNull(),
  nodePower: doublePrecision('node_power'),
  nodeCount: integer('node_count'),
  tps: doublePrecision('tps'),
  totalPowerDemand: doublePrecision('total_power_demand'),
  consumptionTx: doublePrecision('consumption_tx'),
  consumptionTxNode: doublePrecision('consumption_tx_node'),
  hashrate: doublePrecision('hashrate'),
  consensusAbbr: varchar('consensus_abbreviation'),
  consensusMechanism: varchar('consensus_mechanism'),
})
