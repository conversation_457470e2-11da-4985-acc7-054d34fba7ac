import { relations } from 'drizzle-orm'
import { integer, pgTable, text, timestamp } from 'drizzle-orm/pg-core'
import { asset } from './asset'

export const esgPair = pgTable('esg_pair', {
  id: text('id').notNull(),
  assetId: integer('asset_id').notNull(),
  createdAt: timestamp('created_at').notNull(),
})

export const esgPairRelations = relations(esgPair, ({ one }) => ({
  asset: one(asset, {
    fields: [esgPair.assetId],
    references: [asset.id],
  }),
}))

export type EsgPairType = typeof esgPair.$inferSelect
