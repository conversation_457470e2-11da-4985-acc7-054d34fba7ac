import { relations } from 'drizzle-orm'
import { integer, pgTable, serial, text, timestamp, varchar } from 'drizzle-orm/pg-core'
import { asset } from './asset'

export const url = pgTable('url', {
  id: serial('id').primaryKey(),
  source: text('source').notNull(),
  sourceSlug: text('source_slug').notNull(),
  sourceId: text('source_id'),
  name: text('name').notNull(),
  symbol: varchar('symbol').notNull(),
  platform: varchar('platform').notNull(),
  url: text('url').notNull(),
  identifier: text('identifier').notNull(),
  createdAt: timestamp('created_at').notNull(),
  updatedAt: timestamp('updated_at').notNull(),
  assetId: integer('asset_id'),
  status: varchar('status'),
  language: text('language'),
})

export const urlRelations = relations(url, ({ one }) => ({
  asset: one(asset, {
    fields: [url.assetId],
    references: [asset.id],
  }),
}))
