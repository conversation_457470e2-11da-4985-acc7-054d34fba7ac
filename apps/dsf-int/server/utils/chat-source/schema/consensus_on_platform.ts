import { relations } from 'drizzle-orm'
import { integer, pgTable, timestamp } from 'drizzle-orm/pg-core'
import { consensus } from './consensus'
import { platform } from './platform'

export const consensusOnPlatform = pgTable('consensus_on_platforms', {
  platformId: integer('platform_id').notNull(),
  consensusId: integer('consensus_id').notNull(),
  createdAt: timestamp('created_at').notNull(),
})

export const consensusOnPlatformRelations = relations(consensusOnPlatform, ({ one }) => ({
  consensus: one(consensus, {
    fields: [consensusOnPlatform.consensusId],
    references: [consensus.consensusId],
  }),
  platform: one(platform, {
    fields: [consensusOnPlatform.platformId],
    references: [platform.id],
  }),
}))
