import { relations } from 'drizzle-orm'
import { integer, pgTable, timestamp, varchar } from 'drizzle-orm/pg-core'
import { asset } from './asset'

export const stablecoinPair = pgTable('stablecoin_pair', {
  id: varchar('id').notNull(),
  assetId: integer('asset_id').notNull(),
  createdAt: timestamp('created_at').notNull(),
})

export const stablecoinPairRelations = relations(stablecoinPair, ({ one }) => ({
  asset: one(asset, {
    fields: [stablecoinPair.assetId],
    references: [asset.id],
  }),
}))
