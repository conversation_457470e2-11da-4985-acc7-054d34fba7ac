import { integer, pgTable, text, pgEnum, varchar, boolean, timestamp } from 'drizzle-orm/pg-core'

const platformEnum = pgEnum('platform', ['DISCORD', 'GITHUB', 'REDDIT', 'TELEGRAM', 'TWITTER'])

export const community = pgTable('community', {
  id: integer('id').primaryKey(),
  name: text('name').notNull(),
  platform: platformEnum('platform').notNull(),
  platformId: varchar('platform_id').notNull(),
  isOfficial: boolean('is_official').notNull().default(false),
  isQualified: boolean('is_qualified').notNull().default(false),
  url: text('url'),
  createdAt: timestamp('created_at').notNull(),
  updatedAt: timestamp('updated_at').notNull(),
})
