import { relations } from 'drizzle-orm'
import { integer, pgTable, text, timestamp } from 'drizzle-orm/pg-core'
import { asset } from './asset'
import { community } from './community'

export const communityOnAsset = pgTable('community_on_asset', {
  assetId: integer('asset_id').notNull(),
  communityId: text('community_id').notNull(),
  createdAt: timestamp('created_at').notNull(),
})

export const communityOnAssetRelations = relations(communityOnAsset, ({ one }) => ({
  asset: one(asset, {
    fields: [communityOnAsset.assetId],
    references: [asset.id],
  }),
  community: one(community, {
    fields: [communityOnAsset.communityId],
    references: [community.id],
  }),
}))
