import { integer, doublePrecision, varchar, timestamp } from 'drizzle-orm/pg-core'
import { tsdbSchema } from './tsdb'

export const tsdbCarbonEmissionFeeds = tsdbSchema.table('mvw_esg_co2_metrics', {
  assetId: integer('asset_id'),
  platformId: integer('platform_id'),
  name: varchar('name'),
  assetType: varchar('asset_type'),
  type: varchar('type'),
  consensus_mechanism: varchar('consensus_mechanism'),
  emission: doublePrecision('co2_emission'),
  emissionPerTx: doublePrecision('co2_emission_per_tx'),
  emissionPerTxPerNode: doublePrecision('co2_emission_per_tx_per_node'),
  tps: doublePrecision('tps'),
  validators: doublePrecision('validators'),
  latestDate: timestamp('latest_date'),
})
