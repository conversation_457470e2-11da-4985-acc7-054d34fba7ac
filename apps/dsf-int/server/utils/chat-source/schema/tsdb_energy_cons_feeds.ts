import { date, doublePrecision, integer, varchar } from 'drizzle-orm/pg-core'
import { tsdbSchema } from './tsdb'

export const tsdbEnergyConsFeeds = tsdbSchema.table('energy_cons_feeds', {
  name: varchar('name'),
  date: date('date'),
  layer: varchar('layer'),
  powerUse: doublePrecision('poweruse_bestguess_w'),
  energyConsumptionTx: doublePrecision('energyconstx_bestguess_wh'),
  energyConsumptionTxNode: doublePrecision('energyconstxnode_bestguess_wh'),
  throughputTps: doublePrecision('throughput_tps'),
  validators: doublePrecision('validators'),
  consensusMechanism: varchar('consensus_mechanism'),
  type: varchar('type'),
  source: varchar('source'),
  assetId: integer('asset_id'),
  platformId: integer('platform_id'),
})
