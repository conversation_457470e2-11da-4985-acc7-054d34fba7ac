import Mustache from 'mustache'

export function contactUsTemplate(data: {
  firstName: string
  lastName: string
  email: string
  topic: string
  message: string
  company: string
}) {
  const config = useRuntimeConfig()

  const html = `
    <!DOCTYPE html>
      <html lang="en" xmlns:v="urn:schemas-microsoft-com:vml">
      <head>
        <meta charset="utf-8">
        <meta name="x-apple-disable-message-reformatting">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="format-detection" content="telephone=no, date=no, address=no, email=no, url=no">
        <meta name="color-scheme" content="light dark">
        <meta name="supported-color-schemes" content="light dark">
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:wght@400;500;600;700&display=swap" rel="stylesheet" media="screen">
        <!--[if mso]>
            <noscript>
              <xml>
                <o:OfficeDocumentSettings xmlns:o="urn:schemas-microsoft-com:office:office">
                  <o:PixelsPerInch>96</o:PixelsPerInch>
                </o:OfficeDocumentSettings>
              </xml>
            </noscript>
            <style>
              td,
              th,
              div,
              p,
              a,
              h1,
              h2,
              h3,
              h4,
              h5,
              h6 {
                font-family: 'Segoe UI', sans-serif;
                mso-line-height-rule: exactly;
              }
            </style>
          <![endif]-->
        <title>Nodiens Contact Us</title>
        <style>
          .visited-text-black:visited {
            color: #000 !important
          }
          @media (max-width: 600px) {
            .sm-my-6 {
              margin-top: 24px !important;
              margin-bottom: 24px !important
            }
            .sm-px-4 {
              padding-left: 16px !important;
              padding-right: 16px !important
            }
            .sm-px-5 {
              padding-left: 20px !important;
              padding-right: 20px !important
            }
          }
        </style>
      </head>
      <body style="font-family: IBM Plex Mono, monospace; margin: 0; width: 100%; background-color: #F7F7FA; padding: 40px 0; -webkit-font-smoothing: antialiased; word-break: break-word">
        <div style="display: none">
          Nodiens Contact Us
          &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847;
        </div>
        <div role="article" aria-roledescription="email" aria-label="Nodiens Contact Us" lang="en">
          <div class="sm-px-4" style="background-color: #F7F7FA; font-family: IBM Plex Mono, monospace; color: #1B1F2B">
            <table align="center" cellpadding="0" cellspacing="0" role="none">
              <tr>
                <td style="width: 660px; max-width: 100%">
                  <div class="sm-my-6" style="margin-top: 40px; margin-bottom: 40px; padding-left: 32px; padding-right: 32px; text-align: left">
                    <a href="${config.public.appUrl}">
                      <img src="${config.supabaseUrl}/storage/v1/object/public/assets/logo.png" width="195" alt="Horizon Risk Labs" style="max-width: 100%; vertical-align: middle; line-height: 1">
                    </a>
                  </div>
                  <table style="width: 100%;" cellpadding="0" cellspacing="0" role="none">
                    <tr>
                      <td class="sm-px-5" style="border-radius: 4px; background-color: #fff; padding: 32px; font-size: 16px; color: #334155; box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05)">
                        <p style="margin: 0 0 20px; font-size: 24px; font-weight: 600; line-height: 24px">Thank you for reaching out to Nodiens</p>
                        <div style="margin: 0 0 12px; line-height: 20px">
                          Hi {{firstName}} {{lastName}}, your submission has been received. Our team is dedicated to assisting you, and we will
                          be in touch with you shortly to follow up on your request.
                        </div>
                        <div style="margin: 0 0 12px; line-height: 20px;">
                          For your records, here’s a copy of the
                          information you submitted to us:
                        </div>
                        <div style="margin-bottom: 20px; margin-left: 20px">
                          <hr>
                          <p style="margin: 0; line-height: 20px;">First Name: {{firstName}}</p>
                          <hr>
                          <p style="margin: 0; line-height: 20px;">Last Name: {{lastName}}</p>
                          <hr>
                          <p style="margin: 0; line-height: 20px;">Email Address: {{email}}</p>
                          <hr>
                          <p style="margin: 0; line-height: 20px;">Company: {{company}}</p>
                          <hr>
                          <p style="margin: 0; line-height: 20px;">Topic: {{topic}}</p>
                          <hr>
                          <p style="margin: 0; line-height: 20px;">Message: {{message}}</p>
                          <hr>
                        </div>
                        <p style="margin: 0 0 12px; line-height: 20px;">
                          You may also contact us at:
                        </p>
                        <a href="mailto:<EMAIL>" style="margin: 0 0 12px; display: inline-block; cursor: pointer; line-height: 20px; color: #000; text-decoration-line: underline">
                          <EMAIL>
                        </a>
                      </td>
                    </tr>
                    <tr role="separator">
                      <td>&zwj;
                        <p style="text-align: center; font-size: 12px">
                          <a href="${config.public.appUrl}" class="visited-text-black" target="_blank">Nodiens.com</a>
                          is a website operated by Horizon Risk Labs Limited.
                        </p>
                        <p style="text-align: center; font-size: 12px;">This email was sent to <span style="text-decoration-line: underline;">{{email}}</span>
                        </p>
                        <p style="text-align: center; font-size: 12px;">
                          <a href="${config.public.appUrl}" target="_blank" style="font-size: 12px; color: #000;">Nodiens.com</a>
                          |
                          <a href="${config.public.appUrl}/legal/terms" target="_blank" style="font-size: 12px; color: #000;">Terms &
                            Privacy</a>
                        </p>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
            </table>
          </div>
        </div>
      </body>
    </html>
  `

  return Mustache.render(html, data)
}
