import Mustache from 'mustache'

export function submissionTemplate(data: {
  email: string
  type: string
  submittedAt: string
  content: string
  from: string
}) {
  const html = `<!DOCTYPE html>
  <html lang="en">
    <head>
      <meta charset="UTF-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <title>Bug Report</title>
    </head>
    <body>
      <div>
        <p><b>Submitted At :</b> {{submittedAt}}</p>
        <p><b>From :</b> {{from}} - <a href="{{email}}">{{email}}</a><p>
        <p><b>Type :</b> {{type}}</p>
      </div>
      <hr />
      {{{content}}}
    </body>
  </html>
  `

  return Mustache.render(html, data)
}
