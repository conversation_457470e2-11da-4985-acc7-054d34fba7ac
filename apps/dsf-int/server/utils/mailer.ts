import nodemailer from 'nodemailer'

const config = useRuntimeConfig()

export async function sendEmail(opt: { to: string; subject: string; text?: string; html: string }) {
  if (!config.smtpHost || !config.smtpPort || !config.smtpUser || !config.smtpPassword) {
    throw new Error('smtp requires smtp environment')
  }

  const transporter = nodemailer.createTransport({
    host: config.smtpHost,
    port: config.smtpPort,
    secure: false,
    auth: {
      user: config.smtpUser,
      pass: config.smtpPassword,
    },
  })

  const info = await transporter.sendMail({
    from: config.smtpEmail,
    to: opt.to,
    subject: opt.subject,
    text: opt.text,
    html: opt.html,
  })

  return info
}
