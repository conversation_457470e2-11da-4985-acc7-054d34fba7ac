import { jsonb, pgTable, serial, timestamp, uuid, varchar } from 'drizzle-orm/pg-core'
import { users } from './users'

export const UserSubscriptions = pgTable('user_subscriptions', {
  id: serial('id').primaryKey(),
  userId: uuid('user_id')
    .notNull()
    .references(() => users.userId),
  planId: varchar('plan_id').notNull(),
  planName: varchar('plan_name').notNull(),
  status: varchar('status').notNull(),
  paymentStatus: varchar('payment_status').notNull(),
  switchRawData: jsonb('switch_raw_data').$type<Record<string, unknown>>(),
  expiredDate: timestamp('expired_date'),
  activeAt: timestamp('active_at').defaultNow(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  rawData: jsonb('raw_data').default({}).$type<Record<string, unknown>>(),
})
