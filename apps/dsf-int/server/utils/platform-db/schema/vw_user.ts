import { boolean, integer, jsonb, pgTable, text, timestamp, uuid, varchar } from 'drizzle-orm/pg-core'
import { authUsers } from './auth/users'
import { userRoleType, userStatusType } from './users'

export const vwUser = pgTable('vw_user', {
  id: uuid('id').primaryKey(),
  userId: uuid('user_id')
    .notNull()
    .references(() => authUsers.id),
  displayName: varchar('display_name').notNull(),
  firstName: varchar('first_name').notNull(),
  lastName: varchar('last_name').notNull(),
  dashboard: jsonb('dashboard'),
  dashboardChange: integer('dashboard_changes').notNull().default(0),
  suspendedReason: text('suspended_reason'),
  suspendedAt: timestamp('suspended_at'),
  deletedAt: timestamp('deleted_at'),
  role: userRoleType('role').notNull(),
  status: userStatusType('status').notNull(),
  rawOnboarding: jsonb('raw_onboarding').notNull(),
  company: varchar('company'),
  hashpackAccountId: text('hashpack_account_id'),
  web3Auth: jsonb('web3_auth'),
  rawUserMetaData: jsonb('raw_user_meta_data').default({}).$type<Record<string, unknown>>(),
  excludedPaywall: boolean('excluded_paywall').notNull().default(false),
  email: varchar('email').notNull(),
})
