import { relations } from 'drizzle-orm'
import { integer, jsonb, text, timestamp, uuid, varchar, customType, pgTable, boolean } from 'drizzle-orm/pg-core'
import { authUsers } from './auth/users'

export const userRoleType = customType<{ data: 'user' | 'admin' | 'super_admin' }>({
  dataType() {
    return 'string'
  },
})

export const userStatusType = customType<{ data: 'active' | 'inactive' | 'suspend' }>({
  dataType() {
    return 'string'
  },
})

export const users = pgTable('users', {
  id: uuid('id').primaryKey(),
  userId: uuid('user_id')
    .notNull()
    .references(() => authUsers.id),
  displayName: varchar('display_name').notNull(),
  dashboard: jsonb('dashboard'),
  dashboardChange: integer('dashboard_changes').notNull().default(0),
  suspendedReason: text('suspended_reason'),
  suspendedAt: timestamp('suspended_at'),
  deletedAt: timestamp('deleted_at'),
  role: userRoleType('role').notNull(),
  status: userStatusType('status').notNull(),
  rawOnboarding: jsonb('raw_onboarding').notNull(),
  company: varchar('company'),
  hashpackAccountId: text('hashpack_account_id'),
  web3Auth: jsonb('web3_auth'),
  excludedPaywall: boolean('excluded_paywall').notNull().default(false),
})

export const usersRelations = relations(users, ({ one }) => ({
  authUser: one(authUsers, {
    fields: [users.userId],
    references: [users.id],
  }),
}))

export type UserSchemaType = typeof users.$inferSelect
