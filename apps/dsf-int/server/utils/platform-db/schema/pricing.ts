import { boolean, doublePrecision, integer, jsonb, pgTable, text, timestamp, varchar } from 'drizzle-orm/pg-core'

export const Pricing = pgTable('pricings', {
  id: integer('id').primaryKey(),
  name: varchar('name').notNull(),
  description: text('description').notNull(),
  monthlyPrice: doublePrecision('monthly_price').notNull(),
  annualPrice: doublePrecision('annual_price').notNull(),
  stripePriceIds: jsonb('stripe_price_ids').notNull().$type<Record<string, unknown>>(),
  features: jsonb('features').notNull().$type<{
    [key: string]: Record<string, boolean | string | number>
  }>(),
  createdAt: timestamp('created_at').notNull(),
  isEnabled: boolean('is_enabled').notNull().default(false),
  level: integer('level').notNull(),
})
