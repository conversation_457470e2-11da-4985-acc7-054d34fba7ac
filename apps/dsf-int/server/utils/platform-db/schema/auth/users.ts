import { uuid, varchar, text, timestamp, jsonb, integer, boolean } from 'drizzle-orm/pg-core'
// import { authSchema } from './schema'
import { relations } from 'drizzle-orm'
import { authSchema } from './schema'
import { users } from '../users'

export const authUsers = authSchema.table('users', {
  instanceId: uuid('instance_id'),
  id: uuid('id').primaryKey().notNull(),
  aud: varchar('aud', { length: 255 }),
  role: varchar('role', { length: 255 }),
  email: varchar('email', { length: 255 }),
  encryptedPassword: varchar('encrypted_password', { length: 255 }),
  emailConfirmedAt: timestamp('email_confirmed_at'),
  invitedAt: timestamp('invited_at'),
  confirmationToken: varchar('confirmation_token', { length: 255 }),
  confirmationSentAt: timestamp('confirmation_sent_at'),
  recoveryToken: varchar('recovery_token', { length: 255 }),
  recoverySentAt: timestamp('recovery_sent_at'),
  emailChangeTokenNew: varchar('email_change_token_new', { length: 255 }),
  emailChange: varchar('email_change', { length: 255 }),
  emailChangeSentAt: timestamp('email_change_sent_at'),
  lastSignInAt: timestamp('last_sign_in_at'),
  rawAppMetaData: jsonb('raw_app_meta_data'),
  rawUserMetaData: jsonb('raw_user_meta_data'),
  isSuperAdmin: boolean('is_super_admin'),
  createdAt: timestamp('created_at'),
  updatedAt: timestamp('updated_at'),
  phone: text('phone'),
  phoneConfirmedAt: timestamp('phone_confirmed_at'),
  phoneChange: text('phone_change').default(''),
  phoneChangeToken: varchar('phone_change_token', { length: 255 }).default(''),
  phoneChangeSentAt: timestamp('phone_change_sent_at'),
  confirmedAt: timestamp('confirmed_at'),
  emailChangeTokenCurrent: varchar('email_change_token_current', { length: 255 }).default(''),
  emailChangeConfirmStatus: integer('email_change_confirm_status').default(0),
  bannedUntil: timestamp('banned_until'),
  reauthenticationToken: varchar('reauthentication_token', { length: 255 }).default(''),
  reauthenticationSentAt: timestamp('reauthentication_sent_at'),
  isSSOUser: boolean('is_sso_user').default(false).notNull(),
  deletedAt: timestamp('deleted_at'),
  isAnonymous: boolean('is_anonymous').default(false).notNull(),
})

export const authUsersRelations = relations(authUsers, ({ one }) => ({
  users: one(users, {
    fields: [authUsers.id],
    references: [users.userId],
    relationName: 'public_users_auth',
  }),
}))
