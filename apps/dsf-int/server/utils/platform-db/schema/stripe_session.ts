import { doublePrecision, jsonb, pgTable, serial, text, timestamp, uuid, varchar } from 'drizzle-orm/pg-core'

export const StripeSession = pgTable('stripe_session', {
  id: serial('id').primaryKey(),
  userId: uuid('user_id'),
  sessionId: text('session_id'),
  rawData: jsonb('raw_data').$type<Record<string, unknown>>(),
  productRawData: jsonb('product_raw_data').$type<Record<string, unknown>>(),
  amountCapturable: doublePrecision('amount_capturable'),
  status: varchar('status').default('unpaid'),
  cancelledAt: timestamp('canceled_at'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
})
