import { drizzle } from 'drizzle-orm/postgres-js'
import postgres from 'postgres'
import * as schema from './schema'

const runtimeConfig = useRuntimeConfig()

const client = postgres({
  host: runtimeConfig.nodiensDbHost,
  port: runtimeConfig.nodiensDbPort,
  username: runtimeConfig.nodiensDbUser,
  password: runtimeConfig.nodiensDbPassword,
  database: runtimeConfig.nodiensDbName,
  ssl: false,
})

export const platformClient = drizzle(client, {
  schema,
})
