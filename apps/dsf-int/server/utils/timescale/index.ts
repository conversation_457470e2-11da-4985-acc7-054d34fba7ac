import postgres from 'postgres'
import { drizzle } from 'drizzle-orm/postgres-js'
import * as schema from './schema'

const runtimeConfig = useRuntimeConfig()

const client = postgres({
  host: runtimeConfig.timescaleHost,
  port: runtimeConfig.timescalePort,
  user: runtimeConfig.timescaleDbUser,
  password: runtimeConfig.timescaleDbPassword,
  database: runtimeConfig.timescaleDbName,
  max: runtimeConfig.timescaleDbMaxConnection,
  ssl: runtimeConfig.timescaleDbSsl,
})

export const timescaleClient = drizzle(client, {
  schema,
})
