import { doublePrecision, integer, pgTable, timestamp, varchar } from 'drizzle-orm/pg-core'

export const redditIndicator = pgTable('reddit_indicators', {
  subreddit_id: varchar('subreddit_id'),
  timestamp: timestamp('timestamp'),
  timeframe: integer('timeframe'),
  nr_posts: integer('nr_posts'),
  nr_comments: integer('nr_comments'),
  nr_nested_comments: integer('nr_nested_comments'),
  nr_hot_posts: integer('nr_hot_posts'),
  nr_odd_posts: integer('nr_odd_posts'),
  nr_subscribers: integer('nr_subscribers'),
  nr_unique_authors: integer('nr_unique_authors'),
  nr_words: integer('nr_words'),
  nr_bad_words: integer('nr_bad_words'),
  nr_posts_net_upvotes: integer('nr_posts_net_upvotes'),
  nr_comments_net_upvotes: integer('nr_comments_net_upvotes'),
  ind_engage_ratio: doublePrecision('ind_engage_ratio'),
  ind_odd_post_ratio: doublePrecision('ind_odd_post_ratio'),
  ind_hot_post_ratio: doublePrecision('ind_hot_post_ratio'),
  ind_unique_creator_ratio: doublePrecision('ind_unique_creator_ratio'),
  ind_nested_reply_ratio: doublePrecision('ind_nested_reply_ratio'),
  ind_bad_word_ratio: doublePrecision('ind_bad_word_ratio'),
  ind_timeframe_net_vote: doublePrecision('ind_timeframe_net_vote'),
  ind_creator_skew: doublePrecision('ind_creator_skew'),
  ind_creator_diversity: doublePrecision('ind_creator_diversity'),
})

export type redditIndicatorType = typeof redditIndicator.$inferSelect
