import { timestamp, pgTable, varchar, text } from 'drizzle-orm/pg-core'

// Define the decentralisation tree table schema
export const decentralisationTree = pgTable('decentralisation_tree', {
  metricName: varchar('metric_name'),
  metricCluster: varchar('metric_cluster'),
  createdAt: timestamp('created_at').notNull(),
  rawJsonStr: text('raw_json_str'),
})

export type DecentralisationTree = typeof decentralisationTree.$inferSelect
