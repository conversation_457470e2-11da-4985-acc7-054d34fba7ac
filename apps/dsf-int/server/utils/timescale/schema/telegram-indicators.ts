import { bigint, doublePrecision, integer, pgTable, timestamp } from 'drizzle-orm/pg-core'

export const telegramIndicator = pgTable('telegram_indicators', {
  tg_chat_id: bigint('tg_chat_id', { mode: 'number' }),
  timestamp: timestamp('timestamp'),
  timeframe: integer('timeframe'),
  nr_messages: integer('nr_messages'),
  nr_members: integer('nr_members'),
  nr_commenters: integer('nr_commenters'),
  nr_commenters_rotation: integer('nr_commenters_rotation'),
  nr_deleted_users: integer('nr_deleted_users'),
  nr_bot_users: integer('nr_bot_users'),
  nr_words: integer('nr_words'),
  nr_bad_words: integer('nr_bad_words'),
  ind_engage_ratio: doublePrecision('ind_engage_ratio'),
  ind_participation: doublePrecision('ind_participation'),
  ind_commenters_skew: doublePrecision('ind_commenters_skew'),
  ind_commenters_quality: doublePrecision('ind_commenters_quality'),
  ind_commenters_rotation: doublePrecision('ind_commenters_rotation'),
  ind_bad_word_ratio: doublePrecision('ind_bad_word_ratio'),
  ind_bot_ratio: doublePrecision('ind_bot_ratio'),
})

export type telegramIndicatorType = typeof telegramIndicator.$inferSelect
