import { date, doublePrecision, integer, pgTable, text, timestamp, varchar } from 'drizzle-orm/pg-core'

export const co2DailyMetrics = pgTable('mvw_esg_co2_daily_metrics', {
  metricCluster: text('metric_cluster'),
  metric: text('metric'),
  entityName: text('entity_name'),
  assetId: integer('asset_id'),
  platformId: integer('platform_id'),
  date: date('date'),
  locationType: text('location_type'),
  countryAlpha3Code: text('country_alpha_3_code'),
  locationIdentifier: text('location_identifier'),
  energyType: text('energy_type'),
  unit: text('unit'),
  consensusMechanism: text('consensus_mechanism'),
  type: text('type'),
  assetType: text('asset_type'),
  lowerBoundValue: doublePrecision('lower_bound_value'),
  bestGuessValue: doublePrecision('best_guess_value'),
  upperBoundValue: doublePrecision('upper_bound_value'),
  processedAt: timestamp('processed_at'),
  validators: doublePrecision('validators'),
  tps: doublePrecision('tps'),
})

export const carbonEmissionFeeds = pgTable('mvw_esg_co2_metrics', {
  assetId: integer('asset_id'),
  platformId: integer('platform_id'),
  name: varchar('name'),
  assetType: varchar('asset_type'),
  type: varchar('type'),
  consensus_mechanism: varchar('consensus_mechanism'),
  emission: doublePrecision('co2_emission'),
  emissionPerTx: doublePrecision('co2_emission_per_tx'),
  emissionPerTxPerNode: doublePrecision('co2_emission_per_tx_per_node'),
  tps: doublePrecision('tps'),
  validators: doublePrecision('validators'),
  latestDate: timestamp('latest_date'),
})
