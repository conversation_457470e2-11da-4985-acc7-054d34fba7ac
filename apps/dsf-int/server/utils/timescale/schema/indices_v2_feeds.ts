import { doublePrecision, integer, pgTable, timestamp, varchar } from 'drizzle-orm/pg-core'

export const indicesV2Feeds = pgTable('indices_v2_feeds', {
  assetId: integer('asset_id').notNull(),
  cmcId: integer('cmc_id').notNull(),
  platform: varchar('platform').notNull(),
  timestamp: timestamp('timestamp').notNull(),
  moodIndex: doublePrecision('mood_index'),
  trustIndex: doublePrecision('trust_index'),
  moodRank: doublePrecision('mood_rank'),
  trustRank: doublePrecision('trust_rank'),
  communitySize: integer('community_size'),
  energyConsumptionTx: doublePrecision('energy_consumption_tx'),
  nrMessages: doublePrecision('nr_messages'),
  v1VulgarityIndex: doublePrecision('v1_vulgarity_index'),
  dayChangedMood: doublePrecision('1d_changed_mood'),
  dayChangedTrust: doublePrecision('1d_changed_trust'),
})
