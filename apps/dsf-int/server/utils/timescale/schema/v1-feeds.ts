import { boolean, integer, pgTable, text, varchar } from 'drizzle-orm/pg-core'

export const v1Feeds = pgTable('v1_feeds', {
  platform: varchar('platform').notNull(),
  platform_id: varchar('platform_id').notNull(),
  cmc_id: integer('cmc_id'),
  name: text('name'),
  symbol: varchar('symbol'),
  logo: text('logo'),
  rank: integer('rank'),
  website: text('website'),
  url: text('url'),
  asset_type: varchar('asset_type'),
  is_publishable: boolean('is_publishable'),
  slug: varchar('slug'),
})

export type V1Feeds = typeof v1Feeds.$inferSelect
