import { doublePrecision, integer, pgTable, timestamp, varchar } from 'drizzle-orm/pg-core'

export enum MetricNames {
  ConcentrationOfInfluenceAmongDevelopersHHI = 'Concentration of Influence Among Developers (HHI)',
  ConcentrationOfInfluenceAmongAuthorsHHI = 'Concentration of Influence among Authors (HHI)',
  ConsensusPowerConcentrationHHI = 'Consensus Power Concentration (HHI)',
  ConsensusPowerConcentrationNakamotoCoefficient = 'Consensus Power Concentration (Nakamoto Coefficient)',
  ConsensusPowerInequalityGiniCoefficient = 'Consensus Power Inequality (Gini Coefficient)',
  ConsensusPowerInequalityTheilIndex = 'Consensus Power Inequality (Theil index)',
  DistributionOfAuthorsGiniCoefficient = 'Distribution of Authors (Gini Coefficient)',
  DistributionOfDevelopersGiniCoefficient = 'Distribution of developers (Gini coefficient)',
  DiversityOfParticipantsShannonEntropy = 'Diversity of Participants (Shannon Entropy)',
  ReviewersPowerConcentrationHHI = 'Reviewers Power Concentration (HHI)',
}

export enum MetricCluster {
  ConsensusPower = 'Consensus Power',
  ImprovementProposal = 'Improvement Proposal',
  ReferenceClientDevelopment = 'Reference Client Development',
}

export const DecentralizeFilter: Record<string, [MetricNames, MetricCluster] | null> = {
  ipAuthDistrGini: [MetricNames.DistributionOfAuthorsGiniCoefficient, MetricCluster.ImprovementProposal],
  ipParticipantDivGini: [MetricNames.DiversityOfParticipantsShannonEntropy, MetricCluster.ImprovementProposal],
  ipAuthorInflConcHHI: [MetricNames.ConcentrationOfInfluenceAmongAuthorsHHI, MetricCluster.ImprovementProposal],
  ipGovOrdinal: null,
  rcpDevDistrGini: [MetricNames.DistributionOfDevelopersGiniCoefficient, MetricCluster.ReferenceClientDevelopment],
  rcpParticipantDivShannon: [
    MetricNames.DiversityOfParticipantsShannonEntropy,
    MetricCluster.ReferenceClientDevelopment,
  ],
  rcpDevInflConcHHI: [MetricNames.ConcentrationOfInfluenceAmongDevelopersHHI, MetricCluster.ReferenceClientDevelopment],
  rcdRevrPowerConcHHI: [MetricNames.ReviewersPowerConcentrationHHI, MetricCluster.ReferenceClientDevelopment],
  consensusPowerNeGini: [MetricNames.ConsensusPowerInequalityGiniCoefficient, MetricCluster.ConsensusPower],
  consensusPowerNeTheil: [MetricNames.ConsensusPowerInequalityTheilIndex, MetricCluster.ConsensusPower],
  consensusPowerConcNakamoto: [
    MetricNames.ConsensusPowerConcentrationNakamotoCoefficient,
    MetricCluster.ConsensusPower,
  ],
  consensusPowerConcHHI: [MetricNames.ConsensusPowerConcentrationHHI, MetricCluster.ConsensusPower],
  coinDistrOrdinal: null,
}

export const decentralisationRaw = pgTable('decentralisation_raw', {
  metricName: varchar('metric_name'),
  metricCluster: varchar('metric_cluster'),
  date: timestamp('date'),
  processedAt: timestamp('processed_at'),
  assetId: integer('asset_id'),
  value: doublePrecision('value'),
})

export const decentralisationNormalised = pgTable('decentralisation_normalised', {
  metricName: varchar('metric_name'),
  metricCluster: varchar('metric_cluster'),
  date: timestamp('date'),
  processedAt: timestamp('processed_at'),
  assetId: integer('asset_id'),
  value: doublePrecision('value'),
})
