import { doublePrecision, integer, pgTable, timestamp, varchar } from 'drizzle-orm/pg-core'

export const indiceV2 = pgTable('indices_v2', {
  assetId: integer('asset_id').notNull(),
  platform: varchar('platform').notNull(),
  timestamp: timestamp('timestamp').notNull(),
  moodIndex: doublePrecision('mood_index'),
  trustIndex: doublePrecision('trust_index'),
  communitySize: integer('community_size'),
  nrMessages: doublePrecision('nr_messages'),
  moodRank: doublePrecision('mood_rank'),
  trustRank: doublePrecision('trust_rank'),
  energyConsumptionTx: doublePrecision('energy_consumption_tx'),
  v1VulgarityIndex: doublePrecision('v1_vulgarity_index'),
})

type indiceV2Type = typeof indiceV2.$inferSelect

export interface IndiceV2 extends indiceV2Type {
  numericTimestamp?: number
}
