import { boolean, date, doublePrecision, pgTable, text, varchar } from 'drizzle-orm/pg-core'

export const vwEsgv2 = pgTable('esgv3', {
  name: varchar('name').notNull(),
  date: date('date').notNull(),
  source: text('source').notNull(),
  validators: doublePrecision('validators'),
  throughputTps: doublePrecision('throughput_tps'),
  poweruseLowerW: doublePrecision('poweruse_lowerbound_w'),
  poweruseBestGuessW: doublePrecision('poweruse_bestguess_w'),
  poweruseUpperW: doublePrecision('poweruse_upperbound_w'),
  energyConsumptionTxLowerWh: doublePrecision('energyconstx_lowerbound_wh'),
  energyConsumptionTxBestGuessWh: doublePrecision('energyconstx_bestguess_wh'),
  energyConsumptionTxUpperWh: doublePrecision('energyconstx_upperbound_wh'),
  energyConsumptionTxNodeLowerWh: doublePrecision('energyconstxnode_lowerbound_wh'),
  energyConsumptionTxNodeBestGuessWh: doublePrecision('energyconstxnode_bestguess_wh'),
  energyConsumptionTxNodeUpperWh: doublePrecision('energyconstxnode_upperbound_wh'),
  isForwardFilled: boolean('isforwardfilled'),
  type: varchar('type').notNull(),
})
