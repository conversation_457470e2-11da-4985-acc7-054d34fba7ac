import { doublePrecision, integer, pgTable, primaryKey, timestamp, varchar } from 'drizzle-orm/pg-core'
import { relations } from 'drizzle-orm'
import { esgBlockchain } from './esg-blockchain'

export const esgTimeseries = pgTable(
  'esg_timeseries',
  {
    blockchain_id: varchar('blockchain_id'),
    index_group: varchar('index_group').notNull(),
    measured_at: timestamp('measured_at').notNull(),
    node_power: doublePrecision('node_power'),
    nodeCount: integer('node_count'),
    tps: doublePrecision('tps'),
    totalPowerDemand: doublePrecision('total_power_demand'),
    consumptionTx: doublePrecision('consumption_tx'),
    consumptionTxNode: doublePrecision('consumption_tx_node'),
    hashrate: doublePrecision('hashrate'),
    consensusAbbr: varchar('consensus_abbreviation'),
    consensusMechanism: varchar('consensus_mechanism'),
  },
  table => ({
    pk: primaryKey(table.blockchain_id, table.index_group, table.measured_at),
  })
)

export const esgTimeseriesRelations = relations(esgTimeseries, ({ one }) => ({
  blockchain: one(esgBlockchain, {
    fields: [esgTimeseries.blockchain_id],
    references: [esgBlockchain.id],
  }),
}))

export type EsgTimeseries = typeof esgTimeseries.$inferSelect
