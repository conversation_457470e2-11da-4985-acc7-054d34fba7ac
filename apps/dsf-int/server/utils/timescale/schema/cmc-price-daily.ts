import { relations } from 'drizzle-orm'
import { doublePrecision, integer, pgTable, timestamp } from 'drizzle-orm/pg-core'
import { v1Feeds } from './v1-feeds'

export const cmcPriceDaily = pgTable('cmc_price_daily', {
  cmc_id: integer('cmc_id').notNull(),
  timestamp: timestamp('timestamp').notNull(),
  open: doublePrecision('open'),
  high: doublePrecision('high'),
  low: doublePrecision('low'),
  close: doublePrecision('close'),
  volume: doublePrecision('volume'),
  marketcap: doublePrecision('marketcap'),
})

export const cmcPriceDailyRelations = relations(cmcPriceDaily, ({ one }) => ({
  v1Feeds: one(v1Feeds, {
    fields: [cmcPriceDaily.cmc_id],
    references: [v1Feeds.cmc_id],
  }),
}))

export type CmcPriceDaily = typeof cmcPriceDaily.$inferSelect
