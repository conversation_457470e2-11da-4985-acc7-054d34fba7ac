import { doublePrecision, pgTable, varchar, date, text, integer, boolean } from 'drizzle-orm/pg-core'

export const dailyEnergyCons = pgTable('daily_energy_cons', {
  name: varchar('name'),
  layer: varchar('layer'),
  consensusMechanism: varchar('consensus_mechanism'),
  validators: doublePrecision('validators'),
  throughputTps: doublePrecision('throughput_tps'),
  date: date('date'),
  type: text('type'),
  assetId: integer('asset_id'),
  platformId: integer('platform_id'),
})

export const analyticsEnergyCons = pgTable('analytics_energy_cons', {
  name: varchar('name'),
  date: date('date'),
  layer: text('layer'),
  powerUseLowerBoundW: doublePrecision('poweruse_lowerbound_w'),
  powerUseBestGuessW: doublePrecision('poweruse_bestguess_w'),
  powerUseUpperBoundW: doublePrecision('poweruse_upperbound_w'),
  energyConsTxLowerBoundWh: doublePrecision('energyconstx_lowerbound_wh'),
  energyConsTxBestGuessWh: doublePrecision('energyconstx_bestguess_wh'),
  energyConsTxUpperBoundWh: doublePrecision('energyconstx_upperbound_wh'),
  energyConsTxNodeLowerBoundWh: doublePrecision('energyconstxnode_lowerbound_wh'),
  energyConsTxNodeBestGuessWh: doublePrecision('energyconstxnode_bestguess_wh'),
  energyConsTxNodeUpperBoundWh: doublePrecision('energyconstxnode_upperbound_wh'),
  isForwarldFilled: boolean('isforwardfilled'),
  assetId: integer('asset_id'),
  platformId: integer('platform_id'),
})

export const energyConsFeeds = pgTable('energy_cons_feeds', {
  name: varchar('name'),
  date: date('date'),
  layer: text('layer'),
  powerUseBestGuessW: doublePrecision('poweruse_bestguess_w'),
  energyConsTxBestGuessWh: doublePrecision('energyconstx_bestguess_wh'),
  energyConsTxPerNodeBestGuessWh: doublePrecision('energyconstxnode_bestguess_wh'),
  validators: doublePrecision('validators'),
  throughputTps: doublePrecision('throughput_tps'),
  consensusMechanism: varchar('consensus_mechanism'),
  type: text('type'),
  assetId: integer('asset_id'),
  platformId: integer('platform_id'),
})
