import { doublePrecision, integer, pgTable, timestamp, varchar } from 'drizzle-orm/pg-core'

export const indice = pgTable('indices', {
  platform: varchar('platform').notNull(),
  platform_id: varchar('platform_id').notNull(),
  timestamp: timestamp('timestamp'),
  timeframe: integer('timeframe'),
  community_size: integer('community_size'),
  avg_msgs: integer('avg_msgs'),
  mood_index: doublePrecision('mood_index'),
  trust_index: doublePrecision('trust_index'),
})

export type Indice = typeof indice.$inferSelect
