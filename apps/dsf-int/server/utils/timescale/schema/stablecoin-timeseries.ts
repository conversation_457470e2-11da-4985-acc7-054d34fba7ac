import { doublePrecision, pgTable, timestamp, varchar, integer } from 'drizzle-orm/pg-core'

export const stablecoinTimeseries = pgTable('stablecoin_indices_timeseries', {
  symbol: varchar('symbol').notNull(),
  createdAt: timestamp('created_at').notNull(),
  marketCap: doublePrecision('market_cap'),
  thirtyDayVolatility: doublePrecision('thirty_day_volatility'),
  '24hrTradingVolCex': doublePrecision('_24hr_trading_vol_cex'),
  '24hrTradingVolDex': doublePrecision('_24hr_trading_vol_dex'),
  aYearMaxDepegDays: doublePrecision('a_year_max_depeg_days').notNull(),
  numberOfPairsCex: integer('number_of_pairs_cex'),
  numberOfPairsDex: integer('number_of_pairs_dex'),
  costOfLiquidity10ThouPcCex: doublePrecision('cost_of_liquidity_10_thou_pc_cex'),
  costOfLiquidityHundrethPcCex: doublePrecision('cost_of_liquidity_hundreth_pc_cex'),
  costofLiquidity1PcCex: doublePrecision('cost_of_liquidity_1_pc_cex'),
  costofLiquidity100Cex: doublePrecision('cost_of_liquidity_100_cex'),
  costOfLiquidity10kCex: doublePrecision('cost_of_liquidity_10k_cex'),
  costOfLiquidity1mCex: doublePrecision('cost_of_liquidity_1m_cex'),
  liquidityConcentrationCex: doublePrecision('liquidity_concentration_cex'),
  costOfLiquidity10ThouPcDex: doublePrecision('cost_of_liquidity_10_thou_pc_dex'),
  costOfLiquidityHundrethPcDex: doublePrecision('cost_of_liquidity_hundreth_pc_dex'),
  costOfLiquidity1PcDex: doublePrecision('cost_of_liquidity_1_pc_dex'),
  costOfLiquidity100Dex: doublePrecision('cost_of_liquidity_100_dex'),
  costOfLiquidity10kDex: doublePrecision('cost_of_liquidity_10k_dex'),
  costOfLiquidity1mDex: doublePrecision('cost_of_liquidity_1m_dex'),
  liquidityConcentrationDex: doublePrecision('liquidity_concentration_dex'),
  assetId: integer('asset_id').notNull(),
  price: doublePrecision('price'),
})
