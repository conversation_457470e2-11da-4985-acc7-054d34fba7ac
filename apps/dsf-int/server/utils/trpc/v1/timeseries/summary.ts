import { set, sub } from 'date-fns'
import { and, asc, between, desc, eq, inArray, sql } from 'drizzle-orm'
import { divideArray } from '~/helper/number-helper'
import { timescaleClient } from '~/server/utils/timescale'
import { cmcPriceDaily, indice, redditIndicator, telegramIndicator, v1Feeds } from '~/server/utils/timescale/schema'
import { cacheMaxAgeConfig } from '~/server/utils/config'

export const cachedSumIndice = cachedFunction(
  async (platform: string) => {
    const feeds = await timescaleClient.query.v1Feeds.findFirst({
      columns: {
        platform_id: true,
        platform: true,
        cmc_id: true,
        logo: true,
        name: true,
        rank: true,
        url: true,
        website: true,
      },
      where: eq(v1Feeds.platform_id, platform),
    })

    if (feeds === undefined) {
      throw new Error('FEEDS_NOT_FOUND')
    }

    const timeNow = new Date()
    const minTime = new Date(2023, 1, 1)

    const indices = await timescaleClient
      .select({
        community_size: indice.community_size,
        avg_msgs: indice.avg_msgs,
        mood_index: indice.mood_index,
        trust_index: indice.trust_index,
        timestamp: indice.timestamp,
      })
      .from(indice)
      .where(and(eq(indice.platform_id, platform), between(indice.timestamp, minTime, timeNow)))
      .orderBy(asc(indice.timestamp))
    const startTimestamp = indices.length > 0 && indices[0] && indices[0].timestamp ? indices[0].timestamp : new Date(0)
    const endTimestamp =
      indices.length > 0 && indices[indices.length - 1] && indices[indices.length - 1]?.timestamp
        ? indices[indices.length - 1]?.timestamp
        : new Date(0)

    const prices = await timescaleClient
      .select({
        close: cmcPriceDaily.close,
        volume: cmcPriceDaily.volume,
        marketcap: cmcPriceDaily.marketcap,
        timestamp: cmcPriceDaily.timestamp,
      })
      .from(cmcPriceDaily)
      .where(
        and(
          eq(cmcPriceDaily.cmc_id, feeds.cmc_id!),
          between(cmcPriceDaily.timestamp, startTimestamp ?? new Date(0), endTimestamp ?? new Date(0)),
        ),
      )
      .orderBy(asc(cmcPriceDaily.timestamp))

    return {
      feeds,
      series: prices.map((price) => {
        const indiceIndex = indices.findIndex((ind) => ind.timestamp?.toDateString() === price.timestamp.toDateString())
        return {
          ...indices[indiceIndex],
          ...price,
        }
      }),
    }
  },
  {
    maxAge: cacheMaxAgeConfig,
    getKey: (platform: string) => `csi-${platform}`,
  },
)

export const cachedGraphIndice = cachedFunction(
  async (startDate: string, endDate: string, platform: string[]) => {
    const indices = await timescaleClient
      .select({
        mood_index: indice.mood_index,
        platform_id: indice.platform_id,
        trust_index: indice.trust_index,
        timestamp: indice.timestamp,
        avg_msgs: indice.avg_msgs,
        community_size: indice.community_size,
      })
      .from(indice)
      .where(and(sql`timestamp >= ${startDate}`, sql`timestamp <= ${endDate}`, inArray(indice.platform_id, platform)))

    return indices
  },
  {
    maxAge: cacheMaxAgeConfig,
    getKey: (startDate: string, endDate: string, platform: string) => `cgi-${startDate}-${endDate}-${platform}`,
  },
)

export const cachedRedditStatsSummary = cachedFunction(
  async (id: string) => {
    const today = set(new Date(), { hours: 0, minutes: 0, seconds: 0, milliseconds: 0 })
    const twoWeekEarlier = sub(today, { days: 14 })
    const stats = await timescaleClient.query.indice.findFirst({
      where: and(eq(indice.platform_id, id)),
      orderBy: desc(indice.timestamp),
    })

    const redditStats = await timescaleClient.query.redditIndicator.findMany({
      columns: {
        nr_comments: true,
        nr_bad_words: true,
        nr_unique_authors: true,
      },
      where: and(eq(redditIndicator.subreddit_id, id), between(redditIndicator.timestamp, twoWeekEarlier, today)),
      orderBy: desc(redditIndicator.timestamp),
    })

    return {
      bad_words: divideArray<(typeof redditStats)[0]>(redditStats, 'nr_bad_words'),
      message: divideArray<(typeof redditStats)[0]>(redditStats, 'nr_comments'),
      bots: 0,
      community: stats?.community_size ?? 0,
    }
  },
  {
    maxAge: cacheMaxAgeConfig,
    getKey: (id: string) => `crss-${id}`,
  },
)

export const cachedTelegramStatsSummary = cachedFunction(
  async (id: string) => {
    const today = set(new Date(), { hours: 0, minutes: 0, seconds: 0, milliseconds: 0 })
    const twoWeekEarlier = sub(today, { days: 14 })
    const stats = await timescaleClient.query.indice.findFirst({
      where: and(eq(indice.platform_id, id)),
      orderBy: desc(indice.timestamp),
    })

    const telegramStats = await timescaleClient.query.telegramIndicator.findMany({
      columns: {
        nr_bot_users: true,
        nr_bad_words: true,
        nr_messages: true,
      },
      where: and(
        eq(telegramIndicator.tg_chat_id, Number.parseInt(id)),
        between(redditIndicator.timestamp, twoWeekEarlier, today),
      ),
      orderBy: desc(redditIndicator.timestamp),
    })

    return {
      bad_words: divideArray<(typeof telegramStats)[0]>(telegramStats, 'nr_bad_words'),
      message: divideArray<(typeof telegramStats)[0]>(telegramStats, 'nr_messages'),
      bots: divideArray<(typeof telegramStats)[0]>(telegramStats, 'nr_bot_users'),
      community: stats?.community_size ?? 0,
    }
  },
  {
    maxAge: cacheMaxAgeConfig,
    getKey: (id: string) => `ctss-${id}`,
  },
)

export const cachedSummary = cachedFunction(
  async (platform: string, slug: string) => {
    const feeds = await timescaleClient.query.v1Feeds.findMany({
      columns: {
        is_publishable: false,
        asset_type: false,
      },
      where: eq(v1Feeds.slug, slug),
    })
    const selectedFeeds = feeds.find((data) => data.platform === platform)

    if (!selectedFeeds) {
      throw new Error('PLATFORM_NOT_FOUND')
    }

    const timeNow = new Date()
    const minTime = new Date('2023-01-01')
    // const minTime = sub(timeNow, { years: 1 })

    const indices = await timescaleClient
      .select({
        community_size: indice.community_size,
        avg_msgs: indice.avg_msgs,
        mood_index: indice.mood_index,
        trust_index: indice.trust_index,
        timestamp: indice.timestamp,
      })
      .from(indice)
      .where(and(eq(indice.platform_id, selectedFeeds.platform_id), between(indice.timestamp, minTime, timeNow)))
      .orderBy(asc(indice.timestamp))
    const startTimestamp = indices.length > 0 && indices[0] && indices[0].timestamp ? indices[0].timestamp : new Date(0)
    const endTimestamp =
      indices.length > 0 && indices[indices.length - 1] && indices[indices.length - 1]?.timestamp
        ? indices[indices.length - 1]?.timestamp
        : new Date(0)

    const prices = await timescaleClient
      .select({
        close: cmcPriceDaily.close,
        volume: cmcPriceDaily.volume,
        marketcap: cmcPriceDaily.marketcap,
        timestamp: cmcPriceDaily.timestamp,
      })
      .from(cmcPriceDaily)
      .where(
        and(
          eq(cmcPriceDaily.cmc_id, selectedFeeds.cmc_id!),
          between(cmcPriceDaily.timestamp, startTimestamp ?? new Date(0), endTimestamp ?? new Date(0)),
        ),
      )
      .orderBy(asc(cmcPriceDaily.timestamp))

    return {
      available_platform: feeds.map((data) => ({
        platform: data.platform,
        platform_id: data.platform_id,
        community_url: data.url,
      })),
      selected_platform: selectedFeeds.platform,
      feeds: selectedFeeds,
      series: prices.map((price) => {
        const indiceIndex = indices.findIndex((ind) => ind.timestamp?.toDateString() === price.timestamp.toDateString())
        return {
          ...indices[indiceIndex],
          ...price,
        }
      }),
    }
  },
  {
    maxAge: cacheMaxAgeConfig,
    getKey: (platform: string, slug: string) => `css-${platform}-${slug}`,
  },
)
