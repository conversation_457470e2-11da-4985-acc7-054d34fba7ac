import { desc, sql } from 'drizzle-orm'
import { numericDisplay } from '~/helper/number-helper'
import type { TopIdentifier } from '~/server/trpc/routers/v2/types/dashboard'
import { wcTopics } from '~/server/utils/timescale/schema'

export const cachedTopDaily = cachedFunction(
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async (_ = 'dashboard-top-daily') => {
    const result: TopIdentifier[] = []

    // Today query
    await Promise.all([
      // Trust query
      timescaleClient.query.indicesV2Feeds
        .findMany({
          limit: 3,
          orderBy: (fields, { desc }) => desc(fields.moodRank),
          columns: {
            trustIndex: true,
            assetId: true,
            dayChangedTrust: true,
          },
          where: (fields, { and, ne, isNotNull }) => and(ne(fields.trustRank, sql`'NaN'`), isNotNull(fields.trustRank)),
        })
        .then((moods) =>
          moods.forEach((mood, index) => {
            result.push({
              rank: index + 1,
              id: mood.assetId,
              type: 'trust',
              firstValue: mood.dayChangedTrust,
            })
          }),
        ),
      // Mood Query
      timescaleClient.query.indicesV2Feeds
        .findMany({
          limit: 3,
          orderBy: (fields, { desc }) => desc(fields.moodRank),
          columns: {
            moodIndex: true,
            assetId: true,
            dayChangedMood: true,
          },
          where: (fields, { and, ne, isNotNull }) => and(ne(fields.moodRank, sql`'NaN'`), isNotNull(fields.moodRank)),
        })
        .then((moods) =>
          moods.forEach((mood, index) => {
            result.push({
              rank: index + 1,
              id: mood.assetId,
              type: 'mood',
              firstValue: mood.dayChangedMood,
            })
          }),
        ),
      // Topic Trends
      timescaleClient
        .select({
          topic: wcTopics.topic,
          occurrences: sql<number>`cast(sum(${wcTopics.occurrences}) as int)`,
        })
        .from(wcTopics)
        .where(sql`${wcTopics.date} = (select max(${wcTopics.date}) from ${wcTopics})`)
        .groupBy(wcTopics.topic)
        .orderBy(desc(sql`sum(${wcTopics.occurrences})`))
        .limit(3)
        .then((res) =>
          res.forEach((topic, index) => {
            result.push({
              firstValue: index + 1,
              id: topic.topic ?? '',
              rank: index + 1,
              type: 'topic',
              name: topic.topic ?? '',
            })
          }),
        ),
    ])

    const assetIDs = result
      .filter((asset) => asset.type !== 'topic')
      .map((d) => d.id)
      .filter((asset, id, arr) => arr.indexOf(asset) === id) as number[]

    // Fetch second position
    await Promise.all([
      // Fetch assets
      getCachedAsset(assetIDs, 'id').then((assets) =>
        Object.keys(assets).forEach((key) => {
          result.forEach((res) => {
            if (res.id === parseInt(key)) {
              res.name = assets[key].name
              res.logo = assets[key].logo
              res.slug = assets[key].slug
              res.symbol = assets[key].symbol
              res.assetType = assets[key].type
            }
          })
        }),
      ),
    ])

    return {
      data: {
        mood: result
          .filter((d) => d.type === 'mood')
          .map((data) => {
            return {
              name: data.name,
              logo: data.logo,
              daily_different: numericDisplay(data.firstValue ? data.firstValue * 100 : 0).replace('-', '') + ' %',
              indicator: (data.firstValue ?? 0) > (data.secondValue ?? 0) ? '>' : '<',
              slug: data.slug,
              symbol: data.symbol,
              type: data.assetType,
            }
          }),
        trust: result
          .filter((d) => d.type === 'trust')
          .map((data) => {
            return {
              name: data.name,
              logo: data.logo,
              daily_different: numericDisplay(data.firstValue ? data.firstValue * 100 : 0).replace('-', '') + ' %',
              indicator: (data.firstValue ?? 0) > (data.secondValue ?? 0) ? '>' : '<',
              slug: data.slug,
              symbol: data.symbol,
              type: data.assetType,
            }
          }),
        topic: result
          .filter((d) => d.type === 'topic')
          .map((data) => ({
            name: data.name,
            slug: data.slug,
          })),
      },
      timestamp: new Date().getTime(),
    }
  },
  {
    getKey: () => 'dashboard-top-daily',
    maxAge: cacheMaxAgeConfig,
  },
)
