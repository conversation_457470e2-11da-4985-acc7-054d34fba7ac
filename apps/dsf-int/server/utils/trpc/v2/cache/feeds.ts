import { eq } from 'drizzle-orm'
import { url } from '~/server/utils/chat-source/schema'
import { v1Feeds } from '~/server/utils/timescale/schema/v1-feeds'

export const cachedFeeds = cachedFunction(
  async (slug: string) => {
    const feeds = await timescaleClient.query.v1Feeds.findMany({
      columns: {
        name: true,
        logo: true,
        website: true,
        platform: true,
        platform_id: true,
        cmc_id: true,
      },
      where: eq(v1Feeds.slug, slug),
    })

    if (feeds.length === 0) {
      throw Error('FEED_NOT_FOUND')
    }

    return feeds
  },
  {
    maxAge: cacheMaxAgeConfig,
    getKey: (slug: string) => `fdata-${slug}`,
  }
)

export const cachedURL = cachedFunction(
  async (slug: string) => {
    const urls = await chatSourceClient
      .select({
        name: url.name,
        platform: url.platform,
        url: url.url,
      })
      .from(url)
      .where(eq(url.sourceSlug, slug))
      .groupBy(url.name, url.platform, url.url)

    return urls
  },
  {
    getKey: (slug: string) => `furls-${slug}`,
    maxAge: cacheMaxAgeConfig,
  }
)
