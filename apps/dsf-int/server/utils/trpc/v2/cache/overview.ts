import { format } from 'date-fns'
import { and, between, desc, eq, inArray, sql } from 'drizzle-orm'
import { numberFormat } from '~/helper/number-helper'
import { timescaleClient } from '~/server/utils/timescale'
import { cmcPriceDaily, indice, redditIndicator, telegramIndicator } from '~/server/utils/timescale/schema'

export const OverviewRecapCached = cachedFunction(
  async (platform: { id: string; type: string }[], cmcId: number) => {
    const { now, yty } = yearToYear()

    const lastUpdatedAt = await timescaleClient.query.indice.findFirst({
      columns: {
        timestamp: true,
      },
      where: inArray(
        indice.platform_id,
        platform.map((res) => res.id),
      ),
      orderBy: desc(indice.timestamp),
    })

    const responses = {
      legends: [] as string[],
      indexes: {
        mood: [] as number[],
        trust: [] as number[],
      },
      prices: {
        price: [] as number[],
        volume: [] as number[],
      },
      messages: {
        telegram: [] as number[],
        reddit: [] as number[],
      },
      community: {
        telegram: [] as number[],
        reddit: [] as number[],
      },
      lastUpdatedAt: lastUpdatedAt?.timestamp,
    }

    await timescaleClient
      .select({
        platform: indice.platform,
        platform_id: indice.platform_id,
        month: sql<Date>`DATE_TRUNC('month', ${indice.timestamp}) as month`,
        mood_index: sql<number>`avg(${indice.mood_index}) as mood_index`,
        trust_index: sql<number>`avg(${indice.trust_index}) as trust_index`,
        community_size: sql<number>`avg(${indice.community_size}) as community_size`,
        latestDate: sql<number>`max(${indice.timestamp}) as latestDate`,
      })
      .from(indice)
      .where(
        and(
          inArray(
            indice.platform_id,
            platform.map((platform) => platform.id),
          ),
          between(indice.timestamp, yty, now),
        ),
      )
      .groupBy(sql`DATE_TRUNC('month', ${indice.timestamp}), ${indice.platform_id}, ${indice.platform}`)
      .orderBy(sql`DATE_TRUNC('month', ${indice.timestamp}) asc`)
      .then((result) =>
        result.reduce(
          (
            r: {
              [key: string]: {
                reddit?: (typeof result)[0]
                telegram?: (typeof result)[0]
              }
            },
            i,
          ) => {
            const month = format(i.month, 'MMM yy')

            if (!r[month]) {
              r[month] = {}
            }

            if (i.platform === 'reddit') {
              r[month].reddit = i
            } else {
              r[month].telegram = i
            }

            return r
          },
          {},
        ),
      )
      .then((result) =>
        Object.keys(result).forEach((data) => {
          responses.legends.push(data.replace(' ', "'"))
          if (result[data]) {
            // Indexes
            if (result[data].reddit && result[data].telegram) {
              responses.indexes.mood.push(
                numberFormat((result[data].reddit!.mood_index + result[data].telegram!.mood_index) / 2),
              )
              responses.indexes.trust.push(
                numberFormat((result[data].reddit!.trust_index + result[data].telegram!.trust_index) / 2),
              )
            } else {
              responses.indexes.mood.push(
                numberFormat(result[data].reddit!.mood_index ?? result[data].telegram!.mood_index),
              )
              responses.indexes.trust.push(
                numberFormat(result[data].reddit!.trust_index ?? result[data].telegram!.trust_index),
              )
            }

            // Community sizes
            if (result[data].reddit) {
              responses.community.reddit.push(numberFormat(result[data].reddit!.community_size))
            }

            if (result[data].telegram) {
              responses.community.telegram.push(numberFormat(result[data].telegram!.community_size))
            }
          }
        }),
      )

    await timescaleClient
      .select({
        month: sql<Date>`DATE_TRUNC('month', ${cmcPriceDaily.timestamp}) as month`,
        close: sql<number>`avg(${cmcPriceDaily.close}) as close`,
        volume: sql<number>`avg(${cmcPriceDaily.volume}) as volume`,
      })
      .from(cmcPriceDaily)
      .where(and(eq(cmcPriceDaily.cmc_id, cmcId), between(cmcPriceDaily.timestamp, yty, now)))
      .groupBy(sql`DATE_TRUNC('month', ${cmcPriceDaily.timestamp})`)
      .orderBy(sql`DATE_TRUNC('month', ${cmcPriceDaily.timestamp}) asc`)
      .then((result) =>
        result.forEach((data) => {
          responses.prices.price.push(data.close!)
          responses.prices.volume.push(data.volume!)
        }),
      )

    await Promise.all(
      platform.map(async (platform) => {
        if (platform.type === 'reddit') {
          ;(
            await timescaleClient
              .select({
                month: sql<Date>`DATE_TRUNC('month', ${redditIndicator.timestamp}) as month`,
                messages: sql<number>`sum(${redditIndicator.nr_comments}) as messages`,
              })
              .from(redditIndicator)
              .where(and(eq(redditIndicator.subreddit_id, platform.id), between(redditIndicator.timestamp, yty, now)))
              .groupBy(sql`DATE_TRUNC('month', ${redditIndicator.timestamp})`)
              .orderBy(sql`DATE_TRUNC('month', ${redditIndicator.timestamp}) asc`)
          ).forEach((data) => {
            responses.messages.reddit.push(numberFormat(data.messages))
          })
        } else if (platform.type === 'telegram') {
          ;(
            await timescaleClient
              .select({
                month: sql<Date>`DATE_TRUNC('month', ${telegramIndicator.timestamp}) as month`,
                messages: sql<number>`sum(${telegramIndicator.nr_messages}) as messages`,
              })
              .from(telegramIndicator)
              .where(
                and(
                  eq(telegramIndicator.tg_chat_id, Number.parseInt(platform.id)),
                  between(telegramIndicator.timestamp, yty, now),
                ),
              )
              .groupBy(sql`DATE_TRUNC('month', ${telegramIndicator.timestamp})`)
              .orderBy(sql`DATE_TRUNC('month', ${telegramIndicator.timestamp})`)
          ).forEach((data) => {
            responses.messages.telegram.push(numberFormat(data.messages))
          })
        }
      }),
    )

    return responses
  },
  {
    maxAge: cacheMaxAgeConfig,
    getKey: (platform: { id: string; type: string }[], cmcId: number) =>
      `overview-recap-${platform.map((data) => data.id).join('-')}-${cmcId}`,
  },
)
