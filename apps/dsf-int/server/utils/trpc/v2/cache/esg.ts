import { format } from 'date-fns'
import { between, isNotNull } from 'drizzle-orm'
import Hashids from 'hashids'

const hashids = new Hashids()
const { now, yty } = yearToYear()
export type ytyEsgTypes = {
  measured_at: number
  avg_consumption_kwh: number
  lower_consumption_kwh: number
  upper_consumption_kwh: number
  avg_consumption_wh_tx: number
  lower_consumption_wh_tx: number
  upper_consumption_wh_tx: number
}

export const ytyEsgOverview = cachedFunction(
  async (slug: string) => {
    const esgSlug = slug.split('-')
    const [assetId, platformId] = hashids.decode(esgSlug[esgSlug.length - 1] as string)

    const esg = await timescaleClient.query.analyticsEnergyCons
      .findMany({
        columns: {
          powerUseLowerBoundW: true,
          powerUseBestGuessW: true,
          powerUseUpperBoundW: true,
          energyConsTxBestGuessWh: true,
          energyConsTxLowerBoundWh: true,
          energyConsTxUpperBoundWh: true,
          date: true,
        },
        where: (fields, { and, eq }) =>
          and(
            isNotNull(fields.powerUseLowerBoundW),
            isNotNull(fields.powerUseBestGuessW),
            isNotNull(fields.powerUseUpperBoundW),
            isNotNull(fields.energyConsTxBestGuessWh),
            isNotNull(fields.energyConsTxLowerBoundWh),
            isNotNull(fields.energyConsTxUpperBoundWh),
            and(
              assetId ? eq(fields.assetId, assetId as number) : undefined,
              platformId ? eq(fields.platformId, platformId as number) : undefined,
            ),
            between(fields.date, format(yty, 'yyyy-MM-dd'), format(now, 'yyyy-MM-dd')),
          ),
        orderBy: (fields, { asc }) => asc(fields.date),
      })
      .then((res) =>
        res.reduce((res: ytyEsgTypes[], data) => {
          res.push({
            measured_at: new Date(data.date ?? '').getTime(),
            avg_consumption_kwh: Number(data.powerUseBestGuessW ?? 0),
            lower_consumption_kwh: Number(data.powerUseLowerBoundW ?? 0),
            upper_consumption_kwh: Number(data.powerUseUpperBoundW ?? 0),
            avg_consumption_wh_tx: Number(data.energyConsTxBestGuessWh ?? 0),
            lower_consumption_wh_tx: Number(data.energyConsTxLowerBoundWh ?? 0),
            upper_consumption_wh_tx: Number(data.energyConsTxUpperBoundWh ?? 0),
          })

          return res
        }, []),
      )

    return esg
  },
  {
    getKey: (slug: string) => `ytyesgov-${slug}`,
    maxAge: cacheMaxAgeConfig,
  },
)

export const getEsgCached = cachedFunction(
  async () => {
    const esgPairs = await chatSourceClient.query.esgPairV2.findMany()
    return esgPairs
  },
  {
    getKey: () => 'esgs-cached',
    maxAge: cacheMaxAgeConfig,
  },
)
