import { and, asc, between, eq, inArray, sql } from 'drizzle-orm'
import { asset, community } from '~/server/utils/chat-source/schema'
import {
  cmcPriceDaily,
  indice,
  indiceV2,
  redditIndicator,
  telegramIndicator,
  v1Feeds,
} from '~/server/utils/timescale/schema'
import { timescaleClient } from '~/server/utils/timescale'
import type { IndiceV2 } from '~/server/utils/timescale/schema/indices-v2'
import { formatToSlug } from '~/helper/string-helper'

const { now, yty } = yearToYear()

export const ytyMessagesCached = cachedFunction(
  async (feeds: [string, string][]) => {
    const messages = await (async () => {
      const data: {
        [key: string]: {
          timestamp: number
          bots: number
        }[]
      } = {}
      for (const feed of feeds) {
        if (feed[0] === 'telegram') {
          ;(
            await timescaleClient.query.telegramIndicator.findMany({
              columns: {
                nr_bot_users: true,
                timestamp: true,
              },
              where: and(
                eq(telegramIndicator.tg_chat_id, Number.parseInt(feed[1])),
                between(telegramIndicator.timestamp, yty, now),
              ),
              orderBy: asc(telegramIndicator.timestamp),
            })
          ).forEach((res) => {
            if (data['telegram'] === undefined) {
              data['telegram'] = []
            }

            const resTimestamp = res.timestamp?.getTime()
            const findKey = data['telegram'].findIndex((d) => d.timestamp === resTimestamp)

            if (findKey > -1 && data['telegram'][findKey]) {
              data['telegram'][findKey] = {
                ...data['telegram'][findKey],
                bots: (res.nr_bot_users ?? 0) + data['telegram'][findKey].bots,
              }
            } else {
              data['telegram'].push({
                timestamp: res.timestamp!.getTime(),
                bots: res.nr_bot_users ?? 0,
              })
            }
          })
        }
        if (feed[0] === 'reddit') {
          ;(
            await timescaleClient.query.redditIndicator.findMany({
              columns: {
                nr_posts: true,
                timestamp: true,
                nr_bad_words: true,
              },
              where: and(eq(redditIndicator.subreddit_id, feed[1]), between(redditIndicator.timestamp, yty, now)),
              orderBy: asc(redditIndicator.timestamp),
            })
          ).forEach((res) => {
            if (data['reddit'] === undefined) {
              data['reddit'] = []
            }

            const resTimestamp = res.timestamp?.getTime()
            const findKey = data['reddit'].findIndex((d) => d.timestamp === resTimestamp)

            if (findKey > -1 && data['reddit'][findKey]) {
              data['reddit'][findKey] = {
                ...data['reddit'][findKey],
              }
            } else {
              data['reddit'].push({
                timestamp: res.timestamp!.getTime(),
                bots: 0,
              })
            }
          })
        }
      }
      return data
    })()

    return messages
  },
  {
    getKey: (platformId: [string, string][]) =>
      `ytymsg-${platformId.reduce((r: string, d) => (r = r = r + d.join('-')), '')}`,
  },
)

export const ytyPricesCached = cachedFunction(
  async (cmcId: number[]) => {
    const prices = await timescaleClient.query.cmcPriceDaily.findMany({
      columns: {
        close: true,
        volume: true,
        timestamp: true,
      },
      where: and(inArray(cmcPriceDaily.cmc_id, cmcId), between(cmcPriceDaily.timestamp, yty, now)),
      orderBy: asc(cmcPriceDaily.timestamp),
    })

    return prices.map((price) => ({
      ...price,
      timestamp: price.timestamp.getTime(),
    }))
  },
  {
    getKey: (platformId: string[]) => `ytypc-${platformId.join('-')}`,
  },
)

export const ytyIndicesCached = cachedFunction(
  async (platformId: string[]) => {
    const indices = (
      await timescaleClient.query.indice.findMany({
        columns: {
          mood_index: true,
          trust_index: true,
          community_size: true,
          platform: true,
          timestamp: true,
        },
        where: and(
          inArray(
            indice.platform_id,
            platformId.map((feed) => feed),
          ),
          between(indice.timestamp, yty, now),
        ),
        orderBy: asc(indice.timestamp),
      })
    ).reduce(
      (
        res: {
          [key: string]: (typeof data | { timestamp: number })[]
        },
        data,
      ) => {
        if (res[data.platform] === undefined) {
          res[data.platform] = []
        }
        res[data.platform]?.push({
          ...data,
          timestamp: data.timestamp!.getTime(),
        })

        return res
      },
      {},
    )

    return indices
  },
  {
    maxAge: cacheMaxAgeConfig,
    getKey: (platformId: string[]) => `ytyic-${platformId.join('-')}`,
  },
)

export const feedChartCached = cachedFunction(
  async (slug: string) => {
    const platformId: string[] = []

    const feeds = (
      await timescaleClient.query.v1Feeds.findMany({
        columns: {
          platform_id: true,
          platform: true,
          name: true,
        },
        where: eq(v1Feeds.slug, slug),
      })
    ).reduce(
      (
        res: {
          [key: string]: (typeof data)[]
        },
        data,
      ) => {
        if (data.platform === 'reddit') {
          if (!res.reddit) {
            res.reddit = []
          }

          res.reddit.push(data)
        }

        if (data.platform === 'telegram') {
          if (!res.telegram) {
            res.telegram = []
          }

          res.telegram.push(data)
        }

        platformId.push(data.platform_id)

        return res
      },
      {},
    )

    const info = await chatSourceClient.query.asset.findFirst({
      columns: {
        logo: true,
        name: true,
        website: true,
        domain: true,
        symbol: true,
      },
      where: eq(asset.slug, slug),
    })

    const communities = await chatSourceClient.query.community.findMany({
      where: inArray(community.platformId, platformId),
    })

    Object.keys(feeds).forEach((key) => {
      const filterCommunities = communities.filter((c) => c.platform.toLowerCase() === key)
      feeds[key]?.forEach((chatSource) => {
        const community = filterCommunities.find((c) => c.platformId === chatSource.platform_id)
        if (community && community.platform[0]) {
          chatSource.name = `${community.platform[0].toLowerCase()}/${community.name}`
        } else {
          chatSource.name = null
        }
      })
    })

    return {
      feeds,
      platformId,
      info,
    }
  },
  {
    maxAge: cacheMaxAgeConfig,
    getKey: (slug: string) => `feed-chart-${slug}`,
  },
)

export const ytyIndicesV2Cached = cachedFunction(
  async (id: number) => {
    const indices: Record<string, IndiceV2[]> = {}
    await timescaleClient
      .select()
      .from(indiceV2)
      .where(
        and(
          sql`${indiceV2.timestamp} >= (select max(${indiceV2.timestamp}) from ${indiceV2}) - interval '365 day'`,
          sql`${indiceV2.timestamp} <= (select max(${indiceV2.timestamp}) from ${indiceV2})`,
          eq(indiceV2.assetId, id),
        ),
      )
      .orderBy(asc(indiceV2.timestamp))
      .then((res) =>
        res.forEach((indice) => {
          const key = formatToSlug(indice.platform)
          if (indices[key] === undefined) {
            indices[key] = []
          }

          indices[key].push({
            ...indice,
            numericTimestamp: indice.timestamp.getTime(),
          })
        }),
      )

    return indices
  },
  {
    getKey: (id: number) => `yty-indices-v2-${id}`,
  },
)
