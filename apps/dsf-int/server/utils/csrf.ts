import jwt from 'jsonwebtoken'

export type CsrfSession = {
  token: string
  refreshToken: string
  expired_at: number
}

/**
 * Provides functions for generating and validating CSRF tokens.
 *
 * @returns an object containing the `generateCsrf`, `validateCsrf`, and `refreshToken` functions.
 *
 * @example
 *
 **/
export function useCsrf() {
  const $config = useRuntimeConfig()

  /**
   * Generates a new CSRF token and refresh token.
   *
   * @param payload - the payload to sign
   * @returns an object containing the token, refresh token, and expiration timestamp
   */
  function generateCsrf(payload: Record<string, string>): CsrfSession {
    const token = jwt.sign(payload, $config.key, { expiresIn: '5m' })
    const refreshToken = jwt.sign(payload, $config.key, { expiresIn: '60m' })

    return {
      token,
      refreshToken,
      expired_at: Date.now() + 5 * 60 * 1000,
    }
  }

  /**
   * Validates a given CSRF token.
   *
   * @param token - The CSRF token to validate.
   * @returns An object containing the payload if the token is valid, an indication
   *          of whether the token is expired, and any verification error encountered.
   *
   * The payload is included if the token is successfully verified and not expired.
   * If the token is expired, `is_expired` is set to true.
   * Any error encountered during verification is included in the `error` field.
   */
  function validateCsrf(token: string): {
    payload?: Record<string, string>
    is_expired?: boolean
    error: jwt.VerifyErrors | null
  } {
    try {
      const verify = jwt.verify(token, $config.key) as Record<string, string>

      return {
        payload: verify,
        is_expired: false,
        error: null,
      }
    } catch (error) {
      const _err = error as jwt.VerifyErrors
      if (_err.name === 'TokenExpiredError') {
        return {
          is_expired: true,
          error: _err,
        }
      }
      return {
        error: _err,
      }
    }
  }

  /**
   * Generates a new CSRF token based on the given refresh token.
   *
   * The refresh token is validated and if it is still valid, a new CSRF token is generated
   * using the same payload. If the refresh token is expired or invalid, the function returns
   * the result of the validation.
   *
   * @param refreshToken - The refresh token to use.
   * @returns The result of the validation, which may include a new CSRF token, an indication
   *          of whether the refresh token is expired, and any verification error encountered.
   */
  function refreshToken(refreshToken: string) {
    const verify = validateCsrf(refreshToken)

    if (verify.payload) {
      return generateCsrf({
        user: verify.payload?.user || '',
      })
    }

    return verify.payload
  }

  return {
    generateCsrf,
    validateCsrf,
    refreshToken,
  }
}
