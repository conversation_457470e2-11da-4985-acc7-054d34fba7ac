interface CarbonEmissionRow {
  name: string
  logo: string
  slug: string
  asset_type: string
  layer_type: string
  consensus_mechanism: string[]
  co_emission: string
  co_emission_tx: string
  co_emission_tx_node: string
  validators: string
  tps: string
}

interface EnergyConsumptionSqlRow {
  assetName: string | null
  assetSymbol: string | null
  assetSlug: string | null
  assetLogo: string | null
  platformName: string | null
  platformSymbol: string | null
  platformSlug: string | null
  platformLogo: string | null
  layer: string | null
  consensusMechanism: string | null
  type: string | null
  powerUse: number | null
  energyConsPerTx: number | null
  energyConsPerTxPerNode: number | null
  validators: number | null
  tps: number | null
  assetId: number | null
  platformId: number | null
  totalCount: number | null
}

type CarbonEmissionChartEntries =
  | {
      mode: 'source'
      entries: {
        type: string
        timeseries: [number, number][]
      }[]
    }
  | {
      mode: 'annualized'
      entries: [
        number,
        {
          avg: number
          top: number
          lower: number
          tps: number
          validators: number
        },
      ][]
    }

interface EnergyConsumptionRow {
  name: string
  logo: string
  slug: string
  asset_type: string
  layer_type: string
  consensus_mechanism: string[]
  energy_cons: string
  energy_cons_tx: string
  energy_cons_tx_node: string
  validators: string
  tps: string
}