import { UserSchemaType } from '~/server/utils/platform-db/schema/users'

interface User {
  id: string
  role: UserSchemaType['role']
  first_name: string
  last_name: string
  company: string | null
  auth_providers: string[]
  email: string
  registration_date: string
  suspended_reason: string | null
  plan: string | null
  hashpack_account: string | null
  last_login: string | null
  onboarding_status: string | null
  activation_plan_date: string | null
  expired_plan_date: string | null
}

interface UserProfile extends User {
  plan_history: Record<string, string>[]
  discounts: Record<string, string>[]
  support_logs: Record<string, string>[]
}
