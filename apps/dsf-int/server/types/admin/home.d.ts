interface UserStatistic {
  total_record: number
  entry: { count: number; name: string }[]
}

interface UserSummary {
  user_status: UserStatistic
  user_plans: UserStatistic
  total_revenue: number
  total_user: number
  revenue_history: [number, number][]
}

interface UserRevenueHistory {
  name: string
  plan: string
  total_revenue: string
}

interface AssetPopularity {
  logo?: string
  name: string
  view: string
  percentage: string
}

interface MetricPopularity {
  group_name: string
  metric: {
    metric_name: string
    view_count: number
    percentage?: number
  }[]
}
