import { useSessionStore } from '~/stores/session'
import { GUEST_ROUTES } from '~/constants/routes'
import { PUBLIC_ROUTES } from '~/constants/routes'
import { removeTrailingSlash } from '~/server/utils/string'

export default defineNuxtRouteMiddleware(async (to) => {
  const parsedPath = removeTrailingSlash(to.path)

  const { userSession } = useSessionStore()
  const isGuestRoute = GUEST_ROUTES.some((route) => parsedPath === route)
  const isPublicRoute = PUBLIC_ROUTES.some((route) => parsedPath === route)

  if (!isGuestRoute && !isPublicRoute) {
    if (parsedPath !== '/' && !userSession) {
      return navigateTo('/auth/login?redirect=' + encodeURIComponent(parsedPath))
    }
  }
})
