import { DISABLED_ROUTES } from '~/constants/routes'
import { removeTrailingSlash } from '~/server/utils/string'
import { useUserStore } from '~/stores/profile'
import { useSessionStore } from '~/stores/session'
import type { CsrfSession } from '~/server/utils/csrf'

const CSRF_TIMEOUT = 120 // TODO: We need to remove it !

export default defineNuxtRouteMiddleware(async (to) => {
  const { cleanUpClientSession, setSession, userSession } = useSessionStore()
  const userStore = useUserStore()

  const csrfState = useState<CsrfSession | undefined>('csrf-state') // TODO: We need to remove it !
  const parsedPath = removeTrailingSlash(to.path)

  if (DISABLED_ROUTES.some((route) => parsedPath === route)) {
    return navigateTo('/')
  }

  const { $client, $authClient, $authFetchClient, $serverApiClient } = useNuxtApp()

  // Initialize CSRF Protection
  // Because CSRF State was not set in first time, it will be first process will run and cannot be run twice
  if (!csrfState.value) {
    const csrfToken = await $client.v2.public.session.generate.query()
    csrfState.value = csrfToken

    // Always check if session is still null !
    if (userSession === null) {
      const { data } = await $authClient.useSession($authFetchClient)
      if (!data.value?.session) {
        await cleanUpClientSession({ force: true })
      }

      if (data.value?.session) {
        setSession(data.value?.session)
      }
    }

    // Getting user info & always check if user profile is still null
    if (userStore.userProfile === null) {
      const { data, error } = await $serverApiClient('get', '/nodiens/api/v1/account/profile')

      if (error) {
        await cleanUpClientSession({ force: true })
      }

      if (data.value?.payload) {
        userStore.setProfile(data?.value.payload)
      }
    }
  }

  // TODO: Need to remove it !
  else {
    const different = csrfState.value.expired_at / 1000 - Date.now() / 1000
    if (different < CSRF_TIMEOUT) {
      const newCsrf = await $client.v2.public.session.refresh.mutate({
        token: csrfState.value.refreshToken,
      })

      csrfState.value = newCsrf
    }
  }
})
