import { useFeatureFlags } from '~/stores/feature-flags'
import { FEATURE_FLAGS, RELEASED_FEATURE_FLAGS } from '~/constants/feature-flags'

export default defineNuxtRouteMiddleware(async () => {
  const { featureFlags, syncFeatureFlags } = useFeatureFlags()

  const updatedFlags = featureFlags
    .filter((ff) => !RELEASED_FEATURE_FLAGS.includes(ff.key))
    .concat(
      FEATURE_FLAGS.filter(
        (ff) => !featureFlags.some((c) => c.key === ff.key) && !RELEASED_FEATURE_FLAGS.includes(ff.key),
      ),
    )

  syncFeatureFlags(updatedFlags)
})
