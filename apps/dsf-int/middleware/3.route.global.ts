import { useUserStore } from '~/stores/profile'
import { removeTrailingSlash } from '~/server/utils/string'
import { useSessionStore } from '~/stores/session'

export default defineNuxtRouteMiddleware(to => {
  const { userSession } = useSessionStore()
  const parsedPath = removeTrailingSlash(to.path)
  const userStore = useUserStore()

  const isAdminRoute = parsedPath.startsWith('/admin')
  const isAdmin = userStore?.userProfile?.role?.toLowerCase() === 'admin'

  //TODO: check with the requirements if admin user is also allowed to access the old routes not just admin routes
  if (!userSession && isAdminRoute && !isAdmin) {
    return navigateTo('/')
  }
})