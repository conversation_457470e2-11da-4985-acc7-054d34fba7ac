import { GUEST_ROUTES } from '~/constants/routes'
import { removeTrailingSlash } from '~/server/utils/string'
import { useUserStore } from '~/stores/profile'
import { useSessionStore } from '~/stores/session'

export default defineNuxtRouteMiddleware(async (to) => {
  const { $serverApiClient } = useNuxtApp()
  const { userSession } = useSessionStore()
  const parsedPath = removeTrailingSlash(to.path)

  const matchedRoute = GUEST_ROUTES.some((route) => parsedPath === route)

  if (matchedRoute) {
    const { setProfile } = useUserStore()

    if (userSession) {
      const { data } = await $serverApiClient('get', '/nodiens/api/v1/account/profile')

      if (data.value?.payload) {
        setProfile(data.value.payload)
        return navigateTo('/')
      }
    }
  }
})
