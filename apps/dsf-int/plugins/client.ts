import { createTRPCNuxtClient, httpLink, httpBatchLink } from 'trpc-nuxt/client'
import type { AppRouter } from '~/server/trpc/routers'
import { type FetchError } from 'ofetch'
import { useSessionStore } from '~/stores/session'
import type { CsrfSession } from '~/server/utils/csrf'

function isFetchError(error: unknown): error is FetchError {
  return error instanceof Error && error.name === 'FetchError'
}

// catch part is redacted
async function customFetch(input: RequestInfo, init?: RequestInit & { method: 'GET' }) {
  const { clearSession } = useSessionStore()

  return globalThis.$fetch
    .raw(input.toString(), init)
    .catch(async (e) => {
      if (isFetchError(e) && e.response) {
        if (e.statusCode === 401 && import.meta.client) {
          clearSession({ force: true })
        }
        return e.response
      }
      throw e
    })
    .then((response) => ({
      ...response,
      headers: response.headers,
      json: () => Promise.resolve(response._data),
    }))
}

export default defineNuxtPlugin(
  async (): Promise<{
    provide: {
      client: ReturnType<typeof createTRPCNuxtClient<AppRouter>>
      batchClient: ReturnType<typeof createTRPCNuxtClient<AppRouter>>
    }
  }> => {
    const csrfState = useState<CsrfSession | undefined>('csrf-state')
    const { csrfServerHeader } = useRuntimeConfig()
    const headers = useRequestHeaders()
    const requestHeader: Record<string, string> = {
      ...headers,
    }

    if (import.meta.server) {
      requestHeader[csrfServerHeader] = 'true'
    }

    const handledFetch = async (url: RequestInfo | URL, options?: RequestInit) => {
      const response = await customFetch(
        url as RequestInfo,
        {
          ...options,
          headers: {
            'X-CSRF': csrfState.value?.token,
            ...options?.headers,
          },
        } as RequestInit & { method: 'GET' },
      )
      return response
    }

    const client = createTRPCNuxtClient<AppRouter>({
      links: [
        httpLink({
          url: '/api/trpc',
          headers: requestHeader,
          fetch: handledFetch,
        }),
      ],
    })

    const batchClient = createTRPCNuxtClient<AppRouter>({
      links: [
        httpBatchLink({
          url: '/api/trpc',
          headers: requestHeader,
          fetch: handledFetch,
        }),
      ],
    })

    return {
      provide: {
        client,
        batchClient,
      },
    }
  },
)
