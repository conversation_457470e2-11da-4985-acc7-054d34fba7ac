import { createAuthClient } from 'better-auth/vue' // make sure to import from better-auth/vue

export default defineNuxtPlugin((nuxtApp) => {
  const headers = nuxtApp.ssrContext?.event.node.req.headers as Record<string, string>
  const runtimeConfig = useRuntimeConfig()
  const baseURL = runtimeConfig.public.apiBaseUrl
  const basePath = runtimeConfig.public.authBasePath

  const authClient = createAuthClient({
    baseURL,
    basePath,
  })

  const authFetchClient = (req: string) => {
    // We need to remove /api/auth because we already set basePath on better auth client
    if (basePath !== '' || basePath) {
      req = req.replace('/api/auth', '')
    }

    return useFetch(req, {
      headers,
      baseURL: baseURL + basePath,
    })
  }

  return {
    provide: {
      authClient,
      authFetchClient,
    },
  }
})
