import createClient, { type MaybeOptionalInit } from 'openapi-fetch'
import type { HttpMethod, PathsWithMethod, RequiredKeysOf } from 'openapi-typescript-helpers'
import type { paths } from '~/types/schema'

type InitParam<Init> =
  RequiredKeysOf<Init> extends never ? [(Init & { [key: string]: unknown })?] : [Init & { [key: string]: unknown }]

export default defineNuxtPlugin((nuxtApp) => {
  const apiClient = createClient<paths>({
    baseUrl: nuxtApp.$config.public.apiBaseUrl,
    credentials: 'include',
    headers: nuxtApp.ssrContext?.event.node.req.headers,
  })

  function serverApiClient<Path extends PathsWithMethod<paths, Method>, Method extends HttpMethod>(
    method: Method,
    url: Path,
    ...init: InitParam<MaybeOptionalInit<paths[Path], Method>>
  ) {
    return useAsyncData(`open-api-${url}`, async () => {
      const { data, error } = await apiClient.request(method, url, ...init)

      if (error) {
        throw error
      }

      return data
    })
  }

  return {
    provide: {
      serverApiClient,
      apiClient,
    },
  }
})
