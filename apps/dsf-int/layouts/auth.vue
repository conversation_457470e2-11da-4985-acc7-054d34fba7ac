<script setup lang="ts">
import { useTable } from '~/composables/useTable'

const { $client } = useNuxtApp()

const { HIGHLIGHT_CLASSNAME } = useTable()

const { data, status, refresh } = await useLazyAsyncData(
  'previewAssets',
  async () => await $client.v2.public.indices.moodPreview.query(),
  {
    immediate: false,
    server: false,
  },
)

onMounted(() => refresh())

const tableColumns = computed(() => [
  {
    key: 'number',
    label: '#',
  },
  {
    key: 'name',
    label: 'Name',
    class: 'w-[300px]',
  },
  {
    key: 'mood_index',
    label: 'Mood Index',
    class: `${HIGHLIGHT_CLASSNAME} w-[100px]`,
    rowClass: HIGHLIGHT_CLASSNAME,
    description: `An indicator of the sentiment level in tracked communities of a crypto asset. This uses proprietary methodologies to extract sentiment-related features and natural language processing techniques to quantify and aggregate the mood.<br/>
<br/>
This is a value scaled from 100 without boundaries. A higher score indicates more positive mood and expectations within the tracked communities.`,
  },
  {
    key: 'mood_percentage',
    label: '1d %',
    description:
      'The percentage change in the mood index of a crypto asset over the last 24 hours. This shows the short-term expectation shifts in the market for a crypto asset.',
  },
  {
    key: 'data_chart',
    label: '90d',
    description: 'The mood index trend across the tracked communities of a crypto asset over the last 90 days.',
  },
])

useHead({
  titleTemplate: '%s | Nodiens',
})
</script>

<template>
  <div class="min-h-screen">
    <div>
      <Navbar
        :dashboard="false"
        hide-auth-button
      />

      <div class="auth-container grid grid-cols-1 items-start justify-center py-10 md:grid-cols-2">
        <div class="p-5 md:pl-16 md:pr-0">
          <UCard class="md:-10">
            <NuxtPage />
          </UCard>
        </div>
        <div class="hidden flex-col py-5 md:flex">
          <div
            class="border-neutrals-200 dark:border-neutrals-500 dark:bg-neutrals-900 m-6 mt-0 rounded-lg border-2 bg-white"
          >
            <Suspense>
              <UTableWrapper
                :pending="!data || status === 'pending'"
                :data="data"
                :columns="tableColumns"
                class="table-preview rounded-lg"
              >
                <!-- Start Custom Header -->
                <template #number-header="head">
                  <div class="flex w-full cursor-default items-center justify-center gap-1 hover:cursor-default">
                    <span class="text-left">{{ head.column.label }}</span>
                  </div>
                </template>

                <template #name-header="head">
                  <button
                    class="flex w-full min-w-[6rem] cursor-default items-center justify-between gap-1 hover:cursor-default"
                  >
                    <div class="flex items-center gap-1">
                      <span class="text-left">{{ head.column.label }}</span>
                    </div>
                  </button>
                </template>

                <template #mood_index-header="head">
                  <button
                    class="flex w-full min-w-[6rem] cursor-default items-center justify-between gap-1 hover:cursor-default"
                  >
                    <div class="flex items-center gap-1">
                      <span class="text-left">{{ head.column.label }}</span>

                      <InformationPopover class="mt-1">
                        <TooltipContent>
                          <p v-html="head.column.description" />
                        </TooltipContent>
                      </InformationPopover>
                    </div>
                  </button>
                </template>

                <template #mood_percentage-header="head">
                  <button
                    class="flex w-full min-w-[4rem] cursor-default items-center justify-between gap-1 hover:cursor-default"
                  >
                    <div class="flex items-center gap-1">
                      <span class="text-left">{{ head.column.label }}</span>

                      <InformationPopover class="mt-1">
                        <TooltipContent>
                          <p>{{ head.column.description }}</p>
                        </TooltipContent>
                      </InformationPopover>
                    </div>
                  </button>
                </template>

                <template #data_chart-header="head">
                  <button
                    class="flex w-full min-w-[6rem] cursor-default items-center justify-between gap-1 hover:cursor-default"
                  >
                    <div class="flex items-center gap-1">
                      <span class="text-left">{{ head.column.label }}</span>

                      <InformationPopover class="mt-1">
                        <TooltipContent>
                          <p>{{ head.column.description }}</p>
                        </TooltipContent>
                      </InformationPopover>
                    </div>
                  </button>
                </template>
                <!-- End Custom Header -->

                <!-- Start Custom Data -->
                <template #number-data="{ index }">
                  <div class="flex cursor-default items-center justify-center">
                    <p>
                      {{ index ? index + 1 : '-' }}
                    </p>
                  </div>
                </template>

                <template #name-data="{ row }">
                  <div class="flex cursor-default items-center gap-2">
                    <CoinDisplayName
                      :coin="row"
                      disableHover
                    />
                  </div>
                </template>

                <template #mood_index-data="{ row }">
                  <div class="cursor-default">
                    <p>
                      {{ row.mood }}
                    </p>
                  </div>
                </template>

                <template #mood_percentage-data="{ row, index }">
                  <div class="flex cursor-default items-center gap-1">
                    <img
                      v-if="row.symbol_compare === '>'"
                      src="~/assets/svg/arrow-triangle-up.svg"
                      :alt="`rank-indicator-up-${index}`"
                      width="16"
                      height="16"
                    />
                    <img
                      v-else
                      src="~/assets/svg/arrow-triangle-down.svg"
                      :alt="`rank-indicator-down-${index}`"
                      width="16"
                      height="16"
                    />
                    <span
                      :class="[
                        row.symbol_compare === '>'
                          ? 'text-accent-green-600 dark:text-accent-green-400'
                          : 'text-accent-red-600 dark:text-accent-red-400',
                      ]"
                    >
                      {{ row.changed }}
                    </span>
                  </div>
                </template>

                <template #data_chart-data="{ row }">
                  <div
                    v-if="row?.chart.length > 0"
                    class="cursor-default"
                  >
                    <IndexChart
                      class="!h-[30px] !w-full"
                      :type="1"
                      :data="row.chart"
                    />
                  </div>
                  <div
                    v-else
                    class="px-3 py-4"
                  >
                    -
                  </div>
                </template>
              </UTableWrapper>
            </Suspense>
          </div>
          <div class="p-5 pt-0">
            <h3 class="text-center text-2xl font-semibold text-white">
              Leverage our proprietary risk metrics for crypto assets
            </h3>
          </div>
        </div>
      </div>
      <!-- <slot /> -->
    </div>
    <UNotifications />
  </div>
</template>

<style>
.table-preview > table > tbody > tr:first-child > td {
  border-top: none !important;
  border-bottom: none !important;
}
.table-preview > table > tbody > tr:last-child > td {
  border-top: none !important;
  border-bottom: none !important;
}
</style>
