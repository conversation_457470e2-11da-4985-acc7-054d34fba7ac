<script setup lang="ts">
import { z } from 'zod'

const { $colorMode, $client } = useNuxtApp()

const toast = useToast()
const emailInput = ref('')
const showModal = ref(false)

const emailSchema = z
  .string({ required_error: 'Email is required.' })
  .email('Email must be a valid address.')
  .nonempty('Email cannot be empty.')

const handleSubscribe = () => {
  const result = emailSchema.safeParse(emailInput.value)
  result.success && (showModal.value = true)
}

const onSubscribe = async () => {
  try {
    await $client.v2.public.newsletter.subscribeEmail.mutate({
      email: emailInput.value,
    })

    toast.add({
      color: 'green',
      title: 'Subscription Successful!',
      description: "You'll receive the latest tips via email. Stay tuned!",
      icon: 'i-heroicons-check-circle',
    })
  } catch (e: any) {
    if (e?.data?.code === 'CONFLICT') {
      toast.add({
        title: 'Oops!',
        color: 'red',
        description: "You're already subscribed to our newsletter. Stay tuned for more tips!",
        icon: 'i-heroicons-exclamation-triangle-20-solid',
      })
    }
    if (e?.data?.code === 'TOO_MANY_REQUESTS') {
      toast.add({
        title: 'Oops!',
        color: 'red',
        description: e.message ?? "You're already subscribed to our newsletter. Stay tuned for more tips!",
        icon: 'i-heroicons-exclamation-triangle-20-solid',
      })
    } else {
      toast.add({
        title: 'Oops!',
        color: 'red',
        description: 'Something went wrong. Please try again later.',
        icon: 'i-heroicons-exclamation-triangle-20-solid',
      })
    }
  } finally {
    emailInput.value = ''
  }
}

useHead({
  titleTemplate: '%s | Nodiens',
})

const darkMode = computed({
  get() {
    return $colorMode.value === 'dark'
  },
  set(value) {
    if (value) {
      $colorMode.preference = 'dark'
    } else {
      $colorMode.preference = 'light'
    }
  },
})
</script>

<template>
  <div class="flex min-h-screen flex-col">
    <Navbar dashboard />
    <main class="flex-1">
      <slot />
    </main>

    <footer class="dark:bg-neutrals-900 grid-cols-1 gap-4 bg-white p-5 md:grid-cols-12 md:p-10 lg:grid">
      <div class="flex flex-col gap-4 text-black md:col-span-5 dark:text-white">
        <div>
          <ColorScheme>
            <img
              v-if="darkMode"
              src="~/assets/media/nodiens-logo-white.webp"
              alt="logo"
              width="195"
              height="50"
              class="h-[40px] w-auto"
            />
            <img
              v-if="!darkMode"
              src="~/assets/media/nodiens-logo-black.webp"
              alt="logo"
              width="195"
              height="50"
              class="h-[40px] w-auto"
            />
          </ColorScheme>
        </div>
        <div class="flex items-center gap-3">
          <span class="text-neutrals-800 text-base font-medium dark:text-white">Backed By</span>
          <ColorScheme>
            <img
              v-if="!darkMode"
              src="~/assets/media/esc-logo-white.svg"
              alt="logo"
              width="139"
              height="50"
              class="h-[40px] w-auto"
            />
            <img
              v-if="darkMode"
              src="~/assets/media/esc-logo-black.svg"
              alt="logo"
              width="139"
              height="50"
              class="h-[40px] w-auto"
            />
          </ColorScheme>
        </div>
      </div>

      <div class="mt-4 flex flex-col gap-2 text-black md:col-span-3 lg:col-span-2 lg:mt-0 dark:text-white">
        <h3 class="mb-1 text-lg font-medium">Resources</h3>
        <NuxtLink href="/faq?category=general"> FAQs </NuxtLink>
        <NuxtLink href="/contact"> Contact Us </NuxtLink>
        <NuxtLink href="/bug"> Bug Report </NuxtLink>
        <NuxtLink href="/feature"> Feature Request </NuxtLink>
      </div>

      <div class="mt-4 flex flex-col gap-2 text-black md:col-span-3 lg:col-span-2 lg:mt-0 dark:text-white">
        <h3 class="mb-1 text-lg font-medium">Legal</h3>
        <NuxtLink href="/legal/terms"> Terms of Service </NuxtLink>
        <NuxtLink href="/legal/privacy"> Privacy Policy </NuxtLink>
        <NuxtLink href="/legal/cookie"> Cookie Policy </NuxtLink>
        <NuxtLink href="/legal/disclaimer"> Legal Disclaimer </NuxtLink>
        <NuxtLink href="/legal/financial"> Financial Disclaimer </NuxtLink>
      </div>

      <div class="mt-4 flex flex-col gap-2 md:col-span-12 lg:col-span-3 lg:mt-0">
        <h3 class="mb-1 text-lg font-medium">Newsletter</h3>
        <p class="text-neutrals-300 dark:text-neutrals-300 text-base">
          Stay ahead with expert insights and in-depth research on the latest trends in Web3, Blockchain innovation,
          DeFi, CeFi, and more.
        </p>
        <div class="flex flex-row items-center justify-between rounded border border-black px-4 py-3 dark:border-white">
          <input
            v-model="emailInput"
            type="email"
            name="email"
            placeholder="Your email address"
            class="placeholder:text-neutrals-300 dark:placeholder:text-neutrals-300 w-full bg-transparent text-base text-black dark:bg-transparent dark:text-white"
          />
          <div
            class="h-[16px] w-[16px] cursor-pointer"
            @click="handleSubscribe"
          >
            <svg
              width="16"
              height="17"
              viewBox="0 0 16 17"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M3.33398 8.50016H12.6673M12.6673 8.50016L8.00065 3.8335M12.6673 8.50016L8.00065 13.1668"
                stroke="currentColor"
                stroke-width="1.33333"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>
        </div>
      </div>
    </footer>
    <TermsAndPrivacyModal
      v-model="showModal"
      @agreed="onSubscribe"
    />
    <UNotifications />
  </div>
</template>
