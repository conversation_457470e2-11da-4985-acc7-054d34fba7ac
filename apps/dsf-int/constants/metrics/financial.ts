import { CHART_TYPES } from '~/constants/charts'

export const financialMetricGroups = [
  {
    group: 'Market Dynamics',
    color: '#8B5CF6',
    description: '',
    metrics: [
      {
        label: 'Market Cap',
        description:
          "The total market value of a crypto asset's circulating supply, denominated in United States Dollars (USD). It is analogous to the free-float capitalization in the stock market.",
        compareUnit: 'marketCap',
        multipleCompareUnit: false,
        chartType: CHART_TYPES.LINE_CHART,
      },
      {
        label: 'Price',
        description: `Price is the market price of a single unit of a crypto asset, denominated in United States Dollars (USD). This reflects the trading price from various exchanges, offering a snapshot of a crypto asset's value daily.`,
        compareUnit: 'price',
        multipleCompareUnit: false,
        chartType: CHART_TYPES.LINE_CHART,
      },
      {
        label: 'Volatility (30d)',
        description: `A risk measure for the market price of a crypto asset, calculated as the 30-day rolling standard deviation of daily price returns. This represents the extent of price fluctuations over the past month.<br />
          <br />
          A lower value indicates that the market price has been relatively stable, reflecting lower volatility and potentially lower risk for investors.`,
        compareUnit: 'monthVolatility',
        multipleCompareUnit: false,
        chartType: CHART_TYPES.LINE_CHART,
      },
      {
        label: 'Depeg Rate*',
        description: `The percentage of days over the last 365 days, or since the launch (if less than 365 days), during which the market price of a stablecoin fell below 0.995 USD.<br/>
          <br />
          Note that this metric applies only to stablecoins.`,
        compareUnit: 'depegDays',
        multipleCompareUnit: false,
        chartType: CHART_TYPES.LINE_CHART,
      },
    ],
  },
  {
    group: 'Centralised Exchange',
    color: '#3EA95B',
    description: '',
    metrics: [
      {
        label: 'CEX Volume (24h)',
        description:
          'A measure of how much of a crypto asset was traded in the last 24 hours across the tracked Centralized Exchanges (CEXs), denominated in United States Dollars (USD). This is estimated by aggregating the 24-hour trading volume of all token pairs involving a specific crypto asset across the tracked CEXs.',
        compareUnit: 'dayVolumeCex',
        multipleCompareUnit: false,
        chartType: CHART_TYPES.LINE_CHART,
      },
      {
        label: '# CEX Pairs',
        description:
          'The number of token pairs involving a specific crypto asset that are actively traded on the tracked Centralized Exchanges (CEXs). A pair is considered actively traded if its 24-hour trading volume exceeds 100,000 USD.',
        compareUnit: 'cexPairs',
        multipleCompareUnit: false,
        chartType: CHART_TYPES.LINE_CHART,
      },
      {
        label: 'CEX Liq Cost',
        description:
          "The average percentage of a crypto asset's market price paid to execute a market order of a selected size on the tracked Centralized Exchanges (CEXs). This represents the average cost of trading a specific crypto asset based on the Marginal Cost of Immediacy (MCI) metric.",
        compareUnit: 'cexLiqCost',
        multipleCompareUnit: true,
        chartType: CHART_TYPES.LINE_CHART,
      },
    ],
  },
  {
    group: 'Decentralised Exchange',
    color: '#FF2323',
    description: '',
    metrics: [
      {
        label: 'DEX Volume (24h)',
        description: `A measure of how much of a crypto asset was traded in the last 24 hours across the tracked Decentralized Exchanges (DEXs), denominated in United States Dollars (USD). This is estimated by aggregating the 24-hour trading volume of all token pairs involving a specific crypto asset across the tracked DEXs.`,
        compareUnit: 'dayVolumeDex',
        multipleCompareUnit: false,
        chartType: CHART_TYPES.LINE_CHART,
      },
      {
        label: '# DEX Pairs',
        description: `The number of token pairs involving a specific crypto asset that are actively traded on the tracked Decentralized Exchanges (DEXs). A pair is considered actively traded if its 24-hour trading volume exceeds 100,000 USD.`,
        compareUnit: 'dexPairs',
        multipleCompareUnit: false,
        chartType: CHART_TYPES.LINE_CHART,
      },
      {
        label: 'DEX Liq Cost',
        description: `The average percentage of a crypto asset's market price paid to execute a market order of a selected size on the tracked Decentralized Exchanges (DEXs). This represents the average cost of trading a specific crypto asset based on the Marginal Cost of Immediacy (MCI) metric.`,
        compareUnit: 'dexLiqCost',
        multipleCompareUnit: true,
        chartType: CHART_TYPES.LINE_CHART,
      },
      {
        label: 'DEX Liq Conc',
        description: `A coefficient measuring the concentration of liquidity providers for a crypto asset on the tracked Decentralized Exchanges (DEXs).<br />
          <br />
          A value close to 0 indicates highly distributed liquidity, while a value close to 1 indicates highly concentrated liquidity among few providers.`,
        compareUnit: 'dexLiqConcentrate',
        multipleCompareUnit: false,
        chartType: CHART_TYPES.LINE_CHART,
      },
    ],
  },
]

const getMetricGroup = (index: number) => {
  const metric = financialMetrics[index]

  const group = financialMetricGroups.find((group) => {
    return group.metrics.find((_metric) => {
      return _metric.label === metric
    })
  })

  return group
}

const getMetricObj = (index: number) => {
  const metric = financialMetrics[index]
  const metricObjArray = financialMetricGroups.map((group) => group.metrics).flat()

  return metricObjArray.find((metricObj) => metricObj.label === metric)
}

export const financialCompareKeyMetrics = financialMetricGroups
  .map((group) => group.metrics.map((metric) => metric.compareUnit).flat())
  .flat()

export const financialMetrics = financialMetricGroups
  .map((group) => group.metrics.map((metric) => metric.label).flat())
  .flat()

export const financialMetricColor = (index: number) => {
  const group = getMetricGroup(index)

  return group!.color
}

export const getFinancialMetricObjKey = (key: string, index: number) => {
  const group = getMetricObj(index)

  return (group as Record<string, unknown>)[key]
}
