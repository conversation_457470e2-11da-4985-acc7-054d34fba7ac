// algoControlled: yes = true; no = false
// communityInvolvement: yes = true; no/skip = false
// transparency: yes = true; no/skip = false
// decisionMaking: committee/yes = true; otherwise = false

export default [
  {
    "name": "Bitcoin",
    "algoControlled": true,
    "communityInvolvement": false,
    "transparency": false,
    "decisionMaking": false
  },
  {
    "name": "Ethereum",
    "algoControlled": false,
    "communityInvolvement": false,
    "transparency": true,
    "decisionMaking": true
  },
  {
    "name": "<PERSON><PERSON><PERSON>",
    "algoControlled": false,
    "communityInvolvement": false,
    "transparency": true,
    "decisionMaking": true
  },
  {
    "name": "Cardano",
    "algoControlled": false,
    "communityInvolvement": true,
    "transparency": false,
    "decisionMaking": true
  },
  {
    "name": "XRPL",
    "algoControlled": false,
    "communityInvolvement": false,
    "transparency": false,
    "decisionMaking": true
  },
  {
    "name": "Algorand",
    "algoControlled": false,
    "communityInvolvement": false,
    "transparency": false,
    "decisionMaking": true
  },
  {
    "name": "<PERSON><PERSON>",
    "algoControlled": false,
    "communityInvolvement": false,
    "transparency": true,
    "decisionMaking": true
  },
  {
    "name": "Polkadot",
    "algoControlled": false,
    "communityInvolvement": true,
    "transparency": false,
    "decisionMaking": false
  },
  {
    "name": "Avalanche",
    "algoControlled": false,
    "communityInvolvement": false,
    "transparency": true,
    "decisionMaking": true
  },
  {
    "name": "Tezos",
    "algoControlled": false,
    "communityInvolvement": true,
    "transparency": false,
    "decisionMaking": false
  },
  {
    "name": "Tron",
    "algoControlled": false,
    "communityInvolvement": false,
    "transparency": true,
    "decisionMaking": true
  },
  {
    "name": "Polygon",
    "algoControlled": false,
    "communityInvolvement": false,
    "transparency": true,
    "decisionMaking": true
  },
  {
    "name": "Near",
    "algoControlled": false,
    "communityInvolvement": false,
    "transparency": true,
    "decisionMaking": true
  },
  {
    "name": "BNB",
    "algoControlled": false,
    "communityInvolvement": false,
    "transparency": true,
    "decisionMaking": true
  },
  {
    "name": "SUI",
    "algoControlled": false,
    "communityInvolvement": false,
    "transparency": true,
    "decisionMaking": true
  },
  {
    "name": "Celestia",
    "algoControlled": false,
    "communityInvolvement": false,
    "transparency": true,
    "decisionMaking": true
  },
  {
    "name": "Aptos",
    "algoControlled": false,
    "communityInvolvement": false,
    "transparency": true,
    "decisionMaking": true
  },
  {
    "name": "Cosmos",
    "algoControlled": false,
    "communityInvolvement": true,
    "transparency": false,
    "decisionMaking": false
  },
  {
    "name": "Graph",
    "algoControlled": false,
    "communityInvolvement": false,
    "transparency": true,
    "decisionMaking": true
  },
  {
    "name": "FetchAI",
    "algoControlled": false,
    "communityInvolvement": false,
    "transparency": true,
    "decisionMaking": true
  },
  {
    "name": "TON",
    "algoControlled": false,
    "communityInvolvement": true,
    "transparency": false,
    "decisionMaking": false
  },
  {
    "name": "Litecoin",
    "algoControlled": true,
    "communityInvolvement": false,
    "transparency": false,
    "decisionMaking": false
  },
  {
    "name": "Mina",
    "algoControlled": false,
    "communityInvolvement": false,
    "transparency": true,
    "decisionMaking": true
  },
  {
    "name": "dYdX",
    "algoControlled": false,
    "communityInvolvement": true,
    "transparency": false,
    "decisionMaking": false
  },
  {
    "name": "Terra",
    "algoControlled": false,
    "communityInvolvement": true,
    "transparency": false,
    "decisionMaking": false
  },
  {
    "name": "Casper",
    "algoControlled": false,
    "communityInvolvement": false,
    "transparency": true,
    "decisionMaking": true
  },
  {
    "name": "Synthetix",
    "algoControlled": false,
    "communityInvolvement": true,
    "transparency": false,
    "decisionMaking": false
  },
  {
    "name": "Cronos",
    "algoControlled": false,
    "communityInvolvement": true,
    "transparency": false,
    "decisionMaking": false
  },
  {
    "name": "Akash",
    "algoControlled": false,
    "communityInvolvement": true,
    "transparency": false,
    "decisionMaking": false
  },
  {
    "name": "Fantom",
    "algoControlled": false,
    "communityInvolvement": false,
    "transparency": true,
    "decisionMaking": true
  },
  {
    "name": "Kava",
    "algoControlled": false,
    "communityInvolvement": true,
    "transparency": false,
    "decisionMaking": false
  },
  {
    "name": "Injective",
    "algoControlled": false,
    "communityInvolvement": true,
    "transparency": false,
    "decisionMaking": false
  },
  {
    "name": "Axelar",
    "algoControlled": false,
    "communityInvolvement": false,
    "transparency": true,
    "decisionMaking": true
  },
  {
    "name": "SEI Network",
    "algoControlled": false,
    "communityInvolvement": false,
    "transparency": true,
    "decisionMaking": true
  },
  {
    "name": "Osmosis",
    "algoControlled": false,
    "communityInvolvement": true,
    "transparency": false,
    "decisionMaking": false
  },
  {
    "name": "ZetaChain",
    "algoControlled": false,
    "communityInvolvement": true,
    "transparency": false,
    "decisionMaking": false
  },
  {
    "name": "MultiversX",
    "algoControlled": false,
    "communityInvolvement": false,
    "transparency": true,
    "decisionMaking": true
  },
  {
    "name": "Radix",
    "algoControlled": false,
    "communityInvolvement": false,
    "transparency": true,
    "decisionMaking": true
  },
  {
    "name": "Uniswap",
    "algoControlled": false,
    "communityInvolvement": true,
    "transparency": false,
    "decisionMaking": false
  },
  {
    "name": "Aave",
    "algoControlled": false,
    "communityInvolvement": true,
    "transparency": false,
    "decisionMaking": false
  },
  {
    "name": "Maker",
    "algoControlled": false,
    "communityInvolvement": true,
    "transparency": false,
    "decisionMaking": false
  },
  {
    "name": "Mantle",
    "algoControlled": false,
    "communityInvolvement": true,
    "transparency": false,
    "decisionMaking": false
  },
  {
    "name": "Lido DAO",
    "algoControlled": false,
    "communityInvolvement": true,
    "transparency": false,
    "decisionMaking": false
  },
  {
    "name": "Ethena",
    "algoControlled": false,
    "communityInvolvement": false,
    "transparency": false,
    "decisionMaking": true
  },
  {
    "name": "Gnosis",
    "algoControlled": false,
    "communityInvolvement": true,
    "transparency": false,
    "decisionMaking": false
  },
  {
    "name": "ENS",
    "algoControlled": false,
    "communityInvolvement": true,
    "transparency": false,
    "decisionMaking": false
  },
  {
    "name": "Ocean",
    "algoControlled": false,
    "communityInvolvement": true,
    "transparency": false,
    "decisionMaking": false
  },
  {
    "name": "Compound",
    "algoControlled": false,
    "communityInvolvement": true,
    "transparency": false,
    "decisionMaking": false
  },
  {
    "name": "0xProtocol",
    "algoControlled": false,
    "communityInvolvement": true,
    "transparency": false,
    "decisionMaking": false
  },
  {
    "name": "Dexe",
    "algoControlled": false,
    "communityInvolvement": true,
    "transparency": false,
    "decisionMaking": false
  }
]
