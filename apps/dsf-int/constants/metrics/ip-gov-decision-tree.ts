// ipGovern: Community = true / Single Ent = false
// votingProcess: Yes = true / No = false
// decisionMaking: Committee = true / Individual = false

export default [
  {
    "name": "Bitcoin",
    "ipGovern": true,
    "votingProcess": true,
    "decisionMaking": false,
    "finalScore": 1
  },
  {
    "name": "Ethereum",
    "ipGovern": true,
    "votingProcess": false,
    "decisionMaking": false,
    "finalScore": 0.8
  },
  {
    "name": "<PERSON><PERSON><PERSON>",
    "ipGovern": false,
    "votingProcess": false,
    "decisionMaking": true,
    "finalScore": 0.3
  },
  {
    "name": "Cardano",
    "ipGovern": true,
    "votingProcess": false,
    "decisionMaking": false,
    "finalScore": 0.8
  },
  {
    "name": "XRPL",
    "ipGovern": true,
    "votingProcess": true,
    "decisionMaking": false,
    "finalScore": 1
  },
  {
    "name": "Algorand",
    "ipGovern": true,
    "votingProcess": false,
    "decisionMaking": false,
    "finalScore": 0.8
  },
  {
    "name": "Solana",
    "ipGovern": true,
    "votingProcess": false,
    "decisionMaking": false,
    "finalScore": 0.8
  },
  {
    "name": "Polkadot",
    "ipGovern": true,
    "votingProcess": true,
    "decisionMaking": false,
    "finalScore": 1
  },
  {
    "name": "Avalanche",
    "ipGovern": true,
    "votingProcess": false,
    "decisionMaking": false,
    "finalScore": 0.8
  },
  {
    "name": "Tezos",
    "ipGovern": true,
    "votingProcess": false,
    "decisionMaking": false,
    "finalScore": 0.8
  },
  {
    "name": "Tron",
    "ipGovern": true,
    "votingProcess": false,
    "decisionMaking": false,
    "finalScore": 0.8
  },
  {
    "name": "Polygon",
    "ipGovern": true,
    "votingProcess": false,
    "decisionMaking": false,
    "finalScore": 0.8
  },
  {
    "name": "Near",
    "ipGovern": true,
    "votingProcess": true,
    "decisionMaking": false,
    "finalScore": 1
  },
  {
    "name": "BNB",
    "ipGovern": true,
    "votingProcess": true,
    "decisionMaking": false,
    "finalScore": 1
  },
  {
    "name": "SUI",
    "ipGovern": true,
    "votingProcess": false,
    "decisionMaking": false,
    "finalScore": 0.8
  },
  {
    "name": "Celestia",
    "ipGovern": true,
    "votingProcess": false,
    "decisionMaking": false,
    "finalScore": 0.8
  },
  {
    "name": "Aptos",
    "ipGovern": true,
    "votingProcess": false,
    "decisionMaking": false,
    "finalScore": 0.8
  },
  {
    "name": "Cosmos",
    "ipGovern": true,
    "votingProcess": false,
    "decisionMaking": false,
    "finalScore": 0.8
  },
  {
    "name": "Graph",
    "ipGovern": true,
    "votingProcess": false,
    "decisionMaking": false,
    "finalScore": 0.8
  },
  {
    "name": "FetchAI",
    "ipGovern": true,
    "votingProcess": false,
    "decisionMaking": false,
    "finalScore": 0.8
  },
  {
    "name": "TON",
    "ipGovern": true,
    "votingProcess": false,
    "decisionMaking": false,
    "finalScore": 0.8
  },
  {
    "name": "Litecoin",
    "ipGovern": true,
    "votingProcess": false,
    "decisionMaking": false,
    "finalScore": 0.8
  }
]
