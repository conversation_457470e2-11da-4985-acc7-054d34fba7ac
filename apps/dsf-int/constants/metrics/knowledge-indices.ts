import { CHART_TYPES } from '~/constants/charts'

export const knowledgeMetricGroups = [
  {
    group: 'Repository Contribution',
    color: '#3EA95B',
    description: '',
    metrics: [
      {
        label: 'Maintainers Gini Coeff',
        description: `Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc volutpat, velit eget tincidunt sollicitudin, lorem diam efficitur diam, ut fringilla.`,
        compareUnit: 'rcpDevDistrGini',
        multipleCompareUnit: false,
        chartType: CHART_TYPES.LINE_CHART,
      },
      {
        label: 'Maintainers Nakamoto Coeff',
        description: `Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc volutpat, velit eget tincidunt sollicitudin, lorem diam efficitur diam, ut fringilla.`,
        compareUnit: 'rcpParticipantDivShannon',
        multipleCompareUnit: false,
        chartType: CHART_TYPES.LINE_CHART,
      },
    ],
  },
  {
    group: 'Code Development Volatility',
    color: '#FF2323',
    description: '',
    metrics: [
      {
        label: 'Commit Frequency',
        description: `Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc volutpat, velit eget tincidunt sollicitudin, lorem diam efficitur diam, ut fringilla.`,
        compareUnit: 'ipAuthDistrGini',
        multipleCompareUnit: false,
        chartType: CHART_TYPES.LINE_CHART,
      },
      {
        label: 'Commit Interval',
        description: `Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc volutpat, velit eget tincidunt sollicitudin, lorem diam efficitur diam, ut fringilla.`,
        compareUnit: 'ipParticipantDivGini',
        multipleCompareUnit: false,
        chartType: CHART_TYPES.LINE_CHART,
      },
      {
        label: 'Issue Resolution Agility',
        description: `Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc volutpat, velit eget tincidunt sollicitudin, lorem diam efficitur diam, ut fringilla.`,
        compareUnit: 'ipAuthorInflConcHHI',
        multipleCompareUnit: true,
      },
    ],
  },
  {
    group: 'Developers Community Engagement',
    color: '#FF8D24',
    description: '',
    metrics: [
      {
        label: 'Issue Interaction Index',
        description: `Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc volutpat, velit eget tincidunt sollicitudin, lorem diam efficitur diam, ut fringilla.`,
        compareUnit: 'consensusPowerNeGini',
        multipleCompareUnit: false,
        chartType: CHART_TYPES.LINE_CHART,
      },
      {
        label: 'Stargazer Growth Rate',
        description: `Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc volutpat, velit eget tincidunt sollicitudin, lorem diam efficitur diam, ut fringilla.`,
        compareUnit: 'consensusPowerNeTheil',
        multipleCompareUnit: false,
        chartType: CHART_TYPES.LINE_CHART,
      },
      {
        label: 'Fork Expansion Factor',
        description: `Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc volutpat, velit eget tincidunt sollicitudin, lorem diam efficitur diam, ut fringilla.`,
        compareUnit: 'consensusPowerConcNakamoto',
        multipleCompareUnit: false,
        chartType: CHART_TYPES.LINE_CHART,
      },
    ],
  },
  {
    group: 'Whitepaper Readability',
    color: '#8B5CF6',
    description: '',
    metrics: [
      {
        label: 'Reading Time',
        description: `Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc volutpat, velit eget tincidunt sollicitudin, lorem diam efficitur diam, ut fringilla.`,
        compareUnit: 'coinDistrOrdinal',
        multipleCompareUnit: false,
        chartType: CHART_TYPES.LINE_CHART,
      },
      {
        label: 'Mdn Readability Score',
        description: `Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc volutpat, velit eget tincidunt sollicitudin, lorem diam efficitur diam, ut fringilla.`,
        compareUnit: 'coinDistrOrdinal',
        multipleCompareUnit: false,
        chartType: CHART_TYPES.LINE_CHART,
      },
      {
        label: 'Avg # Words/Sentence',
        description: `Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc volutpat, velit eget tincidunt sollicitudin, lorem diam efficitur diam, ut fringilla.`,
        compareUnit: 'coinDistrOrdinal',
        multipleCompareUnit: false,
        chartType: CHART_TYPES.LINE_CHART,
      },
      {
        label: 'Avg # Syllables/Word',
        description: `Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc volutpat, velit eget tincidunt sollicitudin, lorem diam efficitur diam, ut fringilla.`,
        compareUnit: 'coinDistrOrdinal',
        multipleCompareUnit: false,
        chartType: CHART_TYPES.LINE_CHART,
      },
    ],
  },
  {
    group: 'Whitepaper Influence',
    color: '#00B0FF',
    description: '',
    metrics: [
      {
        label: 'Decay Rate',
        description: `Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc volutpat, velit eget tincidunt sollicitudin, lorem diam efficitur diam, ut fringilla.`,
        compareUnit: 'coinDistrOrdinal',
        multipleCompareUnit: false,
        chartType: CHART_TYPES.LINE_CHART,
      },
    ],
  },
]

const getMetricGroup = (index: number) => {
  const metric = knowledgeMetrics[index]

  const group = knowledgeMetricGroups.find((group) => {
    return group.metrics.find((_metric) => {
      return _metric.label === metric
    })
  })

  return group
}

const getMetricObj = (index: number) => {
  const metric = knowledgeMetrics[index]
  const metricObjArray = knowledgeMetricGroups.map((group) => group.metrics).flat()

  return metricObjArray.find((metricObj) => metricObj.label === metric)
}

export const knowledgeCompareKeyMetrics = knowledgeMetricGroups
  .map((group) => group.metrics.map((metric) => metric.compareUnit).flat())
  .flat()

export const knowledgeMetrics = knowledgeMetricGroups
  .map((group) => group.metrics.map((metric) => metric.label).flat())
  .flat()

export const knowledgeMetricColor = (index: number) => {
  const group = getMetricGroup(index)

  return group!.color
}

export const getKnowledgeMetricObjKey = (key: string, index: number) => {
  const group = getMetricObj(index)

  return (group as Record<string, unknown>)[key]
}
