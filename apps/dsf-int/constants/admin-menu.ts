export const useAdminMenu = () => {
  const route = useRoute()

  const adminMenu = computed(() => [
    {
      label: 'Summary',
      icon: 'i-heroicons-home',
      to: '/admin',
      exact: true,
      activeClass: 'bg-red-900',
    },
    {
      label: 'Users',
      icon: 'i-material-symbols:person-2-outline-rounded',
      to: '/admin/users',
      active: route.path.includes('/admin/users'),
    },
    {
      label: 'Exclude Paywall',
      icon: 'i-ri:secure-payment-line',
      to: '/admin/exclude-paywall',
      active: route.path.includes('/admin/exclude-paywall')
    },
  ])

  return {
    adminMenu,
  }
}
