export const COMMUNITY_INDICES_LEVEL_DESCRIPTIONS = {
  layer1: `The Level 1 Mood (or Trust) Index represents a platform- and community-agnostic metric for a single crypto asset. It aggregates the Level 2 Mood (or Trust) Index values for that asset across all tracked platforms and communities, weighting each by its community size. The result is a single, comprehensive measure of the asset's overall sentiment or trust across platforms and communities for each individual crypto asset.`,
  layer2: `This metric consolidates all Level 3 (community-specific) Mood (or Trust) scores for a given crypto asset on a single platform. Each community's score is weighted by its size, resulting in a platform-level index that captures overall sentiment (or trust), independent of individual community differences.`,
  layer3: `The Level 3 Mood (or Trust) Index provides a detailed, platform-specific (e.g., Telegram, Reddit) and community-specific view for each tracked crypto asset. By measuring each community individually, it delivers the most granular insight into how sentiment or trust varies across different platforms within the same ecosystem.`,
}

export const CLIMATE_TOOLTIPS = {
  esgLayer: `The architecture layer of the underlying blockchain network of a crypto asset. This contributes uniquely to the functionality, scalability, and security of the blockchain.<br/><br/>Layer 1 (L1) is a basic blockchain protocol that provides the foundation for a blockchain ecosystem. Layer 0 (L0) is a base layer to multiple relay chains. Layer 2 (L2) is a scaling solution that enables high throughput of transactions whilst relying on the underlying L1 for additional security.`,
  consensusMechanism: `The consensus mechanism represents the process by which participants of a decentralized network come to an agreement on what the transaction history is, outlining the conditions that need to be met for new transaction batches to be added to the history.`,
}

export const ASSET_CRYPTO_TOOLTIPS = {
  vulgarity_index: `By leveraging NLP models, the Index extracts the proportion of vulgar language used within the tracked communities of a crypto asset over the last 14 days. This provides insights into the tone and appropriateness of the content, helping to assess the overall quality and suitability for various audiences.`,
  price: `The market price of a single unit of a crypto asset, denominated in United States Dollars (USD). This reflects the trading price from various exchanges, offering a snapshot of a crypto asset's value on the current day.`,
  mood_ranking: `The ranking of a crypto asset based on its Mood Index and adjusted for its relative market capitalization. <br/>
<br/>
They are sorted in descending order of their weighted Mood Index.
`,
  trust_ranking: `The ranking of a crypto asset based on its Trust Index and adjusted for its relative market capitalisation.<br/>
<br/>
They are sorted in descending order of their weighted Trust Index.`,
  messages: `The total number of posts, comments, and messages exchanged across the tracked communities of a crypto
                  asset over the last 14 days. This shows the overall level of activity and interaction within these
                  communities.`,
  bots: `The number of bots identified across the tracked Telegram communities of a crypto asset over the last 14 days.<br/>
<br/>
Note that there may not be any tracked Telegram communities for some crypto assets, in which case this value will be 0.`,
  mood_trust_index: `Mood Index is an indicator of the mood level in tracked communities of a crypto asset. This uses proprietary methodologies to extract sentiment-related features and natural language processing techniques to quantify and aggregate the mood.<br/>
<br/>
Trust Index is an indicator of the trust level in tracked communities of a crypto asset. This considers diversity, engagement, moderation, consistency, and transparency.<br/>
<br/>
These values are scaled from 100 without boundaries. A higher mood index indicates more positive mood and expectations within the tracked communities, while a higher trust index indicates greater trust and confidence within the tracked communities.`,
  price_volume: `Price is the market price of a single unit of a crypto asset, denominated in United States Dollars (USD). This reflects the trading price from various exchanges, offering a snapshot of a crypto asset's value daily.<br/>
<br/>
Volume is a measure of how much of a crypto asset was traded in the last 24 hours, denominated in United States Dollars (USD). This provides insights into the trading activity and liquidity of a crypto asset, indicating how actively it is being bought and sold in the market.`,
}

export const ASSET_STABLECOIN_TOOLTIPS = {
  vulgarity_index: `By leveraging NLP models, the Index extracts the proportion of vulgar language used within the tracked communities of a crypto asset over the last 14 days. This provides insights into the tone and appropriateness of the content, helping to assess the overall quality and suitability for various audiences.`,
  price: `The market price of a single unit of a crypto asset, denominated in United States Dollars (USD). This reflects the trading price from various exchanges, offering a snapshot of a crypto asset's value on the current day.`,
  mood_ranking: `The ranking of a crypto asset based on its Mood Index and adjusted for market capitalisation.<br/>
<br/>
They are sorted in descending order of their weighted Mood Index.`,
  trust_ranking: `The ranking of a crypto asset based on its Trust Index and adjusted for market capitalisation.<br/>
<br/>
They are sorted in descending order of their weighted Trust Index.`,
  messages: `The total number of posts, comments, and messages exchanged across the tracked communities of a crypto asset over the last 14 days. This shows the overall level of activity and interaction within these communities.`,
  bots: `The number of bots identified across the tracked Telegram communities of a crypto asset over the last 14 days.<br/>
<br/>
Note that there may not be any tracked Telegram communities for some crypto assets, in which case this value will be 0.`,
  mood_trust_index: `Mood Index is an indicator of the mood level in tracked communities of a crypto asset. This uses proprietary methodologies to extract sentiment-related features and natural language processing techniques to quantify and aggregate the mood.<br/>
<br/>
Trust Index is an indicator of the trust level in tracked communities of a crypto asset. This considers diversity, engagement, moderation, consistency, and transparency.<br/>
<br/>
These values are scaled from 100 without boundaries. A higher mood index indicates more positive mood and expectations within the tracked communities, while a higher trust index indicates greater trust and confidence within the tracked communities.`,
  maket_cap_volume: `Market cap is the total market value of a crypto asset's circulating supply, denominated in United States Dollars (USD). It is analogous to the free-float capitalization in the stock market.<br/>
<br/>
Volume is a measure of how much of a crypto asset was traded in the last 24 hours, denominated in United States Dollars (USD). This provides insights into the trading activity and liquidity of a crypto asset, indicating how actively it is being bought and sold in the market.`,
}
