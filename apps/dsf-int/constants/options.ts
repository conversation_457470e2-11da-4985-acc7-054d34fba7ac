import type { ShareButtonVariant } from '~/components/molecule/ShareButton/types'

export const COIN_SIZE_LIST = [
  {
    id: '100',
    name: '$100.00',
    compareSize: {
      cex: 'cexLiqCost100',
      dex: 'dexLiqCost100',
    },
  },
  {
    id: '10k',
    name: '$10,000',
    compareSize: {
      cex: 'cexLiqCost10k',
      dex: 'dexLiqCost10k',
    },
  },
  {
    id: '1m',
    name: '$1.0M',
    compareSize: {
      cex: 'cexLiqCost1m',
      dex: 'dexLiqCost1m',
    },
  },
  {
    id: '0.001_24h_vol',
    name: '0.001% 24H Volume',
    compareSize: {
      cex: 'cexLiqCost0.001_24h_vol',
      dex: 'dexLiqCost0.001_24h_vol',
    },
  },
  {
    id: '0.1_24h_vol',
    name: '0.100% 24H Volume',
    compareSize: {
      cex: 'cexLiqCost0.1_24h_vol',
      dex: 'dexLiqCost0.1_24h_vol',
    },
  },
  {
    id: '1_24h_vol',
    name: '1.00% 24H Volume',
    compareSize: {
      cex: 'cexLiqCost1_24h_vol',
      dex: 'dexLiqCost1_24h_vol',
    },
  },
]

export const SOCIAL_MEDIA_LIST: ShareButtonVariant[] = ['twitter', 'facebook', 'linkedin', 'telegram', 'reddit']

export const HOME_TABLE_RANK_OPTION_DESCRIPTIONS = {
  moodIndex: `The Mood Ranking ranks crypto assets based on their Mood Index, adjusted for market capitalization to reflect both sentiment and the asset’s relative market presence.<br/>
<br/>
Assets are ranked in descending order by their weighted Mood Index, providing a comparative view of community sentiment across the crypto ecosystem.`,
  trustIndex: `The Trust Ranking orders crypto assets by their Trust Index, adjusting for market capitalization to offer a more balanced view of their communities' quality in relation to market size.<br/>
<br/>
Assets are ranked in descending order according to their weighted Trust Index, offering a comparative measure of community quality across the crypto landscape.`,
}

export const HOME_TABLE_RANK_OPTION_VALUES = {
  moodIndex: 'mood_index',
  trustIndex: 'trust_index',
  communitySize: 'community_size',
  nrMessages: 'nr_messages',
  energyConsumptionTx: 'energy_consumption_tx',
} as const

export const HOME_TABLE_RANK_OPTION_LABELS = {
  [HOME_TABLE_RANK_OPTION_VALUES.moodIndex]: 'Mood Ranking',
  [HOME_TABLE_RANK_OPTION_VALUES.trustIndex]: 'Trust Ranking',
  [HOME_TABLE_RANK_OPTION_VALUES.communitySize]: 'Community Size',
  [HOME_TABLE_RANK_OPTION_VALUES.nrMessages]: '# Messages (14d)',
  [HOME_TABLE_RANK_OPTION_VALUES.energyConsumptionTx]: 'Energy Cons/Tx',
} as const

export const COMMUNITY_ASSET_OPTION_LABELS = {
  crypto: 'Cryptos',
  stable: 'Stablecoins',
} as Record<string, string>

export const COMMUNITY_TABLE_RANK_VALUES = {
  communitySize: 'community_size',
  messageCount: 'message_count',
  rankIndicator: 'rank_indicator',
  aDayChangedPercentage: 'a_day_changed_percentage',
} as const

export const COMMUNITY_TABLE_MOOD_RANK_OPTION_LABELS = {
  [COMMUNITY_TABLE_RANK_VALUES.communitySize]: 'Community Size',
  [COMMUNITY_TABLE_RANK_VALUES.messageCount]: '# Messages (14d)',
  [COMMUNITY_TABLE_RANK_VALUES.rankIndicator]: 'Mood Ranking',
  [COMMUNITY_TABLE_RANK_VALUES.aDayChangedPercentage]: 'Mood Index (1d %)',
} as const

export const COMMUNITY_TABLE_TRUST_RANK_OPTION_LABELS = {
  [COMMUNITY_TABLE_RANK_VALUES.communitySize]: 'Community Size',
  [COMMUNITY_TABLE_RANK_VALUES.messageCount]: '# Messages (14d)',
  [COMMUNITY_TABLE_RANK_VALUES.rankIndicator]: 'Trust Ranking',
  [COMMUNITY_TABLE_RANK_VALUES.aDayChangedPercentage]: 'Trust Index (1d %)',
}

export const COMMUNITY_TABLE_RANK_OPTION_DESCRIPTIONS = {
  mood: `The ranking of a crypto asset based on its Mood Index and adjusted for market capitalisation.<br/>
<br/>
They are sorted in descending order of their weighted Mood Index.`,
  trust: `The ranking of a crypto asset based on its Trust Index and adjusted for market capitalization.<br/>
<br/>
They are sorted in descending order of their weighted Trust Index.`,
}

export const ESG_CHART_TYPE_OPTION_LABEL = {
  powerUse: 'Power Use',
  powerUsePerTx: 'Power Use / Tx',
  powerUsePerTxNode: 'Power Use / Tx / Node',
  emission: 'CO₂ Emissions',
  pertx: 'CO₂ Emissions/Tx',
  pertxnode: 'CO₂ Emissions/Tx/Node',
  source: 'Sources',
} as Record<string, string>

export const ESG_CARBON_EMISSION_COLOR_SOURCES = {
  Bioenergy: {
    lineColor: 'rgba(136, 48, 57, 1)',
    bgColor: 'rgba(136, 48, 57, 0.5)',
  },
  Coal: {
    lineColor: 'rgba(109, 62, 145, 1)',
    bgColor: 'rgba(109, 62, 145, 0.5)',
  },
  Gas: {
    lineColor: 'rgba(193, 80, 101, 1)',
    bgColor: 'rgba(193, 80, 101, 0.5)',
  },
  Hydro: {
    lineColor: 'rgba(0, 132, 126, 1)',
    bgColor: 'rgba(0, 132, 126, 0.5)',
  },
  Solar: {
    lineColor: 'rgba(40, 107, 187, 1)',
    bgColor: 'rgba(40, 107, 187, 0.5)',
  },
  Wind: {
    lineColor: 'rgba(0, 41, 91, 1)',
    bgColor: 'rgba(0, 41, 91, 0.5)',
  },
  Oil: {
    lineColor: 'rgba(229, 110, 90, 1)',
    bgColor: 'rgba(229, 110, 90, 0.5)',
  },
  Nuclear: {
    lineColor: 'rgba(188, 42, 90, 1)',
    bgColor: 'rgba(188, 42, 90, 0.5)',
  },
  'Other Fossil': {
    lineColor: 'rgba(188, 142, 90, 1)',
    bgColor: 'rgba(188, 142, 90, 0.5)',
  },
  'Other Renewables': {
    lineColor: 'rgba(87, 129, 69, 1)',
    bgColor: 'rgba(87, 129, 69, 0.5)',
  },
} as Record<
  string,
  {
    lineColor: string
    bgColor: string
  }
>

export const ESG_TABLE_RANK_OPTION_LABELS = {
  power_cons: 'Power Use',
  energy_tx: 'Energy Cons/Tx',
  energy_tx_node: 'Energy Cons/Tx/Node',
  validators: '# Validators',
  tps: 'Throughput',
} as Record<string, string>
