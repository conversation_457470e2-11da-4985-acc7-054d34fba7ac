export const CHART_CLASS_COLUMN = 'w-[12rem] md:w-[16rem]'
export const WIDE_CHART_CLASS_COLUMN = 'w-[16rem] md:w-[24rem]'

export const COMMUNITY_TABLE_TOPIC_COLUMN = [
  {
    key: 'number',
    label: '#',
    enabled: true,
  },
  {
    key: 'topic_name',
    label: 'Topic',
    enabled: true,
    description: `A single word that represents a complex topic discussed across the tracked communities of all crypto assets. This is identified using the Latent Dirichlet Allocation (LDA) model.`,
  },
  {
    key: 'platform',
    label: 'Platforms',
    enabled: true,
    description: `The tracked platforms where a topic is mentioned on the current day. The platform logos are sorted from left to right in descending order of topic frequency.`,
  },
  {
    key: 'occurrences',
    label: 'Frequency (1d)',
    enabled: true,
    description: `The minimum number of times a topic is mentioned across the tracked communities of all crypto assets on the current day.`,
  },
  {
    key: 'occurrences_chart',
    label: 'Frequency Chart (90d)',
    enabled: true,
    description: `The topic frequency trend across the tracked communities of all crypto assets over the last 90 days.`,
    class: WIDE_CHART_CLASS_COLUMN,
  },
]
