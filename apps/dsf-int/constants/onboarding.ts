const options = {
  popper: {
    placement: 'top',
    modifiers: [
      {
        name: 'flip',
        enabled: false,
      },
    ],
  },
  scrollToStep: {
    enabled: true,
    options: {
      behavior: 'smooth',
      block: 'start',
      inline: 'nearest',
    },
  },
}

export const ONBOARDING_CRYPTO_SUMMARY_PAGE = [
  {
    attachTo: { element: '#onboarding-step-1' },
    content: {
      title: 'Snapshot Stats',
      description: 'Check out these cards for the quick stats: price, mood, messages, bots, and more...',
    },
    options: {
      ...options,
      scrollToStep: {
        enabled: false,
      },
    },
  },
  {
    attachTo: { element: '#onboarding-step-2' },
    content: {
      title: 'Trend Overviews',
      description:
        'Take a peek at these charts for the trends: sentiment, trading, chats, community size, global power consumption and energy consumption per transaction.',
    },
    options,
  },
  {
    attachTo: { element: '#onboarding-step-3' },
    content: {
      title: 'Mood Index & Trust Index ',
      description:
        "The Mood & Trust chart reflects the sentiment dynamics and trust levels over time; a crucial indicator of the cryptocurrency's social sentiment.",
    },
    options,
  },
  {
    attachTo: { element: '#onboarding-step-4' },
    content: {
      title: 'Volume & Price Chart',
      description:
        'Examine the Price & Volume chart to understand the relationship between Asset’s trading volume and its price fluctuations.',
    },
    options,
  },
  {
    attachTo: { element: '#onboarding-step-5' },
    content: {
      title: '# Messages',
      description:
        'The Number of Messages chart compares the daily message activity across Telegram and Reddit, highlighting engagement trends.',
    },
    options,
  },
  {
    attachTo: { element: '#onboarding-step-6' },
    content: {
      title: 'Community Size Chart',
      description: 'Observe the Community Size chart to track the growth or decline of the Assets community.',
    },
    options,
  },
  {
    attachTo: { element: '#onboarding-step-7' },
    content: {
      title: 'Power Cons',
      description:
        'Explore the Global Power Consumption Chart to monitor the distribution and trends of energy consumption across different regions and industries worldwide.',
    },
    options,
  },
  {
    attachTo: { element: '#onboarding-step-8' },
    content: {
      title: 'Energy Cons/Tx',
      description:
        'Explore the Energy Consumption per Transaction Chart to gain insights into the environmental impact of individual transactions.',
    },
    options,
  },
]

export const ONBOARDING_STABLE_SUMMARY_PAGE = [
  {
    attachTo: { element: '#onboarding-step-1' },
    content: {
      title: 'Snapshot Stats',
      description: 'Check out these cards for the quick stats: price, mood, messages, bots, and more...',
    },
    options: {
      ...options,
      scrollToStep: {
        enabled: false,
      },
    },
  },
  {
    attachTo: { element: '#onboarding-step-2' },
    content: {
      title: 'Trend Overviews',
      description:
        'Take a peek at these charts for the trends: sentiment, trading and average of liqudity cost, chats, community size.',
    },
    options,
  },
  {
    attachTo: { element: '#onboarding-step-3' },
    content: {
      title: 'Mood & Trust Chart',
      description:
        "The Mood & Trust chart reflects the sentiment dynamics and trust levels over time; a crucial indicator of the cryptocurrency's social sentiment.",
    },
    options,
  },
  {
    attachTo: { element: '#onboarding-step-4' },
    content: {
      title: 'Market Cap & Volume Chart',
      description:
        'Dive into the Market Cap & Volume Chart to assess the market dynamics, understanding the interplay between market capitalization and trading volume to inform investment strategies and market sentiment.',
    },
    options,
  },
  {
    attachTo: { element: '#onboarding-step-5' },
    content: {
      title: 'Avg Liquidity Cost',
      description:
        'Explore the Avg. Liquidity Cost Chart for insights into trade execution costs, aiding in strategic decision-making and risk management.',
    },
    options,
  },
  {
    attachTo: { element: '#onboarding-step-6' },
    content: {
      title: 'Price',
      description:
        'Track asset performance with the Price Chart, offering key insights into price trends for informed decision-making.',
    },
    options,
  },
  {
    attachTo: { element: '#onboarding-step-7' },
    content: {
      title: '# Messages',
      description:
        'The Number of Messages chart compares the daily message activity across Telegram and Reddit, highlighting engagement trends.',
    },
    options: {
      popper: {
        placement: 'top',
        modifiers: [
          {
            name: 'flip',
            enabled: false,
          },
        ],
      },
      scrollToStep: {
        enabled: true,
        options: {
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest',
        },
      },
    },
  },
  {
    attachTo: { element: '#onboarding-step-8' },
    content: {
      title: 'Community Size Chart',
      description: 'Observe the Community Size chart to track the growth or decline of the Assets community.',
    },
    options,
  },
]

export const ONBOARDING_PATHS = {
  CRYPTO_SUMMARY: '/crypto',
  STABLECOIN_SUMMARY: '/stablecoin',
}
