export const CLIMATE_METRIC_SLUGS = {
  energyConsumption: 'energy-consumption',
  carbonEmission: 'carbon-emission',
} as const

export const CLIMATE_METRIC_KEYS = {
  'energy-consumption': 'energyConsumption',
  'carbon-emission': 'carbonEmission',
} as Record<string, 'energyConsumption' | 'carbonEmission'>

export const ENERGY_CONSUMPTION_TYPES = ['powerUse', 'powerUsePerTx', 'powerUsePerTxNode']

export const ENERGY_CONSUMPTION_TYPE_OTIONS = [
  {
    label: 'Power Use',
    value: 'powerUse',
  },
  {
    label: 'Power Use / Tx',
    value: 'powerUsePerTx',
  },
  {
    label: 'Power Use / Tx /Node',
    value: 'powerUsePerTxNode',
  },
]

export const CARBON_EMISSION_TYPE_OPTIONS = [
  {
    label: 'Global',
    value: 'emission',
  },
  {
    label: '/Tx',
    value: 'pertx',
  },
  {
    label: '/Tx/Node',
    value: 'pertxnode',
  },
  {
    label: 'Sources',
    value: 'source',
  },
]

export const CLIMATE_METRIC_OPTIONS = [
  {
    label: 'Energy Cons',
    value: CLIMATE_METRIC_SLUGS.energyConsumption,
    disabled: false,
  },
  {
    label: 'CO₂ Emissions',
    value: CLIMATE_METRIC_SLUGS.carbonEmission,
    disabled: false,
  },
]
