## Getting Started

### Prerequisites

- [Node.js v18+](https://nodejs.org/en/)
- [Pnpm](https://pnpm.io/)
- [Stripe CLI](https://stripe.com/docs/stripe-cli)

### Setup

1. In root directory, run `pnpm install`
2. Go to apps/dsf-int
3. Copy file `.env.example ` into `.env`
4. Run `pnpm dev`
   Open [http://localhost:3001](http://localhost:3001) with your browser to see the result.

### Run Stripe

1. Log in into Stripe Test Dashboard
2. Copy public key and secret key into `.env`
3. Run `pnpm stripe:listen`
4. Copy the webhook secret into `.env`
