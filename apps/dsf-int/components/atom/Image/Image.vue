<script setup lang="ts">
const props = defineProps<{
  src: string;
  alt?: string;
  class?: string;
  height: number | string;
  width: number | string;
}>()

const { data: imageSrcBlob, pending } = await useLazyFetch(props.src)

const imageSrc = computed(() => URL.createObjectURL(imageSrcBlob.value as Blob))

const generateStyles = () => {
  return {
    ...(!['auto', '100%'].includes(props.width.toString()) && { width: `${props.width.toString()}px` }),
    ...(!['auto', '100%'].includes(props.height.toString()) && { height: `${props.height.toString()}px` })
  }
}

</script>

<template>
  <USkeleton
    v-if="pending"
    :style="{
      width: `${props.width.toString()}px`,
      height: `${props.height.toString()}px`
    }"
  />
  <img
    v-else
    :height="props.height"
    :width="props.width"
    :class="{
      [`${props.class}`]: true,
      'w-auto': props.width === 'auto',
      'h-auto': props.height === 'auto',
      'w-full': props.width === '100%',
      'h-full': props.height === '100%',
    }"
    :src="imageSrc"
    :style="generateStyles()"
    :alt="props.alt"
  />
</template>
