<script setup lang="ts">
const props = defineProps<{
  accept?: string;
}>()

const fileList = defineModel<File[]>({
  type: Array,
  default: () => [] as File[],
})

const addFile = (e: Event) => {
  const files = e.target instanceof HTMLInputElement ? [...e.target.files as FileList] : []
  fileList.value = [...fileList.value, ...files]
}

const removeItem = (index: number) => {
  fileList.value.splice(index, 1)
}

const addBorderToWrapper = (e: Event) => {
  const element = e.target as Element
  const wrapper = element.closest('.file-input')
  if (wrapper) {
    wrapper.classList.add('!border-2', '!border-base-primary-800', '!border-dashed')
  }
}

const removeWrapperBorder = (e: Event) => {
  const element = e.target as Element
  const wrapper = element.closest('.file-input')
  if (wrapper) {
    wrapper.classList.remove('!border-2', '!border-base-primary-800', '!border-dashed')
  }
}

const handleDrop = (e: DragEvent) => {
  const files = e.dataTransfer?.files || []
  fileList.value = [...fileList.value, ...files]
  removeWrapperBorder(e)
}

</script>

<template>
  <label
    class="file-input rounded dark:rounded-none dark:bg-neutrals-500 bg-neutrals-200 border border-dashed dark:border-neutrals-900 border-neutrals-300 cursor-pointer w-full flex flex-col items-center justify-center gap-4 p-7"
    @drop.prevent="handleDrop"
    @dragover.prevent="addBorderToWrapper"
    @dragleave.prevent="removeWrapperBorder"
  >
    <img
      src="~/assets/svg/upload.svg"
      class="m-0 invert dark:invert-0"
    >
    <p class="m-0 text-center">Drag & drop files or <span class="text-base-primary-500">Browse</span></p>
    <input
      type="file"
      :accept="props.accept"
      multiple
      class="hidden"
      @change="addFile"
    >
  </label>
  <ul
    v-if="fileList.length > 0"
    class="p-0 m-0"
  >
    <li
      v-for="(file, i) in fileList"
      :key="i"
      class="list-none p-3 m-0 border border-base-primary-800 rounded my-3 flex items-center justify-between"
    >
      {{ file.name }}
      <UIcon
        name="i-material-symbols-close-rounded"
        class="text-accent-red-500 text-[20px] cursor-pointer hover:brightness-50 transition-all duration-300"
        @click="removeItem(i)"
      />
    </li>
  </ul>
</template>
