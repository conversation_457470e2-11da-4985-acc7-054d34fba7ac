<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps<{
  modelValue?: string
  placeholder?: string
  type?: 'text' | 'password'
}>()
const emit = defineEmits(['update:modelValue'])

const value = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  },
})
</script>

<template>
  <input
    v-model="value"
    :type="type"
    class="ring-ring-color bg-neutrals-300-color my-1 rounded px-4 py-2 outline-0 transition-all duration-75 focus:bg-white focus:ring-4"
    :placeholder="props.placeholder"
  />
</template>
