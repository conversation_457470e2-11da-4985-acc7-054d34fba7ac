<template>
  <div class="flex flex-col items-center justify-center py-6 gap-3">
    <div class="flex items-center justify-center rounded-full bg-[#c1c1c12e] p-3">
      <div class="w-[70px] h-[70px] flex items-center justify-center rounded-full bg-[#c1c1c12e]">
        <UIcon
          name="i-heroicons-archive-box"
          class="w-6 h-6 text-[#7C7C7C] dark:text-white"
        />
      </div>
    </div>
    <span class="text-sm text-[#7C7C7C] dark:text-white">Filter not applied.</span>
    <span class="text-sm text-[#7C7C7C] dark:text-white">Choose one to start exploring.</span>
  </div>
</template>
<script setup lang="ts">
</script>
