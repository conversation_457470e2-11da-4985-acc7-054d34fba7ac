<script
  setup
  lang="ts"
>
import { LottieNodiensLoaderBlack, LottieNodiensLoaderWhite } from "assets/lottie";

const { isMobile } = useDeviceScreen();
const { $colorMode } = useNuxtApp();

const darkMode = computed({
  get() {
    return $colorMode.value === 'dark'
  },
  set(value) {
    if (value) {
      $colorMode.preference = 'dark'
    } else {
      $colorMode.preference = 'light'
    }
  }
})
</script>

<template>
  <ClientOnly>
    <div class="h-full grid place-items-center contrast-[.5]">
      <LottieAnimation
        :animation-data="darkMode ? LottieNodiensLoaderWhite : LottieNodiensLoaderBlack"
        :width="isMobile ? 150 : 200"
        :height="isMobile ? 150 : 200"
      />
    </div>
  </ClientOnly>
</template>

<style scoped>

</style>