<script setup lang="ts">
const password = defineModel<string>()

const showPassword = ref(false)
</script>

<template>
  <UInput v-model="password" :type="!showPassword ? 'password' : 'text'" :ui="{ icon: { trailing: { pointer: '' } } }">
    <template #trailing>
      <UButton
        :icon="!showPassword ? 'i-heroicons-eye' : 'i-heroicons-eye-slash'"
        size="sm"
        variant="ghost"
        color="gray"
        type="button"
        @click="showPassword = !showPassword"
      />
    </template>
  </UInput>
</template>
