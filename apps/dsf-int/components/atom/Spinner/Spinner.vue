<template>
  <div class="spinner">
    <div class="spinner-container">
      <div class="spinner-item" />
      <div class="spinner-item" />
      <div class="spinner-item" />
      <div class="spinner-item" />
    </div>
  </div>
</template>

<style scoped>
.spinner {
  display: flex;
  justify-content: center;
  align-items: center;
}

.spinner-container {
  display: flex;
  justify-content: space-between;
  width: 80px;
  height: 16px;
}

.spinner-item {
  @apply bg-brand-600;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
