<script setup lang="ts">
import type { TRPCError } from '@trpc/server'

const showModal = defineModel<boolean>()
const selectedUsers = ref<
  {
    email: string
    name: string
    company: string | null
    role: string
    userId: string
    id: string
  }[]
>([])

const toast = useToast()
const { $client } = useNuxtApp()

const emit = defineEmits(['addSuccess'])

const onSubmit = async () => {
  try {
    await $client.admin.v1.excludedPaywall.assign.mutate(selectedUsers.value.map((user) => user.userId))
    toast.add({
      color: 'green',
      title: 'Request successfully sent!',
      description: 'Successfully saved!',
      icon: 'i-heroicons-check-circle',
    })
    emit('addSuccess')
    showModal.value = false
  } catch (e) {
    const _e = e as TRPCError
    toast.add({
      color: 'red',
      title: 'Request was not sent!',
      description: _e.message ?? 'Something went wrong. Please try again later.',
      icon: 'i-heroicons-exclamation-triangle-20-solid',
    })
  }
}

const searchUsers = async (search: string) => {
  try {
    const { data } = await $client.admin.v1.excludedPaywall.list.query({
      excludedStatus: false,
      searchTerm: search,
    })
    return data || []
  } catch (e) {
    const _e = e as TRPCError
    toast.add({
      color: 'red',
      title: 'Request was not sent!',
      description: _e.message ?? 'Something went wrong. Please try again later.',
      icon: 'i-heroicons-exclamation-triangle-20-solid',
    })
    return [] // Ensure the function always returns an array
  }
}

const removeUserFromList = (id: number | string) => {
  const index = selectedUsers.value.findIndex((user) => user.id === id)
  selectedUsers.value.splice(index, 1)
}

watch(showModal, () => {
  selectedUsers.value = []
})
</script>

<template>
  <UModal v-model="showModal">
    <UCard>
      <div class="flex items-center justify-between">
        <p class="text-2xl font-bold">Add Users</p>
        <UButton
          icon="i-mdi-close"
          variant="ghost"
          color="white"
          size="md"
          @click="showModal = false"
        />
      </div>
      <div class="my-4">
        <USelectMenu
          v-model="selectedUsers"
          :searchable="searchUsers"
          multiple
          option-attribute="name"
          selected-icon=""
          :ui-menu="{
            base: 'scrollable',
          }"
          variant="none"
        >
          <template #option="option">
            <div class="flex items-center gap-2">
              <UCheckbox
                color="sky"
                :model-value="option.selected"
                :label="option.option.name"
              />
            </div>
          </template>
          <template #label>
            <div class="flex flex-wrap gap-2">
              <template v-if="selectedUsers.length > 0">
                <UBadge
                  v-for="(user, i) in selectedUsers"
                  :key="i"
                  color="base-primary"
                  variant="subtle"
                >
                  {{ user.name }}
                  <template #trailing>
                    <UButton
                      icon="i-mdi-close"
                      variant="ghost"
                      color="sky"
                      size="2xs"
                      class="cursor-pointer"
                      @click.stop="removeUserFromList(user.id)"
                    />
                  </template>
                </UBadge>
              </template>
              <span
                v-else
                class="text-neutrals-300"
              >
                Select Users
              </span>
            </div>
          </template>
        </USelectMenu>
      </div>
      <div class="flex items-start justify-end">
        <UButton
          label="Add Users"
          color="midnight"
          variant="solid"
          size="lg"
          @click="onSubmit"
        />
      </div>
    </UCard>
  </UModal>
</template>

<style scoped></style>
