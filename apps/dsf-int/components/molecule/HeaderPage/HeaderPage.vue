<script setup lang="ts">
// Constants
import { PAGE_HEADERS } from '~/constants/cms'

// Types
import type { HeaderPage } from 'app-types/payload-types'

interface HeaderPageProps {
  pageName: string
  center?: boolean
  noPadding?: boolean
  transparent?: boolean
  hideNavigation?: boolean
  subTitleClass?: string
  faqSlug?: string
  comparisonSlug?: string
}

const props = defineProps<HeaderPageProps>()

const { data, status } = await useLazyAsyncData<HeaderPage>(
  `pageHeader_${props.pageName}`,
  async (ctx) => {
    const headerId = PAGE_HEADERS.find((header) => header.name === props.pageName)?.id ?? '0'
    return $fetch<HeaderPage>(`${ctx?.$config.public.payloadApi}/header-pages/${headerId}`)
  },
  {
    server: true,
    immediate: true,
    watch: [() => props.pageName],
  },
)

defineOptions({
  inheritAttrs: false,
})
</script>

<template>
  <ClientOnly>
    <div
      class="flex w-full flex-col gap-y-4 bg-transparent px-5 py-16 lg:gap-y-6"
      :class="{ 'text-center': center, '!p-0': noPadding, '!bg-transparent': transparent }"
      v-bind="$attrs"
    >
      <template v-if="status === 'pending'">
        <div
          class="flex flex-col justify-center gap-y-4"
          :class="[`${center ? 'items-center' : 'items-start'}`]"
        >
          <USkeleton class="h-4 w-1/3 lg:h-5" />
          <USkeleton class="h-4 w-2/3 lg:h-5" />
        </div>
      </template>
      <template v-else>
        <h2 class="text-2xl font-bold text-black lg:text-3xl dark:text-white">
          {{ data?.title }}
        </h2>
        <p
          class="text-base text-black dark:text-white"
          :class="[subTitleClass]"
          v-html="data?.subtitle"
        />
      </template>

      <div
        v-if="!hideNavigation"
        class="flex flex-1 flex-wrap items-center justify-start gap-3"
      >
        <UButton
          color="black"
          leading-icon="i-heroicons-question-mark-circle-20-solid"
          label="FAQ"
          :to="`/faq?category=${faqSlug}`"
        />

        <!-- <NuxtLink
          href="/under-maintenance"
          class="bg-base-primary-50 text-base-primary-500 hover:bg-base-primary-500 flex items-center gap-x-1.5 rounded-md px-4 py-2 text-sm hover:text-white"
        >
          <UIcon name="i-heroicons-book-open-20-solid" class="h-[20px] w-[20px]" />
          Methodology
        </NuxtLink>

        <NuxtLink
          :to="`/comparison?category=${props.comparisonSlug}`"
          class="bg-base-primary-50 text-base-primary-500 hover:bg-base-primary-500 flex items-center gap-x-1.5 rounded-md px-4 py-2 text-sm hover:text-white"
        >
          <IconCompareFiles />
          Comparison
        </NuxtLink> -->
      </div>
    </div>
  </ClientOnly>
</template>
