<script setup lang="ts">
const columns = [
  {
    key: 'number',
    label: '#',
    isVisible: true,
    enabled: true,
    description: '',
  },
  {
    key: 'id',
    label: 'Submission ID',
  },
  {
    key: 'date',
    label: 'Submission Date',
  },
  {
    key: 'type',
    label: 'Submission Type',
  },
]

const data = [
  {
    number: 1,
    id: 'SUP-2024-001',
    date: '1/25/2024, 5:30:00 PM',
    type: 'Business Enquiry',
  },
  {
    number: 2,
    id: 'SUP-2024-002',
    date: '1/26/2024, 10:45:00 PM',
    type: 'Bug Report',
  },
  {
    number: 3,
    id: 'SUP-2024-003',
    date: '1/27/2024, 4:15:00 PM',
    type: 'Feature Request',
  },
]
</script>

<template>
  <UCard>
    <div class="mb-10 flex items-center justify-between">
      <div>
        <p class="mb-1 text-2xl font-bold">Support Log</p>
        <p>View user's support history.</p>
      </div>
    </div>
    <UTable
      :columns="columns"
      :rows="data"
    />
  </UCard>
</template>
