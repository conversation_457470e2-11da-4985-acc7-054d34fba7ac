<script setup lang="ts">
import { LastPriceAnimationMode, LineStyle, PriceScaleMode } from 'lightweight-charts'

// Constants
import { PRICE_SCALE_HELPER_NUMBER } from '~/constants/lightweight-chart'

// Helpers
import { numericDisplay } from '~/helper/number-helper'
import { formatOverviewChartTimeTick } from '~/helper/string-helper'

// Types
import type { Series } from '~/composables/types/light-chart'
import type { AutoscaleInfoProvider, DeepPartial, Time } from 'lightweight-charts'

const { $colorMode } = useNuxtApp()

export interface OverviewChartDataset {
  type: 'line' | 'area' | 'range' | 'histogram' | 'stacked-bar'
  name: string | string[]
  color: string
  data: { time: string; value: null | number | number[] | { min: number; max: number } }[]
  showLastMarker?: boolean
  markerBackgroundColor?: string
  markerShadowColor?: string
  areaTopColor?: string
  areaBottomColor?: string
}

const props = defineProps<{
  logarithmic?: boolean
  hideTimescale?: boolean
  priceFormatter?: (price: number) => string
  datasets: OverviewChartDataset[]
  decimals?: number
  autoScaleInfoProvider?: DeepPartial<AutoscaleInfoProvider | undefined>
}>()

const SERIES_BASE_OPTIONS = {
  priceScaleId: 'right',
  lastValueVisible: false,
  priceLineVisible: false,
}

const chartRef = ref<HTMLElement>()

const darkMode = computed({
  get() {
    return $colorMode.value === 'dark'
  },
  set(value) {
    if (value) {
      $colorMode.preference = 'dark'
    } else {
      $colorMode.preference = 'light'
    }
  },
})

const { isMobile, isDesktop } = useDeviceScreen()

const {
  chart: chartInstance,
  tools,
  addLineSeries,
  addAreaSeries,
  addAreaRangeSeries,
  addHistogramSeries,
  addStackedBarsSeries,
  setPriceScaleVisibility,
  renderChart,
  destroyChart,
} = useLightChart()

/**
 * Set circle marker for specific time point
 */
const setMarkers = (series: Series, color: string, shadowColor: string, time: Time) => {
  series?.setMarkers([
    { time, position: 'inBar', shape: 'circle', color, size: 1.5 },
    { time, position: 'inBar', shape: 'circle', color: shadowColor, size: 2.5 },
  ])
}

const initializeChart = () => {
  if (chartInstance.value === undefined) {
    renderChart(
      chartRef,
      {
        grid: {
          horzLines: {
            color: '#C3C3C3',
            style: LineStyle.Dashed,
          },
          vertLines: {
            visible: false,
          },
        },
        crosshair: {
          horzLine: {
            visible: false,
            labelVisible: false,
          },
          vertLine: {
            labelVisible: false,
          },
        },
        leftPriceScale: {
          visible: false,
          borderVisible: false,
          entireTextOnly: true,
          scaleMargins: {
            bottom: 0.01,
          },
        },
        rightPriceScale: {
          visible: isMobile.value ? false : true,
          mode: props?.logarithmic ? PriceScaleMode.Logarithmic : PriceScaleMode.Normal,
          borderVisible: false,
          entireTextOnly: true,
          scaleMargins: {
            bottom: 0.1,
          },
          minimumWidth: 70,
        },
        timeScale: {
          visible: !props?.hideTimescale,
          secondsVisible: false,
          borderVisible: false,
          borderColor: 'rgba(197, 203, 206, 1)',
          lockVisibleTimeRangeOnResize: true,
          tickMarkMaxCharacterLength: 4,
          tickMarkFormatter: formatOverviewChartTimeTick,
        },
        handleScale: {
          pinch: false,
          mouseWheel: false,
          axisPressedMouseMove: false,
        },
        handleScroll: {
          mouseWheel: false,
          vertTouchDrag: false,
          horzTouchDrag: false,
          pressedMouseMove: false,
        },
        localization: {
          priceFormatter: (price: number) =>
            props?.priceFormatter?.(price / PRICE_SCALE_HELPER_NUMBER) ??
            numericDisplay(price / PRICE_SCALE_HELPER_NUMBER, props?.decimals),
        },
      },
      darkMode.value,
    )
  }

  if (props.datasets.length > 0) {
    props.datasets.forEach((dataset) => {
      let series: Series
      const seriesKeyName =
        dataset.type === 'stacked-bar'
          ? 'bar-series'
          : typeof dataset.name === 'string'
            ? dataset.name.toLowerCase().split(' ').join('-')
            : 'series'

      const data = dataset.data.map(({ time, value }) => {
        const isRange = dataset.type === 'range'
        const isStacked = dataset.type === 'stacked-bar'
        const low = value && typeof value === 'object' && 'min' in value ? value.min * PRICE_SCALE_HELPER_NUMBER : null
        const high = value && typeof value === 'object' && 'max' in value ? value.max * PRICE_SCALE_HELPER_NUMBER : null
        const lineValue = value !== null && typeof value === 'number' ? value * PRICE_SCALE_HELPER_NUMBER : null
        const values = Array.isArray(value) ? value.map((v) => v * PRICE_SCALE_HELPER_NUMBER) : null
        return {
          time,
          customValues: { name: dataset.name },
          ...(isRange && low !== null && high !== null
            ? { low, high }
            : isStacked && values !== null
              ? { values }
              : { value: lineValue ?? undefined }),
        }
      })

      if (!data.length) {
        return
      }

      switch (dataset.type) {
        case 'stacked-bar':
          series = addStackedBarsSeries(seriesKeyName, {
            ...SERIES_BASE_OPTIONS,
          })
          break
        case 'histogram':
          series = addHistogramSeries(seriesKeyName, {
            ...SERIES_BASE_OPTIONS,
            color: dataset.color,
            autoscaleInfoProvider: props.autoScaleInfoProvider,
          })
          break
        case 'line':
          series = addLineSeries(seriesKeyName, {
            ...SERIES_BASE_OPTIONS,
            color: dataset.color,
            lineWidth: 2,
            crosshairMarkerVisible: false,
            lastPriceAnimation: LastPriceAnimationMode.Continuous,
          })

          if (dataset?.showLastMarker) {
            setMarkers(
              series,
              dataset.markerBackgroundColor ?? dataset.color,
              dataset.markerShadowColor ?? dataset.color,
              dataset.data[dataset.data.length - 1]?.time ?? '',
            )
          }
          break
        case 'area':
          series = addAreaSeries(seriesKeyName, {
            ...SERIES_BASE_OPTIONS,
            lineWidth: 2,
            crosshairMarkerVisible: false,
            lineColor: dataset.color,
            topColor: dataset.areaTopColor ?? 'rgba(195, 195, 195, 0.15)',
            bottomColor: dataset.areaBottomColor ?? 'rgba(195, 195, 195, 0.15)',
            lastPriceAnimation: LastPriceAnimationMode.Continuous,
          })

          if (dataset?.showLastMarker) {
            setMarkers(
              series,
              dataset.markerBackgroundColor ?? dataset.color,
              dataset.markerShadowColor ?? dataset.color,
              dataset.data[dataset.data.length - 1]?.time ?? '',
            )
          }
          break
        case 'range':
          series = addAreaRangeSeries(seriesKeyName, {
            ...SERIES_BASE_OPTIONS,
            closeLineWidth: 1,
            highLineWidth: 1,
            lowLineWidth: 1,
            areaTopColor: dataset.color,
            areaBottomColor: dataset.color,
            color: dataset.color,
            closeLineColor: 'rgba(255, 255, 255, 0)',
            highLineColor: 'rgba(79, 189, 109, 0)',
            lowLineColor: 'rgba(79, 189, 109, 0)',
          })
          break
      }

      series?.setData(data)
    })

    tools.prepareTooltip({
      darkMode: darkMode.value,
      valueFormatter: (price: number) =>
        props?.priceFormatter?.(price / PRICE_SCALE_HELPER_NUMBER) ??
        numericDisplay(price / PRICE_SCALE_HELPER_NUMBER, props.decimals),
    })
    tools.reArrangeChart({ autosize: true })

    // Add right offset on last item
    if (props.datasets.some((dataset) => dataset.showLastMarker)) {
      chartInstance.value?.timeScale().applyOptions({
        rightOffset: 8,
      })
    }
  }
}
const handleResize = () => tools.fitContent()

watch(darkMode, (value) => {
  tools.setDark(value)
})

watch(isMobile, (value) => {
  if (value) {
    setPriceScaleVisibility('right', false)
  } else {
    setPriceScaleVisibility('right', true)
  }
})

watch(
  () => props.logarithmic,
  (value) => {
    if (typeof value !== undefined) {
      chartInstance.value?.priceScale('right').applyOptions({
        mode: value ? PriceScaleMode.Logarithmic : PriceScaleMode.Normal,
        autoScale: true,
      })
    }
  },
)

onMounted(() => {
  window.addEventListener('resize', handleResize)

  if (import.meta.client) {
    setTimeout(() => {
      initializeChart()
    }, 50)
  }

  watch(
    isDesktop,
    (value) => {
      if (value) {
        chartInstance.value?.timeScale().applyOptions({
          tickMarkMaxCharacterLength: 4,
        })
      } else {
        chartInstance.value?.timeScale().applyOptions({
          tickMarkMaxCharacterLength: 8,
        })
      }
    },
    { immediate: true },
  )
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  destroyChart()
})
</script>

<template>
  <ClientOnly>
    <NoDataAvailable
      v-if="props.datasets.length === 0"
      class="h-full w-full"
    />
    <div
      v-else
      ref="chartRef"
      class="h-full w-full"
    />
  </ClientOnly>
</template>
