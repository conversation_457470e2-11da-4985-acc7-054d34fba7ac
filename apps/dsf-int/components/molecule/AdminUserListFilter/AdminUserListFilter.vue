<script
  setup
  lang="ts"
>
import { companyList, emailList } from "~/constants/example/admin-dashboard";

const visible = ref(false)
const selectedCompanies = ref<(string | number)[]>([])
const selectedEmails = ref<(string | number)[]>([])

const applyFilters = () => {
  visible.value = false
}

</script>

<template>
  <UButton
    label="Filters"
    icon="i-material-symbols-filter-list"
    color="black"
    size="md"
    variant="outline"
    truncate
    class="min-h-[42px] border"
    @click="visible = true"
  />
  <UModal v-model="visible">
    <UCard>
      <div class="mb-5">
        <p class="text-2xl font-bold">Filter Users</p>
        <p class="text-base font-normal">Set the filters to narrow down the user list. Click apply when you're done.</p>
      </div>

      <div class="grid grid-cols-2 mb-4">
        <div class="flex flex-col gap-2">
          <p class="text-base font-semibold">Filter Users</p>
          <UCheckbox
            color="blue"
            label="User"
          />
          <UCheckbox
            color="blue"
            label="Admin"
          />
        </div>

        <AdminUserListFilterCheckboxSearch
          v-model="selectedCompanies"
          :items="companyList"
          header="Company"
          placeholder="Company"
        />
      </div>

      <div class="grid grid-cols-2 mb-4">
        <div class="flex flex-col gap-2">
          <p class="text-base font-semibold">Auth Provider</p>
          <UCheckbox
            color="blue"
            label="Email"
          />
          <UCheckbox
            color="blue"
            label="Hashpack"
          />
        </div>

        <AdminUserListFilterCheckboxSearch
          v-model="selectedEmails"
          :items="emailList"
          header="Email Domain"
          placeholder="Email"
        />
      </div>

      <div class="grid grid-cols-1 mb-4">
        <div class="flex flex-col gap-2">
          <p class="text-base font-semibold">Registration Date</p>
          <AdminUserListFilterDateRange />
        </div>
      </div>

      <div class="grid grid-cols-2 mb-4">
        <div class="flex flex-col gap-2">
          <p class="text-base font-semibold">Status</p>
          <UCheckbox
            color="blue"
            label="Active"
          />
          <UCheckbox
            color="blue"
            label="Suspended"
          />
        </div>

        <div class="flex flex-col gap-2">
          <p class="text-base font-semibold">Plan</p>
          <div class="grid grid-cols-2">
            <UCheckbox
              color="blue"
              label="Basic"
            />
            <UCheckbox
              color="blue"
              label="Enterprise"
            />
          </div>
          <div class="grid grid-cols-1">
            <UCheckbox
              color="blue"
              label="Pro"
            />
          </div>
        </div>
      </div>
      <div class="flex justify-end">
        <UButton
          label="Apply Filters"
          color="midnight"
          size="md"
          variant="solid"
          class="min-h-[42px]"
          @click="applyFilters"
        />
      </div>
    </UCard>
  </UModal>
</template>

<style scoped>

</style>