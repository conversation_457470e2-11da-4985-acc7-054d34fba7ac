<template>
  <div class="flex flex-col gap-2">
    <p class="text-base font-semibold">
      {{ header }}
    </p>
    <UInput
      v-model="searchText"
      variant="outline"
      trailing-icon="i-heroicons-magnifying-glass"
      class="w-full md:w-auto"
      :placeholder="`Search ${placeholder}`"
      :ui="{
        base: '!bg-transparent',
      }"
    />
    <div class="scrollable max-h-32">
      <UCheckbox
        v-model="allSelected"
        color="blue"
        label="Select All"
      />
      <UCheckbox
        v-for="(item, i) in items"
        :key="i"
        v-model="selected"
        :value="item.value"
        color="blue"
        :label="item.label"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
const props = defineProps<{
  items: {
    label: string
    value: string | number
  }[]
  header: string
  placeholder: string
}>()

const searchText = ref('')

const selected = defineModel({
  type: Array,
  default: () => [],
})

const allSelected = computed({
  get: () => selected.value.length === props.items.length,
  set: (value) => {
    if (value) {
      selected.value = props.items.map((x) => x.value)
      return
    }
    selected.value = []
  },
})
</script>
