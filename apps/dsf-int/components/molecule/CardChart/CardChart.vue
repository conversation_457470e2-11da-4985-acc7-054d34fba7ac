<script setup lang="ts">
import LottieNodiensLoader from '~/components/atom/LottieNodiensLoader.vue'

interface CardChartProps {
  title: string
  subtitle?: string
  legends: {
    label: string
    colorClass: string
  }[]
  enableLogButton?: boolean
  isLoading?: boolean
  bodyClass?: string
}
defineProps<CardChartProps>()

const log = ref(true)
const onClickLog = () => (log.value = !log.value)
</script>

<template>
  <UCard>
    <div class="mb-4 flex flex-col items-start justify-between gap-2 lg:flex-row">
      <div class="flex flex-1 items-center gap-x-1">
        <h2 class="text-neutrals-400 text-lg font-medium dark:text-white">
          {{ title }}
        </h2>
        <InformationPopover
          v-if="!!subtitle"
          class="mt-1.5"
        >
          <TooltipContent>
            <p v-html="subtitle" />
          </TooltipContent>
        </InformationPopover>
      </div>

      <div class="flex items-center gap-x-4">
        <div
          v-for="(legend, legendIndex) in legends"
          :key="legendIndex"
          class="flex items-center gap-x-2"
        >
          <div
            class="h-[16px] min-h-[16px] w-[16px] min-w-[16px] rounded-full"
            :class="[legend.colorClass]"
          />
          <span class="whitespace-nowrap text-sm text-black dark:text-white">{{ legend.label }}</span>
        </div>
        <div
          v-if="enableLogButton"
          class="flex items-center gap-3"
        >
          <UButton
            size="md"
            variant="outline"
            :color="log ? 'brand' : 'soft-gray'"
            class="!font-normal"
            @click="onClickLog"
          >
            LOG
          </UButton>
        </div>
        <UButton
          v-else
          size="md"
          variant="outline"
          :color="log ? 'brand' : 'soft-gray'"
          class="hidden !font-normal"
        >
          Button
        </UButton>
      </div>
    </div>
    <div :class="[bodyClass]">
      <div
        v-if="isLoading"
        class="grid h-full w-full place-items-center"
      >
        <ClientOnly>
          <LottieNodiensLoader />
        </ClientOnly>
      </div>
      <slot
        v-else
        :logarithmic="log"
      />
    </div>
  </UCard>
</template>
