<script setup lang="ts">
import type { ShareButtonVariant } from './types'

const props = defineProps<{
  link: string
  variant: ShareButtonVariant
}>()

const onShare = () => {
  const url = encodeURIComponent(props.link)
  if (props.variant === 'twitter') {
    window.open(`https://twitter.com/intent/tweet?url=${url}`, '_blank')
  } else if (props.variant === 'facebook') {
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`, '_blank')
  } else if (props.variant === 'linkedin') {
    window.open(`https://www.linkedin.com/shareArticle?mini=true&url=${url}`, '_blank')
  } else if (props.variant === 'telegram') {
    window.open(`https://t.me/share/url?url=${url}`, '_blank')
  } else if (props.variant === 'reddit') {
    window.open(`https://www.reddit.com/submit?url=${url}`, '_blank')
  }
}
</script>
<template>
  <button @click="onShare">
    <div class="flex cursor-pointer items-center justify-center rounded-lg border-2 p-2">
      <div v-if="props.variant === 'twitter'">
        <img class="hidden dark:block" src="@/assets/svg/twitter-x.svg" width="24" />
        <img class="dark:hidden" src="@/assets/svg/twitter-x-light.svg" width="24" />
      </div>
      <div v-else-if="props.variant === 'facebook'">
        <img class="hidden dark:block" src="@/assets/svg/facebook-circle-fill.svg" width="24" />
        <img class="dark:hidden" src="@/assets/svg/facebook-circle-fill-light.svg" width="24" />
      </div>
      <div v-else-if="props.variant === 'linkedin'">
        <img class="hidden dark:block" src="@/assets/svg/linkedin-box-fill.svg" width="24" />
        <img class="dark:hidden" src="@/assets/svg/linkedin-box-fill-light.svg" width="24" />
      </div>
      <div v-else-if="props.variant === 'telegram'">
        <img class="hidden dark:block" src="@/assets/svg/telegram-fill.svg" width="24" />
        <img class="dark:hidden" src="@/assets/svg/telegram-fill-light.svg" width="24" />
      </div>
      <div v-else-if="props.variant === 'reddit'">
        <img class="hidden dark:block" src="@/assets/svg/reddit-fill.svg" width="24" />
        <img class="dark:hidden" src="@/assets/svg/reddit-fill-light.svg" width="24" />
      </div>
    </div>
  </button>
</template>
