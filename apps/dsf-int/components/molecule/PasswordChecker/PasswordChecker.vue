<script setup lang="ts">
import { z } from 'zod'
import { checkPasswordRules } from '~/helper/string-helper'

const props = withDefaults(
  defineProps<{
    password: string
  }>(),
  {
    password: '',
  },
)

const validation = computed(() => {
  try {
    checkPasswordRules.parse(props.password)
    return {}
  } catch (e) {
    if (e instanceof z.ZodError) {
      return e.errors.reduce((res: Record<string, string>, error, i) => {
        res[error.path.join('-') ?? `e-${i}`] = error.message
        return res
      }, {})
    }
    return {}
  }
})
</script>

<template>
  <div
    class="bg-neutrals-100/50 border-neutrals-300 dark:bg-neutrals-600 dark:border-neutrals-700 my-2.5 w-full rounded-none border-b px-3.5 py-2.5 text-sm"
  >
    <div class="font-semibold">Password must contain</div>
    <ul class="ml-4 list-disc">
      <li
        :class="[
          props.password.length === 0 ? '' : validation.minCharachter === undefined ? 'text-green-500' : 'text-red-500',
        ]"
      >
        At least 6 characters in length
      </li>
      <li
        :class="[
          props.password.length === 0 ? '' : validation.haveNumber === undefined ? 'text-green-500' : 'text-red-500',
        ]"
      >
        At least one number (0-9)
      </li>
      <li
        :class="[
          props.password.length === 0 ? '' : validation.haveLowerCase === undefined ? 'text-green-500' : 'text-red-500',
        ]"
      >
        At least one lower case letter (a-z)
      </li>
      <li
        :class="[
          props.password.length === 0 ? '' : validation.haveUpperCase === undefined ? 'text-green-500' : 'text-red-500',
        ]"
      >
        At least one upper case letter (A-Z)
      </li>
      <li
        :class="[
          props.password.length === 0
            ? ''
            : validation.haveNonAlphaNumeric === undefined
              ? 'text-green-500'
              : 'text-red-500',
        ]"
      >
        At least one non-alphanumeric character (e.g. !@#$%^&*)
      </li>
      <li
        :class="[
          props.password.length === 0
            ? ''
            : validation.noSequentialNumber === undefined
              ? 'text-green-500'
              : 'text-red-500',
        ]"
      >
        No sequential numbers (repeating, ascending, or descending)
      </li>
    </ul>
  </div>
</template>
