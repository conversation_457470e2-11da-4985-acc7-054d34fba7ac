<script
  setup
  lang="ts"
>
const route = useRoute();
const router = useRouter();

const props = defineProps<{
  column: string;
  buttonClass?: string;
  isDefault?: boolean;
}>();

const isSorted = computed(() => {
  const query = Object.values(route.query)
  return query.includes(props.column)
})

const isAsc = computed(() => {
  const queryKeys = Object.keys(route.query)
  return queryKeys.includes('asc')
})

const sortByColumn = async () => {
  const query = { ...route.query }
  if (query.sort !== props.column) {
    delete query.asc
    delete query.desc
  }
  if (isSorted.value) {
    if (query.asc) {
      delete query.asc
      query.desc = '1'
    } else {
      delete query.desc
      query.asc = '1'
    }
  } else {
    query.sort = props.column
    query.desc = '1'
  }
  await navigateTo({
    query
  })
}

onMounted(() => {
  if (props.isDefault && !route.query.sort && (!route.query.asc || !route.query.desc)) {
    router.push({
      query: {
        ...route.query,
        desc: '1',
        sort: props.column,
      },
    })
  }
})

</script>

<template>
  <UButton
    color="white"
    class="text-xs"
    :class="[props.buttonClass]"
    variant="ghost"
    @click="sortByColumn"
  >
    <slot />
    <div
      :class="{
        'dark:bg-white bg-[#ebebeb]': isSorted
      }"
      class="flex items-center"
    >
      <UIcon
        :name="
          isSorted ?
            isAsc
              ? 'i-material-symbols-keyboard-arrow-up'
              : 'i-material-symbols-keyboard-arrow-down'
            : 'i-material-symbols-keyboard-arrow-down'
        "
        :class="{
          'dark:!bg-[#000000]': isSorted
        }"
        class="text-[18px] dark:bg-[#ffffff] bg-[#000000]"
      />
    </div>
  </UButton>
</template>
