<script lang="ts" setup>
import { PAGE_IDS } from '~/constants/cms'
import type { Page } from 'app-types/payload-types'

const props = defineProps<{
  class?: string
}>()

const emit = defineEmits(['reachedBottom'])

const { data } = await useLazyAsyncData<Page>(
  'privacyPolicy',
  async (ctx) => {
    return $fetch<Page>(
      `${ctx?.$config.public.payloadApi}/pages/${PAGE_IDS.find((page) => page.name === 'privacy')?.id ?? '0'}`,
    )
  },
  {
    server: false,
    immediate: true,
  },
)

const handleScroll = (el: HTMLElement) => {
  const reachedBottom = el.scrollHeight - el.scrollTop === el.clientHeight
  if (reachedBottom) {
    emit('reachedBottom', true)
  }
}

onMounted(() => {
  const el = document.getElementById('privacy-policy')
  if (el) {
    el.addEventListener('scroll', () => handleScroll(el))
  }
})
</script>

<template>
  <div
    id="privacy-policy"
    :class="props.class"
    v-html="data?.content"
  />
</template>
