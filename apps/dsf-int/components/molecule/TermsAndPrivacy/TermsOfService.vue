<script lang="ts" setup>
import { PAGE_IDS } from '~/constants/cms'
import type { Page } from 'app-types/payload-types'
import useCmsTextHandler from '~/composables/useCmsTextHandler'

const props = defineProps<{
  class?: string
}>()

const emit = defineEmits(['reachedBottom'])

const { counterFontWeightHandler } = useCmsTextHandler()

const { data } = await useLazyAsyncData<Page>(
  'termsOfService',
  async (ctx) => {
    return $fetch<Page>(
      `${ctx?.$config.public.payloadApi}/pages/${PAGE_IDS.find((page) => page.name === 'terms')?.id ?? '0'}`,
    )
  },
  {
    server: false,
    immediate: true,
  },
)

const handleScroll = (el: HTMLElement) => {
  const reachedBottom = el.scrollHeight - el.scrollTop === el.clientHeight
  if (reachedBottom) {
    emit('reachedBottom', true)
  }
}

onMounted(() => {
  const el = document.getElementById('terms-of-service')
  if (el) {
    el.addEventListener('scroll', () => handleScroll(el))
  }
})

watch(data, () => {
  nextTick(() => {
    counterFontWeightHandler('#terms-of-service')
  })
})
</script>

<template>
  <slot name="header" />
  <Suspense>
    <div
      id="terms-of-service"
      :class="[props.class, 'cms-content']"
      v-html="data?.content"
    />
  </Suspense>
</template>
