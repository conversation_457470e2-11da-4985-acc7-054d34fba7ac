<script lang="ts" setup>
const showModal = ref(false)

const agree = defineModel({
  type: Boolean,
  default: () => false,
})

const onClick = (e: Event) => {
  e.preventDefault()
  showModal.value = true
}
</script>

<template>
  <TermsAndPrivacyModal
    v-model="showModal"
    @agreed="agree = true"
    @rejected="agree = false"
  />
  <UCheckbox
    v-model="agree"
    name="agreement"
    color="sky"
    class="select-none"
    @click="onClick"
  >
    <template #label>
      I have read and accept the
      <NuxtLink
        class="text-brand-400 no-underline"
        to="/legal/terms"
        target="_blank"
      >
        Terms of Service</NuxtLink
      >
      and
      <NuxtLink
        class="text-brand-400 no-underline"
        to="/legal/privacy"
        target="_blank"
      >
        Privacy Policy</NuxtLink
      >
    </template>
  </UCheckbox>
</template>
