<script setup lang="ts">

const isOpen = defineModel({
  type: Boolean,
  default: () => false,
})

const emit = defineEmits(['agreed', 'rejected'])
const disableButton = ref(true)

const handleAgree = () => {
  emit('agreed')
  isOpen.value = false
}

const handleReject = () => {
  disableButton.value = true
  isOpen.value = false
  emit('rejected')
}

</script>

<template>
  <UModal
    v-model="isOpen"
    fullscreen
    prevent-close
    :ui="{
      fullscreen: 'max-w-[calc(100vw-7%)] h-[calc(100vh-100px)]',
      container: 'items-center'
    }"
  >
    <UCard
      :ui="{
        ring: '',
        divide: 'divide-y divide-gray-100 dark:divide-gray-800 overflow-hidden min-h-full',
        body: {
          base: 'flex flex-col gap-5 min-h-full max-h-full'
        }
      }"
    >
      <TermsOfService
        class="scrollable grow shrink min-h-full basis-auto"
        @reached-bottom="disableButton = false"
      >
        <template #header>
          <HeaderPage
            page-name="terms"
            hide-navigation
            no-padding
            transparent
          />
        </template>
      </TermsOfService>
      <div class="flex justify-between items-center mt-5 flex-wrap basis-auto grow-0 shrink-0">
        <p class="mb-10 sm:m-0">
          Scroll to the end to accept
        </p>
        <div class="flex gap-2 flex-wrap">
          <UButton
            size="lg"
            color="black"
            label="I accept the Terms of Service"
            :disabled="disableButton"
            @click="handleAgree"
            class="w-full sm:w-auto items-center justify-center"
          />
          <UButton
            size="lg"
            color="black"
            class="w-full sm:w-auto items-center justify-center"
            label="Reject and Exit"
            @click="handleReject"
          />
        </div>
      </div>
    </UCard>
  </UModal>
</template>
