<script setup lang="ts">
const props = defineProps<{
  coin: {
    [key: string]: any
  }
  noPadding?: boolean
  textClass?: string
  truncate?: boolean
  growName?: boolean
  disableHover?: boolean
}>()
</script>

<template>
  <div
    class="flex w-full cursor-pointer items-center gap-2 px-3 py-2"
    :class="{
      '!p-0': props.noPadding,
      '!cursor-default': props.disableHover,
    }"
    @click="$emit('click')"
  >
    <UAvatar v-if="props?.coin?.logo" :src="props.coin?.logo" size="xs" :alt="`logo-${props.coin?.name}`" />
    <div
      class="min-w-[0px] whitespace-normal break-words text-left"
      :class="[props.textClass, { 'w-[60%] !truncate sm:w-auto': truncate }, { grow: growName }]"
    >
      {{ props.coin?.name ?? '-' }}
    </div>
    <div
      v-if="props?.coin?.symbol"
      class="hidden break-words text-sm md:block"
      :class="props.textClass"
    >
      ({{ props.coin?.symbol }})
    </div>
  </div>
</template>
