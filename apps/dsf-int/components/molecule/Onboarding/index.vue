<script setup lang="ts">
import { VOnboardingWrapper, VOnboardingStep, useVOnboarding } from 'v-onboarding'

interface OnboardingProps {
  steps: {
    attachTo: { element: string }
    content: {
      title: string
      description: string
    }
  }[]
  visible: boolean
}

const props = defineProps<OnboardingProps>()

const emit = defineEmits<{
  (e: 'onFinish'): void
}>()

const wrapper = ref(null)
const { start, finish } = useVOnboarding(wrapper)

const onboardingFinish = () => {
  finish()
  emit('onFinish')
}

watch(
  () => props.visible,
  (value) => value && start(),
)

onMounted(() => {
  props.visible && start()
})

onUnmounted(() => {
  document.body.style.pointerEvents = ''
})
</script>

<template>
  <VOnboardingWrapper
    ref="wrapper"
    :steps="props.steps"
  >
    <template #default="{ previous, next, step, isFirst, isLast }">
      <VOnboardingStep>
        <div class="ring-neutrals-500 m-2 -mb-10 max-w-96 bg-white shadow ring-2 sm:rounded-2xl dark:bg-black">
          <div class="px-4 py-5 sm:px-6 sm:py-4">
            <div class="flex items-center justify-between gap-1">
              <h3 class="font-semibold">{{ step.content.title }}</h3>
              <UButton
                trailing-icon="i-heroicons-x-mark"
                size="sm"
                color="gray"
                variant="ghost"
                @click="finish"
              />
            </div>

            <div class="mt-2">
              <p>{{ step.content.description }}</p>
            </div>

            <div class="mt-5 flex items-center gap-3">
              <UButton
                v-if="!isFirst"
                :ui="{ rounded: 'rounded-lg' }"
                label="Previous"
                color="white"
                variant="outline"
                size="md"
                class="flex-1 justify-center"
                @click="previous"
              />
              <UButton
                :ui="{ rounded: 'rounded-lg' }"
                :label="isLast ? 'Finish' : 'Next'"
                color="black"
                size="md"
                class="flex-1 justify-center"
                @click="
                  () => {
                    if (isLast) {
                      onboardingFinish()
                      return
                    }
                    next()
                  }
                "
              />
            </div>
          </div>
        </div>
      </VOnboardingStep>
    </template>
  </VOnboardingWrapper>
</template>
