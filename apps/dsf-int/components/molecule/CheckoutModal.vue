<script setup lang="ts">
import { IconCreditCard } from 'assets/svg'
import type { v2AssetWithId } from '~/server/trpc/trpc'
import { numericDisplay } from '@dsf/int/helper/number-helper'

const showModalSubscribe = defineModel<boolean>()
const annual = defineModel<boolean>('annual', {
  required: true
});
const modalFormState = defineModel<{
  period: string
  payment_method: string
  promo_code: string
}>('modalFormState', {
  required: true,
})

const props = defineProps<{
  selectedPlan: any;
  selectedCoins: v2AssetWithId[];
  isSubscribing: boolean;
  isUpdatingAssets: boolean;
}>()

const emit = defineEmits(['onCheckout'])

const hasAddons = computed(() => props.selectedCoins.length > Number(props.selectedPlan?.features.max_assets!))

const totalAddons = computed(() => {
  return props.selectedCoins.length > props.selectedPlan?.features.max_assets
    ? props.selectedCoins.length - Number(props.selectedPlan?.features.max_assets!)
    : 0
})

const subTotalAddons = computed(() => {
  return (totalAddons.value * (props.selectedPlan?.features.add_on_price ?? 0)).toFixed(1)
})

const grandTotal = computed(() => {
  if (annual.value) {
    return numericDisplay(Number(subTotalAddons.value) + (props.selectedPlan?.annual_price * 12)!, 2)
  }

  return (Number(subTotalAddons.value) + props.selectedPlan?.monthly_price!).toFixed(1)
})

const onClickAnnual = () => {
  modalFormState.value!.period = 'annual'
  annual.value = true
}

const onClickMonthly = () => {
  modalFormState.value!.period = 'monthly'
  annual.value = false
}

</script>

<template>
  <UModal v-model="showModalSubscribe">
    <UCard>
      <h2 class="mb-6 text-2xl font-medium text-black dark:text-white">Nodiens {{ selectedPlan?.name }} Plan</h2>
      <template v-if="!isUpdatingAssets">
        <h3 class="text-neutrals-800 mb-4 text-xl font-medium dark:text-white">Billing Period</h3>
        <div class="grid grid-cols-1 gap-3 md:grid-cols-2">
          <SelectionCard
            :class="{ active: modalFormState!.period === 'monthly' }"
            @click="onClickMonthly"
          >
            <div class="mb-2 flex items-center justify-between gap-2">
              <h4 class="text-lg font-medium">Monthly</h4>
            </div>
            <span class="text-base">
              <span class="text-xl font-semibold">${{ selectedPlan?.monthly_price }}</span>
              / Month
            </span>
          </SelectionCard>
          <SelectionCard
            :class="{ active: modalFormState!.period === 'annual' }"
            @click="onClickAnnual"
          >
            <div class="mb-2 flex items-center justify-between gap-2">
              <h4 class="text-lg font-medium">Annually</h4>
              <span class="bg-accent-green-600 rounded-md px-2.5 py-1 text-base text-white"> Save 20%</span>
            </div>
            <span class="text-base">
              <span class="text-xl font-semibold">${{ selectedPlan?.annual_price }}</span>
              / Year
            </span>
            <p class="text-neutrals-800 mt-1 text-base dark:text-white">
              Billed ${{ numericDisplay(selectedPlan?.annual_price * 12, 2) }} annually
            </p>
          </SelectionCard>
        </div>
      </template>
      <div class="mb-4 mt-6 flex flex-col gap-2">
        <h3 class="text-neutrals-800 text-xl font-medium dark:text-white">Your Selected Cryptocurrencies</h3>
        <span class="text-neutrals-800 dark:text-neutrals-50 text-sm"
          >You'll have access to detailed data and analytics for:
        </span>
        <div class="flex max-h-96 flex-wrap items-center gap-3 overflow-y-auto">
          <UBadge
            v-for="asset in selectedCoins"
            :key="asset.symbol"
            color="base-primary"
            variant="asset"
          >
            {{ asset.name }} ({{ asset.symbol }})
          </UBadge>
        </div>
      </div>

      <h3 class="text-neutrals-800 mb-4 mt-6 text-xl font-medium dark:text-white">Payment Method</h3>
      <div class="grid grid-cols-1 gap-3 md:grid-cols-2">
        <div
          class="modal-option-item items-center justify-center"
          :class="{ active: modalFormState!.payment_method === 'stripe' }"
          @click="modalFormState!.payment_method = 'stripe'"
        >
          <IconCreditCard class="h-[30px] w-auto" />
        </div>
      </div>
      <div class="modal-option-item mb-5 mt-6">
        <h4 class="mb-2 text-lg font-semibold">Order Summary</h4>
        <div class="mb-1 flex items-center justify-between">
          <span>Nodiens {{ selectedPlan?.name }} ({{ annual ? 'Annually' : 'Monthly' }})</span>
          <span class="font-semibold"
            >${{ annual ? numericDisplay(selectedPlan?.annual_price * 12, 2) : selectedPlan?.monthly_price }}</span
          >
        </div>
        <div v-if="hasAddons" class="mb-1 flex items-center justify-between">
          <span>Additional Assets ({{ totalAddons }}x)</span>
          <span class="font-semibold">${{ subTotalAddons }}</span>
        </div>
        <UDivider
          class="my-4"
          :ui="{
            border: {
              base: 'flex border-neutrals-800 dark:border-white',
            },
          }"
        />
        <div class="mb-1 flex items-center justify-between">
          <span>Total</span>
          <span class="font-semibold">${{ grandTotal }}</span>
        </div>
      </div>
      <UButton
        class="bg-base-primary-800 dark:bg-base-primary-800 dark:text-white"
        size="lg"
        color="midnight"
        variant="solid"
        label="Upgrade Now"
        block
        :loading="isSubscribing"
        :disabled="isSubscribing"
        @click="emit('onCheckout')"
      />
    </UCard>
  </UModal>
</template>

<style scoped></style>
