<script setup lang="ts">
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js'
import { Pie } from 'vue-chartjs'

ChartJS.register(ArcElement, Tooltip, Legend)

const DEFAULT_OPTIONS = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    title: {
      display: false,
    },
    legend: {
      display: false,
    },
  },
  elements: {
    point: {
      radius: 0,
    },
  },
}

defineProps<{
  pending: boolean;
  title: string
  icon: string
  legends?: {
    label: string
    color: string
  }[]
  chartData?: { labels: string[]; datasets: { backgroundColor: string[]; data: number[] }[] }
  options?: Record<string, string>
}>()
</script>

<template>
  <UCard class="grow basis-[250px]">
    <LottieNodiensLoader v-if="pending" />
    <div
      v-else
      class="flex h-full flex-col gap-3"
    >
      <div class="flex items-center justify-between">
        {{ title }}
        <UIcon :name="icon" class="text-3xl" />
      </div>

      <div class="flex-1">
        <Pie v-if="chartData" :data="chartData" :options="{ ...DEFAULT_OPTIONS, ...options }" class="max-h-[200px]" />
      </div>

      <div v-if="legends?.length" class="flex flex-row items-center justify-center gap-3">
        <div v-for="(legend, legendIndex) in legends" :key="legendIndex" class="flex items-center gap-x-2">
          <div
            class="h-[12px] min-h-[12px] w-[12px] min-w-[12px] rounded-full"
            :style="{ backgroundColor: legend.color }"
          />
          <span class="whitespace-nowrap text-sm text-black dark:text-white">{{ legend.label }}</span>
        </div>
      </div>
    </div>
  </UCard>
</template>
