<script setup lang="ts">
import { capitalizeFirstLetter } from '@dsf/int/helper/string-helper'

// Types
import type { v2AssetWithId } from '~/server/trpc/trpc'
import type { TRPCError } from '@trpc/server'

const emit = defineEmits<{
  (e: 'onSubscribe', payload: { planId: number; selectedCoins: v2AssetWithId[] }): void
}>()

const showModal = defineModel({
  type: Boolean,
  default: () => false,
})

const props = withDefaults(
  defineProps<{
    planId: number
    type?: string
    maxSelectableCoins: number
    planExpiryDate?: string
  }>(),
  {
    type: '',
  },
)

const { $client } = useNuxtApp()
const toast = useToast()

const searchText = ref('')
const selectedCoins = ref([])

const { data, status, refresh } = useLazyAsyncData(
  async () => {
    try {
      return $client.v2.user.account.availableAssets.query({
        terms: searchText.value,
        product_id: props.planId,
      })
    } catch (e) {
      const _e = e as TRPCError
      toast.add({
        color: 'red',
        title: 'Something went wrong!',
        description: _e.message ?? 'Something went wrong. Please try again later.',
        icon: 'i-heroicons-exclamation-triangle-20-solid',
      })
      return null
    }
  },
  {
    watch: [searchText],
    immediate: false,
  },
)

const isFetchingCoins = computed(() => status.value === 'pending')

const hasAddons = computed(() => selectedCoins.value.length > props.maxSelectableCoins)

const totalAddons = computed(() => {
  const total = selectedCoins.value.length - props.maxSelectableCoins
  return total > 0 ? `+${total}` : ''
})

const modalTitle = computed(() => {
  return props.type ? `Nodiens ${capitalizeFirstLetter(props.type)} Plan` : 'Update Your Asset Selection'
})

const description = computed(() => {
  const text = props.type
    ? `You'll have access to detailed data and analytics for your selected cryptocurrencies.`
    : `Your updated selection will take effect on ${props.planExpiryDate}`

  return `Choose up to ${props.maxSelectableCoins} cryptocurrencies from our list. ` + text
})

const onContinue = () => {
  showModal.value = false
  emit('onSubscribe', { planId: props.planId, selectedCoins: selectedCoins.value })
}

watch(showModal, () => {
  selectedCoins.value = []
  searchText.value = ''
  if (showModal.value) {
    refresh()
  }
})
</script>

<template>
  <UModal v-model="showModal">
    <UCard>
      <div>
        <div class="flex items-start justify-between">
          <p class="mb-8 text-2xl font-bold dark:text-white">
            {{ modalTitle }}
          </p>
          <UButton
            icon="i-mdi-close"
            variant="ghost"
            color="white"
            @click="showModal = false"
          />
        </div>
        <p class="mb-4 text-xl font-medium dark:text-white">Select Your Preferred Cryptocurrencies</p>
        <p class="mb-4 text-base dark:text-white">
          {{ description }}
        </p>
        <UInput
          v-model="searchText"
          variant="transparent"
          trailing-icon="i-heroicons-magnifying-glass"
          class="mb-4 w-full md:w-auto"
          placeholder="Search Cryptocurrencies"
          :ui="{ base: '!border-neutrals-200 !border' }"
        />
        <div
          class="border-neutrals-200 scrollable mb-4 max-h-60 overflow-y-auto rounded-[4px] border p-2 dark:border-white"
        >
          <div class="grid gap-2 sm:grid-cols-1 md:grid-cols-2">
            <template v-if="isFetchingCoins">
              <USkeleton class="h-7 w-full" />
              <USkeleton class="h-7 w-full" />
              <USkeleton class="h-7 w-full" />
              <USkeleton class="h-7 w-full" />
              <USkeleton class="h-7 w-full" />
              <USkeleton class="h-7 w-full" />
              <USkeleton class="h-7 w-full" />
              <USkeleton class="h-7 w-full" />
              <USkeleton class="h-7 w-full" />
              <USkeleton class="h-7 w-full" />
            </template>
            <div
              v-for="(coin, i) in data"
              v-else
              :key="i"
              class="flex items-center p-1"
            >
              <UCheckbox
                v-model="selectedCoins"
                :value="coin"
                color="sky"
                :ui="{
                  wrapper: 'items-center',
                }"
              >
                <template #label>
                  <CoinDisplayName
                    no-padding
                    :coin="coin"
                  />
                </template>
              </UCheckbox>
            </div>
          </div>
        </div>

        <UAlert
          v-if="hasAddons"
          class="mb-4"
          color="yellow"
          variant="subtle"
          title="Heads up!"
          :description="`You have added ${totalAddons} additional assets as Add-ons.`"
        />

        <div class="flex flex-wrap items-center justify-between gap-2">
          <div
            class="w-full sm:w-auto"
            :class="{
              'text-red-500': selectedCoins.length > props.maxSelectableCoins,
            }"
          >
            Selected: {{ selectedCoins.length }}/{{ props.maxSelectableCoins }}
          </div>
          <UButton
            :label="type ? 'Continue to Plan Details' : 'Update Selection'"
            color="base-primary"
            size="lg"
            class="w-full justify-center text-center sm:w-auto"
            @click="onContinue"
          />
        </div>
      </div>
    </UCard>
  </UModal>
</template>

<style scoped></style>
