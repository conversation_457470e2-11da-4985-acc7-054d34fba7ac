<script setup lang="ts">
import type { AssetType, CommunityAsset } from '~/types'

const props = defineProps<{
  modelValue: AssetType | undefined
  loading?: boolean
  searchAssets: (query: string) => Promise<AssetType[] | CommunityAsset[]>
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: AssetType): void
  (e: 'change', value: AssetType): void
}>()

const loadingSearchAsset = ref(false)
const selectedAsset = ref(props.modelValue)

const handleSearch = async (q: string) => {
  loadingSearchAsset.value = true
  const assets = await props.searchAssets(q).finally(() => (loadingSearchAsset.value = false))
  return assets
}

const handleChange = (newValue: AssetType) => {
  selectedAsset.value = newValue
  emit('update:modelValue', newValue)
  emit('change', newValue)
}

watch(
  () => props.modelValue,
  (newVal) => (selectedAsset.value = newVal),
)
</script>

<template>
  <div class="w-full md:min-w-[300px] md:max-w-[300px]">
    <USkeleton
      v-if="loading"
      class="h-10 w-full"
    />
    <USelectMenu
      v-else
      v-model="selectedAsset"
      size="md"
      variant="solid"
      color="black"
      searchable-placeholder="Search name or symbol"
      clear-search-on-close
      :loading="loadingSearchAsset"
      :debounce="600"
      :search-attributes="['name', 'symbol']"
      :searchable="handleSearch"
      @update:model-value="handleChange"
    >
      <template #label>
        <div
          v-if="selectedAsset"
          class="flex items-center gap-x-2 overflow-hidden"
        >
          <CoinDisplayName
            :coin="selectedAsset"
            no-padding
            grow-name
          />
        </div>
        <span v-else>Select Asset</span>
      </template>

      <template #option="{ option: asset }">
        <CoinDisplayName
          :coin="asset"
          no-padding
          grow-name
        />
      </template>
    </USelectMenu>
  </div>
</template>
