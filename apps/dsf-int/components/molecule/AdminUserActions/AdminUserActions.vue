<script
  setup
  lang="ts"
>

const toast = useToast();
const { $client } = useNuxtApp();
const route = useRoute()
const id = route.params.id

const passwordResetLoading = ref(false)
const magicLinkLoading = ref(false)

const sendPasswordReset = async () => {
  passwordResetLoading.value = true
  try {
    const { message } = await $client.admin.v1.user.sendResetPassword.mutate({ id: id as string })
    toast.add({
      color: 'green',
      title: 'Request successfully sent!',
      description: message || 'Successfully sent!',
      icon: 'i-heroicons-check-circle'
    })
  } catch (err) {
    const error = err as Error

    toast.add({
      title: 'Error',
      icon: 'i-heroicons-exclamation-triangle-20-solid',
      description: error.message ?? 'An error occurred while sending email.',
      color: 'red'
    })
  }
  passwordResetLoading.value = false
}

const sendMagicLink = async () => {
  magicLinkLoading.value = true
  try {
    const { message } = await $client.admin.v1.user.sendMagicLink.mutate({ id: id as string })
    toast.add({
      color: 'green',
      title: 'Request successfully sent!',
      description: message || 'Successfully sent!',
      icon: 'i-heroicons-check-circle'
    })
  } catch (err) {
    const error = err as Error

    toast.add({
      title: 'Error',
      icon: 'i-heroicons-exclamation-triangle-20-solid',
      description: error.message ?? 'An error occurred while sending email.',
      color: 'red'
    })
  }
  magicLinkLoading.value = false
}

</script>

<template>
  <UCard>
    <div class="mb-10 flex items-center justify-between">
      <div>
        <p class="mb-1 font-bold text-2xl">Actions</p>
        <p>Perform actions for this user.</p>
      </div>
    </div>
    <UButton
      class="mb-5"
      variant="solid"
      color="black"
      size="xl"
      icon="i-heroicons:envelope-16-solid"
      label="Send Password Reset Email"
      block
      :loading="passwordResetLoading"
      @click="sendPasswordReset"
    />
    <UButton
      variant="solid"
      color="black"
      size="xl"
      icon="i-heroicons:envelope-16-solid"
      label="Send Magic Link"
      block
      :loading="magicLinkLoading"
      @click="sendMagicLink"
    />
  </UCard>
</template>