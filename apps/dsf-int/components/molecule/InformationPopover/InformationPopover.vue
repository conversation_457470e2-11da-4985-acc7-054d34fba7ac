<script setup lang="ts">
import { isMobileDevice } from '~/helper/utilities-helper'

defineProps<{
  iconClass?: string
  revertIcon?: boolean
  popper?: {
    [key: string]: string | number | boolean
  }
}>()

const popoverMode = computed(() => {
  const isMobileDevice2 = isMobileDevice()
  return isMobileDevice2 ? 'click' : 'hover'
})
</script>

<template>
  <UPopover
    :mode="popoverMode"
    :popper="popper"
    :ui="{
      container: 'z-[60]',
    }"
  >
    <InformationIcon :icon-class="iconClass" :revert-icon="revertIcon" />

    <template #panel>
      <slot />
    </template>
  </UPopover>
</template>
