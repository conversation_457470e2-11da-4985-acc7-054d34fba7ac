<script
  setup
  lang="ts"
>

const props = defineProps<{
  allowed: boolean;
  small?: boolean;
  loading?: boolean;
}>()

</script>

<template>
  <div :class="[props.allowed ? '' : 'relative']">
    <div
      v-if="!props.allowed && !props.loading"
      class="absolute inset-0 z-10 backdrop-blur-sm flex flex-col items-center gap-4 justify-center"
      :class="{ '!gap-2': props.small }"
    >
      <div>
        <p
          class="text-center text-4xl font-bold text-black dark:text-white"
          :class="{ '!text-xl': props.small }"
        >
          Unlock This Feature
        </p>
        <p
          v-if="!props.small"
          class="text-center text-base font-medium text-black dark:text-white"
        >
          Upgrade plan and take full control of your charts.
        </p>
      </div>
      <NuxtLink to="/settings/billing">
        <UButton
          :size="props.small ? 'sm' : 'md'"
          label="Upgrade Plan"
          color="black"
        />
      </NuxtLink>
    </div>
    <slot />
  </div>
</template>