<script setup lang="ts">
import { format } from 'date-fns'
import { numericDisplay } from '@dsf/int/helper/number-helper'
import { usePricingText } from '~/composables/usePricingText'

// Types
import type { v2Pricing, v2CurrentSubscription, v2AssetWithId } from '~/server/trpc/trpc'

const emit = defineEmits<{
  (e: 'onSubscribe', payload: { planId: number; selectedCoins: v2AssetWithId[] }): void
}>()

const { formatPlan } = usePricingText()

const props = defineProps<{
  currentSubscription: v2CurrentSubscription | null
  data: v2Pricing
  isAnnual: boolean
  isSubscribing?: boolean
}>()

const showAssetSelectorModal = ref(false)

const isCurrentSubscription = computed(() => {
  return Number(props.currentSubscription?.plan_id) === props.data?.id
})

const disableButtonUpgradeFirstCondition = computed(() => props.currentSubscription?.plan?.id === props.data?.id)
const disableButtonUpgradeThirdCondition = computed(
  () => props.currentSubscription?.switch_raw_data?.tier_id === props.data?.id,
)

const upgradeButtonText = computed(() => {
  if (!props.currentSubscription) {
    return 'Select Plan'
  }
  if (props.currentSubscription?.plan?.id === props.data?.id) {
    return 'Current Plan'
  } else {
    const upgradeRawData = props.currentSubscription?.switch_raw_data as { tier_id?: number; active_at?: string }

    const isCurrentTierHigher = (props.currentSubscription?.plan?.level ?? 0) > (props.data?.level ?? 0)

    if (upgradeRawData?.tier_id === props.data.id) {
      return `Your plan will be ${isCurrentTierHigher ? 'downgraded' : 'upgraded'} on ${format(new Date(upgradeRawData?.active_at ?? new Date()), 'MMM dd, yyyy')}`
    }

    if (isCurrentTierHigher) {
      return 'Downgrade Plan'
    }

    return 'Upgrade Plan'
  }
})

const onClickSubscribe = () => {
  showAssetSelectorModal.value = true
}

const onEmitSubscribe = ({ planId, selectedCoins }: { planId: number; selectedCoins: v2AssetWithId[] }) => {
  emit('onSubscribe', { planId, selectedCoins })
}

const upgradeButtonIsDisabled = computed(
  () => disableButtonUpgradeFirstCondition.value || disableButtonUpgradeThirdCondition.value,
)
</script>

<template>
  <UCard class="flex h-full flex-col justify-between gap-5">
    <h3 class="text-neutrals-800 mb-1.5 text-xl font-medium dark:text-white">
      {{ data?.name }}
    </h3>

    <div class="mt-5 flex items-center gap-2">
      <span class="text-neutrals-800 text-3xl font-semibold dark:text-white">
        {{ isAnnual ? `$${data.annual_price}` : `$${data.monthly_price}` }}
      </span>
      <span class="text-neutrals-800 text-sm dark:text-white">/ Month</span>
    </div>

    <p
      v-if="isAnnual"
      class="text-neutrals-800 mt-1 text-base dark:text-white"
    >
      Billed ${{ numericDisplay(data.annual_price * 12, 2) }} annually
    </p>

    <div class="my-5 flex flex-col gap-4">
      <div
        v-for="(feature, i) in formatPlan(props.data, props.data.name)"
        :key="i"
        class="flex items-center gap-2"
      >
        <div class="flex h-[26px] w-[26px] shrink-0 items-center justify-center rounded-full bg-white dark:bg-white/10">
          <UIcon
            v-if="feature.value === true"
            name="i-heroicons-check"
            class="text-neutrals-800 strike h-3.5 w-3.5 dark:text-white"
          />
          <UIcon
            v-else
            name="i-heroicons-x-mark"
            class="text-neutrals-400 h-3.5 w-3.5"
          />
        </div>
        <span
          class="text-neutrals-800 dark:text-neutrals-50 text-base"
          :class="{ '!text-neutrals-400 line-through': !feature.value }"
        >
          {{ feature.name }}
        </span>
      </div>
    </div>
    <UButton
      v-if="isCurrentSubscription || disableButtonUpgradeThirdCondition"
      :loading="isSubscribing"
      :disabled="isSubscribing || upgradeButtonIsDisabled"
      :label="upgradeButtonText"
      size="md"
      color="midnight-darker"
      variant="outline"
      block
      @click="onClickSubscribe"
    />
    <UButton
      v-else
      :loading="isSubscribing"
      :disabled="isSubscribing || upgradeButtonIsDisabled"
      :label="upgradeButtonText"
      size="md"
      color="midnight-darker"
      variant="solid"
      block
      @click="onClickSubscribe"
    />
  </UCard>
  <PlanAssetSelector
    v-model="showAssetSelectorModal"
    :plan-id="props.data.id!"
    :type="props.data.name"
    :max-selectable-coins="props.data.features.max_assets as number"
    @on-subscribe="onEmitSubscribe"
  />
</template>
