<script setup lang="ts">
import SuspendUserModal from "~/components/molecule/AdminUserDangerZone/SuspendUserModal.vue";

const { $client } = useNuxtApp()
const toast = useToast()
const route = useRoute();
const suspendModal = ref(false)

const handleDelete = async () => {
  const userResponse = confirm('Do you want to proceed?')

  if (!userResponse) {
    return
  }

  try {
    const response = await $client.admin.v1.user.deleteUser.mutate({ id: (route.params.id as string).trim() })

    toast.add({
      title: 'Success',
      description: response.message,
    })

    navigateTo('/admin/users')
  } catch (err) {
    const error = err as Error

    toast.add({
      title: 'Error',
      icon: 'i-heroicons-exclamation-triangle-20-solid',
      description: error.message ?? 'An error occurred while deleting the user.',
      color: 'red',
    })
  }
}

</script>

<template>
  <UCard class="!ring-accent-red-800">
    <div class="mb-10 flex items-center justify-between">
      <div>
        <p class="text-accent-red-600 mb-1 text-2xl font-bold">Danger Zone</p>
        <p>Be careful with these actions.</p>
      </div>
    </div>
    <UButton
      class="mb-5"
      variant="solid"
      size="xl"
      icon="i-material-symbols:shield"
      label="Remove MFA Factors"
      block
      :ui="{
        variant: {
          solid: '!bg-accent-red-900 !text-white',
        },
      }"
    />
    <UButton
      class="mb-5"
      variant="solid"
      size="xl"
      icon="i-material-symbols:block"
      label="Suspend User"
      @click="suspendModal = true"
      block
      :ui="{
        variant: {
          solid: '!bg-accent-red-900 !text-white',
        },
      }"
    />
    <UButton
      variant="solid"
      size="xl"
      icon="i-material-symbols:delete"
      label="Delete User"
      block
      :ui="{
        variant: {
          solid: '!bg-accent-red-900 !text-white',
        },
      }"
      @click="handleDelete"
    />
  </UCard>
  <SuspendUserModal v-model="suspendModal" />
</template>
