<script
  setup
  lang="ts"
>

import type { TRPCError } from "@trpc/server";

const { $client } = useNuxtApp()
const toast = useToast()
const suspendReason = ref('')
const route = useRoute();

const suspendModal = defineModel({
  type: Boolean,
  default: () => false
})

const closeSuspendModal = () => {
  suspendReason.value = ''
  suspendModal.value = false
}

const onSuspendUser = async () => {
  try {
    const { message } = await $client.admin.v1.user.suspendUser.mutate({
      userId: route.params.id as string,
      reason: suspendReason.value
    })

    closeSuspendModal()

    toast.add({
      color: 'green',
      title: 'Request successfully sent!',
      description: message || 'Successfully suspended!',
      icon: 'i-heroicons-check-circle'
    })
  } catch (e) {
    const _e = e as TRPCError
    toast.add({
      color: 'red',
      title: 'Request was not sent!',
      description: _e.message ?? 'Something went wrong. Please try again later.',
      icon: 'i-heroicons-exclamation-triangle-20-solid'
    })
  }
}

</script>

<template>
  <UModal v-model="suspendModal">
    <div class="p-4">
      <div class="mb-10">
        <div class="mb-5">
          <p class="mb-1 text-2xl font-bold">Suspend User</p>
          <p>Are you sure you want to suspend this user? They will not be able to access the platform until unsuspended.</p>
        </div>
        <form @submit.prevent="onSuspendUser">
          <UFormGroup
            label="Reason"
            name="reason"
            class="mb-5"
          >
            <UInput
              v-model="suspendReason"
              name="reason"
              type="text"
            />
          </UFormGroup>
          <div class="flex gap-4 justify-end">
            <UButton
              label="Cancel"
              color="black"
              size="md"
              variant="ghost"
              truncate
              class="min-h-[42px] border"
              @click="closeSuspendModal"
            />
            <UButton
              label="Suspend User"
              color="midnight"
              size="md"
              variant="solid"
              class="min-h-[42px]"
              type="submit"
            />
          </div>
        </form>
      </div>
    </div>
  </UModal>
</template>

<style scoped>

</style>