<script setup lang="ts">
import { Chart as ChartJS, registerables } from 'chart.js'
import { Chart as VueChart } from 'vue-chartjs'
import { STABLECOIN_LINE_STYLE } from '~/constants/stablecoin'

// Types
import type { ScriptableScaleContext, ScriptableScalePointLabelContext, ChartEvent, Chart } from 'chart.js'
import type { ChartComponentRef } from 'vue-chartjs'
import { dexMetricColor, dexMetrics } from '~/constants/metrics/decentralisation-indices'

ChartJS.register(...registerables)

ChartJS.defaults.font.family = 'IBM Plex Mono'

const { $client } = useNuxtApp()
const route = useRoute()

const emit = defineEmits<{
  (e: 'onClickLabel', index: number): void
  (e: 'onFinishLoading', result: boolean): boolean
}>()

const { $colorMode } = useNuxtApp()
const radarChartRef = ref<ChartComponentRef<'radar'>>()
const radarChartKey = ref(0)
const selectedLabel = ref(0)

const { isMobile } = useDeviceScreen()

const coinsQuery = computed(() => {
  const coins: string[] = []

  if (route.query.project1) {
    coins.push(route.query.project1 as string)
  }
  if (route.query.project2) {
    coins.push(route.query.project2 as string)
  }
  if (route.query.project3) {
    coins.push(route.query.project3 as string)
  }

  return coins
})

const getBase64 = (): string => {
  return radarChartRef.value?.chart?.toBase64Image('image/png', 1) ?? ''
}

defineExpose({
  getBase64,
})

const { data: spiderData, pending } = await useLazyAsyncData(
  'getSpiderData',
  async () => {
    if (coinsQuery.value.length) {
      const data = await $client.v2.user.decentralize.chart.query({
        slugs: coinsQuery.value,
      })
      emit('onFinishLoading', true)
      return data
    }
    return null
  },
  {
    watch: [coinsQuery],
  },
)

const darkMode = computed({
  get() {
    return $colorMode.value === 'dark'
  },
  set(value) {
    if (value) {
      $colorMode.preference = 'dark'
    } else {
      $colorMode.preference = 'light'
    }
  },
})

// this will be formed based on the data props
const dataTemp = computed(() => {
  return {
    labels: dexMetrics.map((label) => {
      const parts = label.split(' ')
      const firstPart = parts.slice(0, 2).join(' ')
      const secondPart = parts.slice(2).join(' ')
      return [firstPart, secondPart]
    }),
    datasets: coinsQuery.value.map((coin, coinIndex) => {
      const coinColor = []

      if (route.query.project1) {
        coinColor.push(STABLECOIN_LINE_STYLE['coin-1'].color)
      }
      if (route.query.project2) {
        coinColor.push(STABLECOIN_LINE_STYLE['coin-2'].color)
      }
      if (route.query.project3) {
        coinColor.push(STABLECOIN_LINE_STYLE['coin-3'].color)
      }

      const coinData = spiderData.value?.find((data) => data.slug === coin)

      return {
        label: coin,
        borderColor: coinColor[coinIndex],
        fill: false,
        data: [
          Number(coinData?.metrics.ipAuthDistrGini ?? 0),
          Number(coinData?.metrics.ipParticipantDivGini ?? 0),
          Number(coinData?.metrics.ipAuthorInflConcHHI ?? 0),
          Number(coinData?.metrics.ipGovOrdinal ?? 0), // red limit
          Number(coinData?.metrics.rcpDevDistrGini ?? 0),
          Number(coinData?.metrics.rcpParticipantDivShannon ?? 0),
          Number(coinData?.metrics.rcpDevInflConcHHI ?? 0),
          Number(coinData?.metrics.rcdRevrPowerConcHHI ?? 0), // green limit
          Number(coinData?.metrics.consensusPowerNeGini ?? 0),
          Number(coinData?.metrics.consensusPowerNeTheil ?? 0),
          Number(coinData?.metrics.consensusPowerConcNakamoto ?? 0),
          Number(coinData?.metrics.consensusPowerConcHHI ?? 0), // purple limit
          Number(coinData?.metrics.coinDistrOrdinal ?? 0),
        ],
        borderWidth: 1.5,
        hidden: false,
      }
    }),
  }
})

const maxData = computed(() => {
  const flatData = dataTemp.value.datasets.map((item) => item.data).flat()
  return Math.max(...flatData)
})

function getLabelIndex(x: number, y: number, pointLabelItems: any) {
  let labelIndex = -1

  for (let i = 0; i < pointLabelItems.length; i++) {
    const item = pointLabelItems[i]
    if (x >= item.left && x <= item.right && y >= item.top && y <= item.bottom) {
      labelIndex = i
      break
    }
  }

  return labelIndex
}

watch(
  [selectedLabel, radarChartRef, dataTemp],
  ([_, newRadarChartRef]) => {
    // Update radar chart
    if (newRadarChartRef?.chart) {
      newRadarChartRef?.chart?.update()
    }
  },
  {
    immediate: true,
  },
)

watch(darkMode, () => {
  if (radarChartRef.value?.chart) {
    radarChartRef.value.chart.update()
  }
})
</script>
<template>
  <USkeleton
    v-if="pending || coinsQuery.length === 0"
    class="h-full w-full"
  />
  <VueChart
    v-else
    :key="radarChartKey"
    ref="radarChartRef"
    type="radar"
    :data="dataTemp"
    :options="{
      plugins: {
        title: {
          display: false,
        },
        legend: {
          display: false,
        },
      },
      responsive: true,
      maintainAspectRatio: false,
      elements: {
        point: {
          radius: 0,
        },
      },
      scales: {
        r: {
          startAngle: 0,
          pointLabels: {
            font: (ctx: ScriptableScalePointLabelContext) => {
              const size = isMobile ? 8 : 12
              if (ctx.index === selectedLabel) {
                return {
                  weight: 'bold',
                  size,
                }
              }
              return {
                weight: 'normal',
                size,
              }
            },
            color: (context) => {
              if (context.index === selectedLabel) {
                return '#fff'
              }
              return dexMetricColor(context.index)
            },
            backdropColor: (context) =>
              context.index === selectedLabel ? dexMetricColor(selectedLabel) : 'transparent',
            backdropPadding: (context) => (context.index === selectedLabel ? 5 : 2),
            borderRadius: (context) => (context.index === selectedLabel ? 3 : 2),
            padding: () => 8,
          },
          beginAtZero: true,
          max: maxData,
          ticks: {
            display: false,
          },
          border: {
            dash: (ctx: ScriptableScaleContext) => {
              if (ctx.tick.value === maxData) {
                return []
              } else {
                return [4, 4]
              }
            },
          },
          grid: {
            color: '#C3C3C3',
            lineWidth: (ctx: ScriptableScaleContext) => {
              const ticksLen = ctx.chart.scales?.r?.ticks?.length || 0
              const midTickIndex = Math.ceil(ticksLen / 2)

              if (ctx.index === 3 || ctx.tick.value === maxData || ctx.index === midTickIndex) {
                return 1
              }

              return 0
            },
          },
          angleLines: {
            color: '#C3C3C3',
            borderDash: [4, 4],
          },
        },
      },
      onHover: ({ x, y }: ChartEvent, _, chart: Chart) => {
        const canvas = chart.canvas
        const index = getLabelIndex(x || 0, y || 0, (chart.scales.r as any).pointLabelItems)

        if (index !== -1) {
          canvas.style.cursor = 'pointer'
        } else {
          canvas.style.cursor = 'default'
        }
      },
      onClick: (ctx: ChartEvent, _, chart: Chart) => {
        const { x, y } = ctx
        const index = getLabelIndex(x || 0, y || 0, (chart.scales.r as any).pointLabelItems)

        if (index !== -1) {
          selectedLabel = index
          $emit('onClickLabel', index)
        }
      },
    }"
  />
</template>
