<script setup lang="ts">
import { Chart as ChartJS, type ChartData, registerables } from 'chart.js'
import { Chart as VueChart } from 'vue-chartjs'

// Types
import type { ScriptableScaleContext, ScriptableScalePointLabelContext, ChartEvent, Chart } from 'chart.js'
import type { ChartComponentRef } from 'vue-chartjs'

ChartJS.register(...registerables)

ChartJS.defaults.font.family = 'IBM Plex Mono'

const props = defineProps<{
  radarDataset: ChartData
  onClickRadarLabel: (index: number) => void
  radarLoading: boolean
  metricColor: (index: number) => string
}>()

const { $colorMode } = useNuxtApp()
const radarChartRef = ref<ChartComponentRef<'radar'>>()
const radarChartKey = ref(0)
const selectedLabel = ref(0)

const { isMobile } = useDeviceScreen()

const getBase64 = (): string => {
  return radarChartRef.value?.chart?.toBase64Image('image/png', 1) ?? ''
}

defineExpose({
  getBase64,
})

const darkMode = computed({
  get() {
    return $colorMode.value === 'dark'
  },
  set(value) {
    if (value) {
      $colorMode.preference = 'dark'
    } else {
      $colorMode.preference = 'light'
    }
  },
})

const maxData = computed(() => {
  const flatData = props.radarDataset.datasets
    .map((item) => item.data)
    .flat()
    .filter((val): val is number => val !== null)
  return Math.max(...flatData)
})
function getLabelIndex(x: number, y: number, pointLabelItems: any) {
  let labelIndex = -1

  for (let i = 0; i < pointLabelItems.length; i++) {
    const item = pointLabelItems[i]
    if (x >= item.left && x <= item.right && y >= item.top && y <= item.bottom) {
      labelIndex = i
      break
    }
  }

  return labelIndex
}

watch(
  [selectedLabel, radarChartRef, props.radarDataset],
  ([_, newRadarChartRef]) => {
    // Update radar chart
    if (newRadarChartRef?.chart) {
      newRadarChartRef?.chart?.update()
    }
  },
  {
    immediate: true,
  },
)

watch(darkMode, () => {
  if (radarChartRef.value?.chart) {
    radarChartRef.value.chart.update()
  }
})
</script>
<template>
  <USkeleton
    v-if="radarLoading"
    class="h-full w-full"
  />
  <VueChart
    v-else
    :key="radarChartKey"
    ref="radarChartRef"
    type="radar"
    :data="props.radarDataset"
    :options="{
      plugins: {
        title: {
          display: false,
        },
        legend: {
          display: false,
        },
      },
      responsive: true,
      maintainAspectRatio: false,
      elements: {
        point: {
          radius: 0,
        },
      },
      scales: {
        r: {
          startAngle: 0,
          pointLabels: {
            font: (ctx: ScriptableScalePointLabelContext) => {
              const size = isMobile ? 8 : 12
              if (ctx.index === selectedLabel) {
                return {
                  weight: 'bold',
                  size,
                }
              }
              return {
                weight: 'normal',
                size,
              }
            },
            color: (context) => {
              if (context.index === selectedLabel) {
                return '#fff'
              }
              return props.metricColor(context.index)
            },
            backdropColor: (context) =>
              context.index === selectedLabel ? props.metricColor(selectedLabel) : 'transparent',
            backdropPadding: (context) => (context.index === selectedLabel ? 5 : 2),
            borderRadius: (context) => (context.index === selectedLabel ? 3 : 2),
            padding: () => 8,
          },
          beginAtZero: true,
          max: maxData,
          ticks: {
            display: false,
          },
          border: {
            dash: (ctx: ScriptableScaleContext) => {
              if (ctx.tick.value === maxData) {
                return []
              } else {
                return [4, 4]
              }
            },
          },
          grid: {
            color: '#C3C3C3',
            lineWidth: (ctx: ScriptableScaleContext) => {
              const ticksLen = ctx?.chart?.scales?.r?.ticks.length || 0
              const midTickIndex = Math.ceil(ticksLen / 2)

              if (ctx.index === 3 || ctx.tick.value === maxData || ctx.index === midTickIndex) {
                return 1
              }

              return 0
            },
          },
          angleLines: {
            color: '#C3C3C3',
            borderDash: [4, 4],
          },
        },
      },
      onHover: ({ x, y }: ChartEvent, _, chart: Chart) => {
        const canvas = chart.canvas
        // @ts-expect-error ignore
        const index = getLabelIndex(x || 0, y || 0, chart.scales.r._pointLabelItems)

        if (index !== -1) {
          canvas.style.cursor = 'pointer'
        } else {
          canvas.style.cursor = 'default'
        }
      },
      onClick: (ctx: ChartEvent, _, chart: Chart) => {
        const { x, y } = ctx
        // @ts-expect-error ignore
        const index = getLabelIndex(x || 0, y || 0, chart.scales.r._pointLabelItems)

        if (index !== -1) {
          selectedLabel = index
          onClickRadarLabel(index)
        }
      },
    }"
  />
</template>
