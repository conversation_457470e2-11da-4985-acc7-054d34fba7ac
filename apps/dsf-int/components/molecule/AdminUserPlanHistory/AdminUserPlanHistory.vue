<script setup lang="ts">
const columns = [
  {
    key: 'number',
    label: '#',
    isVisible: true,
    enabled: true,
    description: '',
  },
  {
    key: 'plan',
    label: 'Plan',
  },
  {
    key: 'start_date',
    label: 'Start Date',
  },
  {
    key: 'end_date',
    label: 'End Date',
  },
  {
    key: 'price',
    label: 'Price',
  },
]

const data = [
  {
    number: 1,
    plan: 'Enterprise',
    start_date: '01/01/1990',
    end_date: '01/01/2000',
    price: '$5,000 / month',
  },
  {
    number: 2,
    plan: 'Enterprise',
    start_date: '01/01/1990',
    end_date: '-',
    price: '$10,000 / one-time',
  },
  {
    number: 3,
    plan: 'Pro',
    start_date: '01/01/1990',
    end_date: '01/01/2000',
    price: '$4,900 / year',
  },
  {
    number: 4,
    plan: 'Basic',
    start_date: '01/01/1990',
    end_date: '01/01/2000',
    price: '$49.00 / month',
  },
]
</script>

<template>
  <UCard>
    <div class="mb-10">
      <p class="mb-1 text-2xl font-bold">Plan History</p>
      <p>View user's subscription plan history.</p>
    </div>
    <UTable
      :columns="columns"
      :rows="data"
    />
  </UCard>
</template>
