<script
  setup
  lang="ts"
>
import { VueFlow } from '@vue-flow/core'
import { useVueFlow } from "@vue-flow/core";
import TreeChartLegend from "~/components/molecule/TreeChart/TreeChartLegend.vue";
import { useRadarLineChart } from "~/composables/useRadarLineChart";

const props = defineProps<{
  title: string;
  titleColor: string;
  descriptionTooltip: string;
  pending: boolean;
  nodes: any;
  edges: any;
  selectedMetric: string;
}>()

const { fitView } = useVueFlow()
const { coin1Filter, coin2Filter, coin3Filter } = useRadarLineChart()
const { isFeatureAllowed } = useFeature();

const allowFeatureInteraction = computed(() => {
  // metric = ipGovOrdinal | coinDistrOrdinal
  return !!isFeatureAllowed('esg_climate_metrics.decentralisation')
})

onUpdated(() => setTimeout(fitView, 150))

</script>

<template>
  <ClientOnly>
    <CheckFeature
      :allowed="allowFeatureInteraction"
      class="w-full h-full"
    >
      <LottieNodiensLoader v-if="props.pending" />
      <div
        v-else
        class="h-full w-full flex flex-col"
      >
        <div
          class="flex items-center justify-between gap-1 rounded px-4 py-3 grow md:grow-0 w-full md:w-auto"
          :style="`background: ${props.titleColor}`"
        >
          <span class="whitespace-nowrap text-base text-white grow md:grow-0"> {{ props.title }}</span>
          <InformationPopover
            icon-class="mt-2 bg-[#ffffff]"
            revert-icon
          >
            <TooltipContent>
              <p v-html="props.descriptionTooltip" />
            </TooltipContent>
          </InformationPopover>
        </div>
        <VueFlow
          v-if="allowFeatureInteraction"
          fit-view-on-init
          :nodes="nodes"
          :edges="edges"
          :nodes-draggable="false"
          :pan-on-drag="true"
          :nodes-connectable="false"
          :zoom-on-scroll="true"
          :zoom-on-pinch="true"
          :zoom-on-double-click="true"
        >
          <template #node-multipleSourceHandle="props">
            <MultipleSourceHandleNode
              :id="props.id"
              :data="props.data"
            />
          </template>
          <template #node-result="props">
            <ResultNode
              :id="props.id"
              :data="props.data"
            />
          </template>
        </VueFlow>
      </div>
      <TreeChartLegend
        :coin1="coin1Filter"
        :coin2="coin2Filter"
        :coin3="coin3Filter"
      />
    </CheckFeature>
  </ClientOnly>
</template>

<style>
@import '@vue-flow/core/dist/style.css';
@import '@vue-flow/core/dist/theme-default.css';
</style>