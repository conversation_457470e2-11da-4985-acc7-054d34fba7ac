<script
  setup
  lang="ts"
>
import { Position, Handle } from '@vue-flow/core'

const props = defineProps<{
  data: any;
}>();
</script>

<template>
  <!-- NOTE: Regular string as id doesnt work. Needs to be this approach. -->
  <div>
    <Handle
      type="target"
      :position="Position.Top"
    />
    <div class="bg-white text-black text-[12px] p-2 rounded-[3px] flex flex-col items-center justify-center">
      <div>{{ props.data.label }}</div>
    </div>
    <Handle
      v-if="props.data.sources.includes(Position.Left)"
      :id="'source-left'"
      type="source"
      :position="Position.Left"
    />
    <Handle
      v-if="props.data.sources.includes(Position.Right)"
      :id="'source-right'"
      type="source"
      :position="Position.Right"
    />
    <Handle
      v-if="props.data.sources.includes(Position.Bottom)"
      :id="'source-bottom'"
      type="source"
      :position="Position.Bottom"
    />
  </div>
</template>

<style scoped>

</style>