<script
  setup
  lang="ts"
>
import type { CoinFilter } from "~/constants/example/stablecoin";

const props = defineProps<{
  coin1: CoinFilter
  coin2: CoinFilter
  coin3: CoinFilter
}>()

const legends = computed(() => {
  const coinsArray = [props.coin1, props.coin2, props.coin3]
  return coinsArray.reduce<{ color: string; name: string }[]>((acc, coin: CoinFilter) => {
    if (coin.coin) {
      acc.push({
        color: coin.color,
        name: coin.coin.name
      })
    }
    return acc
  }, [])
})

</script>

<template>
  <div class="flex gap-8 items-center justify-center">
    <div
      v-for="(legend, index) in legends"
      :key="index"
      class="flex gap-2 items-center justify-center"
    >
      <div
        class="h-[10px] w-[10px]"
        :style="`background-color: ${legend.color}`"
      />
      <span>
        {{ legend.name }}
      </span>
    </div>
  </div>
</template>