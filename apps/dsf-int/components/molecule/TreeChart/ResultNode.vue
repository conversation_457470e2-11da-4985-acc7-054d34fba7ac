<script
  setup
  lang="ts"
>
import { Handle, Position } from '@vue-flow/core'

const props = defineProps<{
  data: any;
}>();
</script>

<template>
  <div>
    <Handle
      v-if="props.data.type === 'LeftNode'"
      type="target"
      :position="Position.Right"
    />
    <Handle
      v-else-if="props.data.type === 'RightNode'"
      type="target"
      :position="Position.Left"
    />
    <Handle
      v-else
      type="target"
      :position="Position.Top"
    />
    <div class="bg-white text-black text-[12px] p-2 rounded-[3px] flex flex-col items-center justify-center">
      <span>{{ props.data.label }}</span>
      <template
        v-if="props.data.colors"
      >
        <div class="flex gap-0.5">
          <div
            v-for="(color, i) in props.data.colors"
            :key="i"
            class="h-[10px] w-[10px]"
            :style="`background-color: ${color}`"
          />
        </div>
      </template>
    </div>
  </div>
</template>