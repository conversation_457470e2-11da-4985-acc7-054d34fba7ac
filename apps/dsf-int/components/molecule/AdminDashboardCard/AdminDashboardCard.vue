<template>
  <UCard class="basis-[250px] grow">
    <LottieNodiensLoader v-if="pending" />
    <template v-else>
      <div class="flex justify-between items-center">
        {{ header }}
        <UIcon
          :name="icon"
          class="text-3xl"
        />
      </div>
      <p class="text-center text-5xl mt-5 font-bold">
        {{ count }}
      </p>
    </template>
  </UCard>
</template>

<script
  setup
  lang="ts"
>
interface Props {
  header: string;
  icon: string;
  count: number;
  pending: boolean;
}

defineProps<Props>()
</script>