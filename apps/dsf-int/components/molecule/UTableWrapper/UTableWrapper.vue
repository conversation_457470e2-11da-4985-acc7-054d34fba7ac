<script setup lang="ts">
defineProps<{
  pending: boolean
  data: any
  columns: any
}>()
</script>

<template>
  <UTable
    :loading="pending"
    :rows="pending ? [] : (data ?? [])"
    :columns="columns"
    :ui="{
      base: `${pending ? 'block' : null}`,
      thead: `${pending ? 'hidden' : null}`,
      tbody: `${pending ? 'flex items-center justify-center' : null}`,
    }"
  >
    <template
      v-for="slotName in Object.keys($slots)"
      :key="slotName"
      #[slotName]="slotProps"
    >
      <slot
        :name="slotName"
        v-bind="slotProps"
      />
    </template>
    <template #loading-state>
      <LottieNodiensLoader />
    </template>
  </UTable>
</template>
