<script setup lang="ts">
const columns = [
  {
    key: 'number',
    label: '#',
    isVisible: true,
    enabled: true,
    description: '',
  },
  {
    key: 'discount_name',
    label: 'Discount Name',
  },
  {
    key: 'expiry_date',
    label: 'Expiry Date',
  },
  {
    key: 'actions',
    label: 'Actions',
  },
]

const data = [
  {
    number: 1,
    discount_name: 'Early Adopter Discount - 15%',
    expiry_date: '12/31/2025',
  },
  {
    number: 2,
    discount_name: 'Loyalty Bonus - 10%',
    expiry_date: '6/30/2025',
  },
]
</script>

<template>
  <UCard>
    <div class="mb-10 flex items-center justify-between">
      <div>
        <p class="mb-1 text-2xl font-bold">Discounts</p>
        <p>Manage discounts.</p>
      </div>

      <UButton
        label="Add Discount"
        icon="i-heroicons-plus"
        color="black"
        variant="solid"
        size="md"
        truncate
        class="order-1 min-h-[42px] w-full justify-center md:order-2 md:w-auto md:justify-start"
      />
    </div>
    <UTable
      :columns="columns"
      :rows="data"
    >
      <template #actions-data="{}">
        <div class="flex">
          <UButton
            icon="i-material-symbols:delete-outline-sharp"
            size="sm"
            class="!text-gray-500 hover:bg-red-400 hover:!text-red-900 dark:!text-white dark:hover:bg-red-400 dark:hover:!text-red-900"
            variant="ghost"
            label="Delete"
          />
        </div>
      </template>
    </UTable>
  </UCard>
</template>
