<script setup lang="ts">
// Stores
import { useSessionStore } from '~/stores/session'

// Helpers
import { isMobileDevice } from '~/helper/utilities-helper'
import { toCamelCase } from '~/helper/string-helper'

// Constatns
import { CHART_CLASS_COLUMN } from '~/constants/table-columns'
import { PER_PAGE } from '~/constants/api'
import { HTTP_STATUS } from '~/constants/error'
import { HOME_TABLE_RANK_OPTION_VALUES } from '~/constants/options'

// Types
import type { AssetFeedTableQueryParams } from '~/types'
import type { HomePageQuery } from '~/types/page'

const { $apiClient } = useNuxtApp()
const toast = useToast()
const route = useRoute()
const sessionStore = useSessionStore()
const isLogged = sessionStore.isLogged
const { isColumnSorted } = useTable()

const props = defineProps<{
  coinTypes: any
}>()

const tableFilters = computed(() => {
  const query = route.query as HomePageQuery

  const page = Number(query.page ?? 1)
  const terms = query.q || undefined
  const sortBy = query.rank ? toCamelCase(query.rank) : undefined
  const assetType = query.assetType?.toUpperCase().split(',')

  return { page, terms, sortBy, assetType } as AssetFeedTableQueryParams
})

const { data, status } = useLazyAsyncData(
  'getHomeTable',
  async () => {
    if (props.coinTypes.length === 0) return null

    const { response, data } = await $apiClient.GET('/nodiens/api/v1/asset/feeds', {
      params: { query: tableFilters.value },
    })

    if (response.status === HTTP_STATUS.INTERNAL_SERVER_ERROR) {
      toast.add({
        title: 'Error',
        color: 'red',
        description: 'Something went wrong. Please try again later.',
        icon: 'i-heroicons-exclamation-triangle-20-solid',
      })
    }

    return data?.payload || null
  },
  {
    watch: [tableFilters],
    server: false,
  },
)

const isLoading = computed(() => status.value === 'pending')

const page = computed({
  get: () => (!isLogged ? 1 : Number(route.query.page ?? 1)),
  set: async (page) =>
    await navigateTo({
      query: {
        ...route.query,
        page,
      },
    }),
})

const totalItem = computed(() => {
  return data.value?.totalItem || 0
})

const paginationEntriesLabel = computed(() => {
  if (!data.value) return 'Showing 0 to 0 of 0 Entries'

  const { currentPage, perPage: apiPerPage, data: items } = data.value
  const startEntry = (currentPage - 1) * apiPerPage + 1
  const endEntry = startEntry + (items?.length ?? 0) - 1

  return `Showing ${startEntry} to ${endEntry} of ${totalItem.value} Entries`
})

const columns = computed(() => {
  const { moodIndex, trustIndex, communitySize, nrMessages, energyConsumptionTx } = HOME_TABLE_RANK_OPTION_VALUES

  return [
    {
      key: 'number',
      label: '#',
    },
    {
      key: 'name',
      label: 'Name',
      class: 'min-w-[180px]',
    },
    {
      key: 'mood_index',
      label: 'Mood Index',
      class: isColumnSorted(moodIndex, true),
      rowClass: isColumnSorted(moodIndex, true),
      description: `The Mood Index is a proprietary metric that measures the collective sentiment within online communities behind specific crypto assets. Leveraging advanced natural language processing (NLP) and custom sentiment analysis techniques, the index captures, quantifies, and aggregates mood signals from digital community activity.<br/>
<br/>
The index starts at a baseline value of 100 and fluctuates over time, with a minimum bound of 0 and no upper cap. A higher score indicates more positive mood and expectations within the tracked communities.`,
    },
    {
      key: 'trust_index',
      label: 'Trust Index',
      class: isColumnSorted(trustIndex),
      rowClass: isColumnSorted(trustIndex),
      description: `The Trust Index is a proprietary metric that evaluates the overall credibility and quality of online communities surrounding a specific crypto asset. Rather than measuring sentiment, this index captures deeper structural and behavioral signals—such as diversity, engagement quality, moderation practices, consistency of activity, and transparency.<br/>
<br/>
The index is scaled from a baseline of 100, with a minimum of 0 and no upper limit. Higher scores reflect greater perceived trustworthiness, resilience, and cohesion within the community.<br />
<br/>
Unlike sentiment-based indicators, the Trust Index aims to quantify the long-term health and reliability of a community ecosystem.`,
    },
    {
      key: 'community_size',
      label: 'Community Size',
      isVisible: true,
      class: isColumnSorted(communitySize),
      rowClass: isColumnSorted(communitySize),
      description: `The Community Size metric captures the total number of active and inactive members within each tracked online community related to a specific crypto asset. It provides a clear view of the reach, visibility, and potential influence of each individual community, helping to assess the scale of engagement and audience distribution across platforms.<br/>
<br/>
This metric is calculated individually for each asset, enabling direct comparison of community scale across the crypto ecosystem.`,
    },
    {
      key: 'total_message',
      label: '# Messages (14d)',
      class: isColumnSorted(nrMessages),
      rowClass: isColumnSorted(nrMessages),
      description:
        'The total number of posts, comments, and messages exchanged across the tracked communities of a crypto asset over the last 14 days. This shows the overall level of activity and interaction within these communities.',
    },
    {
      key: 'energy',
      label: 'Energy Cons/Tx',
      class: isColumnSorted(energyConsumptionTx),
      rowClass: isColumnSorted(energyConsumptionTx),
      description:
        'The estimated energy consumption per transaction of a crypto asset, measured in Watt-hours (Wh) or their multiples. This is calculated using proprietary methodologies that consider the specific consensus mechanism employed, the type and quantity of hardware utilised, and the real-world transactions being conducted.',
    },
    {
      key: 'chart',
      label: 'Chart (90d)',
      description: `This shows the crypto asset's performance within the selected metric over the last 90 days. This updates dynamically based on the selected ranking metric.`,
      class: CHART_CLASS_COLUMN,
    },
  ]
})

const generateRowNumber = (index: number) => {
  if (!data.value) return index + 1
  const { currentPage, perPage } = data.value
  const startEntry = (currentPage - 1) * perPage + 1
  return startEntry + index
}

const generateRowRoute = (row: any) => {
  const path = row.type === 'CRYPTOCURRENCY' ? '/crypto' : '/stablecoin'
  return `${path}/${row.slug}`
}
</script>

<template>
  <UCard :class="`${isLoading && !isLogged ? 'mb-40' : ''}`">
    <UTableWrapper
      :pending="isLoading"
      :data="data?.data || []"
      :columns="columns"
    >
      <!-- Empty State -->
      <template #empty-state>
        <EmptyFilter v-if="props.coinTypes.length === 0" />
        <EmptyData v-else />
      </template>

      <!-- HEAD -->
      <template #number-header="head">
        <div class="flex w-full items-center justify-center">
          <span class="text-center">{{ head.column.label }}</span>
        </div>
      </template>

      <template #name-header="head">
        <div class="flex items-center">
          <span class="whitespace-nowrap text-left">{{ head.column.label }}</span>
        </div>
      </template>

      <template #mood_index-header="head">
        <div class="flex w-32 items-center">
          <div class="flex w-full items-center gap-x-1">
            <span class="whitespace-nowrap text-left">{{ head.column.label }}</span>

            <InformationPopover class="mt-1">
              <TooltipContent>
                <p v-html="head.column.description" />
              </TooltipContent>
            </InformationPopover>
          </div>
        </div>
      </template>

      <template #trust_index-header="head">
        <div class="flex w-32 items-center">
          <div class="flex w-full items-center gap-x-1">
            <span class="whitespace-nowrap text-left"> {{ head.column.label }} </span>

            <InformationPopover class="mt-1">
              <TooltipContent>
                <p v-html="head.column.description" />
              </TooltipContent>
            </InformationPopover>
          </div>
        </div>
      </template>

      <template #community_size-header="head">
        <div class="flex w-32 items-center">
          <div class="flex w-full items-center gap-x-1">
            <span class="whitespace-nowrap text-left">{{ head.column.label }}</span>

            <InformationPopover class="mt-1">
              <TooltipContent>
                <p v-html="head.column.description" />
              </TooltipContent>
            </InformationPopover>
          </div>
        </div>
      </template>

      <template #total_message-header="head">
        <button class="flex w-32 items-center">
          <div class="flex w-full items-center gap-x-1">
            <span class="cursor-default whitespace-nowrap text-left hover:cursor-default">{{ head.column.label }}</span>

            <InformationPopover class="mt-1">
              <TooltipContent>
                <p v-html="head.column.description" />
              </TooltipContent>
            </InformationPopover>
          </div>
        </button>
      </template>

      <template #energy-header="head">
        <div class="flex w-32 items-center">
          <div class="flex w-full items-center gap-x-1">
            <span class="whitespace-nowrap text-left"> {{ head.column.label }} </span>

            <InformationPopover class="mt-1">
              <TooltipContent>
                <p v-html="head.column.description" />
              </TooltipContent>
            </InformationPopover>
          </div>
        </div>
      </template>

      <template #chart-header="head">
        <div class="flex w-full min-w-[11rem] items-center">
          <span class="text-left">{{ head.column.label }}</span>

          <InformationPopover class="mt-1">
            <TooltipContent>
              <p v-html="head.column.description" />
            </TooltipContent>
          </InformationPopover>
        </div>
      </template>

      <!-- BODY -->
      <template #number-data="{ row, index }">
        <div class="cursor-pointer">
          <NuxtLink :href="generateRowRoute(row)">
            <p class="font-regular text-center">
              {{ generateRowNumber(index) }}
            </p>
          </NuxtLink>
        </div>
      </template>

      <template #name-data="{ row }">
        <NuxtLink :href="generateRowRoute(row)">
          <CoinDisplayName :coin="row" />
        </NuxtLink>
      </template>

      <template #mood_index-data="{ row }">
        <div class="cursor-pointer">
          <NuxtLink :href="generateRowRoute(row)">
            <p>{{ row?.moodIndex ?? '-' }}</p>
          </NuxtLink>
        </div>
      </template>

      <template #trust_index-data="{ row }">
        <div class="cursor-pointer">
          <NuxtLink :href="generateRowRoute(row)">
            <p>{{ row?.trustIndex ?? '-' }}</p>
          </NuxtLink>
        </div>
      </template>

      <template #community_size-data="{ row }">
        <div class="cursor-pointer">
          <NuxtLink :href="generateRowRoute(row)">
            <p>{{ row?.communitySize ?? '-' }}</p>
          </NuxtLink>
        </div>
      </template>

      <template #total_message-data="{ row }">
        <div class="cursor-pointer">
          <NuxtLink :href="generateRowRoute(row)">
            <p>{{ row?.numberOfMessage ?? '-' }}</p>
          </NuxtLink>
        </div>
      </template>

      <template #energy-data="{ row }">
        <div class="cursor-pointer">
          <NuxtLink :href="generateRowRoute(row)">
            <p>{{ row?.energyConsumptionPerTx ?? '-' }}</p>
          </NuxtLink>
        </div>
      </template>

      <template #chart-data="{ row }">
        <NuxtLink
          v-if="row?.graph?.length > 0"
          :href="generateRowRoute(row)"
        >
          <div class="cursor-pointer">
            <IndexChart
              class="!h-[30px] !w-full"
              :type="1"
              :data="row.graph"
            />
          </div>
        </NuxtLink>
        <div
          v-else
          class=""
        >
          -
        </div>
      </template>
    </UTableWrapper>
  </UCard>

  <div
    v-if="totalItem > 0"
    class="flex flex-col-reverse items-center justify-center gap-6 text-center md:flex-row md:justify-between"
  >
    <p class="text-neutrals-800 text-sm dark:text-white">
      {{ paginationEntriesLabel }}
    </p>
    <UPagination
      v-model="page"
      :page-count="data?.perPage ?? PER_PAGE"
      :total="totalItem"
      :max="isMobileDevice() ? 7 : 10"
      :disabled="isLoading"
      :active-button="{
        color: 'black',
      }"
    />
  </div>
</template>
