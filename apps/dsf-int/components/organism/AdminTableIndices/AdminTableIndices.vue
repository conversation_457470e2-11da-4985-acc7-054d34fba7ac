<script setup lang="ts">
const columns = [
  {
    key: 'metric_name',
    label: 'Index/Metric',
    class: '!font-bold',
    rowClass: 'font-bold',
  },
  {
    key: 'view_count',
    label: 'Views',
  },
  {
    key: 'percentage',
    label: 'Percentage',
  },
]

defineProps<{
  title: string
  data: any[] | undefined
  pending: boolean
}>()
</script>

<template>
  <UCard>
    <h1 class="text-neutrals-50 px-4 pt-4 text-lg font-bold">{{ title }}</h1>
    <UTableWrapper
      :columns="columns"
      :data="data"
      :pending="pending"
    />
  </UCard>
</template>
