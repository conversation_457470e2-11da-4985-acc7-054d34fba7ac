<script setup lang="ts">
import { formatToSlug } from '~/helper/string-helper'
import { isMobileDevice } from '~/helper/utilities-helper'

// Constants
import { CHART_CLASS_COLUMN, COMMUNITY_TABLE_TOPIC_COLUMN } from '~/constants/table-columns'
import { PER_PAGE } from '~/constants/api'
import { HTTP_STATUS } from '~/constants/error'
import { COMMUNITY_TABLE_RANK_VALUES } from '~/constants/options'

// Types
import type { MoodTrustIndexTableQueryParams } from '~/types'

const props = defineProps<{
  coinTypes: any
}>()

const { $apiClient } = useNuxtApp()

const toast = useToast()
const route = useRoute()

const { isColumnSorted } = useTable()

const { isFeatureAllowed } = useFeature()

const page = computed({
  get: () => Number(route.query.page ?? 1),
  set: async (page: number) => {
    await navigateTo({
      query: {
        ...route.query,
        page,
      },
    })
  },
})

const index = computed(() => (route.query.index as string) ?? 'mood')
const isTopicAnalytic = computed(() => index.value === 'topic_analytic')
const isMoodIndex = computed(() => index.value === 'mood')

const isIndexTableAllowed = computed(() => {
  return isTopicAnalytic.value
    ? !!isFeatureAllowed('sentiment_metrics.topic_trends')
    : isMoodIndex.value
      ? !!isFeatureAllowed('sentiment_metrics.mood_index_level_1')
      : !!isFeatureAllowed('sentiment_metrics.trust_index_level_1')
})

const tableFilters = computed(() => {
  const query = route.query as Record<string, string | undefined>
  const page = Number(query.page ?? 1)
  const terms = query.q
  const rank = query.rank?.toUpperCase() as MoodTrustIndexTableQueryParams['rank']
  const type = query.assetType?.toUpperCase().split(',') as MoodTrustIndexTableQueryParams['type']

  return {
    page,
    ...(terms && { terms }),
    ...(type && { type }),
    ...(rank && !isTopicAnalytic.value && { rank }),
  } satisfies MoodTrustIndexTableQueryParams
})

const { data, status } = useLazyAsyncData(
  `getCommunityTable.${index.value}`,
  async () => {
    if (props.coinTypes.length === 0) {
      return null
    }

    const url =
      index.value === 'topic_analytic'
        ? '/nodiens/api/v1/topic-trend/'
        : index.value === 'trust'
          ? '/nodiens/api/v1/trust-index/'
          : '/nodiens/api/v1/mood-index/'
    const options = index.value === 'topic_analytic' ? undefined : { params: { query: { ...tableFilters.value } } }

    const { response, data } = await $apiClient.GET(url, options)

    if (response.status === HTTP_STATUS.INTERNAL_SERVER_ERROR) {
      toast.add({
        title: 'Error',
        description: 'Something went wrong. Please try again later.',
        icon: 'i-heroicons-exclamation-triangle-20-solid',
        color: 'red',
      })
    }

    return data?.payload ?? null
  },
  {
    watch: [tableFilters],
    server: false,
  },
)

const isLoading = computed(() => status.value === 'pending')

const moodTrustColumns = computed(() => {
  const {
    communitySize: community_size,
    messageCount: message_count,
    rankIndicator: rank_indicator,
    aDayChangedPercentage: a_day_changed_percentage,
  } = COMMUNITY_TABLE_RANK_VALUES

  return [
    {
      key: 'number',
      label: '#',
      enabled: true,
    },
    {
      key: 'name',
      label: 'Name',
      class: 'min-w-[180px] sm:min-w-[300px]',
      enabled: true,
    },
    {
      key: 'community_size',
      label: 'Community Size',
      class: isColumnSorted(community_size),
      rowClass: isColumnSorted(community_size),
      enabled: true,
      description: `The total number of active and inactive members across the tracked communities of a crypto asset. This shows the overall size and potential reach of these communities.`,
    },
    {
      key: 'number_of_message',
      label: '# Messages (14d)',
      class: isColumnSorted(message_count),
      rowClass: isColumnSorted(message_count),
      enabled: true,
      description: `The total number of posts, comments, and messages exchanged across the tracked communities of a crypto asset over the last 14 days. This shows the overall level of activity and interaction within these communities.`,
    },
    {
      key: 'indexes',
      label: 'Main Data',
      enabled: true,
      class: isColumnSorted(rank_indicator, true),
      rowClass: isColumnSorted(rank_indicator, true),
      description_mood: `An indicator of the sentiment level in tracked communities of a crypto asset. This uses proprietary methodologies to extract sentiment-related features and natural language processing techniques to quantify and aggregate the mood.<br/>
<br/>
This is a value scaled from 100 without boundaries. A higher score indicates more positive mood and expectations within the tracked communities.`,
      description_trust: `An indicator of the trust level in tracked communities of a crypto asset. This considers diversity, engagement, moderation, consistency, and transparency.<br/>
<br/>
This is a value scaled from 100 without boundaries. A higher score indicates greater trust and confidence within the tracked communities.`,
    },
    {
      key: 'changed',
      label: '1D Change',
      class: isColumnSorted(a_day_changed_percentage),
      rowClass: isColumnSorted(a_day_changed_percentage),
      enabled: true,
      description_mood: `The percentage change in the Mood Index of a crypto asset over the last 24 hours. This shows the short-term expectation shifts in the market for a crypto asset.`,
      description_trust: `The percentage change in the Trust Index of a crypto asset over the last 24 hours. This shows the short-term confidence shifts in the market for a crypto asset.`,
    },
    {
      key: 'chart',
      label: '90D Chart',
      enabled: true,
      description_mood: `The Mood Index trend across the tracked communities of a crypto asset over the last 90 days.`,
      description_trust: `The Trust Index trend across the tracked communities of a crypto asset over the last 90 days.`,
      class: CHART_CLASS_COLUMN,
    },
  ]
})

const tableColumns = computed(() => {
  switch (index.value) {
    case 'mood':
      return moodTrustColumns.value
    case 'trust':
      return moodTrustColumns.value
    case 'topic_analytic':
      return COMMUNITY_TABLE_TOPIC_COLUMN
    default:
      return moodTrustColumns.value
  }
})

const perPage = computed(() => {
  return data.value && 'perPage' in data.value ? data.value.perPage : PER_PAGE
})

const totalItem = computed(() => {
  return data.value && 'totalItem' in data.value ? data.value.totalItem : 0
})

const tableData = computed(() => {
  if (isTopicAnalytic.value) return data.value ?? []

  return data.value && 'data' in data.value ? data.value.data : []
})

const paginationEntriesLabel = computed(() => {
  if (!isTopicAnalytic.value && data.value && 'data' in data.value) {
    const { currentPage, perPage: apiPerPage } = data.value
    const startEntry = (currentPage - 1) * apiPerPage + 1
    const endEntry = startEntry + apiPerPage - 1

    return `Showing ${startEntry} to ${endEntry} of ${totalItem.value} Entries`
  }

  return ''
})

const generateRowNumber = (index: number) => {
  if (!isTopicAnalytic.value && data.value && 'data' in data.value) {
    const { currentPage, perPage } = data.value
    const startEntry = (currentPage - 1) * perPage + 1
    return startEntry + index
  }

  return index + 1
}

const generateRowRoute = (row: any) => {
  if (isTopicAnalytic.value) {
    return `/community/topic/${formatToSlug(row?.topic ?? '')}`
  }

  return `/community/${index.value}/${row?.slug ?? ''}`
}
</script>

<template>
  <CheckFeature
    :allowed="isIndexTableAllowed"
    :loading="isLoading"
    class="p-1"
  >
    <div class="flex flex-col gap-5">
      <UCard>
        <UTableWrapper
          :key="index"
          :pending="isLoading"
          :data="tableData"
          :columns="tableColumns"
        >
          <!-- Empty State -->
          <template #empty-state>
            <EmptyFilter v-if="coinTypes.length === 0" />
            <EmptyData v-else />
          </template>

          <!-- Table Head Number -->
          <template #number-header="head">
            <div class="flex w-full items-center justify-center gap-1">
              <span class="text-left">{{ head.column.label }}</span>
            </div>
          </template>

          <!-- Table Head Name -->
          <template #name-header="head">
            <button class="flex w-full min-w-[6rem] items-center justify-between gap-1">
              <div class="flex items-center gap-2">
                <span class="whitespace-nowrap text-left">{{ head.column.label }}</span>
              </div>
            </button>
          </template>

          <!-- Table Head Community Size -->
          <template #community_size-header="head">
            <button class="flex w-full min-w-[6rem] items-center justify-between gap-1">
              <div class="flex items-center gap-x-1">
                <span class="whitespace-nowrap text-left">{{ head.column.label }}</span>

                <InformationPopover class="mt-1">
                  <TooltipContent>
                    <p>{{ head.column.description }}</p>
                  </TooltipContent>
                </InformationPopover>
              </div>
            </button>
          </template>

          <!-- Table Head Number of Messages -->
          <template #number_of_message-header="head">
            <button class="flex w-full min-w-[6rem] items-center justify-between gap-1">
              <div class="flex items-center gap-x-1">
                <span class="whitespace-nowrap text-left">{{ head.column.label }}</span>

                <InformationPopover class="mt-1">
                  <TooltipContent>
                    <p>{{ head.column.description }}</p>
                  </TooltipContent>
                </InformationPopover>
              </div>
            </button>
          </template>

          <!-- Table Head Main Data -->
          <template #indexes-header="head">
            <button class="flex w-full min-w-[6rem] items-center justify-between gap-1">
              <div class="flex items-center gap-x-1">
                <span class="whitespace-nowrap text-left">{{ isMoodIndex ? 'Mood Index' : 'Trust Index' }}</span>

                <InformationPopover class="mt-1">
                  <TooltipContent>
                    <p v-html="isMoodIndex ? head.column.description_mood : head.column.description_trust" />
                  </TooltipContent>
                </InformationPopover>
              </div>
            </button>
          </template>

          <!-- Table Head Change in Data -->
          <template #changed-header="head">
            <button class="flex w-full min-w-[6rem] items-center justify-between gap-1">
              <div class="flex items-center gap-x-1">
                <span class="whitespace-nowrap text-left">{{
                  isMoodIndex ? 'Mood Index (1d %)' : 'Trust Index (1d %)'
                }}</span>

                <InformationPopover class="mt-1">
                  <TooltipContent>
                    <p>
                      {{ isMoodIndex ? head.column.description_mood : head.column.description_trust }}
                    </p>
                  </TooltipContent>
                </InformationPopover>
              </div>
            </button>
          </template>

          <!-- Table Head Chart Data -->
          <template #chart-header="head">
            <button class="flex w-full min-w-[6rem] items-center justify-between gap-1">
              <div class="flex items-center gap-x-1">
                <span class="whitespace-nowrap text-left">{{
                  isMoodIndex ? 'Mood Chart (90d)' : 'Trust Chart (90d)'
                }}</span>

                <InformationPopover class="mt-1">
                  <TooltipContent>
                    <p>
                      {{ isMoodIndex ? head.column.description_mood : head.column.description_trust }}
                    </p>
                  </TooltipContent>
                </InformationPopover>
              </div>
            </button>
          </template>

          <!-- Table Head Topic Anaytic Name -->
          <template #topic_name-header="head">
            <button class="flex w-full min-w-[6rem] items-center justify-between gap-1">
              <div class="flex items-center gap-1">
                <span class="whitespace-nowrap text-left">{{ head.column.label }}</span>

                <InformationPopover class="mt-1">
                  <TooltipContent>
                    <p v-html="head.column.description" />
                  </TooltipContent>
                </InformationPopover>
              </div>
            </button>
          </template>

          <!-- Table Head Top Platform -->
          <template #platform-header="head">
            <button class="flex w-full min-w-[6rem] items-center justify-between gap-1">
              <div class="flex items-center gap-x-1">
                <span class="whitespace-nowrap text-left">{{ head.column.label }}</span>

                <InformationPopover class="mt-1">
                  <TooltipContent>
                    <p>{{ head.column.description }}</p>
                  </TooltipContent>
                </InformationPopover>
              </div>
            </button>
          </template>

          <!-- Table Head Occurences -->
          <template #occurrences-header="head">
            <button class="flex w-full min-w-[6rem] items-center justify-between gap-1">
              <div class="flex items-center gap-x-1">
                <span class="whitespace-nowrap text-left">{{ head.column.label }}</span>

                <InformationPopover class="mt-1">
                  <TooltipContent>
                    <p>{{ head.column.description }}</p>
                  </TooltipContent>
                </InformationPopover>
              </div>
            </button>
          </template>

          <!-- Table Head Occurences Chart -->
          <template #occurrences_chart-header="head">
            <button class="flex w-full min-w-[6rem] items-center justify-between gap-1">
              <div class="flex items-center gap-x-1">
                <span class="whitespace-nowrap text-left">{{ head.column.label }}</span>

                <InformationPopover class="mt-1">
                  <TooltipContent>
                    <p>{{ head.column.description }}</p>
                  </TooltipContent>
                </InformationPopover>
              </div>
            </button>
          </template>

          <!-- Number Data -->
          <template #number-data="{ row, index }">
            <div class="cursor-pointer">
              <NuxtLink :href="generateRowRoute(row)">
                <p class="text-center">
                  {{ generateRowNumber(index) }}
                </p>
              </NuxtLink>
            </div>
          </template>

          <template #name-data="{ row }">
            <NuxtLink :href="generateRowRoute(row)">
              <CoinDisplayName :coin="row" />
            </NuxtLink>
          </template>

          <template #community_size-data="{ row }">
            <div class="cursor-pointer">
              <NuxtLink :href="generateRowRoute(row)">
                <p>{{ row?.communitySize ?? '-' }}</p>
              </NuxtLink>
            </div>
          </template>

          <template #number_of_message-data="{ row }">
            <div class="cursor-pointer">
              <NuxtLink :href="generateRowRoute(row)">
                <p>{{ row?.numberOfMessages ?? '-' }}</p>
              </NuxtLink>
            </div>
          </template>

          <template #indexes-data="{ row }">
            <div class="cursor-pointer">
              <NuxtLink :href="generateRowRoute(row)">
                <p>{{ row?.value ?? '-' }}</p>
              </NuxtLink>
            </div>
          </template>

          <template #changed-data="{ row }">
            <div
              :class="[
                `flex cursor-pointer items-center gap-2`,
                row.percentageIndicator === '<'
                  ? 'text-accent-red-600 dark:text-accent-red-400'
                  : row.percentageIndicator === '>'
                    ? 'text-accent-green-600 dark:text-accent-green-400'
                    : '',
              ]"
            >
              <NuxtLink
                class="flex items-center gap-2"
                :href="generateRowRoute(row)"
              >
                <img
                  v-if="row.percentageIndicator === '>'"
                  src="~/assets/svg/arrow-triangle-up.svg"
                  :alt="`rank-indicator-up-${row.changed}`"
                  width="16"
                  height="16"
                />
                <img
                  v-else-if="row.percentageIndicator === '<'"
                  src="~/assets/svg/arrow-triangle-down.svg"
                  :alt="`rank-indicator-down-${row.changed}`"
                  width="16"
                  height="16"
                />
                <p>{{ row?.aDayChangePercentage ?? '-' }}</p>
              </NuxtLink>
            </div>
          </template>

          <template #chart-data="{ row }">
            <NuxtLink
              v-if="row?.graph.length > 0"
              :href="generateRowRoute(row)"
            >
              <div class="cursor-pointer">
                <IndexChart
                  class="!h-[30px] !w-full"
                  :type="1"
                  :data="row.graph"
                />
              </div>
            </NuxtLink>
            <div
              v-else
              class="px-3 py-4"
            >
              -
            </div>
          </template>

          <!-- Top Analytics Body -->
          <template #topic_name-data="{ row }">
            <div class="cursor-pointer">
              <NuxtLink :href="generateRowRoute(row)">
                <p>{{ row?.topic ?? '-' }}</p>
              </NuxtLink>
            </div>
          </template>

          <template #platform-data="{ row }">
            <div class="cursor-pointer">
              <NuxtLink :href="generateRowRoute(row)">
                <div class="flex flex-nowrap items-center gap-2">
                  <div
                    v-for="(platform, index) in row.platforms as string[]"
                    :key="`platform-item-${index}`"
                  >
                    <div v-if="platform.toUpperCase() === 'REDDIT'">
                      <img
                        src="@/assets/svg/reddit-original.svg"
                        width="24"
                      />
                    </div>

                    <div v-if="platform.toUpperCase() === 'TELEGRAM'">
                      <img
                        src="@/assets/svg/telegram-original.svg"
                        width="24"
                      />
                    </div>

                    <div v-if="platform.toUpperCase() === 'X'">
                      <img
                        class="hidden dark:block"
                        src="@/assets/svg/twitter-x.svg"
                        width="24"
                      />
                      <img
                        class="dark:hidden"
                        src="@/assets/svg/twitter-x-light.svg"
                        width="24"
                      />
                    </div>
                  </div>
                </div>
              </NuxtLink>
            </div>
          </template>

          <template #occurrences-data="{ row }">
            <div class="cursor-pointer">
              <NuxtLink :href="generateRowRoute(row)">
                <p>{{ row?.occurrences ?? '-' }}</p>
              </NuxtLink>
            </div>
          </template>

          <template #occurrences_chart-data="{ row }">
            <NuxtLink
              v-if="row?.graph.length > 0"
              :href="generateRowRoute(row)"
            >
              <div class="cursor-pointer">
                <IndexChart
                  class="!h-[30px] !w-full"
                  :type="1"
                  :data="row.graph"
                />
              </div>
            </NuxtLink>
            <div
              v-else
              class=""
            >
              -
            </div>
          </template>
        </UTableWrapper>
      </UCard>

      <div
        v-if="!isTopicAnalytic && totalItem > 0"
        class="flex flex-col-reverse items-center justify-center gap-6 text-center md:flex-row md:justify-between"
      >
        <p class="text-neutrals-800 text-sm font-medium dark:text-white">
          {{ paginationEntriesLabel }}
        </p>
        <UPagination
          v-model="page"
          :page-count="perPage"
          :total="totalItem"
          :max="isMobileDevice() ? 7 : 10"
          :disabled="isLoading"
          :active-button="{
            color: 'black',
          }"
        />
      </div>
    </div>
  </CheckFeature>
</template>
