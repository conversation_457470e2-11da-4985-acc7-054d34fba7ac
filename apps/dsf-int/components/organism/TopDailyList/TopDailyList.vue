<script setup lang="ts">
import type { paths } from '~/types/schema'

type GetTopAssetRankPayload =
  paths['/nodiens/api/v1/asset/top-rank']['get']['responses']['200']['content']['application/json']['payload']
type TopAssetRankType = keyof Omit<GetTopAssetRankPayload, 'lastCalculation'>

const props = withDefaults(
  defineProps<{
    type: TopAssetRankType
    data?: GetTopAssetRankPayload
    loading?: boolean
  }>(),
  {
    loading: false,
    data: undefined,
  },
)

const title = computed(() => {
  switch (props.type) {
    case 'mood':
      return 'Mood Index'
    case 'trust':
      return 'Trust Index'
    case 'topicAnalytics':
      return 'Topic Trends'
  }
})

const navigateLink = computed(() => {
  const value = props.type === 'topicAnalytics' ? 'topic_analytic' : props.type
  return `/community?index=${value}`
})
</script>
<template>
  <div class="flex-1">
    <NuxtLink
      :to="navigateLink"
      class="flex items-center justify-between gap-2"
    >
      <h6 class="text-neutrals-800 whitespace-nowrap text-xl dark:text-white">
        {{ title }}
      </h6>
      <USkeleton
        v-if="props.loading"
        class="h-5 w-7"
      />
      <ClientOnly v-else>
        <UButton
          :padded="false"
          color="gray"
          variant="link"
          icon="i-heroicons-arrow-right"
        />
      </ClientOnly>
    </NuxtLink>
    <div class="mt-4 flex flex-col gap-4">
      <div
        v-if="props.loading"
        class="flex flex-col gap-4"
      >
        <USkeleton
          v-for="item in 3"
          :key="`top-daily-${props.type}-${item}`"
          class="h-5 w-full"
        />
      </div>
      <div
        v-for="(item, index) in props.data?.[props.type] as GetTopAssetRankPayload['mood']"
        v-else
        :key="`rank-group-${index}`"
      >
        <NuxtLink
          class="flex w-full items-center justify-between"
          :to="
            props.type === 'topicAnalytics'
              ? `/community/topic/${item.slug}`
              : item.type === 'CRYPTOCURRENCY'
                ? `/crypto/${item.slug}`
                : `/stablecoin/${item.slug}`
          "
        >
          <div
            class="font-base text-neutrals-800 flex flex-1 cursor-pointer items-center gap-1 md:gap-2 dark:text-white"
          >
            <span>{{ index + 1 }}.</span>
            <CoinDisplayName
              :coin="item"
              truncate
            />
          </div>

          <div
            v-if="'indicator' in item && 'percentage' in item"
            class="flex items-center gap-2"
          >
            <img
              v-if="item.indicator === '>'"
              src="~/assets/svg/arrow-triangle-up.svg"
              :alt="`rank-indicator-up-${index}`"
              width="16"
              height="16"
            />
            <img
              v-else
              src="~/assets/svg/arrow-triangle-down.svg"
              :alt="`rank-indicator-down-${index}`"
              width="16"
              height="16"
            />
            <span
              :class="[
                'whitespace-nowrap',
                item.indicator === '>'
                  ? 'text-accent-green-600 dark:text-accent-green-400'
                  : 'text-accent-red-600 dark:text-accent-red-400',
              ]"
              >{{ item.percentage }}</span
            >
          </div>
        </NuxtLink>
      </div>
    </div>
  </div>
</template>
