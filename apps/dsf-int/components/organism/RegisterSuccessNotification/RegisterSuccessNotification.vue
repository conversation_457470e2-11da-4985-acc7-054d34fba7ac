<script setup lang="ts">
const props = defineProps<{
  email: string
}>()
</script>

<template>
  <div class="mt-24 flex flex-col items-center justify-center gap-4">
    <img
      class="hidden dark:block"
      src="~@/assets/svg/email-icon.svg"
      width="110"
    >
    <img
      class="dark:hidden"
      src="~@/assets/svg/email-icon-light.svg"
      width="110"
    >

    <h2 class="text-center text-3xl font-medium">
      Almost there!
    </h2>

    <p class="max-w-[500px] text-center">
      We have sent you an email to <strong>{{ props.email }}</strong> to verify your account.
    </p>
    <p class="max-w-[500px] text-center">
      Please follow the instructions in the email to finish the
      registration.
    </p>
  </div>
</template>
