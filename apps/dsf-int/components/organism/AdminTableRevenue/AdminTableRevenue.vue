<script setup lang="ts">
import type { TRPCError } from '@trpc/server'
import { isMobileDevice } from '~/helper/utilities-helper'

const columns = [
  {
    key: 'number',
    label: '#',
    isVisible: true,
    enabled: true,
    description: '',
  },
  {
    key: 'name',
    label: 'Name',
  },
  {
    key: 'plan',
    label: 'Current Plan',
  },
  {
    key: 'total_revenue',
    label: 'Total Revenue',
  },
]

const toast = useToast()
const route = useRoute()
const { $client } = useNuxtApp()

const queryString = computed(() => ({
  page: Number(route.query.revenueByUsersPage ?? 1),
}))

const pending = computed(() => status.value === 'pending')

const perPage = ref(Number(route.query.perPage ?? 10))

const page = computed({
  get: () => Number(route.query.revenueByUsersPage ?? 1),
  set: async (page) =>
    await navigateTo({
      query: {
        ...route.query,
        revenueByUsersPage: page,
      },
    }),
})

const { data, status } = useLazyAsyncData(
  async () => {
    try {
      return await $client.admin.v1.dashboard.revenueHistory.query({
        page: queryString.value?.page,
      })
    } catch (e) {
      const _e = e as TRPCError
      toast.add({
        title: 'Oops!',
        color: 'red',
        description: _e.message ?? 'Something went wrong. Please try again later.',
        icon: 'i-heroicons-exclamation-triangle-20-solid',
      })
      return null
    }
  },
  {
    watch: [page],
  },
)

const totalItems = computed(() => data.value?.totalItem ?? 0)

const paginationEntriesLabel = computed(() => {
  const startEntry = (page.value - 1) * perPage.value + 1
  let endEntry = page.value * perPage.value
  endEntry = Math.min(endEntry, totalItems.value)

  return `Showing ${startEntry} to ${endEntry} of ${totalItems.value} Entries`
})

function generateRowNumber(index: number) {
  const startEntry = (page.value - 1) * perPage.value + 1
  const rowNumber = startEntry + index
  return rowNumber
}
</script>

<template>
  <div class="mb-6">
    <p class="mb-1 mb-5 text-2xl font-bold">Revenue by Users</p>

    <UCard>
      <UTableWrapper
        :columns="columns"
        :data="data?.data"
        :pending="pending"
      >
        <template #number-data="{ index }">
          <p class="font-regular">{{ generateRowNumber(index) }}</p>
        </template>
      </UTableWrapper>
      <div
        v-if="totalItems > 0"
        class="m-4 flex flex-col-reverse items-center justify-center gap-6 text-center md:flex-row md:justify-between"
      >
        <p class="text-neutrals-800 text-sm dark:text-white">
          {{ paginationEntriesLabel }}
        </p>
        <UPagination
          v-model="page"
          :page-count="perPage"
          :total="totalItems"
          :max="isMobileDevice() ? 7 : 10"
          :disabled="false"
          :active-button="{
            color: 'sky',
          }"
        />
      </div>
    </UCard>
  </div>
</template>
