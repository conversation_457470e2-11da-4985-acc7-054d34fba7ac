<script setup lang="ts">
// Stores
import { useSessionStore } from '~/stores/session'
import { isMobileDevice } from '~/helper/utilities-helper'
import { useUserStore } from '~/stores/profile'

interface NavbarProps {
  dashboard: boolean
  hideAuthButton?: boolean
}

defineProps<NavbarProps>()

const { $colorMode, $analytics } = useNuxtApp()
const sessionStore = useSessionStore()
const { userProfile } = useUserStore()
const route = useRoute()

// User with role `admin` or `super-admin` should be marked as admin
const isAdmin = computed(() => userProfile?.role?.toLowerCase() !== 'user')

const isAdminRoute = computed(() => route.path.includes('/admin'))

const isExpanded = ref(false)
const dropdownItems = computed(() => {
  const menu = [
    [
      {
        label: 'Settings',
        slot: 'settings',
        href: '/settings/profile',
        click: () => {
          setCollapsed(false)
        },
      },

      ...(isAdmin.value
        ? [
            {
              label: 'Admin',
              slot: 'admin',
              href: '/admin',
              click: () => {},
            },
          ]
        : []),

      {
        label: 'Sign Out',
        click: () => {
          setCollapsed(false)
          handleSignOut()
        },
      },
    ],
  ]

  return menu
})

function setCollapsed(force?: boolean): boolean {
  if (force !== undefined) {
    return (isExpanded.value = force)
  }

  isExpanded.value = !isExpanded.value
  return isExpanded.value
}

async function handleSignOut() {
  const response = await sessionStore.cleanUpClientSession()

  if (response) {
    // Reset analytics current user
    $analytics.reset()

    return navigateTo('/')
  }
}

const darkMode = computed({
  get() {
    return $colorMode.value === 'dark'
  },
  set(value) {
    if (value) {
      $colorMode.preference = 'dark'
    } else {
      $colorMode.preference = 'light'
    }
  },
})

watch(isExpanded, (newVal) => {
  //to prevent page scrolling when the mobile menu nav is open
  if (newVal) {
    document.body.style.overflow = 'hidden'
  } else {
    document.body.style.overflow = 'auto'
  }
})

const isEsgRoute = computed(() => {
  const ESG_ROUTES = ['/climate', '/esg/decentralisation']
  if (ESG_ROUTES.includes(route.path)) {
    return isMobileDevice() ? 'border-b-2 border-secondary-brand-500' : 'border-b-2 border-base-primary-500'
  }
  return ''
})

const isCommunity = computed(() => {
  const COMMUNITY_ROUTES = ['/community']
  if (COMMUNITY_ROUTES.includes(route.path)) {
    return isMobileDevice() ? 'border-b-2 border-secondary-brand-500' : 'border-b-2 border-base-primary-500'
  }
  return ''
})
</script>

<template>
  <div
    class="border-neutrals-200 dark:border-neutrals-700 dark:bg-neutrals-900 sticky top-0 z-20 flex w-full items-center justify-between border-b bg-white p-5 dark:border-0"
  >
    <NuxtLink
      to="/"
      class="flex items-center justify-center gap-x-3"
    >
      <!-- Temporary Logo for Demo -->
      <ColorScheme>
        <img
          v-if="darkMode"
          src="~/assets/media/nodiens-logo-white.webp"
          alt="logo"
          width="195"
          height="50"
          class="h-[40px] w-auto"
        />
        <img
          v-if="!darkMode"
          src="~/assets/media/nodiens-logo-black.webp"
          alt="logo"
          width="195"
          height="50"
          class="h-[40px] w-auto"
        />
      </ColorScheme>
      <span class="block bg-[#037823] p-1 text-sm font-semibold text-white md:p-1.5 md:text-base"> BETA </span>
    </NuxtLink>

    <DesktopNav
      :is-expanded="isExpanded"
      :set-collapsed="setCollapsed"
      :dashboard="dashboard"
      :dropdown-items="dropdownItems"
      :hide-auth-button="hideAuthButton"
      :is-esg-route="isEsgRoute"
      :is-community-route="isCommunity"
    />

    <UButton
      class="block lg:hidden"
      :icon="isExpanded ? 'i-mdi-close' : 'i-mdi-menu'"
      variant="ghost"
      color="transparent"
      @click="isExpanded = !isExpanded"
    />

    <AdminMobileNav
      v-if="isAdmin && isAdminRoute"
      :is-expanded="isExpanded"
      :set-collapsed="setCollapsed"
      :dashboard="dashboard"
      :dropdown-items="dropdownItems"
      :hide-auth-button="hideAuthButton"
      :is-esg-route="isEsgRoute"
    />

    <MobileNav
      v-else
      :is-expanded="isExpanded"
      :set-collapsed="setCollapsed"
      :dashboard="dashboard"
      :dropdown-items="dropdownItems"
      :hide-auth-button="hideAuthButton"
      :is-esg-route="isEsgRoute"
    />
  </div>
</template>
