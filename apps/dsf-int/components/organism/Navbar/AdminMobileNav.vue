<script
  setup
  lang="ts"
>

import { useAdminMenu } from "~/constants/admin-menu";

const { adminMenu } = useAdminMenu()

const props = defineProps<{
  isExpanded: boolean;
  setCollapsed: (isExpanded: boolean) => void;
}>()

const route = useRoute();

watch(() => route.path, () => props.setCollapsed(false))
</script>

<template>
  <div
    class="transition-all duration-300 overflow-hidden absolute z-[100] w-full inset-0 top-[80px]"
    :class="props.isExpanded ? 'h-svh' : 'h-0'"
  >
    <div class="dark:bg-black flex flex-col bg-white p-4 h-full">
      <UVerticalNavigation
        :links="adminMenu"
        :ui="{
          padding: 'p-5',
          active: 'dark:bg-white dark:before:bg-white dark:text-neutrals-800 bg-neutrals-800 before:bg-neutrals-800 text-white',
          icon: {
            active: 'dark:text-neutrals-800 text-white'
          }
        }"
      />
    </div>
  </div>
</template>
