<script setup lang="ts">
import { useFeatureFlags } from '~/stores/feature-flags/index.js'
import { useSessionStore } from '~/stores/session/index.js'
import { useUserStore } from '~/stores/profile/index.js'

const props = defineProps<{
  isExpanded: boolean
  setCollapsed: (isExpanded: boolean) => void
  dashboard: boolean
  hideAuthButton: boolean | undefined
  dropdownItems: {
    label: string
    click: () => void
  }[][]
  isEsgRoute: string
  isCommunityRoute: string
}>()

const router = useRouter()
const ffStore = useFeatureFlags()
const sessionStore = useSessionStore()
const userStore = useUserStore()
const { isFeatureEnabled } = ffStore
const { userProfile } = storeToRefs(userStore)
const { $colorMode } = useNuxtApp()

const darkMode = computed({
  get() {
    return $colorMode.value === 'dark'
  },
  set(value) {
    if (value) {
      $colorMode.preference = 'dark'
    } else {
      $colorMode.preference = 'light'
    }
  },
})

const navigateToCommunity = (community: string) => {
  router.push(`/community?index=${community}&page=1`)
}
</script>

<template>
  <div
    v-if="props.dashboard"
    class="hidden items-center gap-5 lg:flex"
  >
    <NuxtLink
      to="/"
      class="text-base font-medium text-black dark:text-white"
      active-class="border-b-2 border-base-primary-500"
    >
      Home
    </NuxtLink>
    <NuxtLink
      v-if="isFeatureEnabled('knowledge_indices')"
      to="/technology/knowledge"
      class="text-base font-medium text-black dark:text-white"
      active-class="border-b-2 border-base-primary-500"
    >
      Technology
    </NuxtLink>
    <UDropdown
      mode="click"
      :popper="{
        placement: 'bottom-start',
      }"
      :items="[
        [
          {
            label: 'Mood',
            click: () => navigateToCommunity('mood'),
          },
          {
            label: 'Trust',
            click: () => navigateToCommunity('trust'),
          },
          {
            label: 'Topic Trends',
            click: () => navigateToCommunity('topic_analytic'),
          },
        ],
      ]"
    >
      <span
        class="flex items-center gap-1 text-base font-medium text-black dark:text-white"
        :class="props.isCommunityRoute"
      >
        Community
        <UIcon
          name="i-material-symbols-keyboard-arrow-down"
          class="h-5 w-5"
        />
      </span>
    </UDropdown>
    <NuxtLink
      to="/financial"
      class="text-base font-medium text-black dark:text-white"
      active-class="border-b-2 border-base-primary-500"
    >
      Financial
    </NuxtLink>

    <UDropdown
      mode="click"
      :popper="{
        placement: 'bottom-start',
      }"
      :items="[
        [
          {
            label: 'Climate',
            click: () => router.push('/climate'),
          },
          {
            label: 'Decentralization',
            click: () => router.push('/esg/decentralisation'),
          },
        ],
      ]"
    >
      <span
        class="flex items-center gap-1 text-base font-medium text-black dark:text-white"
        :class="props.isEsgRoute"
      >
        ESG
        <UIcon
          name="i-material-symbols-keyboard-arrow-down"
          class="h-5 w-5"
        />
      </span>
    </UDropdown>
  </div>

  <div class="hidden items-center justify-end lg:flex lg:gap-4">
    <div class="flex items-center gap-2">
      <UIcon
        name="i-heroicons-sun"
        class="text-neutrals-300 text-xl"
      />
      <UToggle v-model="darkMode" />
      <UIcon
        name="i-heroicons-moon"
        class="text-neutrals-300 text-xl"
      />
    </div>

    <UDropdown
      v-if="sessionStore.isLogged"
      :items="props.dropdownItems"
      :popper="{ placement: 'bottom-end' }"
    >
      <UButton
        truncate
        class="max-w-[200px] text-black hover:bg-white dark:text-white dark:hover:bg-black"
        :label="userProfile?.displayName ?? ''"
        trailing-icon="i-heroicons-chevron-down-20-solid"
        variant="ghost"
      />
    </UDropdown>

    <div
      v-else-if="!props.hideAuthButton"
      class="flex items-center justify-center gap-4"
    >
      <NuxtLink to="/auth/login">
        <UButton
          size="lg"
          label="Login"
          color="black"
          variant="outline"
        />
      </NuxtLink>
      <NuxtLink to="/auth/register">
        <UButton
          size="lg"
          label="Register"
          color="black"
        />
      </NuxtLink>
    </div>
  </div>
</template>
