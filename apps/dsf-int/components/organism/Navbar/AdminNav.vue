<template>
  <div class="dark:bg-black bg-white border-none p-8 min-h-full border-r lg:block hidden">
    <UVerticalNavigation
      :links="adminMenu"
      :ui="{
        padding: 'p-5',
        active: 'dark:bg-white dark:before:bg-white dark:text-neutrals-800 bg-neutrals-800 before:bg-neutrals-800 text-white',
        icon: {
          active: 'dark:text-neutrals-800 text-white'
        }
      }"
    />
  </div>
</template>
<script
  setup
  lang="ts"
>

import { useAdminMenu } from "~/constants/admin-menu";

const { adminMenu } = useAdminMenu()

</script>