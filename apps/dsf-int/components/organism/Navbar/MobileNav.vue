<script setup lang="ts">
import { useFeatureFlags } from '~/stores/feature-flags'
import { useSessionStore } from '~/stores/session'
import { useUserStore } from '~/stores/profile'

const props = defineProps<{
  isExpanded: boolean
  setCollapsed: (isExpanded: boolean) => void
  dashboard: boolean
  hideAuthButton: boolean | undefined
  dropdownItems: {
    label: string
    click: () => void
  }[][]
  isEsgRoute: string
}>()

const ffStore = useFeatureFlags()
const sessionStore = useSessionStore()
const userStore = useUserStore()
const { isFeatureEnabled } = ffStore
const { userProfile } = storeToRefs(userStore)
const { $colorMode } = useNuxtApp()
const route = useRoute()

const darkMode = computed({
  get() {
    return $colorMode.value === 'dark'
  },
  set(value) {
    if (value) {
      $colorMode.preference = 'dark'
    } else {
      $colorMode.preference = 'light'
    }
  },
})

watch(
  () => route.path,
  () => props.setCollapsed(false),
)

const communityItems = [
  {
    id: 'mood',
    label: 'Mood',
  },
  {
    id: 'trust',
    label: 'Trust',
  },
  {
    id: 'topic_analytic',
    label: 'Topic Trends',
  },
]
</script>

<template>
  <div
    class="absolute inset-0 top-[80px] z-[100] w-full overflow-hidden transition-all duration-300"
    :class="props.isExpanded ? 'h-svh' : 'h-0'"
  >
    <div class="dark:bg-neutrals-900 flex h-full flex-col bg-white p-4">
      <NuxtLink
        to="/"
        class="py-3 text-base font-medium text-black dark:text-white"
        @click="props.setCollapsed"
      >
        Home
      </NuxtLink>
      <NuxtLink
        v-if="isFeatureEnabled('knowledge_indices')"
        to="/technology/knowledge"
        class="py-3 text-base font-medium text-black dark:text-white"
      >
        Technology
      </NuxtLink>
      <UAccordion
        class="py-3"
        :items="[
          {
            label: 'Community',
          },
        ]"
      >
        <template #default="{ item, open }">
          <UButton
            color="transparent"
            variant="ghost"
            class="border-0 border-gray-200 p-0 dark:border-gray-700"
            :ui="{
              rounded: 'rounded-none',
            }"
          >
            <div class="flex items-center gap-4">
              <span class="text-base font-medium text-black dark:text-white">{{ item.label }}</span>
              <UIcon
                name="i-heroicons-chevron-right-20-solid"
                class="ms-auto h-5 w-5 transform transition-transform duration-200"
                :class="[open && 'rotate-90']"
              />
            </div>
          </UButton>
        </template>
        <template #item>
          <div
            v-for="item in communityItems"
            :key="item.id"
          >
            <NuxtLink
              :to="`/community?index=${item.id}&page=1`"
              class="block py-3 pl-8 text-base font-medium text-black dark:text-white"
              @click="props.setCollapsed(false)"
            >
              {{ item.label }}
            </NuxtLink>
          </div>
        </template>
      </UAccordion>
      <NuxtLink
        to="/financial"
        class="py-3 text-base font-medium text-black dark:text-white"
        @click="props.setCollapsed"
      >
        Financial
      </NuxtLink>
      <UAccordion
        class="py-3"
        :items="[
          {
            label: 'ESG',
          },
        ]"
      >
        <template #default="{ item, open }">
          <UButton
            color="transparent"
            variant="ghost"
            class="border-0 border-gray-200 p-0 dark:border-gray-700"
            :ui="{
              rounded: 'rounded-none',
            }"
          >
            <div class="flex items-center gap-4">
              <span class="text-base font-medium text-black dark:text-white">{{ item.label }}</span>
              <UIcon
                name="i-heroicons-chevron-right-20-solid"
                class="ms-auto h-5 w-5 transform transition-transform duration-200"
                :class="[open && 'rotate-90']"
              />
            </div>
          </UButton>
        </template>
        <template #item>
          <NuxtLink
            to="/climate"
            class="block py-3 pl-8 text-base font-medium text-black dark:text-white"
            @click="props.setCollapsed"
          >
            Climate
          </NuxtLink>
          <NuxtLink
            to="/esg/decentralisation"
            class="block py-3 pl-8 text-base font-medium text-black dark:text-white"
            @click="props.setCollapsed"
          >
            Decentralization
          </NuxtLink>
        </template>
      </UAccordion>

      <div class="flex items-center gap-2 py-3">
        <UIcon name="i-mdi-white-balance-sunny" />
        <UToggle v-model="darkMode" />
        <UIcon name="i-mdi-weather-night" />
      </div>
      <UDropdown
        v-if="props.dashboard && sessionStore.isLogged"
        :items="props.dropdownItems"
        :popper="{ placement: 'bottom-start' }"
      >
        <UButton
          truncate
          class="max-w-[200px] pl-0 text-base font-medium text-black dark:text-white"
          :label="userProfile?.displayName ?? ''"
          trailing-icon="i-heroicons-chevron-down-20-solid"
          variant="ghost"
        />
      </UDropdown>
      <div
        v-else-if="!props.hideAuthButton"
        class="flex items-center justify-start gap-4 py-3"
      >
        <NuxtLink to="/auth/login">
          <UButton
            size="lg"
            label="Sign In"
            color="black"
            variant="outline"
          />
        </NuxtLink>
        <NuxtLink to="/auth/register">
          <UButton
            size="lg"
            label="Sign Up"
            color="black"
          />
        </NuxtLink>
      </div>
    </div>
  </div>
</template>
