<script setup lang="ts">
import type { TRPCError } from '@trpc/server'
import { isMobileDevice } from '~/helper/utilities-helper'
import { add, format } from 'date-fns'

const toast = useToast()
const route = useRoute()
const { $client } = useNuxtApp()

const queryString = computed(() => ({
  page: Number(route.query.assetPopularityPage ?? 1),
  terms: (route.query.assetPopularityTerms ?? undefined) as string | undefined,
  startDate: (route.query.assetPopularityStartDate ?? undefined) as string | undefined,
  endDate: (route.query.assetPopularityEndDate ?? undefined) as string | undefined,
}))

const dateRange = computed({
  get: () => {
    const noDateFilter = !queryString.value?.startDate && !queryString.value?.endDate
    if (noDateFilter) {
      return {
        start: new Date(),
        end: add(new Date(), { days: 7 }),
      }
    }
    return {
      start: new Date(+queryString.value?.startDate!),
      end: new Date(+queryString.value?.endDate!),
    }
  },
  set: async (newVal) => {
    await navigateTo({
      query: {
        ...route.query,
        assetPopularityStartDate: format(+newVal.start, 'T'),
        assetPopularityEndDate: format(+newVal.end, 'T'),
      },
    })
  },
})

const search = computed({
  get: () => route.query.assetPopularityTerms as string,
  set: async (newVal) => {
    await navigateTo({
      query: {
        ...route.query,
        assetPopularityTerms: newVal,
      },
    })
  },
})

const columns = [
  {
    key: 'number',
    label: '#',
    isVisible: true,
    enabled: true,
    description: '',
  },
  {
    key: 'name',
    label: 'Asset',
  },
  {
    key: 'view',
    label: 'Views',
  },
  {
    key: 'percentage',
    label: 'Percentage',
  },
]

const pending = computed(() => status.value === 'pending')

const perPage = ref(Number(route.query.perPage ?? 10))

const page = computed({
  get: () => Number(route.query.assetPopularityPage ?? 1),
  set: async (page) =>
    await navigateTo({
      query: {
        ...route.query,
        assetPopularityPage: page,
      },
    }),
})

const totalItems = computed(() => data.value?.totalItem ?? 0)

const paginationEntriesLabel = computed(() => {
  const startEntry = (page.value - 1) * perPage.value + 1
  let endEntry = page.value * perPage.value
  endEntry = Math.min(endEntry, totalItems.value)

  return `Showing ${startEntry} to ${endEntry} of ${totalItems.value} Entries`
})

function generateRowNumber(index: number) {
  const startEntry = (page.value - 1) * perPage.value + 1
  const rowNumber = startEntry + index
  return rowNumber
}

const { data, status } = useLazyAsyncData(
  async () => {
    try {
      return await $client.admin.v1.dashboard.assetPopularity.query({
        page: queryString.value?.page,
        terms: queryString.value?.terms,
        startDate: queryString.value?.startDate ? format(+queryString.value?.startDate, 'yyyy-MM-dd') : undefined,
        endDate: queryString.value?.endDate ? format(+queryString.value?.endDate, 'yyyy-MM-dd') : undefined,
      })
    } catch (e) {
      const _e = e as TRPCError
      toast.add({
        title: 'Oops!',
        color: 'red',
        description: _e.message ?? 'Something went wrong. Please try again later.',
        icon: 'i-heroicons-exclamation-triangle-20-solid',
      })
      return null
    }
  },
  {
    watch: [page, search, dateRange],
  },
)
</script>

<template>
  <div class="mb-6">
    <div class="flex justify-between">
      <p class="mb-1 mb-5 text-2xl font-bold">Popularity Rankings</p>
      <AdminUserListFilterDateRange v-model="dateRange" />
    </div>
    <UCard>
      <!--      <UInput-->
      <!--        v-model="search"-->
      <!--        variant="outline"-->
      <!--        trailing-icon="i-heroicons-magnifying-glass"-->
      <!--        class="order-3 mx-4 mt-4 w-full sm:order-2 sm:w-fit"-->
      <!--        placeholder="Search Assets"-->
      <!--      />-->
      <UTable
        :columns="columns"
        :rows="data?.data"
        :loading="pending"
      >
        <template #number-data="{ index }">
          <p class="font-regular">{{ generateRowNumber(index) }}</p>
        </template>
      </UTable>
      <div
        v-if="totalItems > 0"
        class="m-4 flex flex-col-reverse items-center justify-center gap-6 text-center md:flex-row md:justify-between"
      >
        <p class="text-neutrals-800 text-sm dark:text-white">
          {{ paginationEntriesLabel }}
        </p>
        <UPagination
          v-model="page"
          :page-count="perPage"
          :total="totalItems"
          :max="isMobileDevice() ? 7 : 10"
          :disabled="false"
          :active-button="{
            color: 'sky',
          }"
        />
      </div>
    </UCard>
  </div>
</template>
