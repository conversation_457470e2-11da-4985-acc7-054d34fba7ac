<script setup lang="ts">
const { $colorMode } = useNuxtApp()

const isDarkMode = computed(() => $colorMode.value === 'dark')

defineEmits<{
  (e: 'onConnectWallet'): void
}>()
</script>

<template>
  <div class="flex flex-col gap-3">
    <h2 class="mb-2 text-2xl font-semibold">Register</h2>
    <p class="text-neutrals-800 mb-5 text-sm dark:text-white">
      Complete the registration process using your Hedera wallet.
    </p>
    <UButton
      :color="isDarkMode ? 'black' : 'white'"
      icon="i-mdi-wallet-bifold-outline"
      size="md"
      variant="solid"
      label="Continue with Hedera Wallet"
      @click="$emit('onConnectWallet')"
      block
    />
  </div>
</template>
