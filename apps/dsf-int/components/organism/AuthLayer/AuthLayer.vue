<template>
  <div class="absolute bottom-0 z-10 flex h-fit w-full flex-col justify-end px-3">
    <div
      class="flex flex-col items-center gap-6 bg-gradient-to-t from-white via-white to-transparent py-32 pb-20 dark:from-black dark:via-black"
    >
      <h3 class="text-neutrals-800 text-center text-2xl font-bold md:text-4xl dark:text-white">
        Unlock the full potential! <br />
        Sign in or register to explore further insights
      </h3>

      <div class="flex items-center justify-center gap-4">
        <NuxtLink to="/auth/login">
          <UButton size="lg" label="Login" color="black" variant="outline" />
        </NuxtLink>
        <NuxtLink to="/auth/register">
          <UButton size="lg" label="Signup" color="black" />
        </NuxtLink>
      </div>
    </div>
  </div>
</template>
