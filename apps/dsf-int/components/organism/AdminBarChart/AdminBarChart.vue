<script setup lang="ts">
import { LineStyle, PriceScaleMode } from 'lightweight-charts'
import { numericDisplay } from '~/helper/number-helper'

const props = defineProps<{
  title: string
  height?: number
  labels: string[]
  datasets: {
    color: string
    value: number
  }[]
}>()

const { $colorMode } = useNuxtApp()

const chartRef = ref<HTMLElement>()

const { chart: chartInstance, tools, addHistogramSeries, renderChart, destroyChart } = useLightChart()

const darkMode = computed({
  get() {
    return $colorMode.value === 'dark'
  },
  set(value) {
    if (value) {
      $colorMode.preference = 'dark'
    } else {
      $colorMode.preference = 'light'
    }
  },
})

const initializeChart = () => {
  if (chartInstance.value === undefined) {
    renderChart(
      chartRef,
      {
        height: props.height ?? 400,
        grid: {
          horzLines: {
            color: '#C3C3C3',
            style: LineStyle.Dashed,
          },
          vertLines: {
            visible: false,
          },
        },
        crosshair: {
          horzLine: {
            visible: false,
            labelVisible: false,
          },
          vertLine: {
            labelVisible: false,
          },
        },
        leftPriceScale: {
          visible: false,
          borderVisible: false,
          entireTextOnly: true,
          scaleMargins: {
            bottom: 0.01,
          },
        },
        rightPriceScale: {
          visible: true,
          mode: PriceScaleMode.Normal,
          borderVisible: false,
          entireTextOnly: true,
          scaleMargins: {
            bottom: 0.1,
          },
          minimumWidth: 70,
        },
        timeScale: {
          visible: true,
          borderVisible: false,
          borderColor: 'rgba(197, 203, 206, 1)',
          fixLeftEdge: true,
          fixRightEdge: true,
          lockVisibleTimeRangeOnResize: true,
          tickMarkFormatter: (index: number) => props.labels[index] ?? index,
          barSpacing: 50,
          minBarSpacing: 50,
        },
        handleScale: {
          pinch: false,
          mouseWheel: false,
          axisPressedMouseMove: false,
        },
        handleScroll: {
          mouseWheel: false,
          vertTouchDrag: false,
          horzTouchDrag: false,
          pressedMouseMove: false,
        },
        localization: {
          priceFormatter: (price: number) => numericDisplay(price),
        },
      },
      darkMode.value
    )
  }

  if (props.datasets.length > 0) {
    const data = props.datasets.map(({ value, color }, index) => ({
      time: index,
      value,
      color,
    }))

    const series = addHistogramSeries('revenue', {
      priceScaleId: 'right',
      lastValueVisible: false,
      priceLineVisible: false,
      base: 0,
      color: 'rgba(0,163,255,1)',
    })

    series?.setData(data as any)
  }

  tools.reArrangeChart({ autosize: true })
}

watch(darkMode, value => {
  tools.setDark(value)
})

onMounted(() => {
  if (import.meta.client) {
    setTimeout(() => {
      initializeChart()
    }, 50)
  }
})

onBeforeUnmount(() => {
  destroyChart()
})
</script>

<template>
  <UCard class="min-h-[400px]">
    <h3>{{ title }}</h3>

    <ClientOnly>
      <div ref="chartRef" class="w-full" />
    </ClientOnly>
  </UCard>
</template>
