<script setup>
import { format } from 'date-fns'
import { HTTP_STATUS } from '~/constants/error'

const { $apiClient } = useNuxtApp()
const toast = useToast()

const { data, status } = useLazyAsyncData(
  'topDailySummary',
  async () => {
    try {
      const { response, data, error } = await $apiClient.GET('/nodiens/api/v1/asset/top-rank')

      if (response.status === HTTP_STATUS.INTERNAL_SERVER_ERROR) {
        throw error
      }

      return data?.payload || null
    } catch {
      toast.add({
        title: 'Error',
        color: 'red',
        description: 'Something went wrong. Please try again later.',
        icon: 'i-heroicons-exclamation-triangle-20-solid',
      })
      return null
    }
  },
  { server: false },
)

const isLoading = computed(() => status.value === 'pending')

const lastCalculation = computed(() =>
  data.value?.lastCalculation ? format(data.value.lastCalculation, 'MMM dd, yyyy') : '-',
)
</script>
<template>
  <UCard>
    <!-- Header -->
    <div class="flex flex-col justify-between gap-2 md:flex-row md:items-center">
      <h5 class="text-neutrals-800 text-2xl font-bold dark:text-white">Daily Summary</h5>
      <USkeleton
        v-if="isLoading"
        class="h-5 w-20"
      />
      <p
        v-else
        class="text-neutrals-800 text-base font-semibold dark:text-white"
      >
        {{ lastCalculation }}
      </p>
    </div>

    <!-- Body -->
    <ClientOnly v-if="!isLoading && !data">
      <NoDataAvailable class="mt-16 text-center text-xl" />
    </ClientOnly>
    <div
      v-else
      class="no-scrollbar mt-8 h-full overflow-x-scroll md:mt-4"
    >
      <div class="flex w-full flex-col justify-between gap-8 md:flex-row md:gap-8">
        <TopDailyList
          type="mood"
          :data="data"
          :loading="isLoading"
          class="border-neutrals-200 border-b pb-8 md:border-b-0 md:border-r md:pb-0 md:pr-8"
        />
        <TopDailyList
          type="trust"
          :data="data"
          :loading="isLoading"
          class="border-neutrals-200 border-b pb-8 md:border-b-0 md:border-r md:pb-0 md:pr-8"
        />
        <TopDailyList
          type="topicAnalytics"
          :data="data"
          :loading="isLoading"
        />
      </div>
    </div>
  </UCard>
</template>
<style>
.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.no-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
</style>
