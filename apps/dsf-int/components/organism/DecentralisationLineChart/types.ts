import type {
  AreaData,
  AreaSeriesOptions,
  AreaStyleOptions,
  DeepPartial,
  IChartApi,
  ISeriesApi,
  LineData,
  LineSeriesOptions,
  LineStyleOptions,
  SeriesOptionsCommon,
  Time,
  WhitespaceData,
} from 'lightweight-charts'

export interface LineDataType {
  type: 'line'
  data: (LineData<Time> | WhitespaceData<Time>)[]
  opt?: {
    title?: string
    color?: string
    priceScaleId?: string
    lastValueVisible?: boolean
  }
}

export interface AreaDataType {
  type: 'area'
  data: (AreaData<Time> | WhitespaceData<Time>)[]
  opt?: {
    lineColor?: string
    topColor?: string
    bottomColor?: string
    title?: string
    priceScaleId?: string
    lastValueVisible?: boolean
  }
}

export interface HistogramDataType {
  type: 'histogram'
  data: { time: string; value: number }[]
  // data: SeriesDataItemTypeMap['Histogram'][]
  opt?: {
    lineColor?: string
    topColor?: string
    bottomColor?: string
    title?: string
    priceScaleId?: string
    lastValueVisible?: boolean
  }
}

export type LightChartDataProps = (LineDataType | AreaDataType | HistogramDataType) & { id: string }

export interface LightChartRef {
  fitContent: () => void
  chart?: IChartApi
  // chart?: ShallowRef<IChartApi | undefined>
  prepareTooltip: () => void
}

export type lineDataType =
  | ISeriesApi<
      'Line',
      Time,
      LineData<Time> | WhitespaceData<Time>,
      LineSeriesOptions,
      DeepPartial<LineStyleOptions & SeriesOptionsCommon>
    >
  | undefined

export type addLineDataType =
  | ISeriesApi<
      'Line',
      Time,
      WhitespaceData<Time> | LineData<Time>,
      LineSeriesOptions,
      DeepPartial<LineStyleOptions & SeriesOptionsCommon>
    >
  | undefined
export type addLineOptType = DeepPartial<LineStyleOptions & SeriesOptionsCommon>

export type addAreaDataType =
  | ISeriesApi<
      'Area',
      Time,
      AreaData<Time> | WhitespaceData<Time>,
      AreaSeriesOptions,
      DeepPartial<AreaStyleOptions & SeriesOptionsCommon>
    >
  | undefined
export type addAreaOptType = DeepPartial<AreaStyleOptions & SeriesOptionsCommon>

export type compareUnitType =
  | 'ipAuthDistrGini'
  | 'ipParticipantDivGini'
  | 'ipAuthorInflConcHHI'
  | 'ipGovOrdinal'
  | 'rcpDevDistrGini'
  | 'rcpParticipantDivShannon'
  | 'rcpDevInflConcHHI'
  | 'rcdRevrPowerConcHHI'
  | 'consensusPowerNeGini'
  | 'consensusPowerNeTheil'
  | 'consensusPowerConcNakamoto'
  | 'consensusPowerConcHHI'
  | 'coinDistrOrdinal'
