<script setup lang="ts">
// Utils
import { isMobileDevice } from '~/helper/utilities-helper'

// Constants
import { PER_PAGE } from '~/constants/api'
import { HTTP_STATUS } from '~/constants/error'

// Types
import type { EsgClimatePageQuery } from '~/types/page'
import type { EsgTableQueryParams } from '~/types'

const { $apiClient } = useNuxtApp()
const route = useRoute()
const router = useRouter()

const { isFeatureAllowed } = useFeature()

const isClimateFeatureAllowed = computed(
  () =>
    !!isFeatureAllowed('esg_climate_metrics.power_use') ||
    !!isFeatureAllowed('esg_climate_metrics.energy_cons_tx') ||
    !!isFeatureAllowed('esg_climate_metrics.energy_cons_tx_node'),
)

const tableFilters = computed(() => {
  const { page, q, algorithm, sort, ...rest } = route.query as EsgClimatePageQuery
  const isAsc = Object.keys(rest).includes('asc')

  const layer = rest.layer?.split(',').map((layer) => layer.toLowerCase().replace('l', ''))
  const type = rest.type?.toUpperCase().split(',')
  const consensusGroup = algorithm?.split(',')

  console.log('layer', layer)

  return {
    sortBy: sort || 'energyConsumption',
    sortFlow: isAsc ? 'ASC' : 'DESC',
    page: Number(page ?? 1),
    terms: q || undefined,
    layer,
    type,
    consensusGroup,
  } as EsgTableQueryParams
})

const isPowerCons = computed(() => route.query.list === 'cons')

const { data, status, refresh } = useLazyAsyncData(
  `getEsgTable.${isPowerCons.value ? 'cons' : 'emissions'}`,
  async () => {
    if (route.query.list) {
      const api = isPowerCons?.value
        ? '/nodiens/api/v1/esg/energy-consumption/'
        : '/nodiens/api/v1/esg/carbon-emission/'

      const { response, data, error } = await $apiClient.GET(api, {
        params: {
          query: { ...tableFilters.value },
        },
      })

      if (response.status === HTTP_STATUS.INTERNAL_SERVER_ERROR) {
        throw error
      }

      return data?.payload || null
    }
    return null
  },
  {
    watch: [tableFilters],
    server: false,
  },
)

const isLoading = computed(() => status.value === 'pending')

const page = computed({
  get: () => Number(route.query.page ?? 1),
  set: async (page) =>
    await router.push({
      query: {
        ...route.query,
        page,
      },
    }),
})

const totalItems = computed(() => data.value?.totalItem ?? 0)

const columns = computed(() => [
  {
    key: 'number',
    label: '#',
    isVisible: true,
    enabled: true,
    sortable: true,
    description: '',
  },
  {
    key: 'name',
    label: 'Name',
    class: 'min-w-[180px] sm:min-w-[300px]',
    description: 'The name of a token or platform.',
    sortKey: 'name',
  },
  {
    key: 'tags',
    label: 'Tags',
    class: 'min-w-[130px]',
    description: '-',
  },
  ...(isPowerCons.value
    ? [
        {
          key: 'energy_cons',
          label: 'Power Use',
          description:
            'The estimated global power usage of a crypto asset, measured in Watts (W) or their multiples. This is calculated using proprietary methodologies tailored to the specific consensus mechanism employed by the underlying blockchain network of a crypto asset, as well as the type and quantity of hardware utilized.',
          sortKey: 'energyConsumption',
        },
      ]
    : [
        {
          key: 'co_emission',
          label: 'CO₂ Emissions',
          description:
            'The estimated global CO₂ emissions of a crypto asset, measured in Grams (g) or their multiples. This is calculated using proprietary methodologies tailored to the specific consensus mechanism employed by the underlying blockchain network of a crypto asset, as well as the type and quantity of hardware utilized.',
          sortKey: 'emission',
        },
      ]),
  ...(isPowerCons.value
    ? [
        {
          key: 'energy_cons_tx',
          label: 'Energy Cons/Tx',
          description:
            'The estimated energy consumption per transaction of a crypto asset, measured in Watt-hours (Wh) or their multiples. This is calculated using proprietary methodologies that consider the specific consensus mechanism employed, the type and quantity of hardware utilized, and the real-world transactions being conducted.',
          sortKey: 'energyConsumptionPerTx',
        },
      ]
    : [
        {
          key: 'co_emission_tx',
          label: 'CO₂ Emissions/Tx',
          description:
            'The estimated CO₂ emissions per transaction of a crypto asset, measured in Grams (g) or their multiples. This is calculated using proprietary methodologies that consider the specific consensus mechanism employed, the type and quantity of hardware utilized, and the real-world transactions being conducted.',
          sortKey: 'emissionPerTx',
        },
      ]),
  ...(isPowerCons.value
    ? [
        {
          key: 'energy_cons_tx_node',
          label: 'Energy Cons/Tx/Node',
          description:
            "The estimated energy consumption per transaction per node of a crypto asset, measured in Watt-hours (Wh), their multiples or subdivisions. This paints a more complete picture of a crypto asset's energy usage, and is calculated using proprietary methodologies that consider the specific consensus mechanism employed, the type and quantity of hardware utilized, and the real-world transactions being conducted.",
          sortKey: 'energyConsumptionPerTxPerNode',
        },
      ]
    : [
        {
          key: 'co_emission_tx_node',
          label: 'CO₂ Emissions/Tx/Node',
          description:
            "The estimated CO₂ emissions per transaction per node of a crypto asset, measured in Grams (g), their multiples or subdivisions. This paints a more complete picture of a crypto asset's energy usage, and is calculated using proprietary methodologies that consider the specific consensus mechanism employed, the type and quantity of hardware utilized, and the real-world transactions being conducted.",
          sortKey: 'emissionPerTxPerNode',
        },
      ]),
  {
    key: 'validators',
    label: '# Validators',
    description:
      'The number of active validator nodes, or active user nodes in the case of the Proof of Work (PoW) consensus mechanism, on the underlying blockchain network of a crypto asset. This does not consider mirror nodes as validators, and likewise this is not factored in the aggregate energy usage figures. This represents the number of physical hardware units that uses energy carrying out block verification in the blockchain network on the current day.',
    sortKey: 'validators',
  },
  {
    key: 'tps',
    label: 'Throughput, tps',
    description:
      'The network throughput or token throughput, expressed in transactions per second (tps). This does not consider vote transactions. Lightning Network transactions are estimated through a proprietary methodology.',
    sortKey: 'throughput',
  },
])

const paginationEntriesLabel = computed(() => {
  if (!data.value) return ''

  const { currentPage, perPage: apiPerPage, data: items } = data.value
  const startEntry = (currentPage - 1) * apiPerPage + 1
  const endEntry = startEntry + (items?.length ?? 0) - 1
  const total = totalItems.value

  return `Showing ${startEntry} to ${endEntry} of ${total} Entries`
})

const generateRowNumber = (index: number) => {
  if (!data.value) return index + 1
  const { currentPage, perPage } = data.value
  const startEntry = (currentPage - 1) * perPage + 1
  return startEntry + index
}

const generateRowLink = (slug: string) => {
  const metricMap: Record<string, string> = {
    cons: 'energy-consumption',
    emissions: 'carbon-emission',
  }
  const list = route.query.list as string
  return `/climate/${metricMap[list] || 'energy-consumption'}/${slug}`
}

onBeforeMount(async () => {
  await router.replace({
    query: {
      list: 'cons',
      sort: 'energyConsumption',
      desc: '1',
    },
  })
  await refresh()
})
</script>

<template>
  <CheckFeature
    :allowed="isClimateFeatureAllowed"
    :loading="isLoading"
    class="p-1"
  >
    <div class="flex flex-col space-y-5">
      <UCard>
        <UTableWrapper
          :pending="isLoading"
          :data="data?.data || []"
          :columns="columns"
        >
          <!-- HEAD -->
          <template #number-header="head">
            <div class="flex w-full items-center justify-center">
              <span class="text-left">{{ head.column.label }}</span>
            </div>
          </template>

          <template #name-header="head">
            <div class="flex min-w-[8rem] items-center gap-1">
              <SortableColumn column="name">
                <span class="whitespace-nowrap text-left">{{ head.column.label }}</span>
                <InformationPopover
                  class="mt-1"
                  icon-class="!w-4 !h-4"
                >
                  <TooltipContent>
                    <p>
                      {{ head.column.description }}
                    </p>
                  </TooltipContent>
                </InformationPopover>
              </SortableColumn>
            </div>
          </template>

          <template #tags-header="head">
            <div class="flex min-w-[8rem] items-center gap-1">
              <span class="whitespace-nowrap text-left">{{ head.column.label }}</span>
            </div>
          </template>

          <template #energy_cons-header="head">
            <div class="flex min-w-[6rem] items-center gap-1">
              <SortableColumn
                :column="head.column.sortKey"
                button-class="-ml-2"
                is-default
              >
                <span class="whitespace-nowrap text-left">{{ head.column.label }}</span>

                <InformationPopover icon-class="mt-1">
                  <TooltipContent>
                    <p>{{ head.column.description }}</p>
                  </TooltipContent>
                </InformationPopover>
              </SortableColumn>
            </div>
          </template>

          <template #co_emission-header="head">
            <div class="flex min-w-[6rem] items-center gap-1">
              <SortableColumn
                :column="head.column.sortKey"
                button-class="-ml-2"
              >
                <span class="whitespace-nowrap text-left">{{ head.column.label }}</span>

                <InformationPopover icon-class="mt-1">
                  <TooltipContent>
                    <p>{{ head.column.description }}</p>
                  </TooltipContent>
                </InformationPopover>
              </SortableColumn>
            </div>
          </template>

          <template #energy_cons_tx-header="head">
            <div class="flex min-w-[6rem] items-center gap-1">
              <SortableColumn
                :column="head.column.sortKey"
                button-class="-ml-2"
              >
                <span class="whitespace-nowrap text-left">{{ head.column.label }}</span>
                <InformationPopover icon-class="mt-1">
                  <TooltipContent>
                    <p>{{ head.column.description }}</p>
                  </TooltipContent>
                </InformationPopover>
              </SortableColumn>
            </div>
          </template>

          <template #co_emission_tx-header="head">
            <div class="flex min-w-[6rem] items-center gap-1">
              <SortableColumn
                :column="head.column.sortKey"
                button-class="-ml-2"
              >
                <span class="whitespace-nowrap text-left">{{ head.column.label }}</span>
                <InformationPopover icon-class="mt-1">
                  <TooltipContent>
                    <p>{{ head.column.description }}</p>
                  </TooltipContent>
                </InformationPopover>
              </SortableColumn>
            </div>
          </template>

          <template #energy_cons_tx_node-header="head">
            <div class="flex min-w-[6rem] items-center gap-1">
              <SortableColumn
                :column="head.column.sortKey"
                button-class="-ml-2"
              >
                <span class="whitespace-nowrap text-left">{{ head.column.label }}</span>

                <InformationPopover icon-class="mt-1">
                  <TooltipContent>
                    <p>{{ head.column.description }}</p>
                  </TooltipContent>
                </InformationPopover>
              </SortableColumn>
            </div>
          </template>

          <template #co_emission_tx_node-header="head">
            <div class="flex min-w-[6rem] items-center gap-1">
              <SortableColumn
                :column="head.column.sortKey"
                button-class="-ml-2"
              >
                <span class="whitespace-nowrap text-left">{{ head.column.label }}</span>

                <InformationPopover icon-class="mt-1">
                  <TooltipContent>
                    <p>{{ head.column.description }}</p>
                  </TooltipContent>
                </InformationPopover>
              </SortableColumn>
            </div>
          </template>

          <template #validators-header="head">
            <div class="flex min-w-[6rem] items-center gap-1">
              <SortableColumn
                :column="head.column.sortKey"
                button-class="-ml-2"
              >
                <span class="whitespace-nowrap text-left">{{ head.column.label }}</span>

                <InformationPopover icon-class="mt-1">
                  <TooltipContent>
                    <p>{{ head.column.description }}</p>
                  </TooltipContent>
                </InformationPopover>
              </SortableColumn>
            </div>
          </template>

          <template #tps-header="head">
            <div class="flex w-full min-w-[6rem] items-center">
              <SortableColumn
                :column="head.column.sortKey"
                button-class="-ml-2"
              >
                <span class="whitespace-nowrap text-left">{{ head.column.label }}</span>

                <InformationPopover icon-class="mt-1">
                  <TooltipContent>
                    <p>{{ head.column.description }}</p>
                  </TooltipContent>
                </InformationPopover>
              </SortableColumn>
            </div>
          </template>

          <!-- BODY -->
          <template #number-data="{ row, index }">
            <div class="flex cursor-pointer items-center justify-center">
              <NuxtLink :href="generateRowLink(row.slug)">
                <p class="text-left">
                  {{ generateRowNumber(index) }}
                </p>
              </NuxtLink>
            </div>
          </template>

          <template #name-data="{ row }">
            <NuxtLink :href="generateRowLink(row.slug)">
              <CoinDisplayName :coin="row" />
            </NuxtLink>
          </template>

          <template #tags-data="{ row }">
            <NuxtLink
              :href="generateRowLink(row.slug)"
              tag="div"
              class="flex gap-2"
            >
              <UBadge
                color="base-primary"
                variant="tag"
                size="xs"
              >
                {{ `L${row.layer}` }}
              </UBadge>
              <UBadge
                color="brand"
                variant="tag"
                size="xs"
              >
                {{ row.type }}
              </UBadge>
              <UBadge
                v-if="row.consensusName"
                color="accent-green"
                variant="tag"
                size="xs"
              >
                {{ row.consensusName }}
              </UBadge>
            </NuxtLink>
          </template>

          <template #energy_cons-data="{ row }">
            <div class="cursor-pointer">
              <NuxtLink :href="generateRowLink(row.slug)">
                <p>{{ row?.energyConsumption ?? '-' }}</p>
              </NuxtLink>
            </div>
          </template>

          <template #co_emission-data="{ row }">
            <div class="cursor-pointer">
              <NuxtLink :href="generateRowLink(row.slug)">
                <p>{{ row?.totalEmission ?? '-' }}</p>
              </NuxtLink>
            </div>
          </template>

          <template #energy_cons_tx-data="{ row }">
            <div class="cursor-pointer">
              <NuxtLink :href="generateRowLink(row.slug)">
                <p>{{ row?.energyConsumptionPerTx ?? '-' }}</p>
              </NuxtLink>
            </div>
          </template>

          <template #co_emission_tx-data="{ row }">
            <div class="cursor-pointer">
              <NuxtLink :href="generateRowLink(row.slug)">
                <p>{{ row?.emissionPerTx ?? '-' }}</p>
              </NuxtLink>
            </div>
          </template>

          <template #energy_cons_tx_node-data="{ row }">
            <div class="cursor-pointer">
              <NuxtLink :href="generateRowLink(row.slug)">
                <p>{{ row?.energyConsumptionPerTxPerNode ?? '-' }}</p>
              </NuxtLink>
            </div>
          </template>

          <template #co_emission_tx_node-data="{ row }">
            <div class="cursor-pointer">
              <NuxtLink :href="generateRowLink(row.slug)">
                <p>{{ row?.emissionPerTxPerNode ?? '-' }}</p>
              </NuxtLink>
            </div>
          </template>

          <template #validators-data="{ row }">
            <div class="cursor-pointer">
              <NuxtLink :href="generateRowLink(row.slug)">
                <p>{{ row?.validators ?? '-' }}</p>
              </NuxtLink>
            </div>
          </template>

          <template #tps-data="{ row }">
            <div class="cursor-pointer">
              <NuxtLink :href="generateRowLink(row.slug)">
                <p>{{ row?.throughput ?? '-' }}</p>
              </NuxtLink>
            </div>
          </template>
        </UTableWrapper>
      </UCard>
      <div
        v-if="totalItems > 0"
        class="mt-5 flex flex-col-reverse items-center justify-center gap-6 text-center md:flex-row md:justify-between"
      >
        <p class="text-neutrals-800 text-sm font-medium dark:text-white">
          {{ paginationEntriesLabel }}
        </p>
        <UPagination
          v-model="page"
          :page-count="data?.perPage || PER_PAGE"
          :total="totalItems"
          :max="isMobileDevice() ? 7 : 10"
          :disabled="isLoading"
          :active-button="{
            color: 'black',
          }"
        />
      </div>
    </div>
  </CheckFeature>
</template>
