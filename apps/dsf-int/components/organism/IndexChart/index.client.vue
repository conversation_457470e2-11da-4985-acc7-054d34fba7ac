<script setup lang="ts">
import { type ChartData, Chart as ChartJS, registerables } from 'chart.js'
import { Chart } from 'vue-chartjs'
import type { dataProps } from './types'

const props = defineProps<{
  type: 'mood_index' | 'trust_index' | number
  data: dataProps[]
  watchRender?: boolean
  platform?: string
}>()

const datasetStyle = {
  borderWidth: 1.2,
  lineTension: 0.25,
}

ChartJS.register(...registerables)

const chartData = ref<ChartData>({
  datasets: [],
})

watch([() => props.type, () => props.platform], () => {
  renderChart()
})

function renderChart() {
  if (props.data[0]) {
    chartData.value = {
      labels: props.data.map(() => ''),
      datasets: [
        {
          label: props.data[0].platform_id,
          data: props.data.map((data) => data[props.type] as number),
          borderColor:
            (props.data[0][props.type] as number) < (props.data[props.data.length - 1]?.[props.type] as number)
              ? 'rgb(34 197 94)'
              : 'rgb(244 63 94)',
          backgroundColor:
            (props.data[0][props.type] as number) < (props.data[props.data.length - 1]?.[props.type] as number)
              ? 'rgb(34 197 94)'
              : 'rgb(244 63 94)',
          fill:
            (props.data[0][props.type] as number) < (props.data[props.data.length - 1]?.[props.type] as number)
              ? 'rgb(34 197 94)'
              : 'rgb(244 63 94)',
          ...datasetStyle,
        },
      ],
    }
  }
}

onMounted(() => {
  renderChart()
})
</script>

<template>
  <Chart
    :data="chartData"
    type="line"
    :options="{
      devicePixelRatio: 1.5,
      maintainAspectRatio: false,
      responsive: true,
      scales: {
        x: {
          display: false, // Hide the x-axis
        },
        y: {
          display: false, // Hide the y-axis
        },
      },
      plugins: {
        legend: {
          display: false, // Hide the legend
        },
        tooltip: {
          enabled: false,
        },
      },
      elements: {
        point: {
          radius: 0, // Hide data points on the line
        },
      },
    }"
  />
</template>
