<script setup lang="ts">
import { useFeatureFlags } from '~/stores/feature-flags'

const config = useRuntimeConfig()
const featureFlagsStore = useFeatureFlags()
const { featureFlags } = storeToRefs(featureFlagsStore)
const { toggleFeatureFlag } = featureFlagsStore
const ffEnabled = computed<boolean>(() => JSON.parse(config.public.featureFlagsEnabled))

const isModalOpen = ref(false)
</script>

<template>
  <div>
    <div
      v-if="ffEnabled"
      class="!fixed bottom-6 right-6 z-50"
    >
      <UTooltip text="Enable/Disable feature that's not released yet">
        <UButton
          icon="i-heroicons-flag"
          color="white"
          variant="solid"
          class="rounded-full p-4 shadow-md"
          @click="isModalOpen = true"
        />
      </UTooltip>
    </div>

    <USlideover v-model="isModalOpen">
      <div class="flex-1 p-5">
        <div class="mb-2 flex items-center gap-x-2">
          <UButton
            icon="i-heroicons-x-mark-20-solid"
            variant="ghost"
            color="black"
            size="md"
            @click="isModalOpen = false"
          />
          <h3 class="text-2xl font-medium text-black dark:text-white">
            Feature Flags
          </h3>
        </div>

        <UAlert
          description="You can enable/disabled features that is not released yet."
          title="What is Feature Flags ?"
          icon="i-heroicons-information-circle"
          color="blue"
          variant="solid"
          class="mb-5"
        />

        <div
          v-for="feature in featureFlags"
          :key="feature.key"
          class="mb-3 flex w-full items-center justify-between"
        >
          <span>{{ feature.label }}</span>
          <UToggle
            :ui="{
              active: 'bg-base-primary-500 dark:bg-base-primary-500',
              inactive: 'bg-[#DFDFDF] dark:bg-[#DFDFDF]',
            }"
            :model-value="feature.enabled"
            on-icon="i-heroicons-check-20-solid"
            off-icon="i-heroicons-x-mark-20-solid"
            @update:model-value="values => toggleFeatureFlag(feature.key)"
          />
        </div>
      </div>
    </USlideover>
  </div>
</template>
