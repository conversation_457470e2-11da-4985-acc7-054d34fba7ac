<script setup lang="ts">
import { SOCIAL_MEDIA_LIST } from '~/constants/options'

interface ShareModalProps {
  sharedText: string
}

const props = defineProps<ShareModalProps>()

const emit = defineEmits<{
  (e: 'onClose'): void
}>()

const onShare = async () => {
  await navigator.clipboard.writeText(props.sharedText)
  emit('onClose')
}
</script>

<template>
  <UModal :model-value="true" prevent-close>
    <div class="flex flex-col gap-6 p-10">
      <div class="flex items-center justify-between">
        <h5 class="text-2xl font-bold text-neutral-800 dark:text-white">Shares</h5>
        <UButton
          color="gray"
          variant="ghost"
          icon="i-heroicons-x-mark-20-solid"
          class="-my-1"
          @click="emit('onClose')"
        />
      </div>
      <div class="relative flex flex-1 items-center">
        <Input :model-value="sharedText" class="w-full pr-28" disabled />
        <UButton class="absolute right-0 whitespace-nowrap" color="cyan" variant="link" @click="onShare">
          Copy Link
        </UButton>
      </div>
      <p class="text-lg font-medium text-neutral-800 dark:text-white">Share on social media</p>
      <div class="flex items-center gap-3">
        <ShareButton
          v-for="(item, index) in SOCIAL_MEDIA_LIST"
          :key="`share-button-${index}`"
          :link="sharedText"
          :variant="item"
        />
      </div>
    </div>
  </UModal>
</template>
