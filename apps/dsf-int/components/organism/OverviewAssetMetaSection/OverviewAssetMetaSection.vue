<script setup lang="ts">
import { titleCase } from '~/helper/string-helper'
import { ASSET_CRYPTO_TOOLTIPS, ASSET_STABLECOIN_TOOLTIPS } from '~/constants/common-tooltips'
import type { AssetMetadataPayload } from '~/types'

const props = defineProps<{
  isLoading: boolean
  data: AssetMetadataPayload | null
  tooltips?: 'crypto' | 'stablecoin'
}>()

const { isFeatureAllowed } = useFeature()

const availableFeatures = computed(() => {
  return {
    mood_ranking: !!isFeatureAllowed('sentiment_metrics.mood_ranking'),
    trust_ranking: !!isFeatureAllowed('sentiment_metrics.trust_ranking'),
    price: !!isFeatureAllowed('financial_metrics.price'),
    messages: !!isFeatureAllowed('sentiment_metrics.messages'),
    bots: !!isFeatureAllowed('sentiment_metrics.bots_tracker'),
    vulgarity_index: !!isFeatureAllowed('sentiment_metrics.vulgarity_index'),
  }
})

const tooltips = computed(() => {
  return props.tooltips === 'stablecoin' ? ASSET_STABLECOIN_TOOLTIPS : ASSET_CRYPTO_TOOLTIPS
})
</script>

<template>
  <OverviewSkeleton v-if="isLoading" />
  <div
    v-else
    class="flex flex-col gap-3"
  >
    <div class="grid grid-cols-1 gap-3 md:grid-cols-12">
      <div class="col-span-1 md:col-span-8">
        <UCard class="h-full">
          <div class="flex flex-col items-start gap-3 md:flex-row">
            <div class="flex items-center gap-x-2 md:min-w-[80px]">
              <img
                :src="data?.logo ?? ''"
                width="80"
                height="80"
                class="block h-[50px] w-[50px] md:h-[80px] md:!w-[80px]"
              />

              <h3 class="mb-1 text-3xl font-bold text-black md:hidden dark:text-white">
                {{ data?.name ?? '' }}
              </h3>
            </div>

            <div class="text-dark-grey flex flex-col flex-wrap text-sm">
              <h3 class="mb-1 hidden text-3xl font-bold text-black md:inline-block dark:text-white">
                {{ data?.name ?? '' }}
              </h3>
              <p class="dark:text-white">
                {{ data?.description ?? '' }}
              </p>

              <div class="mt-4 flex items-center gap-x-3">
                <UButton
                  v-if="data?.website"
                  color="black"
                  leading-icon="i-heroicons-globe-alt"
                  label="Website"
                  :to="data.website"
                  target="_blank"
                />

                <UButton
                  v-for="(link, linkIndex) in data?.urls ?? []"
                  :key="linkIndex"
                  leading-icon="i-mdi-github"
                  :to="link.url ?? undefined"
                  :label="titleCase(link.name ?? 'Link')"
                />
              </div>
            </div>
          </div>
        </UCard>
      </div>
      <div class="col-span-1 flex-col gap-3 md:col-span-4">
        <CheckFeature
          :allowed="availableFeatures.price"
          small
        >
          <UCard class="mb-3">
            <div class="mb-4 flex items-center gap-x-1">
              <h3 class="text-neutrals-400 text-base font-medium dark:text-white">Price</h3>
              <InformationPopover icon-class="mt-1.5">
                <TooltipContent>
                  <p v-html="tooltips.price" />
                </TooltipContent>
              </InformationPopover>
            </div>
            <div class="flex items-center justify-center">
              <span class="text-center text-4xl font-bold text-black dark:text-white">{{ data?.price ?? '-' }}</span>
            </div>
          </UCard>
        </CheckFeature>

        <div class="grid grid-cols-1 gap-3 lg:grid-cols-2">
          <CheckFeature
            :allowed="availableFeatures.mood_ranking"
            small
          >
            <UCard>
              <div class="mb-4 flex items-center gap-x-1">
                <h3 class="text-neutrals-400 text-base font-medium dark:text-white">Mood Ranking</h3>
                <InformationPopover icon-class="mt-1.5">
                  <TooltipContent>
                    <p v-html="tooltips.mood_ranking" />
                  </TooltipContent>
                </InformationPopover>
              </div>
              <div class="flex items-center justify-center">
                <span class="text-center text-4xl font-bold text-black dark:text-white">
                  {{ data?.moodRankPosition ? `#${data.moodRankPosition}` : '-' }}
                </span>
              </div>
            </UCard>
          </CheckFeature>
          <CheckFeature
            :allowed="availableFeatures.trust_ranking"
            small
          >
            <UCard>
              <div class="mb-4 flex items-center gap-x-1">
                <h3 class="text-neutrals-400 text-base font-medium dark:text-white">Trust Ranking</h3>
                <InformationPopover icon-class="mt-1.5">
                  <TooltipContent>
                    <p v-html="tooltips.trust_ranking" />
                  </TooltipContent>
                </InformationPopover>
              </div>
              <div class="flex items-center justify-center">
                <span class="text-center text-4xl font-bold text-black dark:text-white">
                  {{ data?.trustRankPosition ? `#${data.trustRankPosition}` : '-' }}
                </span>
              </div>
            </UCard>
          </CheckFeature>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 gap-3 md:grid-cols-3">
      <CheckFeature
        :allowed="availableFeatures.messages"
        small
      >
        <UCard>
          <div class="flex items-center gap-x-1">
            <h3 class="text-neutrals-400 text-base font-medium dark:text-white"># Messages (14d)</h3>
            <InformationPopover icon-class="mt-1.5">
              <TooltipContent>
                <p v-html="tooltips.messages" />
              </TooltipContent>
            </InformationPopover>
          </div>
          <div class="flex items-center justify-center">
            <span class="text-center text-4xl font-bold text-black dark:text-white">
              {{ data?.messageCount ?? '-' }}
            </span>
          </div>
        </UCard>
      </CheckFeature>
      <CheckFeature
        :allowed="availableFeatures.bots"
        small
      >
        <UCard>
          <div class="flex items-center gap-x-1">
            <h3 class="text-neutrals-400 text-base font-medium dark:text-white"># Bots (14d)</h3>
            <InformationPopover icon-class="mt-1.5">
              <TooltipContent>
                <p v-html="tooltips.bots" />
              </TooltipContent>
            </InformationPopover>
          </div>
          <div class="flex items-center justify-center">
            <span class="text-center text-4xl font-bold text-black dark:text-white">
              {{ data?.botCount ?? '-' }}
            </span>
          </div>
        </UCard>
      </CheckFeature>
      <CheckFeature
        :allowed="availableFeatures.vulgarity_index"
        small
      >
        <UCard>
          <div class="flex items-center gap-x-1">
            <h3 class="text-neutrals-400 text-base font-medium dark:text-white">Vulgarity Index</h3>
            <InformationPopover icon-class="mt-1.5">
              <TooltipContent>
                <p v-html="tooltips.vulgarity_index" />
              </TooltipContent>
            </InformationPopover>
          </div>
          <div class="flex items-center justify-center">
            <span class="text-center text-4xl font-bold text-black dark:text-white">
              {{ data?.badWords ?? '-' }}
            </span>
          </div>
        </UCard>
      </CheckFeature>
    </div>
  </div>
</template>
