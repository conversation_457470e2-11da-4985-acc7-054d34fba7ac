<script setup lang="ts">
// @ts-expect-error ignore
import VueBottomSheet from '@webzlodimir/vue-bottom-sheet'
import { COMMUNITY_INDICES_LEVEL_DESCRIPTIONS } from '~/constants/common-tooltips'
import '@webzlodimir/vue-bottom-sheet/dist/style.css'
import './styles.css'

interface IndexFilterBottomSheetProps {
  layers: {
    key: string
    label: string
    defaultOpen: boolean
  }[]
  filters: string
}

const props = defineProps<IndexFilterBottomSheetProps>()
const emit = defineEmits<{
  (
    e: 'update',
    selected: {
      id: string
      selected: boolean
    }[],
  ): void
}>()
const modalShow = defineModel<boolean>({ required: true })

const bottomSheet = ref<InstanceType<typeof VueBottomSheet>>()
const localFilters = ref<
  {
    id: string
    name: string
    selected: boolean
    type: string
    group?: string
  }[]
>([])

function changeFilter(id: string) {
  const index = localFilters.value.findIndex((d) => d?.id === id)
  if (localFilters.value[index]) {
    localFilters.value[index].selected = !localFilters.value[index].selected
  }
}

function onClose() {
  modalShow.value = false
}

function onCancel() {
  localFilters.value = JSON.parse(props.filters) as {
    id: string
    name: string
    selected: boolean
    type: string
    group?: string
  }[]
  modalShow.value = false
}

function onUpdated() {
  emit(
    'update',
    localFilters.value.map((filter) => ({
      id: filter.id,
      selected: filter.selected,
    })),
  )
  modalShow.value = false
}

watch(
  () => props.filters,
  (newFilters) => {
    if (newFilters !== '[]') {
      localFilters.value = JSON.parse(newFilters) as {
        id: string
        name: string
        selected: boolean
        type: string
        group?: string
      }[]
    }
  },
  {
    immediate: true,
    deep: true,
  },
)

watch(modalShow, (newModalShow) => {
  if (newModalShow) {
    bottomSheet.value?.open()
  } else {
    bottomSheet.value?.close()
  }
})
</script>

<template>
  <ClientOnly>
    <VueBottomSheet
      ref="bottomSheet"
      :overlay-click-close="false"
      @closed="onClose"
    >
      <template #header>
        <div class="p-5 pt-0">
          <h3 class="text-lg font-semibold text-[#1B1F2B] dark:text-white">+ Level</h3>
        </div>
      </template>
      <template #default>
        <div class="max-h-[calc(100vh-250px)] overflow-auto px-5">
          <div
            v-for="(layer, layerIndex) in layers"
            :key="`${layer.label}_${layerIndex}`"
            :class="{ 'mb-4': layerIndex !== layers.length - 1 }"
          >
            <div class="relative mb-1 flex items-center gap-1">
              <span class="truncate text-base font-medium">{{ layer.label }}</span>
              <InformationPopover
                class="left-[76px] mt-2"
                icon-class="text-neutrals-300 dark:text-white"
                :ui="{
                  container: 'z-[60] !p-0',
                  wrapper: 'absolute',
                  base: 'absolute z-10',
                  popper: {
                    strategy: 'absolute',
                  },
                }"
              >
                <TooltipContent class="!w-64">
                  <p
                    v-html="
                      COMMUNITY_INDICES_LEVEL_DESCRIPTIONS[
                        layer.key.toLowerCase() as keyof typeof COMMUNITY_INDICES_LEVEL_DESCRIPTIONS
                      ]
                    "
                  />
                </TooltipContent>
              </InformationPopover>
            </div>
            <div class="flex flex-col gap-2">
              <div
                v-if="layer.key !== 'layer3'"
                class="flex flex-col gap-y-2"
              >
                <div
                  v-for="filter in localFilters.filter((r) => r.type === layer.key)"
                  :key="filter.id"
                >
                  <UCheckbox
                    v-if="layer.key === filter.type"
                    v-model:model-value="filter.selected"
                    :ui="
                      (() => {
                        switch (`${filter.type}-${filter.group?.toLowerCase() ?? filter.name.toLowerCase()}`) {
                          case 'layer1-trust':
                            return {
                              background:
                                'bg-white checked:bg-green-500 checked:hover:bg-green-500 checked:focus:bg-green-500 dark:bg-neutrals-700 dark:checked:bg-green-500 dark:checked:hover:bg-green-500 dark:checked:focus:bg-green-500',
                            }
                          case 'layer2-telegram':
                            return {
                              background:
                                'bg-white checked:bg-violet-600 checked:hover:bg-violet-600 checked:focus:bg-violet-600 dark:bg-neutrals-700 dark:checked:bg-violet-600 dark:checked:hover:bg-violet-600 dark:checked:focus:bg-violet-600',
                            }
                          case 'layer2-reddit':
                            return {
                              background:
                                'bg-white checked:bg-brand-500 checked:hover:bg-brand-500 checked:focus:bg-brand-500 dark:bg-neutrals-700 dark:checked:bg-brand-500 dark:checked:hover:bg-brand-500 dark:checked:focus:bg-brand-500',
                            }
                          default:
                            return {
                              background:
                                'bg-white checked:bg-base-primary-500 checked:hover:bg-base-primary-500 checked:focus:bg-base-primary-500 dark:bg-neutrals-700 dark:checked:bg-base-primary-500 dark:checked:hover:bg-base-primary-500 dark:checked:focus:bg-base-primary-500',
                            }
                        }
                      })()
                    "
                    :name="filter.id"
                    :label="filter.name"
                    @input="changeFilter(filter.id)"
                  />
                </div>
              </div>
              <div
                v-else
                class="flex flex-col gap-y-2"
              >
                <div class="mb-2 flex flex-col gap-y-2">
                  <strong class="text-neutrals-800 text-sm font-medium dark:text-white">Reddit</strong>
                  <div
                    v-for="filter in localFilters.filter(
                      (r) => r.type === layer.key && r.group?.toLocaleLowerCase() === 'reddit',
                    )"
                    :key="filter.id"
                  >
                    <UCheckbox
                      v-if="layer.key === filter.type"
                      v-model:model-value="filter.selected"
                      :ui="
                        (() => {
                          switch (`${filter.type}-${filter.group?.toLowerCase() ?? filter.name.toLowerCase()}`) {
                            case 'layer3-reddit':
                              return {
                                background:
                                  'bg-white checked:bg-brand-300 checked:hover:bg-brand-300 checked:focus:bg-brand-300 dark:bg-neutrals-700 dark:checked:bg-brand-300 dark:checked:hover:bg-brand-300 dark:checked:focus:bg-brand-300',
                              }
                            default:
                              return {
                                background:
                                  'bg-white checked:bg-base-primary-500 checked:hover:bg-base-primary-500 checked:focus:bg-base-primary-500 dark:bg-neutrals-700 dark:checked:bg-base-primary-500 dark:checked:hover:bg-base-primary-500 dark:checked:focus:bg-base-primary-500',
                              }
                          }
                        })()
                      "
                      :name="filter.id"
                      :label="filter.name"
                      @input="changeFilter(filter.id)"
                    />
                  </div>
                </div>
                <div class="flex flex-col gap-y-2">
                  <strong class="text-neutrals-800 text-sm font-medium dark:text-white">Telegram</strong>
                  <div
                    v-for="filter in localFilters.filter(
                      (r) => r.type === layer.key && r.group?.toLocaleLowerCase() === 'telegram',
                    )"
                    :key="filter.id"
                  >
                    <UCheckbox
                      v-if="layer.key === filter.type"
                      v-model:model-value="filter.selected"
                      :ui="
                        (() => {
                          switch (`${filter.type}-${filter.group?.toLowerCase() ?? filter.name.toLowerCase()}`) {
                            case 'layer3-telegram':
                              return {
                                background:
                                  'bg-white checked:bg-violet-300 checked:hover:bg-violet-300 checked:focus:bg-violet-300 dark:bg-neutrals-700 dark:checked:bg-violet-300 dark:checked:hover:bg-violet-300 dark:checked:focus:bg-violet-300',
                              }
                            default:
                              return {
                                background:
                                  'bg-white checked:bg-base-primary-500 checked:hover:bg-base-primary-500 checked:focus:bg-base-primary-500 dark:bg-neutrals-700 dark:checked:bg-base-primary-500 dark:checked:hover:bg-base-primary-500 dark:checked:focus:bg-base-primary-500',
                              }
                          }
                        })()
                      "
                      :name="filter.id"
                      :label="filter.name"
                      @input="changeFilter(filter.id)"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
      <template #footer>
        <div class="grid w-full grid-cols-2 gap-2 p-5">
          <UButton
            variant="solid"
            size="lg"
            color="base-primary"
            class="!bg-base-primary-600 hover:!bg-base-primary-700"
            block
            @click="onUpdated"
          >
            Update
          </UButton>
          <UButton
            variant="solid"
            size="lg"
            color="soft-gray"
            class="!bg-neutrals-100 hover:!bg-neutrals-200 !text-neutrals-400"
            block
            @click="onCancel"
          >
            Cancel
          </UButton>
        </div>
      </template>
    </VueBottomSheet>
  </ClientOnly>
</template>
