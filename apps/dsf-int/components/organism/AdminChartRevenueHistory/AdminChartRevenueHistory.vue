<script setup lang="ts">
import { LineStyle, PriceScaleMode } from 'lightweight-charts'
import { numericDisplay } from '~/helper/number-helper'
import { formatXApex } from '~/helper/string-helper'

const props = defineProps<{
  pending: boolean
  title: string
  legends?: {
    label: string
    colorClass: string
  }[]
  height?: number
  datasets: {
    name: string
    color: string
    data: { time: string; value: null | number }[]
  }[]
}>()

const { $colorMode } = useNuxtApp()

const chartRef = ref<HTMLElement>()

const { chart: chartInstance, tools, addLineSeries, renderChart, destroyChart } = useLightChart()

const darkMode = computed({
  get() {
    return $colorMode.value === 'dark'
  },
  set(value) {
    if (value) {
      $colorMode.preference = 'dark'
    } else {
      $colorMode.preference = 'light'
    }
  },
})

const initializeChart = () => {
  if (chartInstance.value === undefined) {
    renderChart(
      chartRef,
      {
        height: props.height ?? 400,
        grid: {
          horzLines: {
            color: '#C3C3C3',
            style: LineStyle.Dashed,
          },
          vertLines: {
            visible: false,
          },
        },
        crosshair: {
          horzLine: {
            visible: false,
            labelVisible: false,
          },
          vertLine: {
            labelVisible: false,
          },
        },
        leftPriceScale: {
          visible: false,
          borderVisible: false,
          entireTextOnly: true,
          scaleMargins: {
            bottom: 0.01,
          },
        },
        rightPriceScale: {
          visible: true,
          mode: PriceScaleMode.Normal,
          borderVisible: false,
          entireTextOnly: true,
          scaleMargins: {
            bottom: 0.1,
          },
          minimumWidth: 70,
        },
        timeScale: {
          visible: true,
          borderVisible: false,
          borderColor: 'rgba(197, 203, 206, 1)',
          fixLeftEdge: true,
          fixRightEdge: true,
          lockVisibleTimeRangeOnResize: true,
          tickMarkFormatter: (time: string) => formatXApex(time),
        },
        handleScale: {
          pinch: false,
          mouseWheel: false,
          axisPressedMouseMove: false,
        },
        handleScroll: {
          mouseWheel: false,
          vertTouchDrag: false,
          horzTouchDrag: false,
          pressedMouseMove: false,
        },
        localization: {
          priceFormatter: (price: number) => numericDisplay(price),
        },
      },
      darkMode.value
    )
  }

  if (props.datasets.length > 0) {
    props.datasets.forEach(dataset => {
      const seriesKeyName = dataset.name.toLowerCase().split(' ').join('-')

      const data = dataset.data.map(({ time, value }) => ({
        time,
        ...(value !== null && typeof value !== 'undefined' && { value }),
        customValues: { name: dataset.name },
      }))

      if (!data.length) {
        return
      }

      const series = addLineSeries(seriesKeyName, {
        priceScaleId: 'right',
        lastValueVisible: false,
        priceLineVisible: false,
        color: dataset.color,
        lineWidth: 2,
        crosshairMarkerVisible: false,
      })

      series?.setData(data)
    })
  }

  tools.prepareTooltip({
    darkMode: darkMode.value,
    valueFormatter: (price: number) => numericDisplay(price),
  })
  tools.reArrangeChart({ autosize: true })
}

watch(darkMode, value => {
  tools.setDark(value)
})

onMounted(() => {
  if (import.meta.client) {
    setTimeout(() => {
      initializeChart()
    }, 50)
  }
})

onBeforeUnmount(() => {
  destroyChart()
})
</script>

<template>
  <UCard class="min-h-[400px]">
    <div class="flex items-center justify-between">
      <h3>{{ title }}</h3>

      <div v-if="legends?.length" class="flex flex-row items-center justify-center gap-3">
        <div v-for="(legend, legendIndex) in legends" :key="legendIndex" class="flex items-center gap-x-2">
          <div class="h-[12px] min-h-[12px] w-[12px] min-w-[12px] rounded-full" :class="[legend.colorClass]" />
          <span class="whitespace-nowrap text-sm text-black dark:text-white">{{ legend.label }}</span>
        </div>
      </div>
    </div>

    <ClientOnly>
      <div v-if="pending" class="h-full w-full">
        <LottieNodiensLoader />
      </div>
      <div v-else ref="chartRef" class="w-full" />
    </ClientOnly>
  </UCard>
</template>
