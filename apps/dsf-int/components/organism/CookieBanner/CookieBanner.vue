<script setup lang="ts">
import { slateToHtml } from '@slate-serializers/html'

// Icons
import { IconCookies } from '~/assets/svg'

// Types
import type { CookiesContent } from 'app-types/payload-types'

const { data } = await useLazyAsyncData<CookiesContent>(
  'cookieBanner',
  async (ctx) => {
    return $fetch<CookiesContent>(`${ctx?.$config.public.payloadApi}/cookies-content/66d082b8f1b5f1316fa7fc3e`)
  },
  {
    server: false,
    immediate: true,
  },
)

const showCookieBanner = ref(false)
const showModalCustom = ref(false)
const cookiesAgreed = reactive({
  strict_cookies: true,
  analytical_cookies: false,
  functionality_cookies: false,
  targeting_cookies: false,
})

const cookiesAccordion = computed(() => {
  return [
    {
      key: 'strict_cookies',
      label: 'Strictly necessary cookies',
      content: slateToHtml(data?.value?.strict_cookies ?? []),
      defaultOpen: false,
      agreed: true,
    },
    {
      key: 'analytical_cookies',
      label: 'Analytical or performance cookies',
      content: slateToHtml(data?.value?.analytical_cookies ?? []),
      defaultOpen: false,
      agreed: false,
    },
    {
      key: 'functionality_cookies',
      label: 'Functionality cookies',
      content: slateToHtml(data?.value?.functionality_cookies ?? []),
      defaultOpen: false,
      agreed: false,
    },
    {
      key: 'targeting_cookies',
      label: 'Targeting cookies',
      content: slateToHtml(data?.value?.targeting_cookies ?? []),
      defaultOpen: false,
      agreed: false,
    },
  ]
})

const onCustomClick = () => {
  showCookieBanner.value = false
  showModalCustom.value = true
}

const onDismiss = () => {
  showModalCustom.value = false
  showCookieBanner.value = true
}

const onSaveCookies = () => {
  localStorage.setItem('dsf_cookies', JSON.stringify(cookiesAgreed))
  showCookieBanner.value = false
  showModalCustom.value = false
}

const onAcceptAll = () => {
  cookiesAgreed.analytical_cookies = true
  cookiesAgreed.functionality_cookies = true
  cookiesAgreed.targeting_cookies = true

  onSaveCookies()
}

const onRejectAll = () => {
  cookiesAgreed.analytical_cookies = false
  cookiesAgreed.functionality_cookies = false
  cookiesAgreed.targeting_cookies = false

  onSaveCookies()
}

onMounted(() => {
  !localStorage.getItem('dsf_cookies') && (showCookieBanner.value = true)
})
</script>

<template>
  <ClientOnly>
    <div
      v-if="showCookieBanner"
      class="!fixed bottom-0 left-0 z-[10000] p-4 md:p-10"
    >
      <UCard class="md:max-w-[589px]">
        <div class="flex flex-col">
          <IconCookies class="text-neutrals-800 dark:text-white" />
          <div
            class="mt-5 text-sm"
            v-html="slateToHtml(data?.description ?? [])"
          />
          <NuxtLink
            to="/legal/cookie"
            class="text-sm underline"
            target="_blank"
          >
            Cookie Policy
          </NuxtLink>
          <div class="mt-5 grid grid-cols-1 gap-2 md:grid-cols-2">
            <UButton
              variant="solid"
              color="black"
              size="md"
              block
              label="Accept All"
              @click="onAcceptAll"
            />
            <UButton
              variant="solid"
              color="black"
              size="md"
              block
              label="Reject All"
              @click="onRejectAll"
            />
          </div>
          <UButton
            class="mt-3"
            variant="outline"
            color="black"
            size="md"
            block
            label="Customise Settings"
            @click="onCustomClick"
          />
        </div>
      </UCard>
    </div>

    <UModal
      v-model="showModalCustom"
      :ui="{ rounded: 'rounded-none', container: 'max-h-screen overflow-hidden' }"
      @close="showCookieBanner = true"
    >
      <UCard class="max-h-screen overflow-hidden">
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold">Customise Settings</h3>
            <UButton
              size="md"
              variant="ghost"
              color="black"
              icon="i-heroicons-x-mark-solid"
              @click="onDismiss"
            />
          </div>
        </template>
        <div class="relative flex flex-col">
          <div class="max-h-[calc(70vh)] overflow-y-auto pb-24">
            <div
              class="text-neutrals-400 text-sm dark:text-white"
              v-html="slateToHtml(data?.description ?? [])"
            />
            <NuxtLink
              to="/legal/cookie"
              target="_blank"
              class="text-neutrals-400 text-sm underline dark:text-white"
            >
              Cookie Policy
            </NuxtLink>
            <div class="my-5" />

            <UAccordion
              :items="cookiesAccordion"
              :ui="{ wrapper: 'flex flex-col w-full' }"
              multiple
            >
              <template #default="{ item, index, open }">
                <UButton
                  color="gray"
                  variant="ghost"
                  class="border-none"
                  :ui="{ rounded: 'rounded-none', padding: { sm: 'p-1 px-0' } }"
                >
                  <template #leading>
                    <UIcon
                      name="i-heroicons-chevron-right-20-solid"
                      class="h-5 w-5 transform transition-transform duration-200"
                      :class="[open && 'rotate-90']"
                    />
                  </template>
                  <span class="flex-wrap text-left text-sm font-semibold md:text-base">{{ item.label }}</span>
                  <template #trailing>
                    <div
                      v-if="index === 0"
                      class="ms-auto"
                    >
                      <span class="text-accent-green-500 text-sm md:text-base">ALWAYS ACTIVE</span>
                    </div>
                    <UToggle
                      v-else
                      v-model="cookiesAgreed[item.key as keyof typeof cookiesAgreed]"
                      class="ms-auto"
                    />
                  </template>
                </UButton>
              </template>
            </UAccordion>
          </div>

          <div
            class="dark:bg-neutrals-800 absolute bottom-0 left-0 grid w-full grid-cols-1 gap-2 bg-white md:grid-cols-2"
          >
            <UButton
              variant="solid"
              color="black"
              size="md"
              block
              label="Confirm My Choices"
              @click="onSaveCookies"
            />
            <UButton
              variant="solid"
              color="black"
              size="md"
              block
              label="Reject All"
              @click="onRejectAll"
            />
          </div>
        </div>
      </UCard>
    </UModal>
  </ClientOnly>
</template>
