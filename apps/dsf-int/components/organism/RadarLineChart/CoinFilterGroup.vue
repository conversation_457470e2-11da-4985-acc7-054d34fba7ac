<script setup lang="ts">
import { useRadarLine<PERSON>hart } from '~/composables/useRadarLineChart'
import type { CoinFilterCallback } from '~/components/molecule/CoinFilter/types'

// Injectables
const queryKey = inject('queryKey') as string
const coinApi: string = inject('coinApi') as string
const coinFilterLabel: string = inject('coinFilterLabel') as string

const route = useRoute()
const router = useRouter()
const slots = useSlots()
const { loading, coin1Filter, coin2Filter, coin3Filter, selectedSize, radarChartLoaded } = useRadarLineChart()

const changeFilter = (
  value: CoinFilterCallback,
  targetModel: `${typeof queryKey}1` | `${typeof queryKey}2` | `${typeof queryKey}3`,
) => {
  const query: Record<string, string | undefined> = {}
  query[targetModel] = value?.slug
  router.replace({
    query: {
      ...route.query,
      ...query,
    },
  })
}

watch(
  [selectedSize],
  ([newSelectedSize]) => {
    radarChartLoaded.value = false
    const query: Record<string, string> = {}

    if (newSelectedSize && !!slots.filters) {
      query['size'] = newSelectedSize.id
    }

    router.replace({
      query: {
        ...route.query,
        ...query,
      },
    })

    radarChartLoaded.value = true
  },
  {
    deep: true,
  },
)
</script>

<template>
  <div class="flex flex-col gap-3 md:flex-row md:items-center md:justify-between">
    <div class="flex w-full flex-col items-start justify-between gap-3 md:flex-row">
      <div class="w-full">
        <div
          v-if="loading"
          class="grid grid-cols-2 gap-2 md:flex md:flex-wrap"
        >
          <USkeleton
            v-for="i in 4"
            :key="`coin-filter-loader-${i}`"
            class="h-10 flex-1 md:w-32 md:flex-none"
          />
        </div>
        <div
          v-else
          class="mb-5 grid grid-cols-2 gap-2 md:flex md:flex-wrap"
        >
          <CoinFilter
            v-model="coin1Filter"
            :api="coinApi"
            :name="queryKey"
            count="1"
            :coin-filter-label="coinFilterLabel"
            :callback="(value) => changeFilter(value, `${queryKey}1`)"
          />
          <CoinFilter
            v-model="coin2Filter"
            :api="coinApi"
            :name="queryKey"
            count="2"
            :coin-filter-label="coinFilterLabel"
            :callback="(value) => changeFilter(value, `${queryKey}2`)"
          />
          <CoinFilter
            v-model="coin3Filter"
            :api="coinApi"
            :name="queryKey"
            count="3"
            :coin-filter-label="coinFilterLabel"
            :callback="(value) => changeFilter(value, `${queryKey}3`)"
          />
          <slot name="filters" />
        </div>
      </div>
    </div>
  </div>
</template>
