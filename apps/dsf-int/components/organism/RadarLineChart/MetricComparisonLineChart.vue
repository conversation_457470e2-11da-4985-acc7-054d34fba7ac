<script setup lang="ts">
import { LineStyle, type Time } from 'lightweight-charts'
import { format as formatDate } from 'date-fns'
import domtoimage from 'dom-to-image'
import jsPDF from 'jspdf'
import FileSaver from 'file-saver'

// Composables
import { useRadarLineChart } from '~/composables/useRadarLineChart'

// Styles
import 'range-slider-input/dist/style.css'

// Helpers
import { numericDisplay } from '~/helper/number-helper'
import { blobToBase64 } from '~/helper/utilities-helper'
import { formatToSlug } from '~/helper/string-helper'

// Type
import type { lineDataType } from '~/components/organism/DecentralisationLineChart/types'
import type { v2DecentraliseComparison } from '~/server/trpc/trpc'
import LottieNodiensLoader from '~/components/atom/LottieNodiensLoader.vue'

interface StablecoinLineSeries {
  [key: string]: lineDataType
}

const showNoLineData = inject('showNoLineData') as { value: boolean }

const props = defineProps<{
  title: string
  titleColor: string
  chartData?: v2DecentraliseComparison
  selectedMetric?: string
  onGetRadarSnapshot?: () => string
  pending: boolean
  descriptionTooltip: string
  hideValueUnit?: boolean
  isDecentralisation?: boolean
}>()

const { $colorMode } = useNuxtApp()
const {
  chart,
  zoomViews,
  log,
  tools,
  renderChart: renderLightChart,
  renderBottomChart,
  addLineSeries,
  setPriceScaleVisibility,
  destroyChart,
} = useLightChart()
const { coin1Filter, coin2Filter, coin3Filter, valueUnitDecision, valueDecimalDecision, isShareModalOpen } =
  useRadarLineChart()
const exportElement = ref<HTMLElement>()
const radarSnapshot = ref<HTMLImageElement>()
const lineSnapshot = ref<HTMLElement>()
const stablecoinChartRef = ref<HTMLElement>()
const stablecoinChartBottomRef = ref<HTMLElement>()
const printTime = ref<string>()
const route = useRoute()
const config = useRuntimeConfig()
const timeSeriesData = ref<StablecoinLineSeries>({})

const { isMobile } = useDeviceScreen()
const { isFeatureAllowed } = useFeature()

const fullPath = computed(() => {
  const queryParams = new URLSearchParams(route.query as Record<string, string>)
  const { path } = route
  return `${config.public.appUrl}${path}?${queryParams}`
})

const darkMode = computed({
  get() {
    return $colorMode.value === 'dark'
  },
  set(value) {
    if (value) {
      $colorMode.preference = 'dark'
    } else {
      $colorMode.preference = 'light'
    }
  },
})

const yAxisUnit = computed(() => valueUnitDecision(props.selectedMetric))
const valueDecimals = computed(() => valueDecimalDecision(props.selectedMetric))

const isComparisonChartEmpty = computed(() => {
  if (props.chartData === undefined) {
    return true
  }

  const emptyTimeseries = props.chartData?.map((x) => x.timeseries).every((y) => y.length === 0)

  return (
    showNoLineData.value ||
    (Array.isArray(props.chartData) && props.chartData.length === 0) ||
    (Array.isArray(props.chartData) && emptyTimeseries)
  )
})

function renderChart(chartData: v2DecentraliseComparison) {
  const chartOpt = {
    crosshair: {
      horzLine: {
        visible: false,
        labelVisible: false,
      },
      vertLine: {
        labelVisible: false,
      },
    },
    grid: {
      vertLines: {
        visible: false,
      },
      horzLines: {
        style: LineStyle.Solid,
        color: '#B9BDD0',
      },
    },
    rightPriceScale: {
      visible: false,
    },
    leftPriceScale: {
      visible: isMobile.value ? false : true,
      borderVisible: false,
      ticksVisible: false,
      autoScale: true,
      alignLabels: true,
      entireTextOnly: true,
      scaleMargins: {
        ...(props.isDecentralisation
          ? {
              top: 0,
              bottom: 0,
            }
          : {
              bottom: 0.05,
            }),
      },
    },
    timeScale: {
      visible: false,
      fixRightEdge: true,
      rightOffset: 5,
      lockVisibleTimeRangeOnResize: true,
      timeVisible: true,
      secondsVisible: true,
    },
    handleScale: {
      pinch: false,
      mouseWheel: false,
      axisPressedMouseMove: false,
    },
    handleScroll: {
      mouseWheel: false,
      vertTouchDrag: false,
      horzTouchDrag: false,
      pressedMouseMove: false,
    },
    localization: {
      priceFormatter: (price: any) => {
        const value = numericDisplay(price, valueDecimals?.value)
        if (props.hideValueUnit) {
          return value
        }
        return yAxisUnit.value === 'currency' ? `$${value}` : `${value}%`
      },
    },
    milliFormat: true,
    ...(chartData?.[0]?.firstCalculation ? { startCalculation: chartData[0].firstCalculation } : {}),
    ...(chartData?.[0]?.latestCalculation ? { latestCalculation: chartData[0].latestCalculation } : {}),
  }

  const addNewLineSeries = () => {
    // Add Line Series
    if (coin1Filter.value.coin) {
      const lineInstance = addLineSeries(coin1Filter.value.type, {
        priceScaleId: 'left',
        lineWidth: 2,
        color: coin1Filter.value.color,
      })

      if (lineInstance) {
        const timeSeries =
          chartData.find((rawData) => rawData.symbol === coin1Filter.value.coin?.symbol)?.timeseries ?? []
        const chartSeriesData = timeSeries.map((d, id) => ({
          id,
          time: d[0] as Time,
          value: d[1] as number,
          customValues: {
            name: coin1Filter.value.coin?.symbol ?? '-',
          },
        }))

        lineInstance.setData(chartSeriesData)

        timeSeriesData.value[coin1Filter.value.type] = lineInstance

        lineInstance.applyOptions({
          lastValueVisible: false,
          priceLineVisible: false,
          pointMarkersVisible: false,
          pointMarkersRadius: 0,
          visible: true,
        })
      }
    }

    if (coin2Filter.value.coin) {
      const lineInstance = addLineSeries(coin2Filter.value.type, {
        priceScaleId: 'left',
        lineWidth: 2,
        color: coin2Filter.value.color,
      })

      if (props.isDecentralisation) {
        lineInstance?.applyOptions({
          autoscaleInfoProvider: () => {
            return {
              priceRange: {
                minValue: 0,
                maxValue: 1.1,
              },
            }
          },
        })
      }

      if (lineInstance) {
        const timeSeries =
          chartData.find((rawData) => rawData.symbol === coin2Filter.value.coin?.symbol)?.timeseries ?? []
        const chartSeriesData = timeSeries.map((d, id) => ({
          id,
          time: d[0] as Time,
          value: d[1] as number,
          customValues: {
            name: coin2Filter.value.coin?.symbol ?? '-',
          },
        }))

        lineInstance.setData(chartSeriesData)

        timeSeriesData.value[coin2Filter.value.type] = lineInstance

        lineInstance.applyOptions({
          lastValueVisible: false,
          priceLineVisible: false,
          pointMarkersVisible: false,
          pointMarkersRadius: 0,
          visible: true,
        })
      }
    }

    if (coin3Filter.value.coin) {
      const lineInstance = addLineSeries(coin3Filter.value.type, {
        priceScaleId: 'left',
        lineWidth: 2,
        color: coin3Filter.value.color,
      })

      if (lineInstance) {
        const timeSeries =
          chartData.find((rawData) => rawData.symbol === coin3Filter.value.coin?.symbol)?.timeseries ?? []
        const chartSeriesData = timeSeries.map((d, id) => ({
          id,
          time: d[0] as Time,
          value: d[1] as number,
          customValues: {
            name: coin3Filter.value.coin?.symbol ?? '-',
          },
        }))

        lineInstance.setData(chartSeriesData)

        timeSeriesData.value[coin3Filter.value.type] = lineInstance

        lineInstance.applyOptions({
          lastValueVisible: false,
          priceLineVisible: false,
          pointMarkersVisible: false,
          pointMarkersRadius: 0,
          visible: true,
        })
      }
    }
  }

  // First time initialize
  if (chart.value === undefined) {
    renderLightChart(stablecoinChartRef, chartOpt, darkMode.value)
    addNewLineSeries()
    tools.prepareTooltip({
      leftOffset: 60,
      darkMode: darkMode.value,
      valueFormatter: (value) => {
        const valueFormat = numericDisplay(value, valueDecimals.value)
        if (props.hideValueUnit) {
          return valueFormat
        }
        return yAxisUnit.value === 'currency' ? `$${valueFormat}` : `${valueFormat}%`
      },
    })
    renderBottomChart(stablecoinChartBottomRef, '#range-slider', darkMode.value)
    tools.reArrangeChart({ autosize: true })
  }
}

function printContent(target: string, blob?: Blob) {
  const listSelectedCoinSlug = []
  if (coin1Filter.value?.coin) {
    listSelectedCoinSlug.push(coin1Filter.value.coin.slug)
  }
  if (coin2Filter.value?.coin) {
    listSelectedCoinSlug.push(coin2Filter.value.coin.slug)
  }
  if (coin3Filter.value?.coin) {
    listSelectedCoinSlug.push(coin3Filter.value.coin.slug)
  }

  const fileName = `${listSelectedCoinSlug.join('_')}_${formatToSlug(props.title)}_${formatDate(new Date(), 'yyyyMMddHHmm')}_(Nodiens)`
  if (target !== 'pdf') {
    FileSaver.saveAs(blob!, `${fileName}.${target}`)
  } else {
    blobToBase64(blob!, (str) => {
      const pdf = new jsPDF({
        orientation: 'landscape',
      })
      pdf.addImage(str, 'PNG', 10, 10, 280, 110)

      pdf.save(`${fileName}.${target}`)
    })
  }
}

const allowFeatureInteraction = computed(() => {
  if (!props.selectedMetric) {
    return true
  }

  if (route.path.startsWith('/financial')) {
    const featureMericsKeys = {
      marketcap: 'market_cap',
      price: 'price',
      monthvolatility: 'volatility_30_days',
      depegdays: 'depeg_rata',
      dayvolumecex: 'cex_volume_24h',
      cexpairs: 'cex_pairs',
      cexliqcost: 'cex_liquidity_cost',
      dayvolumedex: 'dex_volume_24h',
      dexpairs: 'dex_pairs',
      dexliqcost: 'dex_liquidity_cost',
      dexliqconcentrate: 'dex_liquidity_concentration',
    } as Record<string, string>

    const key = `financial_metrics.${featureMericsKeys[props.selectedMetric.toLowerCase()]}`
    return !!isFeatureAllowed(key!)
  }
  if (route.path.startsWith('/esg')) {
    return !!isFeatureAllowed('esg_climate_metrics.decentralisation')
  }

  return false // Default return value
})

function print(target: 'png' | 'jpg' | 'pdf') {
  let previousValue: { from: Time; to: Time } | undefined
  if (chart.value) {
    const opt = chart.value.timeScale().getVisibleRange()
    if (opt) {
      previousValue = {
        from: opt.from,
        to: opt.to,
      }
    }
    chart.value.timeScale().applyOptions({
      visible: true,
    })
  }

  const canvas = chart.value?.takeScreenshot()
  const getSnapshot = props.onGetRadarSnapshot?.() ?? ''

  if (chart.value) {
    chart.value.timeScale().applyOptions({
      visible: false,
    })
    chart.value.applyOptions({
      width: stablecoinChartRef.value?.offsetWidth,
      height: stablecoinChartRef.value?.offsetHeight,
    })
    if (previousValue) {
      chart.value.timeScale().setVisibleRange({
        from: previousValue.from,
        to: previousValue.to,
      })
    }
  }
  if (canvas && exportElement.value && lineSnapshot.value && radarSnapshot.value) {
    canvas.style.maxWidth = '100%'
    canvas.style.minHeight = '70%'
    exportElement.value!.style.display = 'block'
    exportElement.value.style.transform = 'scale(0)'
    printTime.value = formatDate(new Date(), 'MMM dd, yyyy kk:mm zzz')
    lineSnapshot.value.appendChild(canvas)
    radarSnapshot.value.src = getSnapshot

    const chartWatermark = document.getElementById('chart-watermark')
    if (chartWatermark) {
      const img = chartWatermark.querySelector('img')
      if (img) {
        img.style.left = `${canvas.offsetWidth / 4}px`
        img.style.width = `${canvas.offsetWidth / 2}px`
        img.style.top = `${canvas.offsetHeight / 3.5}px`
      }
    }

    const body = document.querySelector('body')

    if (body) {
      body.style.overflow = 'hidden'

      domtoimage
        .toBlob(exportElement.value, {
          style: {
            transform: 'scale(1)',
            transformOrigin: 'top left',
          },
        })
        .then((blob: any) => {
          printContent(target, blob)
        })
        .finally(() => {
          body.style.overflow = 'unset'
          exportElement.value!.style.display = 'none'
          const canvas = exportElement.value?.querySelector('canvas')
          if (canvas) {
            canvas.remove()
          }
        })
    }
  }
}

watch(darkMode, (value) => {
  tools.setDark(value)
})

watch(
  [() => props.chartData, stablecoinChartRef],
  ([newChartData, newChartRef]) => {
    destroyChart()

    if (newChartRef && newChartData) {
      renderChart(newChartData)
    }
  },
  {
    deep: true,
  },
)

watch(isMobile, (value) => {
  if (value) {
    setPriceScaleVisibility('left', false)
  } else {
    setPriceScaleVisibility('left', true)
  }
})

onUnmounted(() => destroyChart())
</script>

<template>
  <ClientOnly>
    <div class="flex flex-col gap-2">
      <div class="flex flex-wrap items-center justify-between gap-3">
        <!-- Left Column -->
        <USkeleton
          v-if="pending"
          class="h-10 w-60"
        />
        <div
          v-else
          class="flex w-full grow items-center gap-1 rounded px-4 py-3 md:w-auto md:grow-0"
          :style="`background: ${props.titleColor}`"
        >
          <span class="grow whitespace-nowrap text-base text-white md:grow-0"> {{ props.title }}</span>
          <InformationPopover
            icon-class="mt-2 bg-[#ffffff]"
            revert-icon
          >
            <TooltipContent>
              <p v-html="props.descriptionTooltip" />
            </TooltipContent>
          </InformationPopover>
        </div>

        <!-- Right Column -->
        <div class="flex w-full flex-1 grow items-center justify-between md:w-auto md:grow-0">
          <div
            v-if="pending"
            class="ml-auto flex flex-1 items-center justify-between gap-x-3 sm:flex-none lg:ml-0 xl:ml-auto"
          >
            <USkeleton
              v-for="item in 6"
              :key="item"
              class="h-9 w-12"
            />
          </div>
          <div
            v-else
            class="ml-auto flex flex-1 items-center justify-between gap-x-0 sm:flex-none sm:justify-start sm:gap-x-3 lg:ml-0 xl:ml-auto"
          >
            <UButton
              v-for="zoom in zoomViews"
              :key="zoom.id"
              :variant="zoom.active ? 'outline' : 'ghost'"
              :color="zoom.active ? 'brand' : 'black'"
              :class="['px-1.5 !font-normal sm:px-2.5', { 'dark:!text-neutrals-50 !text-black/50': !zoom.active }]"
              :disabled="!allowFeatureInteraction"
              @click="tools.setZoom(zoom.id)"
            >
              {{ zoom.label }}
            </UButton>
            <UButton
              size="md"
              icon="i-material-symbols-share"
              variant="outline"
              color="soft-gray"
              :disabled="!allowFeatureInteraction"
              @click="isShareModalOpen = true"
            />
          </div>

          <div class="flex items-center gap-3 xl:hidden">
            <div
              v-if="pending"
              class="hidden items-center gap-x-2 lg:flex"
            >
              <USkeleton
                v-for="item in 2"
                :key="item"
                class="h-9 w-12"
              />
            </div>
            <div
              v-else
              class="hidden items-center gap-x-2 lg:flex"
            >
              <USkeleton
                v-if="pending"
                class="h-9 w-12"
              />
              <UButton
                size="md"
                variant="outline"
                :color="log ? 'brand' : 'soft-gray'"
                class="!font-normal"
                :disabled="!allowFeatureInteraction"
                @click="tools.handleLog()"
              >
                LOG
              </UButton>
              <UPopover>
                <UButton
                  size="md"
                  color="soft-gray"
                  variant="outline"
                  icon="i-heroicons-arrow-down-tray"
                  :disabled="!allowFeatureInteraction"
                />

                <template #panel="{ close }">
                  <div class="flex flex-col p-2">
                    <UButton
                      color="midnight"
                      variant="ghost"
                      block
                      @click="
                        () => {
                          close()
                          print('pdf')
                        }
                      "
                    >
                      Export as PDF
                    </UButton>
                    <UButton
                      color="midnight"
                      variant="ghost"
                      block
                      @click="
                        () => {
                          close()
                          print('png')
                        }
                      "
                    >
                      Export as PNG
                    </UButton>
                    <UButton
                      color="midnight"
                      variant="ghost"
                      block
                      @click="
                        () => {
                          close()
                          print('jpg')
                        }
                      "
                    >
                      Export as JPG
                    </UButton>
                  </div>
                </template>
              </UPopover>
            </div>
          </div>
        </div>
      </div>

      <div class="hidden flex-wrap items-center justify-end gap-4 lg:flex-nowrap xl:flex">
        <div
          v-if="pending"
          class="flex items-center gap-x-2"
        >
          <USkeleton
            v-for="item in 2"
            :key="item"
            class="h-9 w-12"
          />
        </div>
        <div
          v-else
          class="hidden items-center gap-x-2 lg:flex"
        >
          <USkeleton
            v-if="pending"
            class="h-9 w-12"
          />
          <UButton
            size="md"
            variant="outline"
            :color="log ? 'brand' : 'soft-gray'"
            class="!font-normal"
            :disabled="!allowFeatureInteraction"
            @click="tools.handleLog()"
          >
            LOG
          </UButton>
          <UPopover>
            <UButton
              size="md"
              color="soft-gray"
              variant="outline"
              icon="i-heroicons-arrow-down-tray"
              :disabled="!allowFeatureInteraction"
            />

            <template #panel="{ close }">
              <div class="flex flex-col p-2">
                <UButton
                  color="midnight"
                  variant="ghost"
                  block
                  @click="
                    () => {
                      close()
                      print('pdf')
                    }
                  "
                >
                  Export as PDF
                </UButton>
                <UButton
                  color="midnight"
                  variant="ghost"
                  block
                  @click="
                    () => {
                      close()
                      print('png')
                    }
                  "
                >
                  Export as PNG
                </UButton>
                <UButton
                  color="midnight"
                  variant="ghost"
                  block
                  @click="
                    () => {
                      close()
                      print('jpg')
                    }
                  "
                >
                  Export as JPG
                </UButton>
              </div>
            </template>
          </UPopover>
        </div>
      </div>

      <div class="relative flex flex-1 flex-col">
        <CheckFeature
          :allowed="!!allowFeatureInteraction"
          :loading="pending"
        >
          <div
            v-if="pending"
            class="grid h-[370px] w-full place-items-center 2xl:h-[450px]"
          >
            <LottieNodiensLoader />
          </div>

          <NoDataAvailable
            v-else-if="isComparisonChartEmpty"
            class="h-[370px] 2xl:h-[450px]"
          />

          <template v-else>
            <ClientOnly>
              <div
                id="financial-line-chart-element"
                ref="stablecoinChartRef"
                :class="['h-[370px] 2xl:h-[450px]']"
              />
            </ClientOnly>
            <div class="relative mt-4 h-[84px]">
              <div
                id="range-slider"
                class="block bg-transparent"
              />
              <div
                class="absolute top-0 flex h-[50px] w-full justify-center overflow-hidden"
                style="z-index: 0"
              >
                <div
                  ref="stablecoinChartBottomRef"
                  class="h-[50px] w-full overflow-hidden rounded border border-neutral-400"
                  style="max-width: 98.6607%"
                />
              </div>
            </div>
          </template>
        </CheckFeature>
      </div>

      <div
        ref="exportElement"
        class="absolute bottom-0 left-0 right-0 top-0 flex h-[693px] w-[1648px] flex-col gap-4 bg-white p-8 dark:bg-black"
        style="display: none"
      >
        <h1>Downloaded from Nodiens, {{ printTime }}</h1>

        <div class="relative flex h-full gap-6 p-4 pb-12">
          <ColorScheme>
            <img
              v-if="darkMode"
              src="~/assets/media/chart-watermark-night.png"
              class="absolute z-[1] w-7/12"
              style="left: 50%; top: 50%; transform: translate(-50%, -50%)"
              alt="Chart watermark"
            />
            <img
              v-else
              src="~/assets/media/chart-watermark.png"
              class="absolute z-[1] w-7/12"
              style="left: 50%; top: 50%; transform: translate(-50%, -50%)"
              alt="Chart watermark"
            />
          </ColorScheme>

          <div class="relative flex flex-1 items-end py-4">
            <div class="absolute right-0 top-0 flex flex-col gap-0.5 border border-neutral-300 p-2">
              <div
                v-if="coin1Filter.coin"
                class="flex items-center gap-1.5"
              >
                <div
                  class="h-0.5 w-5"
                  :style="`background: ${coin1Filter.color ?? '#CECECE'}`"
                />
                <span class="text-xs text-neutral-800 dark:text-white">{{ coin1Filter.coin.name }}</span>
              </div>
              <div
                v-if="coin2Filter.coin"
                class="flex items-center gap-1.5"
              >
                <div
                  class="h-0.5 w-5"
                  :style="`background: ${coin2Filter.color ?? '#CECECE'}`"
                />
                <span class="text-xs text-neutral-800 dark:text-white">{{ coin2Filter.coin.name }}</span>
              </div>
              <div
                v-if="coin3Filter.coin"
                class="flex items-center gap-1.5"
              >
                <div
                  class="h-0.5 w-5"
                  :style="`background: ${coin3Filter.color ?? '#CECECE'}`"
                />
                <span class="text-xs text-neutral-800 dark:text-white">{{ coin3Filter.coin.name }}</span>
              </div>
            </div>
            <img
              ref="radarSnapshot"
              class="h-full w-full object-contain dark:bg-transparent"
            />
          </div>
          <div class="h-full border-l dark:border-neutral-500" />
          <div
            ref="lineSnapshot"
            class="flex flex-1 flex-col justify-center gap-y-14 py-4"
          >
            <div
              class="bg-neutrals-600 w-full rounded px-4 py-3"
              :style="`background: ${props.titleColor}`"
            >
              <p class="text-center text-base text-white">
                {{ props.title }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <LazyShareModal
      v-if="isShareModalOpen"
      :shared-text="fullPath"
      @on-close="isShareModalOpen = false"
    />
  </ClientOnly>
</template>
<style>
#range-slider {
  margin: auto;
  width: 100%;
  height: 50px;
  background: transparent;
  overflow: hidden;
}

#range-slider .range-slider__thumb:nth-child(3) {
  transform: translate(0%, -50%) !important;
}

#range-slider .range-slider__thumb:nth-child(4) {
  transform: translate(-100%, -50%) !important;
}

.dark #range-slider .range-slider__thumb {
  border: 1px solid #e5e5e5;
}

#range-slider .range-slider__thumb {
  width: 14px;
  height: 100%;
  border-radius: 4px;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='18' height='18' fill='%23333' viewBox='0 0 24 24'%3E%3Cpath d='M12,16A2,2 0 0,1 14,18A2,2 0 0,1 12,20A2,2 0 0,1 10,18A2,2 0 0,1 12,16M12,10A2,2 0 0,1 14,12A2,2 0 0,1 12,14A2,2 0 0,1 10,12A2,2 0 0,1 12,10M12,4A2,2 0 0,1 14,6A2,2 0 0,1 12,8A2,2 0 0,1 10,6A2,2 0 0,1 12,4Z' /%3E%3C/svg%3E")
    #fff;
  border: 1px solid #c3c3c3;
  background-repeat: no-repeat;
  background-position: center;
}

.dark #range-slider .range-slider__range {
  border: 1px solid #d9d9d9;
}

#range-slider .range-slider__range {
  border-radius: 6px;
  background: rgba(0, 163, 255, 0.1);
  border: 1px solid #c3c3c3;
  box-sizing: border-box;
}
</style>
