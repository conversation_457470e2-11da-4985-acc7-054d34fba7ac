<template>
  <div class="mt-0 md:mt-10">
    <UCard>
      <CoinFilterGroup>
        <template #filters>
          <slot name="filters" />
        </template>
      </CoinFilterGroup>
      <!-- Content Container -->
      <div class="relative w-full">
        <div class="grid min-h-[484px] grid-cols-1 lg:grid-cols-2 2xl:min-h-[692px]">
          <div class="relative col-span-1 md:mr-6">
            <div
              v-if="!props.radarLoading && isCoinSelected"
              class="absolute right-0 top-0 flex flex-col gap-0.5 border border-neutral-300 p-2"
            >
              <div
                v-if="coin1Filter.coin"
                class="flex items-center gap-1.5"
              >
                <div
                  class="h-0.5 w-5"
                  :style="`background: ${STABLECOIN_LINE_STYLE['coin-1']?.color ?? '#CECECE'}`"
                />
                <span class="text-xs text-neutral-800 dark:text-white">{{ coin1Filter.coin?.symbol }}</span>
              </div>
              <div
                v-if="coin2Filter.coin"
                class="flex items-center gap-1.5"
              >
                <div
                  class="h-0.5 w-5"
                  :style="`background: ${STABLECOIN_LINE_STYLE['coin-2']?.color ?? '#CECECE'}`"
                />
                <span class="text-xs text-neutral-800 dark:text-white">{{ coin2Filter.coin?.symbol }}</span>
              </div>
              <div
                v-if="coin3Filter.coin"
                class="flex items-center gap-1.5"
              >
                <div
                  class="h-0.5 w-5"
                  :style="`background: ${STABLECOIN_LINE_STYLE['coin-3']?.color ?? '#CECECE'}`"
                />
                <span class="text-xs text-neutral-800 dark:text-white">{{ coin3Filter.coin?.symbol }}</span>
              </div>
            </div>

            <div
              v-if="!props.radarLoading"
              class="absolute left-0 top-0"
            >
              <InformationPopover
                class="flex items-center justify-center border p-0.5"
                icon-class="border !w-[24px] !h-[24px] flex items-center justify-center"
                :popper="{
                  placement: 'bottom-start',
                }"
              >
                <TooltipContent no-wrap>
                  <div
                    v-for="(group, i) in props.metricGroups"
                    :key="i"
                    class="flex items-center gap-2"
                  >
                    <div
                      class="h-[8px] w-[8px] rounded-full"
                      :style="`background: ${group.color}`"
                    />
                    <p>{{ group.group }}</p>
                  </div>
                  <slot name="legend" />
                </TooltipContent>
              </InformationPopover>
            </div>

            <div class="h-[430px] w-full overflow-visible py-5 sm:h-[544px] md:h-full">
              <MetricsRadarChart
                ref="radarRef"
                :radar-dataset="props.radarDataset"
                :radar-loading="props.radarLoading"
                :metric-color="props.metricColor"
                :metric-obj-key="props.metricObjKey"
                :decimal-decision="props.decimalDecision"
                :unit-decision="props.unitDecision"
              />
            </div>
          </div>

          <div class="col-span-1 lg:border-l lg:pl-6 dark:border-neutral-500">
            <MetricComparisonLineChart
              v-if="props.metricObjKey('chartType', selectedLabelIndex) === CHART_TYPES.LINE_CHART"
              :key="stablecoinLinechartKey"
              :pending="lineLoading"
              class="h-full"
              :title="lineChartTitle"
              :title-color="props.metricColor(selectedLabelIndex)"
              :chart-data="lineChartData ?? undefined"
              :selected-metric="props.metricObjKey('compareUnit', selectedLabelIndex)"
              :selected-label-index="selectedLabelIndex"
              :description-tooltip="props.metricObjKey('description', selectedLabelIndex)"
              :on-get-radar-snapshot="onGetRadarChartImage"
              :hide-value-unit="props.hideValueUnit"
              :is-decentralisation="props.isDecentralisation"
              @on-click-share="isShareModalOpen = true"
            />
            <template v-else-if="props.metricObjKey('chartType', selectedLabelIndex) === CHART_TYPES.DECISION_TREE">
              <TreeChart
                :key="selectedLabelIndex"
                :selected-metric="props.metricObjKey('compareUnit', selectedLabelIndex)"
                :title="lineChartTitle"
                :nodes="_nodes"
                :edges="_edges"
                :title-color="props.metricColor(selectedLabelIndex)"
                :description-tooltip="props.metricObjKey('description', selectedLabelIndex)"
                :pending="props.radarLoading || lineLoading"
              />
            </template>
          </div>
        </div>
      </div>
    </UCard>
  </div>
</template>
<script setup lang="ts">
import { STABLECOIN_LINE_STYLE } from '~/constants/stablecoin'
import type { ChartData } from 'chart.js'
import { COIN_SIZE_LIST } from '~/constants/options'
import { useRadarLineChart } from '~/composables/useRadarLineChart'
import { CHART_TYPES } from '~/constants/charts'
import { useCoinDistrTree } from '~/composables/useCoinDistrTree'
import { useIpGovOrdinalTree } from '~/composables/useIpGovOrdinalTree'

const queryKey = inject('queryKey')

const props = defineProps<{
  radarDataset: ChartData
  radarLoading: boolean
  lineLoading: boolean
  metricColor: (index: number) => string
  lineChartData: any
  metricObjKey: (key: string, index: number) => any
  metricGroups: any[]
  decimalDecision?: (metric?: string) => string | undefined
  unitDecision?: (metric?: string) => string
  hideValueUnit?: boolean
  isDecentralisation?: boolean
}>()
const route = useRoute()
const router = useRouter()
const {
  coin1Filter,
  coin2Filter,
  coin3Filter,
  loading,
  radarRef,
  selectedLabelIndex,
  lineChartTitle,
  isShareModalOpen,
  stablecoinLinechartKey,
} = useRadarLineChart()

const { nodes: ipNodes, edges: ipEdges } = useIpGovOrdinalTree()
const { nodes: coinDistrNodes, edges: coinDistrEdges } = useCoinDistrTree()

const _nodes = computed(() => {
  if (props.metricObjKey('compareUnit', selectedLabelIndex.value) === 'ipGovOrdinal') {
    return ipNodes.value
  }
  if (props.metricObjKey('compareUnit', selectedLabelIndex.value) === 'coinDistrOrdinal') {
    return coinDistrNodes.value
  }
  return []
})

const _edges = computed(() => {
  if (props.metricObjKey('compareUnit', selectedLabelIndex.value) === 'ipGovOrdinal') {
    return ipEdges.value
  }
  if (props.metricObjKey('compareUnit', selectedLabelIndex.value) === 'coinDistrOrdinal') {
    return coinDistrEdges.value
  }
  return []
})

const isCoinSelected = computed(() => {
  return coin1Filter.value.coin || coin2Filter.value.coin || coin3Filter.value.coin
})

const onGetRadarChartImage = (): string => {
  if (radarRef?.value) {
    return radarRef?.value?.getBase64()
  }

  return ''
}

const initialQueryString = () => {
  const query: Record<string, string> = {}
  if (!route.query.size && COIN_SIZE_LIST[0]) {
    query.size = COIN_SIZE_LIST[0].id
  }
  if (!route.query[`${queryKey}1`]) {
    query[`${queryKey}1`] = coin1Filter.value.coin?.slug ?? ''
    query[`${queryKey}2`] = coin2Filter.value.coin?.slug ?? ''
    query[`${queryKey}3`] = coin3Filter.value.coin?.slug ?? ''
  }
  if (!route.query.metric) {
    query.metric = props.metricObjKey('compareUnit', 0)
    selectedLabelIndex.value = 0
  }
  if (Object.keys(query).length > 0) {
    router.replace({
      query,
    })
  }
}

onMounted(() => {
  initialQueryString()
  // TODO: Uncomment
  // if (!assetFound.value) {
  //   if (route.query[`${ queryKey }1`]) {
  //     router.replace(`/community/mood/${ route.query[`${ queryKey }1`] }`)
  //     return
  //   }
  // }
  loading.value = true
  selectedLabelIndex.value = 0
  lineChartTitle.value = props.metricObjKey('label', 0)
  loading.value = false
})
</script>
