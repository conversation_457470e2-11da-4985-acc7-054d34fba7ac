<script setup lang="ts">
import { Chart as ChartJS, type ChartData, registerables } from 'chart.js'
import { Chart as VueChart } from 'vue-chartjs'
import { numericDisplay } from '~/helper/number-helper'

// Types
import type {
  ScriptableScaleContext,
  ScriptableScalePointLabelContext,
  ChartEvent,
  Chart,
  ChartDataset,
} from 'chart.js'
import type { ChartComponentRef } from 'vue-chartjs'
import { useRadarLineChart } from '~/composables/useRadarLineChart'

ChartJS.register(...registerables)

ChartJS.defaults.font.family = 'IBM Plex Mono'

interface CustomRadarDataset extends ChartDataset<'radar'> {
  rawData?: number[] // Custom field to store additional info
}

const props = defineProps<{
  radarDataset: ChartData
  radarLoading: boolean
  metricColor: (index: number) => string
  metricObjKey: (label: string, index: number) => string
}>()

const { $colorMode } = useNuxtApp()
const radarChartRef = ref<ChartComponentRef<'radar'>>()
const radarChartKey = ref(0)
const router = useRouter()
const route = useRoute()

const { isMobile } = useDeviceScreen()
const { selectedLabelIndex, lineChartTitle, valueUnitDecision, valueDecimalDecision } = useRadarLineChart()

const getBase64 = (): string => {
  return radarChartRef.value?.chart?.toBase64Image('image/png', 1) ?? ''
}

defineExpose({
  getBase64,
})

const darkMode = computed({
  get() {
    return $colorMode.value === 'dark'
  },
  set(value) {
    if (value) {
      $colorMode.preference = 'dark'
    } else {
      $colorMode.preference = 'light'
    }
  },
})

const maxData = computed(() => {
  const flatData = props.radarDataset.datasets
    .map((item) => item.data)
    .flat()
    .filter((val): val is number => val !== null)
  return Math.max(...flatData)
})

const isChartDataEmpty = computed(() => {
  const dataAreAllZeroes = props.radarDataset.datasets
    .map((data) => data.data)
    .flat()
    .every((x) => x === 0)
  return (Array.isArray(props.radarDataset?.datasets) && dataAreAllZeroes) || props.radarDataset.datasets.length === 0
})

function getLabelIndex(x: number, y: number, pointLabelItems: any) {
  let labelIndex = -1

  for (let i = 0; i < pointLabelItems.length; i++) {
    const item = pointLabelItems[i]
    if (x >= item.left && x <= item.right && y >= item.top && y <= item.bottom) {
      labelIndex = i
      break
    }
  }

  return labelIndex
}

watch(
  [selectedLabelIndex, radarChartRef, props.radarDataset],
  ([_, newRadarChartRef]) => {
    // Update radar chart
    if (newRadarChartRef?.chart) {
      newRadarChartRef?.chart?.update()
    }
  },
  {
    immediate: true,
  },
)

watch(darkMode, () => {
  if (radarChartRef.value?.chart) {
    radarChartRef.value.chart.update()
  }
})
</script>
<template>
  <ClientOnly>
    <div
      v-if="radarLoading"
      class="grid h-full w-full place-items-center"
    >
      <LottieNodiensLoader />
    </div>
    <NoDataAvailable
      v-else-if="isChartDataEmpty"
      class="v1-3xl:mt-0 h-full md:mt-14"
    />
    <VueChart
      v-else
      :key="radarChartKey"
      ref="radarChartRef"
      type="radar"
      :data="props.radarDataset"
      :options="{
        plugins: {
          title: {
            display: false,
          },
          legend: {
            display: false,
          },
          tooltip: {
            callbacks: {
              label: (context) => {
                const compareUnit = props.metricObjKey('compareUnit', context.dataIndex)
                const dataSet = context.dataset as CustomRadarDataset
                const value = numericDisplay(
                  (dataSet.rawData![context.dataIndex] as string | number) ?? dataSet.data[context.dataIndex],
                  valueDecimalDecision?.(compareUnit) || undefined,
                )

                return context.dataset.label + ': ' + valueUnitDecision(compareUnit) === 'currency'
                  ? `$${value}`
                  : `${value}%`
              },
            },
          },
        },
        responsive: true,
        maintainAspectRatio: false,
        elements: {
          point: {
            radius: 0,
          },
        },
        scales: {
          r: {
            startAngle: 0,
            pointLabels: {
              font: (ctx: ScriptableScalePointLabelContext) => {
                const size = isMobile ? 8 : 12
                if (ctx.index === selectedLabelIndex) {
                  return {
                    weight: 'bold',
                    size,
                  }
                }
                return {
                  weight: 'normal',
                  size,
                }
              },
              color: (context) => {
                if (context.index === selectedLabelIndex) {
                  return '#fff'
                }
                return props.metricColor(context.index)
              },
              backdropColor: (context) =>
                context.index === selectedLabelIndex ? props.metricColor(selectedLabelIndex) : 'transparent',
              backdropPadding: (context) => (context.index === selectedLabelIndex ? 5 : 2),
              borderRadius: (context) => (context.index === selectedLabelIndex ? 3 : 2),
              padding: () => 8,
            },
            beginAtZero: true,
            max: maxData || 0,
            ticks: {
              display: false,
            },
            border: {
              dash: (ctx: ScriptableScaleContext) => {
                if (ctx.tick.value === maxData) {
                  return []
                } else {
                  return [4, 4]
                }
              },
            },
            grid: {
              color: '#C3C3C3',
              lineWidth: (ctx: ScriptableScaleContext) => {
                const ticksLen = ctx?.chart?.scales?.r?.ticks.length || 0
                const midTickIndex = Math.ceil(ticksLen / 2)

                if (ctx.index === 3 || ctx.tick.value === maxData || ctx.index === midTickIndex) {
                  return 1
                }

                return 0
              },
            },
            angleLines: {
              color: '#C3C3C3',
              borderDash: [4, 4],
            },
          },
        },
        onHover: ({ x, y }: ChartEvent, _, chart: Chart) => {
          const canvas = chart.canvas
          // @ts-expect-error ignore
          const index = getLabelIndex(x || 0, y || 0, chart.scales.r._pointLabelItems)

          if (index !== -1) {
            canvas.style.cursor = 'pointer'
          } else {
            canvas.style.cursor = 'default'
          }
        },
        onClick: (ctx: ChartEvent, _, chart: Chart) => {
          const { x, y } = ctx
          // @ts-expect-error ignore
          const index = getLabelIndex(x || 0, y || 0, chart.scales.r._pointLabelItems)

          if (index !== -1) {
            selectedLabelIndex = index
            const compareUnit = props.metricObjKey('compareUnit', index)
            router.replace({
              query: {
                ...route.query,
                metric: compareUnit,
              },
            })
            lineChartTitle = props.metricObjKey('label', index)
          }
        },
      }"
    />
  </ClientOnly>
</template>
