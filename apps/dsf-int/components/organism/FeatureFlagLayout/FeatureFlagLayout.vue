<script setup lang="ts">
import { useFeatureFlags } from '~/stores/feature-flags'

// Types
interface FeatureFlagLayoutProps {
  featureKey: string
}
const props = defineProps<FeatureFlagLayoutProps>()

const config = useRuntimeConfig()
const featureFlagStore = useFeatureFlags()

const isEnabled = computed(() => featureFlagStore.isFeatureEnabled(props.featureKey) && JSON.parse(config.public.featureFlagsEnabled))
</script>

<template>
  <div>
    <div v-if="!isEnabled" class="flex min-h-[65vh] w-full flex-col items-center justify-center text-center">
      <h3 class="mb-1 text-2xl font-medium text-black dark:text-white">Oops</h3>
      <p class="text-sm text-black dark:text-white">
        This feature is not released yet and not enabled in feature flags
      </p>
    </div>

    <div v-else>
      <slot />
    </div>
  </div>
</template>
