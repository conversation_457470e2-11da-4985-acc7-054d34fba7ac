<script setup lang="ts">
import { useFeatureFlags } from '~/stores/feature-flags'

// Types
interface FeatureFlagLayoutProps {
  featureKey: string
}
const props = defineProps<FeatureFlagLayoutProps>()

const config = useRuntimeConfig()
const featureFlagStore = useFeatureFlags()

const isEnabled = computed(() => featureFlagStore.isFeatureEnabled(props.featureKey) && JSON.parse(config.public.featureFlagsEnabled))
</script>

<template>
  <div>
    <div v-if="isEnabled">
      <slot />
    </div>
    <div v-else>
      <slot name="non-ff" />
    </div>
  </div>
</template>
