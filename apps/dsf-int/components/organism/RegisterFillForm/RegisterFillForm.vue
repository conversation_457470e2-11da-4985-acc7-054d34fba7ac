<script setup lang="ts">
import { useChallengeV2 } from 'vue-recaptcha'
import { z } from 'zod'
import { checkPasswordRules } from '~/helper/string-helper'
import { usePasswordChecker } from '~/components/molecule/PasswordChecker/usePasswordChecker'

// Types
import type { FormSubmitEvent } from '#ui/types'
import type { TRPCError } from '@trpc/server'

const schema = z
  .object({
    first_name: z.string().nonempty('First name cannot be empty'),
    last_name: z.string().nonempty('Last name cannot be empty'),
    email: z.string().email('Email must be a valid address').nonempty('Email cannot be empty'),
    company: z.string().optional(),
    password: checkPasswordRules,
    confirm_password: z.string().nonempty('Confirm password cannot be empty'),
    promo: z.string().optional(),
  })
  .refine((data) => data.confirm_password === data.password, {
    path: ['confirm_password'],
    message: "Password doesn't match",
  })

type Schema = z.output<typeof schema>

const props = defineProps<{
  connectedWallet: string
  nonce: number
}>()

defineEmits<{
  (e: 'onResetWallet'): void
}>()

const { $client, $authClient } = useNuxtApp()

const router = useRouter()
const toast = useToast()
const { switchView, view } = usePasswordChecker()
const { getSigner } = useHashpackService()

const formState = reactive<Schema>({
  first_name: '',
  last_name: '',
  email: '',
  company: '',
  password: '',
  confirm_password: '',
  promo: '',
})
const isAgree = ref(false)
const isVerify = ref(false)
const isLoading = ref(false)
const isRecaptchaError = ref(false)

const { root, onVerify, onExpired } = useChallengeV2({
  options: {
    theme: 'light',
    size: 'normal',
  },
})

onVerify(() => {
  isVerify.value = true
  isRecaptchaError.value = false
})

onExpired(() => {
  isVerify.value = false
})

const signUpUsingWeb3 = async (event: FormSubmitEvent<Schema>) => {
  isLoading.value = true
  try {
    const signer = getSigner(props.connectedWallet)
    const signature = await signer.sign([Buffer.from(`${props.nonce}`)])

    await $client.v2.auth.signUpWeb3.mutate({
      email: event.data.email,
      password: event.data.password,
      first_name: event.data.first_name,
      last_name: event.data.last_name,
      confirm_password: event.data.confirm_password,
      company: event.data.company,
      signature,
      accountId: props.connectedWallet,
    })

    router.replace('/auth/register-success?email=' + event.data.email)
  } catch (error) {
    const _error = error as TRPCError
    toast.add({
      title: 'Register Failed',
      description: _error.message,
      icon: 'i-heroicons-exclamation-triangle-20-solid',
    })
  } finally {
    isLoading.value = false
  }
}

const signUpUsingEmail = async (event: FormSubmitEvent<Schema>) => {
  isLoading.value = true
  try {
    await $authClient.signUp.email({
      email: event.data.email,
      password: event.data.password,
      name: `${event.data.first_name} ${event.data.last_name}`,
    })

    router.replace('/auth/register-success?email=' + event.data.email)
  } catch (error) {
    const _error = error as TRPCError
    toast.add({
      title: 'Register Failed',
      description: _error.message,
      icon: 'i-heroicons-exclamation-triangle-20-solid',
    })
  } finally {
    isLoading.value = false
  }
}

const onSubmit = async (event: FormSubmitEvent<Schema>) => {
  if (!isVerify.value) {
    toast.add({
      title: 'Register Failed',
      description: 'Please verify you are human',
      color: 'red',
      icon: 'i-heroicons-exclamation-triangle-20-solid',
    })
    return
  }

  if (!isAgree.value) {
    toast.add({
      title: 'Register Failed',
      color: 'red',
      description: 'You must agree to the terms and conditions',
      icon: 'i-heroicons-exclamation-triangle-20-solid',
    })
    return
  }
  if (props.connectedWallet) {
    // User is registering using hashpack wallet
    await signUpUsingWeb3(event)
  } else {
    // User is registering using email
    await signUpUsingEmail(event)
  }
}
</script>

<template>
  <UForm
    :state="formState"
    :schema="schema"
    @submit="onSubmit"
    class="flex flex-col"
  >
    <h2 class="mb-2 text-2xl font-semibold">Register</h2>
    <p class="text-neutrals-800 mb-5 text-sm dark:text-white">
      Complete the registration process using your Web3 wallet.
    </p>

    <UFormGroup
      v-if="connectedWallet"
      label="Connected Wallet"
      class="mb-3"
    >
      <UInput
        :model-value="connectedWallet"
        disabled
      />
    </UFormGroup>
    <div class="mb-3 grid grid-cols-1 gap-4 md:grid-cols-2">
      <UFormGroup
        label="First Name"
        name="first_name"
      >
        <UInput
          v-model="formState.first_name"
          name="first_name"
        />
      </UFormGroup>
      <UFormGroup
        label="Last Name"
        name="last_name"
      >
        <UInput
          v-model="formState.last_name"
          name="last_name"
        />
      </UFormGroup>
    </div>
    <div class="mb-3 grid grid-cols-1 gap-4 md:grid-cols-2">
      <UFormGroup
        label="Email"
        name="email"
        class="mb-3"
      >
        <UInput
          v-model="formState.email"
          name="email"
          type="email"
        />
      </UFormGroup>
      <UFormGroup
        label="Company"
        hint="Optional"
        name="company"
        class="mb-3"
      >
        <UInput
          v-model="formState.company"
          name="company"
          type="text"
        />
      </UFormGroup>
    </div>

    <div class="mb-3 grid grid-cols-1 gap-4 md:grid-cols-2">
      <UFormGroup
        label="Password"
        name="password"
      >
        <InputPassword
          v-model="formState.password"
          name="password"
          @focus="switchView(false)"
          @blur="switchView(true)"
        />
      </UFormGroup>
      <UFormGroup
        label="Confirm Password"
        name="confirm_password"
      >
        <InputPassword
          v-model="formState.confirm_password"
          name="confirm_password"
          @focus="switchView(false)"
          @blur="switchView(true)"
        />
      </UFormGroup>
    </div>
    <PasswordChecker
      v-if="view"
      :password="formState.password as string"
    />
    <UFormGroup
      label="Promo/Admin Code"
      name="promo"
      help="Do you have a Promo/Admin Code? Insert the code to access promotion and ad hoc services made for you"
      class="mb-3"
    >
      <UInput
        v-model="formState.promo"
        name="promo"
      />
    </UFormGroup>

    <TermsAndPrivacy v-model="isAgree" />

    <div class="my-5 flex flex-col items-center justify-center">
      <div :class="{ 'border border-red-500 p-1': isRecaptchaError }">
        <div ref="root" />
        <span
          v-if="isRecaptchaError"
          class="text-xs text-red-500"
          >Please verify you are human</span
        >
      </div>
    </div>

    <div class="grid grid-cols-2 gap-3">
      <UButton
        color="base-primary"
        size="lg"
        label="Back"
        class="px-12"
        type="button"
        :loading="isLoading"
        variant="outline"
        block
        @click="$emit('onResetWallet')"
      />
      <UButton
        color="base-primary"
        size="lg"
        label="Register"
        class="px-12"
        type="submit"
        :loading="isLoading"
        block
      />
    </div>
  </UForm>
</template>
