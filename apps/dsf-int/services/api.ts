import createClient, { type ClientOptions } from 'openapi-fetch'
import type { paths } from '~/types/schema'

export const createApiClient = (config?: ClientOptions) => {
  let apiBaseUrl = '/api-base'
  if (import.meta.client) {
    apiBaseUrl = useRuntimeConfig().public.apiBaseUrl
  } else {
    apiBaseUrl = process.env.NUXT_API_BASE_URL ?? ''
  }

  const api = createClient<paths>({
    baseUrl: apiBaseUrl,
    credentials: 'include',
    ...config,
  })

  return api
}

export default createApiClient()
