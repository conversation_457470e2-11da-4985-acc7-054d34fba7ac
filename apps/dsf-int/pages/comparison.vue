<script setup lang="ts">
// Types
import type { ClimageComparison } from 'app-types/payload-types'
import Image from '~/components/atom/Image/Image.vue'

const config = useRuntimeConfig()
const { $colorMode } = useNuxtApp()
const route = useRoute()
const router = useRouter()

const { data, refresh } = await useLazyAsyncData<{ docs: ClimageComparison[] }>(
  'comparisonTableData',
  async (ctx) => {
    return $fetch<{ docs: ClimageComparison[] }>(`${ctx?.$config.public.payloadApi}/climage-comparisons/sorted`, {
      method: 'GET',
    })
  },
  {
    server: false,
    immediate: false,
  },
)

const darkMode = computed({
  get() {
    return $colorMode.value === 'dark'
  },
  set(value) {
    if (value) {
      $colorMode.preference = 'dark'
    } else {
      $colorMode.preference = 'light'
    }
  },
})

const categoryList = computed<
  {
    label: string
    value: string
  }[]
>(() => {
  return [
    { label: 'Community Indexes', value: 'community-indices' },
    { label: 'Financial Indexes', value: 'financial-indices' },
    { label: 'Climate Indexes', value: 'climate-indices' },
  ]
})

const handleSelectCategory = (category: string) => router.replace({ query: { category } })

const activeCategory = computed(() =>
  categoryList.value.find((category: { label: string; value: string }) => category.value === route.query.category),
)

watch(
  () => route?.query,
  () => refresh(),
  { deep: true },
)

onMounted(() => {
  if (!route.query.category) {
    router.replace({ query: { category: categoryList.value[0]?.value } })
  }
  refresh()
})
</script>

<template>
  <div>
    <HeaderPage
      page-name="climate-comparison"
      hide-navigation
      center
    />

    <div class="p-5">
      <div class="mb-10 hidden items-center justify-center gap-5 md:flex">
        <UButton
          v-for="(category, i) in categoryList"
          :key="i"
          color="black"
          size="xl"
          :variant="route.query.category === category.value ? 'solid' : 'outline'"
          padded
          :ui="{ rounded: 'rounded' }"
          @click="handleSelectCategory(category.value)"
        >
          {{ category.label }}
        </UButton>
      </div>

      <div class="mb-10 items-center justify-center gap-5 md:hidden">
        <USelectMenu
          v-slot="{ open }"
          size="xl"
          :options="categoryList"
          option-attribute="label"
          value-attribute="value"
          :model-value="route.query.category as string"
          @change="handleSelectCategory"
        >
          <UButton
            color="black"
            class="h-10 flex-1 justify-between"
          >
            {{ activeCategory?.label }}

            <UIcon
              name="i-heroicons-chevron-right-20-solid"
              class="w-5 text-gray-400 transition-transform dark:text-gray-500"
              :class="[open && 'rotate-90 transform']"
            />
          </UButton>
        </USelectMenu>
      </div>

      <UCard>
        <Suspense>
          <table
            class="table-primitive w-full"
            :border="0"
          >
            <thead>
              <tr>
                <th />
                <th
                  v-for="(comparison, comparisonIndex) in data?.docs"
                  :key="comparisonIndex"
                  class="w-[200px] py-3"
                  :class="[comparisonIndex === (data?.docs.length ?? 0) - 1 && 'bg-neutrals-200 dark:bg-neutrals-500']"
                >
                  <div class="flex items-center justify-center">
                    <ClientOnly>
                      <Image
                        v-if="darkMode"
                        :src="`${config.public.payloadApi.replace('/api', '')}${typeof comparison.logo_light === 'object' && 'url' in comparison.logo_light ? comparison.logo_light.url : comparison.logo_light}`"
                        :alt="
                          typeof comparison.logo === 'object' && 'alt' in comparison.logo
                            ? (comparison.logo.alt ?? '')
                            : ''
                        "
                        class="object-contain"
                        height="80"
                        width="140"
                      />
                      <Image
                        v-else
                        :src="`${config.public.payloadApi.replace('/api', '')}${typeof comparison.logo === 'object' && 'url' in comparison.logo ? comparison.logo.url : comparison.logo}`"
                        :alt="
                          typeof comparison.logo === 'object' && 'alt' in comparison.logo
                            ? (comparison.logo.alt ?? '')
                            : ''
                        "
                        class="object-contain"
                        height="80"
                        width="140"
                      />
                    </ClientOnly>
                  </div>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr class="border-neutrals-200 dark:border-neutrals-500 border-b">
                <td class="flex items-center gap-x-1 py-6">
                  <h3 class="text-base font-medium text-gray-900 dark:text-white">L1 PoW</h3>

                  <InformationPopover icon-class="mt-1.5">
                    <TooltipContent>
                      <p>Lorem ipsum dolor sit amet</p>
                    </TooltipContent>
                  </InformationPopover>
                </td>
                <td
                  v-for="(comparison, comparisonIndex) in data?.docs"
                  :key="comparisonIndex"
                  class="py-3 text-center"
                  :class="[comparisonIndex === (data?.docs.length ?? 0) - 1 && 'bg-neutrals-200 dark:bg-neutrals-500']"
                >
                  <div
                    v-if="!!comparison.l1_pow"
                    class="flex flex-col items-center gap-y-1"
                  >
                    <div
                      v-if="comparison.l1_pow.includes('coins')"
                      class="flex items-center justify-center"
                    >
                      <div class="bg-neutrals-100 flex h-[30px] w-[30px] items-center justify-center rounded-full">
                        <UIcon
                          name="i-heroicons-check"
                          class="h-[16px] w-[16px] text-black"
                        />
                      </div>
                    </div>
                    <span class="text-center text-base">
                      {{ comparison.l1_pow }}
                    </span>
                  </div>
                  <div
                    v-else
                    class="flex items-center justify-center"
                  >
                    <div class="bg-neutrals-400/10 flex h-[30px] w-[30px] items-center justify-center rounded-full">
                      <UIcon
                        name="i-heroicons-x-mark"
                        class="text-neutrals-800 h-[16px] w-[16px] dark:text-white"
                      />
                    </div>
                  </div>
                </td>
              </tr>
              <tr class="border-neutrals-200 dark:border-neutrals-500 border-b">
                <td class="flex items-center gap-x-1 py-6">
                  <h3 class="text-base font-medium text-gray-900 dark:text-white">L1 PoS</h3>

                  <InformationPopover icon-class="mt-1.5">
                    <TooltipContent>
                      <p>Lorem ipsum dolor sit amet</p>
                    </TooltipContent>
                  </InformationPopover>
                </td>
                <td
                  v-for="(comparison, comparisonIndex) in data?.docs"
                  :key="comparisonIndex"
                  class="py-3 text-center"
                  :class="[comparisonIndex === (data?.docs.length ?? 0) - 1 && 'bg-neutrals-200 dark:bg-neutrals-500']"
                >
                  <div
                    v-if="!!comparison.l1_pos"
                    class="flex flex-col items-center gap-y-1"
                  >
                    <div
                      v-if="comparison.l1_pos.includes('coins')"
                      class="flex items-center justify-center"
                    >
                      <div class="bg-neutrals-100 flex h-[30px] w-[30px] items-center justify-center rounded-full">
                        <UIcon
                          name="i-heroicons-check"
                          class="h-[16px] w-[16px] text-black"
                        />
                      </div>
                    </div>
                    <span class="text-center text-base">
                      {{ comparison.l1_pos }}
                    </span>
                  </div>
                  <div
                    v-else
                    class="flex items-center justify-center"
                  >
                    <div class="bg-neutrals-400/10 flex h-[30px] w-[30px] items-center justify-center rounded-full">
                      <UIcon
                        name="i-heroicons-x-mark"
                        class="text-neutrals-800 h-[16px] w-[16px] dark:text-white"
                      />
                    </div>
                  </div>
                </td>
              </tr>
              <tr class="border-neutrals-200 dark:border-neutrals-500 border-b">
                <td class="flex items-center gap-x-1 py-6">
                  <h3 class="text-base font-medium text-gray-900 dark:text-white">L1 others</h3>

                  <InformationPopover icon-class="mt-1.5">
                    <TooltipContent>
                      <p>Lorem ipsum dolor sit amet</p>
                    </TooltipContent>
                  </InformationPopover>
                </td>
                <td
                  v-for="(comparison, comparisonIndex) in data?.docs"
                  :key="comparisonIndex"
                  class="py-3 text-center"
                  :class="[comparisonIndex === (data?.docs.length ?? 0) - 1 && 'bg-neutrals-200 dark:bg-neutrals-500']"
                >
                  <div
                    v-if="!!comparison.l1_others"
                    class="flex flex-col items-center gap-y-1"
                  >
                    <div
                      v-if="comparison.l1_others.includes('coins')"
                      class="flex items-center justify-center"
                    >
                      <div class="bg-neutrals-100 flex h-[30px] w-[30px] items-center justify-center rounded-full">
                        <UIcon
                          name="i-heroicons-check"
                          class="text-primary-600 h-[16px] w-[16px]"
                        />
                      </div>
                    </div>
                    <span class="text-center text-base">
                      {{ comparison.l1_others }}
                    </span>
                  </div>
                  <div
                    v-else
                    class="flex items-center justify-center"
                  >
                    <div class="bg-neutrals-400/10 flex h-[30px] w-[30px] items-center justify-center rounded-full">
                      <UIcon
                        name="i-heroicons-x-mark"
                        class="text-neutrals-800 h-[16px] w-[16px] dark:text-white"
                      />
                    </div>
                  </div>
                </td>
              </tr>
              <tr class="border-neutrals-200 dark:border-neutrals-500 border-b">
                <td class="flex items-center gap-x-1 py-6">
                  <h3 class="text-base font-medium text-gray-900 dark:text-white">Aggregate figures</h3>

                  <InformationPopover icon-class="mt-1.5">
                    <TooltipContent>
                      <p>Lorem ipsum dolor sit amet</p>
                    </TooltipContent>
                  </InformationPopover>
                </td>
                <td
                  v-for="(comparison, comparisonIndex) in data?.docs"
                  :key="comparisonIndex"
                  class="py-6 text-center"
                  :class="[comparisonIndex === (data?.docs.length ?? 0) - 1 && 'bg-neutrals-200 dark:bg-neutrals-500']"
                >
                  <div class="flex items-center justify-center">
                    <div
                      v-if="!comparison.aggregate_figures"
                      class="bg-neutrals-700/10 flex h-[30px] w-[30px] items-center justify-center rounded-full"
                    >
                      <UIcon
                        name="i-heroicons-x-mark"
                        class="text-neutrals-800 h-[16px] w-[16px] dark:text-white"
                      />
                    </div>
                    <div
                      v-else
                      class="bg-neutrals-100 flex h-[30px] w-[30px] items-center justify-center rounded-full"
                    >
                      <UIcon
                        name="i-heroicons-check"
                        class="h-[16px] w-[16px] text-black"
                      />
                    </div>
                  </div>
                </td>
              </tr>
              <tr class="border-neutrals-200 dark:border-neutrals-500 border-b">
                <td class="flex items-center gap-x-1 py-6">
                  <h3 class="text-base font-medium text-gray-900 dark:text-white">Per tx figures</h3>

                  <InformationPopover icon-class="mt-1.5">
                    <TooltipContent>
                      <p>Lorem ipsum dolor sit amet</p>
                    </TooltipContent>
                  </InformationPopover>
                </td>
                <td
                  v-for="(comparison, comparisonIndex) in data?.docs"
                  :key="comparisonIndex"
                  class="text-center"
                  :class="[comparisonIndex === (data?.docs.length ?? 0) - 1 && 'bg-neutrals-200 dark:bg-neutrals-500']"
                >
                  <div class="flex items-center justify-center">
                    <div
                      v-if="!comparison.per_tx_figures"
                      class="bg-neutrals-400/10 flex h-[30px] w-[30px] items-center justify-center rounded-full"
                    >
                      <UIcon
                        name="i-heroicons-x-mark"
                        class="text-neutrals-800 h-[16px] w-[16px] dark:text-white"
                      />
                    </div>
                    <div
                      v-else
                      class="bg-neutrals-100 flex h-[30px] w-[30px] items-center justify-center rounded-full"
                    >
                      <UIcon
                        name="i-heroicons-check"
                        class="h-[16px] w-[16px] text-black"
                      />
                    </div>
                  </div>
                </td>
              </tr>
              <tr class="border-neutrals-200 dark:border-neutrals-500 border-b">
                <td class="flex items-center gap-x-1 py-6">
                  <h3 class="text-base font-medium text-gray-900 dark:text-white">Per node figures</h3>

                  <InformationPopover icon-class="mt-1.5">
                    <TooltipContent>
                      <p>Lorem ipsum dolor sit amet</p>
                    </TooltipContent>
                  </InformationPopover>
                </td>
                <td
                  v-for="(comparison, comparisonIndex) in data?.docs"
                  :key="comparisonIndex"
                  class="text-center"
                  :class="[comparisonIndex === (data?.docs.length ?? 0) - 1 && 'bg-neutrals-200 dark:bg-neutrals-500']"
                >
                  <div class="flex items-center justify-center">
                    <div
                      v-if="!comparison.per_node_figures"
                      class="bg-neutrals-400/10 flex h-[30px] w-[30px] items-center justify-center rounded-full"
                    >
                      <UIcon
                        name="i-heroicons-x-mark"
                        class="text-neutrals-800 h-[16px] w-[16px] dark:text-white"
                      />
                    </div>
                    <div
                      v-else
                      class="bg-neutrals-100 flex h-[30px] w-[30px] items-center justify-center rounded-full"
                    >
                      <UIcon
                        name="i-heroicons-check"
                        class="h-[16px] w-[16px] text-black"
                      />
                    </div>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="flex items-center gap-x-1 py-6">
                  <h3 class="text-base font-medium text-gray-900 dark:text-white">Upper & lower bounds</h3>

                  <InformationPopover icon-class="mt-1.5">
                    <TooltipContent>
                      <p>Lorem ipsum dolor sit amet</p>
                    </TooltipContent>
                  </InformationPopover>
                </td>
                <td
                  v-for="(comparison, comparisonIndex) in data?.docs"
                  :key="comparisonIndex"
                  class="text-center"
                  :class="[comparisonIndex === (data?.docs.length ?? 0) - 1 && 'bg-neutrals-200 dark:bg-neutrals-500']"
                >
                  <div class="flex items-center justify-center">
                    <div
                      v-if="!comparison.upper_lower_bounds"
                      class="bg-neutrals-400/10 flex h-[30px] w-[30px] items-center justify-center rounded-full"
                    >
                      <UIcon
                        name="i-heroicons-x-mark"
                        class="text-neutrals-800 h-[16px] w-[16px] dark:text-white"
                      />
                    </div>
                    <div
                      v-else
                      class="bg-neutrals-100 flex h-[30px] w-[30px] items-center justify-center rounded-full"
                    >
                      <UIcon
                        name="i-heroicons-check"
                        class="h-[16px] w-[16px] text-black"
                      />
                    </div>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </Suspense>
      </UCard>
    </div>
  </div>
</template>
