<script setup lang="ts">
import { z } from 'zod'
import type { FormSubmitEvent } from '#ui/types'

// Stores
import { useSessionStore } from '~/stores/session'
import { useUserStore } from '~/stores/profile'

const toast = useToast()
const userStore = useUserStore()
const { userProfile } = storeToRefs(userStore)
const sessionStore = useSessionStore()
const isLogged = sessionStore.isLogged
const runtimeConfig = useRuntimeConfig()
const isSubmitting = ref(false)

const MAX_FILE_SIZE = 2 * 1024 * 1024 // 2MB
const ACCEPTED_MIME_TYPES = ['image/jpeg', 'image/png', 'application/pdf', 'video/mp4', 'video/quicktime']

const schema = z.object({
  first_name: z.string().nonempty('First name cannot be empty'),
  last_name: z.string().nonempty('Last name cannot be empty'),
  email: z.string().nonempty('Email cannot be empty.').email('Email must be a valid email'),
  company: z.string().optional(),
  featureTitle: z
    .string()
    .max(255, 'Feature Title must be at most 255 characters')
    .nonempty('Feature Title cannot be empty.'),
  featureDetails: z
    .string()
    .max(255, 'Feature Details must be at most 255 characters')
    .nonempty('Feature Details cannot be empty.'),
  files: z
    .array(
      z
        .custom<File>((file) => file instanceof File, 'Invalid file')
        .refine((file) => file.size <= MAX_FILE_SIZE, 'File size is too large')
        .refine((file) => ACCEPTED_MIME_TYPES.includes(file.type), 'Unsupported file format'),
    )
    .optional(),
  is_agree: z.boolean().refine((value) => value === true, {
    message: 'You must accept the Terms and Privacy Policy before proceeding',
  }),
})

type Schema = z.output<typeof schema>

const state = reactive<Schema>({
  first_name: '',
  last_name: '',
  email: '',
  company: '',
  featureTitle: '',
  featureDetails: '',
  files: [] as File[],
  is_agree: false,
})

const resetForm = () => {
  state.featureTitle = ''
  state.featureDetails = ''
  state.files = []
  state.is_agree = false
}

const onSubmit = async (event: FormSubmitEvent<Schema>) => {
  isSubmitting.value = true
  try {
    const formData = new FormData()
    formData.append('first_name', event.data.first_name)
    formData.append('last_name', event.data.last_name)
    formData.append('email', event.data.email)
    event.data.company && formData.append('company', event.data.company)
    formData.append('title', event.data.featureTitle)
    formData.append('message', event.data.featureDetails)
    event.data.files?.forEach((file) => formData.append('file', file))

    const { message } = await $fetch<{
      message: string
    }>(`${runtimeConfig.public.supabase.url}/functions/v1/bug-report`, {
      method: 'POST',
      body: formData,
    })

    toast.add({
      color: 'green',
      title: 'Request successfully sent!',
      description: message || 'Successfully saved!',
      icon: 'i-heroicons-check-circle',
    })
    resetForm()
  } catch (e: any) {
    toast.add({
      color: 'red',
      title: 'Request was not sent!',
      description: e.data?.message ?? 'Something went wrong. Please try again later.',
      icon: 'i-heroicons-exclamation-triangle-20-solid',
    })
  } finally {
    isSubmitting.value = false
  }
}

watch(
  userProfile,
  (newProfile) => {
    if (newProfile) {
      state.first_name = (newProfile?.displayName ?? '').split(' ')[0] || ''
      state.last_name = (newProfile?.displayName ?? '').split(' ')[1] || ''
      state.email = newProfile?.email ?? ''
      state.company = newProfile?.company ?? ''
    }
  },
  {
    immediate: true,
    deep: true,
  },
)

useHead({
  title: 'Feature Request',
})
</script>

<template>
  <div>
    <HeaderPage
      page-name="feature-request"
      center
      hide-navigation
      sub-title-class="w-full lg:w-3/5 m-auto"
    />
    <BodyAuxiliary>
      <UForm
        :schema="schema"
        :state="state"
        @submit.prevent="onSubmit"
      >
        <div class="mb-8 grid grid-cols-1 gap-10 sm:grid-cols-2">
          <UFormGroup
            label="First Name"
            name="first_name"
          >
            <UInput
              v-model="state.first_name"
              name="first_name"
              placeholder="Your first name"
              :disabled="isLogged"
            />
          </UFormGroup>

          <UFormGroup
            label="Last Name"
            name="last_name"
          >
            <UInput
              v-model="state.last_name"
              name="last_name"
              placeholder="Your last name"
              :disabled="isLogged"
            />
          </UFormGroup>
        </div>

        <div class="mb-8 grid grid-cols-1 gap-10 sm:grid-cols-2">
          <UFormGroup
            label="Email Address"
            name="email"
          >
            <UInput
              v-model="state.email"
              name="email"
              placeholder="Your email address"
              :disabled="isLogged"
            />
          </UFormGroup>

          <UFormGroup
            label="Company (optional)"
            name="company"
          >
            <UInput
              v-model="state.company"
              name="company"
              placeholder="Your company"
            />
          </UFormGroup>
        </div>

        <div class="mb-8 grid grid-cols-1 gap-4">
          <UFormGroup
            label="Feature Request Title"
            name="featureTitle"
          >
            <UInput
              v-model="state.featureTitle"
              name="featureTitle"
              placeholder="Title of your request"
            />
          </UFormGroup>
        </div>

        <div class="mb-8 grid grid-cols-1 gap-4">
          <UFormGroup
            label="Feature Request Details"
            name="featureDetails"
          >
            <UTextarea
              v-model="state.featureDetails"
              name="featureDetails"
              placeholder="Example:&#10;&#10;1. Describe how [feature name] will enhance the CoinConsole&#10;2. Suggest adding [new functionality/element to [page/section]&#10;3. Explain the benefit of implementing [feature request] for [specific use case/user experience]"
              :rows="5"
            />
          </UFormGroup>
        </div>

        <div class="mb-8 grid grid-cols-1 gap-4">
          <UFormGroup
            label="Any additional files you'd like to share? (optional)"
            name="files"
          >
            <FileInput
              v-model="state.files"
              accept=".png,.jpeg,.jpg,.pdf,.mp4,.mov"
            />
          </UFormGroup>
        </div>

        <UFormGroup
          class="mb-8"
          name="is_agree"
        >
          <TermsAndPrivacy v-model="state.is_agree" />
        </UFormGroup>

        <div class="flex flex-col items-center text-center">
          <UButton
            :loading="isSubmitting"
            color="base-primary"
            size="lg"
            label="Send a Feature Request"
            class="mb-3 px-12"
            type="submit"
            block
          />
        </div>
      </UForm>
    </BodyAuxiliary>
  </div>
</template>
