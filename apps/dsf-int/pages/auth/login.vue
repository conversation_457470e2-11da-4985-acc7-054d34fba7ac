<script setup lang="ts">
import { z } from 'zod'
import type { TRPCError } from '@trpc/server'
// import { useSessionStore } from '~/stores/session'
import { Buffer } from 'buffer'
import { useUserStore } from '~/stores/profile'
import type { FormSubmitEvent } from '#ui/types'
import clientApi from '~/services/api'
import { useSessionStore } from '~/stores/session'

const { $colorMode, $analytics, $client, $authClient } = useNuxtApp()
const { setSession } = useSessionStore()
const router = useRouter()
const route = useRoute()
const toast = useToast()

const isLoginLoading = ref(false)
const isDarkMode = computed(() => $colorMode.value === 'dark')
const { pairingData, connectWallet, disconnect, getSigner } = useHashpackService()
const isSigningHashpack = ref(false)

useHead({
  title: 'Login',
})

definePageMeta({
  layout: 'auth',
})

const schema = z.object({
  email: z.string().nonempty({ message: 'Email cannot be empty.' }).email({ message: 'Invalid email format.' }),
  password: z.string().nonempty({ message: 'Password cannot be empty.' }),
})

type Schema = z.output<typeof schema>

const userStore = useUserStore()

const state = reactive<Schema>({
  email: '',
  password: '',
})

const onSubmit = async (event: FormSubmitEvent<Schema>) => {
  const { email, password } = event.data
  isLoginLoading.value = true

  await $authClient.signIn
    .email({ email, password })
    .then(async ({ error }) => {
      if (error) {
        return toast.add({
          title: 'Login Failed',
          icon: 'i-heroicons-exclamation-triangle-20-solid',
          description: error.message,
          timeout: 5000,
        })
      }

      await onLoggedIn()
      return router.replace((route.query.redirect as string) ?? '/')
    })
    .catch((err) => {
      return toast.add({
        title: 'Login Failed',
        icon: 'i-heroicons-exclamation-triangle-20-solid',
        description: err.message,
        timeout: 5000,
      })
    })
    .finally(() => {
      isLoginLoading.value = false
    })
}

const onWalletConnect = async () => {
  isSigningHashpack.value = true
  if (pairingData.value) {
    isSigningHashpack.value = false
    await disconnect()

    return onWalletConnect()
  }

  await connectWallet()
}

const onLoggedIn = async () => {
  const [session, profile] = await Promise.all([
    $authClient.getSession(),
    clientApi.GET('/nodiens/api/v1/account/profile'),
  ])

  if (session.error) {
    return toast.add({
      title: 'Login Failed',
      icon: 'i-heroicons-exclamation-triangle-20-solid',
      description: session.error.message,
      timeout: 5000,
    })
  }

  if (!session.data) {
    return toast.add({
      title: 'Login Failed',
      icon: 'i-heroicons-exclamation-triangle-20-solid',
      description: 'Something went wrong, please try again.',
      timeout: 5000,
    })
  }

  if (profile.error) {
    return toast.add({
      title: 'Login Failed',
      icon: 'i-heroicons-exclamation-triangle-20-solid',
      description: profile.error.code,
      timeout: 5000,
    })
  }

  userStore.setProfile(profile.data.payload)
  setSession(session.data.session)
  return
}

watch(pairingData, async (newPairing) => {
  if (isSigningHashpack.value === false && newPairing) {
    await disconnect()
  }

  if (isSigningHashpack.value === true && newPairing && newPairing.accountIds[0]) {
    const accountId = newPairing.accountIds[0]
    const signer = getSigner(accountId)

    const getNonce = await $client.v2.auth.nonce.mutate({
      accountId,
    })

    const signature = await signer.sign([Buffer.from(`${getNonce.nonce}`)])

    $client.v2.auth.signInWeb3
      .mutate({
        accountId,
        signature,
      })
      .then(async (res) => {
        if (res) {
          await onLoggedIn()
          router.replace((route.query.redirect as string) ?? '/')
        }
      })
      .catch((err) => {
        const _err = err as TRPCError
        toast.add({
          title: 'Oops!',
          color: 'red',
          description: _err.message ?? 'Something went wrong, please try again later.',
          icon: 'i-heroicons-exclamation-triangle-20-solid',
        })
      })
  }
})

onMounted(async () => {
  window.Buffer = window.Buffer || Buffer

  if (route.hash.includes('error')) {
    history.replaceState(null, '', window.location.href.split('#')[0])
    router.replace({ query: {} })
  }

  if (route.query.registration === 'success' && !route.hash.includes('error')) {
    history.replaceState(null, '', window.location.href.split('#')[0])
    router.replace({ query: {} })
    toast.add({
      title: 'Activation Success',
      description: 'Your email has activated, please sign in to this page',
      icon: 'i-heroicons-check-circle',
    })
    state.email = route.query.email?.toString() ?? ''
  }

  if (pairingData.value) {
    disconnect()
  }

  $analytics.page()
})
</script>

<template>
  <div class="flex flex-col gap-3">
    <h2 class="mb-5 text-3xl font-semibold">Sign In</h2>
    <UForm
      :schema="schema"
      :state="state"
      @submit.prevent="onSubmit"
    >
      <UFormGroup
        label="Email Address"
        name="email"
        class="mb-3"
      >
        <UInput
          v-model="state.email"
          name="email"
        />
      </UFormGroup>
      <UFormGroup
        label="Password"
        name="password"
        class="mb-3"
      >
        <InputPassword
          v-model="state.password"
          name="password"
        />
      </UFormGroup>
      <div class="mt-5 grid grid-cols-2 gap-4">
        <UButton
          color="base-primary"
          type="submit"
          variant="solid"
          size="lg"
          label="Sign In"
          :loading="isLoginLoading"
          block
        />
        <NuxtLink to="/auth/forgot-password">
          <UButton
            label="Forgot Password"
            :color="isDarkMode ? 'white' : 'base-primary'"
            variant="outline"
            size="lg"
            block
          />
        </NuxtLink>
      </div>
      <UDivider
        label="or login with"
        class="my-5"
      />
      <UButton
        :color="isDarkMode ? 'black' : 'white'"
        variant="solid"
        size="lg"
        block
        @click="onWalletConnect"
      >
        <img
          src="~/assets/media/hashpack-logo.png"
          class="h-[20px] w-auto"
        />
      </UButton>
    </UForm>
    <span class="mt-3 text-center text-sm">
      Don't have an account?
      <NuxtLink
        to="/auth/register"
        class="text-brand-400 hover:text-brand-500 font-medium"
        >Sign Up</NuxtLink
      >
    </span>
  </div>
</template>
