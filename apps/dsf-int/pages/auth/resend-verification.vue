<script setup lang="ts">
const { $colorMode } = useNuxtApp()
const router = useRouter()

useHead({
  title: 'Resend Verification',
})

const { $analytics } = useNuxtApp()

const darkMode = computed({
  get() {
    return $colorMode.value === 'dark'
  },
  set(value) {
    if (value) {
      $colorMode.preference = 'dark'
    } else {
      $colorMode.preference = 'light'
    }
  },
})

onMounted(() => {
  // Track anayltic page
  $analytics.page()
})

definePageMeta({
  layout: 'auth',
})
</script>

<template>
  <div class="v1-container bg-white dark:bg-transparent">
    <div class="grid grid-cols-1 md:container md:mx-auto md:grid-cols-2 md:gap-6 md:pt-24 lg:w-10/12">
      <div class="hidden md:col-span-1 md:flex md:flex-col md:items-center md:justify-center">
        <img
          v-if="darkMode === true"
          class="hero-image"
          src="~/assets/media/collect-messages.svg"
        />
        <img
          v-else
          class="hero-image"
          src="~/assets/media/collect-messages-lightmode.svg"
        />
        <h4
          class="mt-4 px-5 text-xl"
          style="line-height: 30px"
        >
          We collect more than 7,000,000
          <strong class="text-brand-300">messages a month</strong> from more than 150,000
          <strong class="text-brand-300">different chat groups</strong> and
          <strong class="text-brand-300">forums</strong> discussing about 3,000 different
          <label class="text-brand-300">stocks</label> and <label class="text-brand-300">cryptocurrencies</label>.
        </h4>
      </div>
      <div class="col-span-1 flex flex-col justify-center gap-3 p-3 pt-10">
        <h2 class="mb-5 text-3xl font-semibold">Resend email verification link</h2>
        <UFormGroup
          label="Email"
          name="email"
        >
          <UInput name="email" />
        </UFormGroup>
        <div class="mt-3 grid grid-cols-2 gap-2">
          <UButton
            size="lg"
            label="Resend Verification Login"
            color="base-primary"
            block
          />
          <UButton
            size="lg"
            label="Go back login"
            :color="darkMode ? 'white' : 'base-primary'"
            variant="outline"
            block
            @click="router.push('/auth/login')"
          />
        </div>
        <span class="text-sm">
          or
          <NuxtLink
            to="/auth/register"
            class="text-brand-400 hover:text-brand-500"
            >sign up now</NuxtLink
          >
          if you don't have an account yet.
        </span>
      </div>
    </div>
  </div>
</template>
