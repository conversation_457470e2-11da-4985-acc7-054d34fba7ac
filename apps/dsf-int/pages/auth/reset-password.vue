<script setup lang="ts">
import { z } from 'zod'
import { checkPasswordRules } from '~/helper/string-helper'
import { usePassword<PERSON>hecker } from '~/components/molecule/PasswordChecker/usePasswordChecker'
import type { FormSubmitEvent } from '#ui/types'

const { $colorMode, $analytics, $authClient } = useNuxtApp()

const credentials = useState<Record<string, string>>()
const route = useRoute()
const toast = useToast()

const isLoading = ref(false)
const darkMode = computed({
  get() {
    return $colorMode.value === 'dark'
  },
  set(value: boolean) {
    if (value) {
      $colorMode.preference = 'dark'
    } else {
      $colorMode.preference = 'light'
    }
  },
})

definePageMeta({
  layout: 'auth',
})

useHead({
  title: 'Reset Password',
})

const schema = z
  .object({
    password: checkPasswordRules,
    confirm_password: z.string().nonempty('Confirm password cannot be empty'),
  })
  .refine((data) => data.confirm_password === data.password, {
    path: ['confirm_password'],
    message: "Password doesn't match",
  })

type Schema = z.output<typeof schema>

const state = reactive<Schema>({
  password: '',
  confirm_password: '',
})

const { switchView, view } = usePasswordChecker()

onMounted(() => {
  if (!route.query.token) {
    toast.add({
      title: 'Error',
      description: 'Reset token error, please try to generate reset password links',
      icon: 'i-mdi-alert-circle-outline',
      color: 'red',
    })
    navigateTo('/auth/login')
  }
  // Track anayltic page
  $analytics.page()
})

const onSubmit = async (event: FormSubmitEvent<Schema>) => {
  const { password } = event.data
  isLoading.value = true
  const { data, error } = await $authClient.resetPassword({
    token: route.query.token as string,
    newPassword: password,
  })

  if (error) {
    toast.add({
      title: 'Error',
      description: error.message,
      icon: 'i-mdi-alert-circle-outline',
      color: 'red',
    })
  }

  if (data?.status) {
    state.password = ''
    state.confirm_password = ''
    toast.add({
      title: 'Reset Password Success',
      description: 'Please login with a new password',
      icon: 'i-mdi-check-circle-outline',
      color: 'green',
    })
    navigateTo('/auth/login')
  }

  isLoading.value = false
}
</script>

<template>
  <div>
    <div
      v-if="!credentials?.error_code"
      class="flex flex-col gap-3"
    >
      <h2 class="mb-5 text-3xl font-semibold">Reset Password</h2>
      <UForm
        :schema="schema"
        :state="state"
        @submit.prevent="onSubmit"
      >
        <UFormGroup
          label="New Password"
          name="password"
          class="mb-3"
        >
          <InputPassword
            v-model="state.password"
            name="password"
            @focus="switchView(false)"
            @blur="switchView(true)"
          />
        </UFormGroup>
        <UFormGroup
          label="Confirm New Password"
          name="password"
          class="mb-3"
        >
          <InputPassword
            v-model="state.confirm_password"
            name="confirm_password"
            @focus="switchView(false)"
            @blur="switchView(true)"
          />
        </UFormGroup>

        <PasswordChecker
          v-if="view"
          :password="state.password"
        />

        <div class="mt-5 grid grid-cols-1 gap-4">
          <UButton
            color="base-primary"
            type="submit"
            size="lg"
            label="Reset Password"
            :loading="isLoading"
            block
          />
        </div>
      </UForm>
    </div>
    <div
      v-if="credentials?.error_code === '403'"
      class="flex flex-col items-center justify-center gap-3"
    >
      <img
        v-if="darkMode"
        src="~/assets/svg/email-icon.svg"
        width="110"
      />
      <img
        v-else
        src="~/assets/svg/email-icon-light.svg"
        width="110"
      />

      <h2 class="mt-4 text-center text-xl font-bold text-black dark:text-white">
        Your password reset link has expired
      </h2>
      <p class="mb-4 text-center text-sm text-black dark:text-white">
        For security reasons, password reset links are only valid for a single use. Please request a new password reset
        link at forgot password page.
      </p>
      <UButton
        color="base-primary"
        type="submit"
        size="lg"
        label="Forgot Password"
        block
        to="/auth/forgot-password"
      />
    </div>
  </div>
</template>
