<script setup lang="ts">
const { $colorMode } = useNuxtApp()

useHead({
  title: 'Register Success',
})

const { $analytics } = useNuxtApp()
const route = useRoute()
const router = useRouter()

const email = ref<string>()

const darkMode = computed({
  get() {
    return $colorMode.value === 'dark'
  },
  set(value) {
    if (value) {
      $colorMode.preference = 'dark'
    } else {
      $colorMode.preference = 'light'
    }
  },
})

onMounted(() => {
  // Track anayltic page
  if (route.query.email !== undefined) {
    email.value = route.query.email as string
    router.replace({
      query: {}
    })
  } else {
    router.replace('/')
  }
  $analytics.page()
})

definePageMeta({
  layout: 'auth'
})
</script>

<template>
  <div class="flex flex-col items-center justify-center">
    <img
      v-if="darkMode"
      src="~/assets/svg/email-icon.svg"
      width="110"
    >
    <img
      v-else
      src="~/assets/svg/email-icon-light.svg"
      width="110"
    >

    <h2 class="my-4 text-center text-lg font-bold text-black dark:text-white">
      Almost there!
    </h2>
    <p class="mb-4 text-center text-sm text-black dark:text-white">
      We have sent you an email to <strong>{{ email }}</strong> email address to verify your account.
    </p>
    <p class="text-center text-sm text-black dark:text-white">
      Please follow the instructions in the email to finish the registration.
    </p>
  </div>
</template>
