<script setup lang="ts">
import { z } from 'zod'
import type { FormSubmitEvent } from '#ui/types'

definePageMeta({
  layout: 'auth',
})

useHead({
  title: 'Forgot Password',
})

const { $colorMode, $analytics, $authClient } = useNuxtApp()
const config = useRuntimeConfig()
const toast = useToast()

const isLoading = ref(false)
const isDarkMode = computed(() => $colorMode.value === 'dark')

const schema = z.object({
  email: z.string().nonempty({ message: 'Email cannot be empty.' }).email({ message: 'Email is not valid.' }),
})

type Schema = z.output<typeof schema>

const state = reactive<Schema>({
  email: '',
})

const onSubmit = async (event: FormSubmitEvent<Schema>) => {
  isLoading.value = true

  const resp = await $authClient.forgetPassword({
    email: event.data.email ?? '',
    redirectTo: `${config.public.appUrl}/auth/reset-password`,
  })

  if (!resp.error) {
    toast.add({
      title: 'Password Reset Request Sent',
      description: 'If a matching account was found, we have sent a password reset link to your email address.',
      icon: 'i-mdi-check-circle-outline',
      color: 'green',
    })

    state.email = ''
  } else {
    toast.add({
      title: 'Error',
      description: resp.error.message ?? '',
      icon: 'i-mdi-alert-circle-outline',
      color: 'red',
    })
  }

  isLoading.value = false
}

onMounted(() => {
  // Track anayltic page
  $analytics.page()
})
</script>

<template>
  <div class="flex flex-col gap-3">
    <h2 class="mb-5 text-3xl font-semibold">Forgot Password</h2>
    <UForm
      :schema="schema"
      :state="state"
      @submit.prevent="onSubmit"
    >
      <UFormGroup
        label="Email Address"
        name="email"
        class="mb-3"
      >
        <UInput
          v-model="state.email"
          name="email"
        />
      </UFormGroup>
      <div class="mt-5 grid gap-4 md:grid-cols-2">
        <UButton
          color="base-primary"
          type="submit"
          size="lg"
          label="Reset Password"
          :loading="isLoading"
          block
        />
        <NuxtLink to="/auth/login">
          <UButton
            label="Back to Sign In"
            :color="isDarkMode ? 'white' : 'base-primary'"
            variant="outline"
            block
            size="lg"
          />
        </NuxtLink>
      </div>
    </UForm>
    <span class="text-center text-sm">
      Don't have an account?
      <NuxtLink
        to="/auth/register"
        class="text-brand-400 hover:text-brand-500 font-medium"
        >Sign Up</NuxtLink
      >
    </span>
    <!-- <NuxtLink to="/auth/resend-verification" class="text-brand-300 hover:text-brand-400 text-sm font-medium">
      Resend email verification list
    </NuxtLink> -->
  </div>
</template>
