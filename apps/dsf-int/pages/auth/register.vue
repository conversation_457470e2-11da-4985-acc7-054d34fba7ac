<script setup lang="ts">
import { Buffer } from 'buffer'

// Types
import type { TRPCError } from '@trpc/server'

const { $analytics, $client } = useNuxtApp()
const toast = useToast()

const { pairingData, connectWallet, disconnect } = useHashpackService()

useHead({
  title: 'Register',
})

const accountId = ref('')
const nonce = ref()
const isSigningHashpack = ref(false)
const signUpUsingEmail = ref(false)

const showFillForm = computed(() => (pairingData.value && nonce.value) || signUpUsingEmail.value)

const onReset = async () => {
  signUpUsingEmail.value = false
  nonce.value = undefined
  accountId.value = ''
  await disconnect()
}

const onWalletConnect = async () => {
  isSigningHashpack.value = true
  if (pairingData.value) {
    isSigningHashpack.value = false
    await disconnect()

    return onWalletConnect()
  }

  await connectWallet()
}

watch(pairingData, async (newPairing) => {
  if (isSigningHashpack.value === false && newPairing) {
    await disconnect()
  }

  if (isSigningHashpack.value === true && newPairing && newPairing.accountIds[0]) {
    $client.v2.auth.registerNonce
      .mutate({
        accountId: newPairing.accountIds[0],
      })
      .then(async (res) => {
        if (res.nonce && newPairing.accountIds[0]) {
          nonce.value = res.nonce
          accountId.value = newPairing.accountIds[0]
        }
      })
      .catch((err) => {
        const _err = err as TRPCError
        toast.add({
          title: 'Oops!',
          color: 'red',
          description: _err.message ?? 'Something went wrong, please try again later.',
          icon: 'i-heroicons-exclamation-triangle-20-solid',
        })
      })
  }
})

onMounted(async () => {
  window.Buffer = window.Buffer || Buffer

  // Track anayltic page
  $analytics.page()

  if (pairingData.value) {
    disconnect()
  }
})

definePageMeta({
  layout: 'auth',
})
</script>

<template>
  <ClientOnly>
    <div class="flex flex-col gap-3">
      <RegisterWithWallet
        v-if="!showFillForm"
        @on-connect-wallet="onWalletConnect"
      />
      <RegisterFillForm
        v-else
        :connected-wallet="accountId"
        :nonce="nonce"
        @on-reset-wallet="onReset"
      />
      <template v-if="!showFillForm">
        <UDivider label="OR" />
        <UButton
          color="black"
          icon="i-material-symbols:stacked-email-outline"
          size="md"
          variant="solid"
          label="Sign Up using Email"
          block
          @click="signUpUsingEmail = true"
        />
      </template>
      <span
        v-if="!showFillForm"
        class="text-center"
        >Already have an account?
        <NuxtLink
          to="/auth/login"
          class="text-brand-400 hover:text-brand-500 font-medium"
          >Sign In</NuxtLink
        ></span
      >
    </div>
  </ClientOnly>
</template>
