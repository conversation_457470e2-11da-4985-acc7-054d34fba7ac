<script setup lang="ts">
useHead({
  title: 'Climate Indexes overview today',
})
const route = useRoute()
const router = useRouter()
const searchTimeoutId = ref<NodeJS.Timeout | undefined | number>()

const selectedType = computed({
  get: () => (route.query.list ?? 'cons') as string,
  set: async (newVal) => {
    await updateQuery({
      list: newVal,
      q: undefined,
      // Reset the sorting to powerUse or emission column on change of the selectedType
      asc: undefined,
      ...(newVal === 'cons' ? { sort: 'energyConsumption', desc: '1' } : { sort: 'emission', desc: '1' }),
    })
  },
})

const dropdownOptions = computed(() => [
  {
    label: 'Energy Cons',
    value: 'cons',
  },
  {
    label: 'CO₂ Emissions',
    value: 'emissions',
  },
])

const layerTypes = computed({
  get: () => [
    [
      {
        label: 'Layer 1',
        value: 'L1',
        disabled: true,
        checked: (route.query.layer as string)?.split(',')?.some((layer) => layer === 'L1') ?? true,
        labelClass: '!text-white',
        tooltip: 'Layer 1 (L1) is a basic blockchain protocol that provides the foundation for a blockchain ecosystem.',
      },
      {
        label: 'Layer 2',
        value: 'L2',
        disabled: true,
        checked: (route.query.layer as string)?.split(',')?.some((layer) => layer === 'L2') ?? true,
        labelClass: '!text-white',
        tooltip:
          'Layer 2 (L2) is a scaling solution that enables high throughput of transactions whilst relying on the underlying L1 for additional security.',
      },
    ],
  ],
  set: async (newVal) => {
    const selected = newVal[0] ? newVal[0].filter((layer) => layer.checked).map((_layer) => _layer.value) : ['']
    await updateQuery({ layer: selected.join(',') })
  },
})

const typesList = computed({
  get: () => [
    [
      {
        label: 'Tokens',
        value: 'Token',
        disabled: true,
        checked: (route.query.type as string)?.split(',')?.some((type) => type === 'Token') ?? true,
        labelClass: '!text-white',
        tooltip:
          'A digital asset built on an existing distributed ledger technology (DLT), typically serving specific purposes like utility, governance, or store of value within an ecosystem.',
      },
      {
        label: 'Platforms',
        value: 'Platform',
        disabled: true,
        checked: (route.query.type as string)?.split(',')?.some((type) => type === 'Platform') ?? true,
        labelClass: '!text-white',
        tooltip:
          'A distributed ledger technology (DLT) infrastructure designed to support decentralized applications (dApps) and smart contracts, enabling broader use cases beyond crypto assets.',
      },
    ],
  ],
  set: async (newVal) => {
    const selected = newVal[0] ? newVal[0].filter((type) => type.checked).map((_type) => _type.value) : ['']
    await updateQuery({ type: selected.join(',') })
  },
})

const algorithmTypes = computed({
  get: () => [
    [
      {
        label: 'PoW',
        value: 'PoW',
        disabled: true,
        checked: (route.query.algorithm as string)?.split(',')?.some((algorithm) => algorithm === 'PoW') ?? true,
        labelClass: '!text-white',
        tooltip:
          'Proof-of-work (PoW) is a consensus mechanism requiring participants to solve complex mathematical problems to validate transactions and secure the network, often energy-intensive.',
      },
      {
        label: 'PoS',
        value: 'PoS',
        disabled: true,
        checked: (route.query.algorithm as string)?.split(',')?.some((algorithm) => algorithm === 'PoS') ?? true,
        labelClass: '!text-white',
        tooltip:
          'Proof-of-stake (PoS) is a consensus mechanism where validators are chosen to secure the network based on the amount of cryptocurrency they hold and are willing to stake.',
      },
      {
        label: 'PoSt',
        value: 'PoSt',
        disabled: true,
        checked: (route.query.algorithm as string)?.split(',')?.some((algorithm) => algorithm === 'PoSt') ?? true,
        labelClass: '!text-white',
        tooltip:
          'Proof-of-spacetime (PoSt) is a consensus mechanism leveraging unused storage space and time as a resource to validate transactions, promoting energy efficiency.',
      },
      {
        label: 'Others',
        value: 'other',
        disabled: true,
        checked: (route.query.algorithm as string)?.split(',')?.some((algorithm) => algorithm === 'other') ?? true,
        labelClass: '!text-white',
        tooltip:
          'Less common or alternative consensus mechanisms beyond Proof-of-work (PoW), Proof-of-stake (PoS), or Proof-of-spacetime (PoSt), such as Proof of Authority (PoA).',
      },
    ],
  ],
  set: async (newVal) => {
    const selected = newVal[0]
      ? newVal[0].filter((algorithm) => algorithm.checked).map((_algorithm) => _algorithm.value)
      : ['']
    await updateQuery({ algorithm: selected.join(',') })
  },
})

const updateQuery = async (query: Record<string, string | undefined>) => {
  await router.push({
    query: {
      ...route.query,
      ...query,
      page: 1,
    },
  })
}

const searchText = computed({
  get: () => (route.query.q ?? '') as string,
  set: (q) => {
    if (searchTimeoutId.value) {
      clearTimeout(searchTimeoutId.value)
    }
    searchTimeoutId.value = setTimeout(async () => {
      await updateQuery({ q })
      searchTimeoutId.value = undefined
    }, 500)
  },
})
</script>

<template>
  <div>
    <HeaderPage
      page-name="climate-indices"
      faq-slug="climate"
      comparison-slug="climate-indices"
    />

    <div class="px-5 py-6">
      <div class="mb-4 flex flex-wrap items-center gap-4 md:mb-6">
        <USelectMenu
          v-model="selectedType"
          variant="solid"
          color="black"
          size="md"
          class="w-[170px]"
          :options="dropdownOptions"
          value-attribute="value"
          option-attribute="label"
        >
          <template #label>
            <span>{{ dropdownOptions.find((x) => x.value === selectedType)?.label }}</span>
          </template>
        </USelectMenu>
        <UDropdown
          :items="layerTypes"
          :ui="{
            item: { disabled: 'cursor-default opacity-100' },
          }"
          :popper="{ placement: 'bottom-start' }"
          class="order-1"
        >
          <UButton
            label="Layer"
            icon="i-material-symbols-filter-list"
            color="black"
            size="md"
            truncate
            class="min-h-[42px] w-full sm:max-w-60"
          />
          <template #item="{ item }">
            <div class="flex items-center gap-1">
              <UCheckbox
                :model-value="item.checked"
                :label="item.label"
                color="sky"
                @change="
                  layerTypes = layerTypes.map((layer) =>
                    layer.map((_layer) =>
                      _layer.value === item.value ? { ..._layer, checked: !_layer.checked } : _layer,
                    ),
                  )
                "
              />
              <InformationPopover
                class="flex items-center justify-center"
                icon-class="!w-5 !h-5"
              >
                <TooltipContent>
                  <p>{{ item.tooltip }}</p>
                </TooltipContent>
              </InformationPopover>
            </div>
          </template>
        </UDropdown>
        <UDropdown
          :items="typesList"
          :ui="{
            item: { disabled: 'cursor-default opacity-100' },
          }"
          :popper="{ placement: 'bottom-start' }"
          class="order-1"
        >
          <UButton
            label="Type"
            icon="i-material-symbols-filter-list"
            color="black"
            size="md"
            truncate
            class="min-h-[42px] w-full sm:max-w-60"
          />
          <template #item="{ item }">
            <div class="flex items-center gap-1">
              <UCheckbox
                :model-value="item.checked"
                :label="item.label"
                color="sky"
                @change="
                  typesList = typesList.map((type) =>
                    type.map((_type) => (_type.value === item.value ? { ..._type, checked: !_type.checked } : _type)),
                  )
                "
              />
              <InformationPopover
                class="flex items-center justify-center"
                icon-class="!w-5 !h-5"
              >
                <TooltipContent>
                  <p>{{ item.tooltip }}</p>
                </TooltipContent>
              </InformationPopover>
            </div>
          </template>
        </UDropdown>
        <UDropdown
          :items="algorithmTypes"
          :ui="{
            item: { disabled: 'cursor-default opacity-100' },
          }"
          :popper="{ placement: 'bottom-start' }"
          class="order-1"
        >
          <UButton
            label="Algorithm"
            icon="i-material-symbols-filter-list"
            color="black"
            size="md"
            truncate
            class="min-h-[42px] w-full sm:max-w-60"
          />
          <template #item="{ item }">
            <div class="flex items-center gap-1">
              <UCheckbox
                :model-value="item.checked"
                :label="item.label"
                color="sky"
                @change="
                  algorithmTypes = algorithmTypes.map((algorithm) =>
                    algorithm.map((_algorithm) =>
                      _algorithm.value === item.value ? { ..._algorithm, checked: !_algorithm.checked } : _algorithm,
                    ),
                  )
                "
              />
              <InformationPopover
                class="flex items-center justify-center"
                icon-class="!w-5 !h-5"
              >
                <TooltipContent>
                  <p>{{ item.tooltip }}</p>
                </TooltipContent>
              </InformationPopover>
            </div>
          </template>
        </UDropdown>
        <UInput
          v-model="searchText"
          placeholder="Search name or symbol"
          variant="transparent"
          trailing-icon="i-heroicons-magnifying-glass"
          class="order-2 ml-0 w-full grow sm:order-1 sm:ml-auto sm:w-auto sm:grow-0"
        />
      </div>

      <EsgTable />
    </div>
  </div>
</template>
