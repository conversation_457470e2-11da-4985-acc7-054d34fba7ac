<script setup lang="ts">
// Constants
import { CLIMATE_TOOLTIPS } from '~/constants/common-tooltips'
import { ESG_CHART_TYPE_OPTION_LABEL } from '~/constants/options'
import { ENERGY_CONSUMPTION_TYPES } from '~/constants/climate'

const { onBack } = useBackHandler()
const { isMobile } = useDeviceScreen()

const {
  esgChartRef,
  esgBottomChartRef,
  isClimateFeatureAllowed,
  isShareModalOpen,
  shareLink,
  selectedType,
  selectedMetric,
  selectedAsset,
  consensusMechanism,
  esgChartTypeOptionLabel,
  typeOptions,
  metricOptions,
  smallestSelectedAvgUnit,
  isLeftPriceScaleTitleVisible,
  isRightPriceScaleTitleVisible,
  isLoading,
  isChartEmpty,
  zoomViews,
  log,
  tools,
  searchAsset,
  handleAssetChange,
} = useClimateChart()
</script>

<template>
  <div>
    <Head>
      <Title>{{ selectedAsset?.name }} Climate Indexes</Title>
    </Head>
    <HeaderPage page-name="climate-indices" />

    <div class="md:px-5 md:py-6">
      <div class="mb-4 hidden justify-between gap-2 md:mb-6 md:flex">
        <UButton
          class="inline-flex"
          variant="outline"
          color="black"
          size="md"
          icon="i-heroicons-arrow-left"
          label="Back"
          @click="onBack"
        />

        <!-- Move ro Reusable Component - ConsensusMechanism -->
        <div class="flex gap-2">
          <div
            v-for="item in consensusMechanism"
            :key="(item as string).replace(' ', '-')"
            class="border-neutrals-300 flex items-center gap-2 rounded border px-3 py-2 dark:border-white"
          >
            <p class="text-neutrals-400 text-base dark:text-white">
              {{ item }}
            </p>
            <InformationPopover class="mt-1">
              <TooltipContent>
                <p v-html="CLIMATE_TOOLTIPS.consensusMechanism" />
              </TooltipContent>
            </InformationPopover>
          </div>
        </div>
      </div>

      <UCard>
        <div class="flex flex-col gap-4">
          <div class="flex flex-col gap-4">
            <!-- Move ro Reusable Component - ConsensusMechanism -->
            <div class="flex gap-2 self-end md:hidden">
              <div
                v-for="item in consensusMechanism"
                :key="(item as string).replace(' ', '_')"
                class="border-neutrals-300 flex items-center gap-2 rounded border px-3 py-2 dark:border-white"
              >
                <p class="text-neutrals-400 text-base dark:text-white">
                  {{ item }}
                </p>
                <InformationPopover class="mt-1">
                  <TooltipContent>
                    <p v-html="CLIMATE_TOOLTIPS.consensusMechanism" />
                  </TooltipContent>
                </InformationPopover>
              </div>
            </div>

            <div class="flex flex-col flex-wrap gap-4 md:flex-row md:justify-between">
              <div>
                <!-- Remove Skeleto loading - SelectMenu has its own loading -->
                <div
                  v-if="isLoading"
                  class="flex gap-2"
                >
                  <USkeleton class="h-10 flex-1 md:w-44" />
                  <USkeleton class="h-10 flex-1 md:w-44" />
                  <USkeleton class="h-10 flex-1 md:w-44" />
                </div>
                <div
                  v-else
                  class="flex flex-col gap-4 md:flex-row"
                >
                  <AssetSelector
                    v-model="selectedAsset"
                    :search-assets="searchAsset"
                    @change="handleAssetChange"
                  />

                  <USelectMenu
                    v-model="selectedType"
                    :options="typeOptions"
                    value-attribute="value"
                    variant="solid"
                    size="md"
                    color="black"
                    class="w-full md:w-fit"
                    :popper="{ placement: 'bottom-start' }"
                    :ui-menu="{
                      container: 'w-min',
                      label: 'text-base',
                    }"
                  />

                  <USelectMenu
                    v-model="selectedMetric"
                    :options="metricOptions"
                    value-attribute="value"
                    variant="solid"
                    color="black"
                    size="md"
                    class="w-full md:w-fit"
                    :popper="{ placement: 'bottom-start' }"
                    :ui-menu="{
                      container: 'w-min',
                      label: 'text-base',
                    }"
                  />
                </div>
              </div>

              <!-- Move to Reusable Component - ChartTimeControls -->
              <div class="md:ml-auto lg:ml-0">
                <div
                  v-if="isLoading"
                  class="flex items-center gap-x-2"
                >
                  <USkeleton
                    v-for="item in 6"
                    :key="item"
                    class="h-9 w-12"
                  />
                </div>
                <div
                  v-else
                  class="flex items-center justify-between gap-1.5 md:gap-2"
                >
                  <UButton
                    v-for="zoom in zoomViews"
                    :key="zoom.id"
                    :variant="zoom.active ? 'outline' : 'ghost'"
                    :color="zoom.active ? 'brand' : 'black'"
                    :class="[
                      'px-1.5 !font-normal sm:px-2.5',
                      { 'dark:!text-neutrals-50 !text-black/50': !zoom.active },
                    ]"
                    @click="tools.setZoom(zoom.id)"
                  >
                    {{ zoom.label }}
                  </UButton>
                  <UButton
                    size="md"
                    icon="i-material-symbols-share"
                    color="soft-gray"
                    variant="outline"
                    @click="isShareModalOpen = true"
                  />
                </div>
              </div>
            </div>
            <div class="flex justify-between">
              <!-- Move to Reusable Component - ChartLegendItem -->
              <div>
                <div
                  v-if="isLoading"
                  class="flex gap-2"
                >
                  <USkeleton class="h-10 w-44" />
                  <USkeleton class="h-10 w-16" />
                </div>
                <div
                  v-else
                  class="flex items-center gap-3"
                >
                  <div class="border-neutrals-300 flex items-center rounded border px-2 py-[6px] dark:border-white">
                    <div class="bg-accent-green-400 h-2 w-2 rounded-full" />
                    <span class="text-neutrals-300 ml-2 text-base capitalize dark:text-white">{{
                      esgChartTypeOptionLabel
                    }}</span>
                  </div>
                  <div class="border-neutrals-300 flex items-center rounded border px-2 py-[6px] dark:border-white">
                    <div class="bg-accent-red-700 h-2 w-2 rounded-full" />
                    <span class="text-neutrals-300 ml-2 text-base dark:text-white">Throughput</span>
                  </div>
                </div>
              </div>

              <div class="hidden md:block">
                <!-- Remove Skeleton loading - Buttons has its own loading -->
                <div
                  v-if="isLoading"
                  class="flex items-center gap-x-2"
                >
                  <USkeleton
                    v-for="item in 2"
                    :key="item"
                    class="h-9 w-12"
                  />
                </div>

                <div
                  v-else
                  class="flex items-center gap-x-2 self-end"
                >
                  <UButton
                    size="md"
                    variant="outline"
                    :color="log ? 'brand' : 'soft-gray'"
                    class="!font-normal"
                    @click="tools.handleLog"
                  >
                    LOG
                  </UButton>
                  <!-- Move to Reusable Component - ChartExportDropdown -->
                  <UPopover>
                    <UButton
                      size="md"
                      color="soft-gray"
                      variant="outline"
                      icon="i-heroicons-arrow-down-tray"
                    />

                    <template #panel="{ close }">
                      <div class="flex flex-col p-2">
                        <UButton
                          color="black"
                          variant="ghost"
                          block
                          @click="
                            () => {
                              close()
                              // print('pdf')
                            }
                          "
                        >
                          Export as PDF
                        </UButton>
                        <UButton
                          color="black"
                          variant="ghost"
                          block
                          @click="
                            () => {
                              close()
                              // print('png')
                            }
                          "
                        >
                          Export as PNG
                        </UButton>
                        <UButton
                          color="black"
                          variant="ghost"
                          block
                          @click="
                            () => {
                              close()
                              // print('jpg')
                            }
                          "
                        >
                          Export as JPG
                        </UButton>
                      </div>
                    </template>
                  </UPopover>
                </div>
              </div>
            </div>
          </div>

          <div
            v-if="isLoading"
            class="v1-4xl:h-[600px] grid h-[400px] w-full place-items-center xl:h-[500px]"
          >
            <LottieNodiensLoader />
          </div>
          <CheckFeature
            v-else
            :allowed="isClimateFeatureAllowed"
          >
            <NoDataAvailable
              v-if="isChartEmpty"
              class="v1-4xl:h-[600px] h-[400px] xl:h-[500px]"
            />
            <div v-else>
              <div
                v-if="!isMobile"
                class="flex w-full items-center px-7 py-1"
              >
                <p
                  v-if="isLeftPriceScaleTitleVisible"
                  class="text-neutrals-300 text-right text-xs font-medium"
                >
                  {{ smallestSelectedAvgUnit }}
                </p>
                <p
                  v-if="isRightPriceScaleTitleVisible"
                  class="text-neutrals-300 ml-auto text-xs font-medium"
                >
                  tps
                </p>
              </div>
              <ClientOnly>
                <div
                  ref="esgChartRef"
                  class="v1-4xl:h-[600px] h-[400px] w-full xl:h-[500px]"
                />
              </ClientOnly>
              <div class="relative my-2 h-fit">
                <div
                  id="range-slider"
                  class="block bg-transparent"
                />
                <div
                  class="absolute top-0 flex h-[50px] w-full justify-center overflow-hidden"
                  style="z-index: 0"
                >
                  <div
                    ref="esgBottomChartRef"
                    class="h-[50px] w-full overflow-hidden rounded border border-neutral-400"
                    style="max-width: 98.6607%"
                  />
                </div>
              </div>
            </div>
          </CheckFeature>
        </div>
      </UCard>
    </div>

    <LazyShareModal
      v-if="isShareModalOpen"
      :shared-text="shareLink"
      @on-close="isShareModalOpen = false"
    />
  </div>
</template>

<style>
#range-slider {
  margin: auto;
  width: 100%;
  height: 50px;
  background: transparent;
  overflow: hidden;
}
#range-slider .range-slider__thumb:nth-child(3) {
  transform: translate(0%, -50%) !important;
}
#range-slider .range-slider__thumb:nth-child(4) {
  transform: translate(-100%, -50%) !important;
}
.dark #range-slider .range-slider__thumb {
  border: 1px solid#e5e5e5;
}
#range-slider .range-slider__thumb {
  width: 14px;
  height: 100%;
  border-radius: 4px;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='18' height='18' fill='%23333' viewBox='0 0 24 24'%3E%3Cpath d='M12,16A2,2 0 0,1 14,18A2,2 0 0,1 12,20A2,2 0 0,1 10,18A2,2 0 0,1 12,16M12,10A2,2 0 0,1 14,12A2,2 0 0,1 12,14A2,2 0 0,1 10,12A2,2 0 0,1 12,10M12,4A2,2 0 0,1 14,6A2,2 0 0,1 12,8A2,2 0 0,1 10,6A2,2 0 0,1 12,4Z' /%3E%3C/svg%3E")
    #fff;
  border: 1px solid #c3c3c3;
  background-repeat: no-repeat;
  background-position: center;
}
.dark #range-slider .range-slider__range {
  border: 1px solid #d9d9d9;
}
#range-slider .range-slider__range {
  border-radius: 6px;
  background: rgba(0, 163, 255, 0.1);
  border: 1px solid #c3c3c3;
  box-sizing: border-box;
}
</style>
