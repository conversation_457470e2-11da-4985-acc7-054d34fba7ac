<script setup lang="ts">
import { format } from 'date-fns'
import { numericDisplay } from '~/helper/number-helper'

const dummyData = [
  [1672012800000, 0],
  [1672099200000, 0],
  [1672185600000, 0],
  [1672272000000, 0],
  [1672358400000, 0],
  [1672444800000, 0],
  [1672531200000, 1121],
  [1672617600000, 1767],
  [1672704000000, 2275],
  [1672790400000, 1696],
  [1672876800000, 1284],
  [1672963200000, 1494],
  [1673049600000, 1200],
  [1673136000000, 1236],
  [1673222400000, 1351],
  [1673308800000, 1708],
  [1673395200000, 1406],
  [1673481600000, 1421],
  [1673568000000, 1453],
  [1673654400000, 1510],
  [1673740800000, 1329],
  [1673827200000, 1517],
  [1673913600000, 1711],
  [1674000000000, 1414],
  [1674086400000, 1430],
  [1674172800000, 1438],
  [1674259200000, 1329],
  [1674345600000, 1193],
  [1674432000000, 1232],
  [1674518400000, 1399],
  [1674604800000, 1310],
  [1674691200000, 1172],
  [1674777600000, 1343],
  [1674864000000, 1173],
  [1674950400000, 1343],
  [1675036800000, 1449],
  [1675123200000, 1650],
  [1675209600000, 1892],
  [1675296000000, 2483],
  [1675382400000, 1874],
  [1675468800000, 1659],
  [1675555200000, 1770],
  [1675641600000, 1826],
  [1675728000000, 2268],
  [1675814400000, 2118],
  [1675900800000, 1748],
  [1675987200000, 1719],
  [1676073600000, 1391],
  [1676160000000, 1462],
  [1676246400000, 1358],
  [1676332800000, 1728],
  [1676419200000, 1588],
  [1676505600000, 1723],
  [1676592000000, 1638],
  [1676678400000, 1479],
  [1676764800000, 1649],
  [1676851200000, 1806],
  [1676937600000, 2380],
  [1677024000000, 1844],
  [1677110400000, 2239],
  [1677196800000, 1790],
  [1677283200000, 1401],
  [1677369600000, 1466],
  [1677456000000, 1775],
  [1677542400000, 2163],
  [1677628800000, 1838],
  [1677715200000, 1679],
  [1677801600000, 1874],
  [1677888000000, 1405],
  [1677974400000, 1215],
  [1678060800000, 1416],
  [1678147200000, 1680],
  [1678233600000, 1774],
  [1678320000000, 1667],
  [1678406400000, 1563],
  [1678492800000, 1622],
  [1678579200000, 1325],
  [1678665600000, 1731],
  [1678752000000, 1969],
  [1678838400000, 1745],
  [1678924800000, 2031],
  [1679011200000, 1539],
  [1679097600000, 1406],
  [1679184000000, 1303],
  [1679270400000, 1512],
  [1679356800000, 1615],
  [1679443200000, 1616],
  [1679529600000, 1429],
  [1679616000000, 1442],
  [1679702400000, 1189],
  [1679788800000, 1122],
  [1679875200000, 1257],
  [1679961600000, 1546],
  [1680048000000, 1456],
  [1680134400000, 1269],
  [1680220800000, 1419],
  [1680307200000, 1498],
  [1680393600000, 1353],
  [1680480000000, 1304],
  [1680566400000, 1716],
  [1680652800000, 1598],
  [1680739200000, 1334],
  [1680825600000, 1128],
  [1680912000000, 1357],
  [1680998400000, 1250],
  [1681084800000, 1359],
  [1681171200000, 1641],
  [1681257600000, 1325],
  [1681344000000, 1319],
  [1681430400000, 1667],
  [1681516800000, 1581],
  [1681603200000, 1580],
  [1681689600000, 1724],
  [1681776000000, 1881],
  [1681862400000, 1683],
  [1681948800000, 1529],
  [1682035200000, 1497],
  [1682121600000, 1461],
  [1682208000000, 1380],
  [1682294400000, 1400],
  [1682380800000, 1406],
  [1682467200000, 1378],
  [1682553600000, 1387],
  [1682640000000, 1450],
  [1682726400000, 1640],
  [1682812800000, 1709],
  [1682899200000, 1666],
  [1682985600000, 2002],
  [1683072000000, 1423],
  [1683158400000, 1482],
  [1683244800000, 2224],
  [1683331200000, 1778],
  [1683417600000, 1598],
  [1683504000000, 1817],
  [1683590400000, 1960],
  [1683676800000, 1627],
  [1683763200000, 1596],
  [1683849600000, 1583],
  [1683936000000, 1515],
  [1684022400000, 1227],
  [1684108800000, 1688],
  [1684195200000, 1719],
  [1684281600000, 1467],
  [1684368000000, 1266],
  [1684454400000, 1531],
  [1684540800000, 1349],
  [1684627200000, 1327],
  [1684713600000, 2134],
  [1684800000000, 1675],
  [1684886400000, 1719],
  [1684972800000, 1885],
  [1685059200000, 2048],
  [1685145600000, 1597],
  [1685232000000, 3380],
  [1685318400000, 2950],
  [1685404800000, 1635],
  [1685491200000, 1424],
  [1685577600000, 1935],
  [1685664000000, 1707],
  [1685750400000, 1614],
  [1685836800000, 1421],
  [1685923200000, 1668],
  [1686009600000, 1785],
  [1686096000000, 1706],
  [1686182400000, 1834],
  [1686268800000, 1523],
  [1686355200000, 1555],
  [1686441600000, 1066],
  [1686528000000, 2229],
  [1686614400000, 2122],
  [1686700800000, 1488],
  [1686787200000, 2245],
  [1686873600000, 1342],
  [1686960000000, 1557],
  [1687046400000, 1285],
  [1687132800000, 1304],
  [1687219200000, 1761],
  [1687305600000, 1890],
  [1687392000000, 1776],
  [1687478400000, 1641],
  [1687564800000, 1361],
  [1687651200000, 1544],
  [1687737600000, 1541],
  [1687824000000, 1669],
  [1687910400000, 1365],
  [1687996800000, 1494],
  [1688083200000, 1758],
  [1688169600000, 1406],
  [1688256000000, 1420],
  [1688342400000, 1761],
  [1688428800000, 1312],
  [1688515200000, 1528],
  [1688601600000, 1314],
  [1688688000000, 1301],
  [1688774400000, 1123],
  [1688860800000, 1115],
  [1688947200000, 1474],
  [1689033600000, 1472],
  [1689120000000, 1290],
  [1689206400000, 1570],
  [1689292800000, 1858],
  [1689379200000, 1154],
  [1689465600000, 1393],
  [1689552000000, 1663],
  [1689638400000, 1700],
  [1689724800000, 1653],
  [1689811200000, 1347],
  [1689897600000, 1330],
  [1689984000000, 1375],
  [1690070400000, 1414],
  [1690156800000, 1399],
  [1690243200000, 1499],
  [1690329600000, 1481],
  [1690416000000, 1306],
  [1690502400000, 1609],
  [1690588800000, 1652],
  [1690675200000, 1268],
  [1690761600000, 1526],
  [1690848000000, 1902],
  [1690934400000, 2242],
  [1691020800000, 1893],
  [1691107200000, 2700],
  [1691193600000, 2841],
  [1691280000000, 2049],
  [1691366400000, 1879],
  [1691452800000, 2166],
  [1691539200000, 1747],
  [1691625600000, 1567],
  [1691712000000, 1870],
  [1691798400000, 1309],
  [1691884800000, 1040],
  [1691971200000, 1568],
  [1692057600000, 3113],
  [1692144000000, 1863],
  [1692230400000, 2610],
  [1692316800000, 1589],
  [1692403200000, 1258],
  [1692489600000, 2506],
  [1692576000000, 1517],
  [1692662400000, 0],
  [1692748800000, 1877],
  [1692835200000, 1791],
  [1692921600000, 1995],
  [1693008000000, 1568],
  [1693094400000, 1325],
  [1693180800000, 1400],
  [1693267200000, 1767],
  [1693353600000, 1786],
  [1693440000000, 1711],
  [1693526400000, 0],
  [1693612800000, 1150],
  [1693699200000, 1057],
  [1693785600000, 1321],
  [1693872000000, 1546],
  [1693958400000, 1816],
  [1694044800000, 1461],
  [1694131200000, 1450],
  [1694217600000, 1128],
  [1694304000000, 989],
  [1694390400000, 1457],
  [1694476800000, 1660],
  [1694563200000, 1618],
  [1694649600000, 1431],
  [1694736000000, 1658],
  [1694822400000, 1294],
  [1694908800000, 1137],
  [1694995200000, 1574],
  [1695081600000, 2063],
  [1695168000000, 1622],
  [1695254400000, 1959],
  [1695340800000, 0],
  [1695427200000, 16792],
  [1695513600000, 20270],
  [1695600000000, 1295],
  [1695686400000, 4129],
  [1695772800000, 4385],
  [1695859200000, 3079],
  [1695945600000, 8560],
  [1696032000000, 1197],
  [1696118400000, 1062],
  [1696204800000, 1279],
  [1696291200000, 1275],
  [1696377600000, 1260],
  [1696464000000, 1688],
  [1696550400000, 2125],
  [1696636800000, 0],
  [1696723200000, 916],
  [1696809600000, 1272],
  [1696896000000, 1331],
  [1696982400000, 1471],
  [1697068800000, 1266],
  [1697155200000, 1353],
  [1697241600000, 1206],
  [1697328000000, 1240],
  [1697414400000, 1792],
  [1697500800000, 1562],
  [1697587200000, 1807],
  [1697673600000, 1664],
  [1697760000000, 1507],
  [1697846400000, 1481],
  [1697932800000, 1443],
  [1698019200000, 1855],
  [1698105600000, 2430],
  [1698192000000, 2321],
  [1698278400000, 3441],
  [1698364800000, 2556],
  [1698451200000, 1732],
  [1698537600000, 1394],
  [1698624000000, 1684],
  [1698710400000, 1914],
  [1698796800000, 2144],
  [1698883200000, 2979],
  [1698969600000, 3400],
  [1699056000000, 2626],
  [1699142400000, 1709],
  [1699228800000, 1788],
  [1699315200000, 1622],
  [1699401600000, 1830],
  [1699488000000, 1848],
  [1699574400000, 2065],
  [1699660800000, 1574],
  [1699747200000, 1260],
  [1699833600000, 1826],
  [1699920000000, 1857],
  [1700006400000, 1859],
  [1700092800000, 1666],
  [1700179200000, 1878],
  [1700265600000, 1528],
  [1700352000000, 1487],
  [1700438400000, 1841],
  [1700524800000, 0],
  [1700611200000, 1541],
  [1700697600000, 1692],
  [1700784000000, 1406],
  [1700870400000, 1311],
  [1700956800000, 1146],
  [1701043200000, 1221],
  [1701129600000, 1435],
  [1701216000000, 1605],
  [1701302400000, 1321],
  [1701388800000, 1540],
  [1701475200000, 1891],
  [1701561600000, 1909],
  [1701648000000, 2219],
  [1701734400000, 2076],
  [1701820800000, 2094],
  [1701907200000, 1755],
  [1701993600000, 1976],
  [1702080000000, 2160],
  [1702166400000, 1586],
  [1702252800000, 1967],
  [1702339200000, 1897],
  [1702425600000, 1590],
  [1702512000000, 2041],
  [1702598400000, 1489],
  [1702684800000, 1526],
  [1702771200000, 3412],
  [1702857600000, 2809],
  [1702944000000, 2521],
  [1703030400000, 2167],
  [1703116800000, 2214],
  [1703203200000, 2124],
  [1703289600000, 1663],
  [1703376000000, 1691],
  [1703462400000, 1834],
  [1703548800000, 2149],
  [1703635200000, 1809],
  [1703721600000, 1694],
  [1703808000000, 1642],
  [1703894400000, 0],
  [1703980800000, 1275],
  [1704067200000, 1302],
  [1704153600000, 1689],
  [1704240000000, 1846],
  [1704326400000, 2120],
  [1704412800000, 2050],
  [1704499200000, 1543],
  [1704585600000, 1437],
  [1704672000000, 1747],
  [1704758400000, 1615],
  [1704844800000, 1692],
  [1704931200000, 1661],
  [1705017600000, 1605],
  [1705104000000, 1223],
  [1705190400000, 1398],
  [1705276800000, 1393],
  [1705363200000, 1394],
  [1705449600000, 1369],
  [1705536000000, 1272],
  [1705622400000, 1547],
  [1705708800000, 1226],
  [1705795200000, 1113],
  [1705881600000, 1245],
  [1705968000000, 1254],
  [1706054400000, 1074],
  [1706140800000, 1017],
  [1706227200000, 1178],
  [1706313600000, 1022],
  [1706400000000, 1188],
  [1706486400000, 1317],
  [1706572800000, 1431],
  [1706659200000, 1418],
  [1706745600000, 1490],
  [1706832000000, 1119],
  [1706918400000, 1255],
  [1707004800000, 1128],
  [1707091200000, 1292],
  [1707177600000, 1212],
  [1707264000000, 1130],
  [1707350400000, 1087],
  [1707436800000, 1277],
  [1707523200000, 1009],
  [1707609600000, 1036],
  [1707696000000, 1145],
  [1707782400000, 1336],
  [1707868800000, 1549],
  [1707955200000, 1589],
  [1708041600000, 1516],
  [1708128000000, 1874],
  [1708214400000, 1512],
  [1708300800000, 1750],
  [1708387200000, 3289],
  [1708473600000, 1363],
  [1708560000000, 1006],
  [1708646400000, 1547],
  [1708732800000, 1081],
  [1708819200000, 959],
  [1708905600000, 1409],
  [1708992000000, 2492],
  [1709078400000, 1898],
  [1709164800000, 0],
  [1709251200000, 1746],
  [1709337600000, 1860],
  [1709424000000, 2006],
  [1709510400000, 2189],
  [1709596800000, 2608],
  [1709683200000, 2029],
  [1709769600000, 2345],
  [1709856000000, 1954],
  [1709942400000, 1893],
  [1710028800000, 1946],
  [1710115200000, 2532],
  [1710201600000, 2284],
  [1710288000000, 2149],
  [1710374400000, 1984],
  [1710460800000, 1858],
  [1710547200000, 1789],
  [1710633600000, 1705],
  [1710720000000, 1838],
  [1710806400000, 2002],
  [1710892800000, 184],
  [1710979200000, 191],
  [1711065600000, 244],
  [1711152000000, 0],
  [1711238400000, 0],
  [1711324800000, 0],
  [1711411200000, 159],
  [1711497600000, 341],
  [1711584000000, 0],
  [1711670400000, 259],
  [1711756800000, 233],
  [1711843200000, 0],
  [1711929600000, 3],
  [1712016000000, 0],
  [1712102400000, 219],
  [1712188800000, 293],
  [1712275200000, 0],
  [1712361600000, 0],
  [1712448000000, 0],
  [1712534400000, 625],
  [1712620800000, 186],
  [1712707200000, 284],
  [1712793600000, 0],
  [1712880000000, 0],
  [1712966400000, 223],
  [1713052800000, 157],
  [1713139200000, 0],
  [1713225600000, 171],
  [1713312000000, 0],
  [1713398400000, 418],
  [1713484800000, 0],
  [1713571200000, 301],
  [1713657600000, 193],
  [1713744000000, 165],
  [1713830400000, 165],
  [1713916800000, 0],
  [1714003200000, 124],
  [1714089600000, 0],
  [1714176000000, 0],
  [1714262400000, 140],
  [1714348800000, 0],
  [1714435200000, 1625],
  [1714521600000, 1171],
  [1714608000000, 0],
  [1714694400000, 1888],
  [1714780800000, 1893],
  [1714867200000, 875],
  [1714953600000, 4751],
  [1715040000000, 4746],
  [1715126400000, 6421],
  [1715212800000, 7058],
  [1715299200000, 6417],
  [1715385600000, 6392],
  [1715472000000, 5067],
  [1715990400000, 56],
  [1716076800000, 28],
  [1716163200000, 102],
  [1716249600000, 247],
  [1716336000000, 128],
  [1716422400000, 0],
  [1716508800000, 0],
  [1716595200000, 424],
  [1716681600000, 423],
  [1716768000000, 521],
  [1716854400000, 557],
  [1716940800000, 0],
  [1717027200000, 794],
  [1717113600000, 684],
  [1717200000000, 558],
  [1717286400000, 636],
  [1717372800000, 701],
  [1717459200000, 868],
  [1717545600000, 1051],
  [1717632000000, 851],
  [1717718400000, 976],
  [1717804800000, 597],
  [1717891200000, 569],
  [1717977600000, 718],
  [1718064000000, 558],
  [1718150400000, 559],
  [1718236800000, 474],
  [1718323200000, 490],
  [1718409600000, 568],
  [1718496000000, 497],
  [1718582400000, 466],
  [1718668800000, 533],
  [1718755200000, 620],
  [1718841600000, 546],
  [1718928000000, 414],
  [1719014400000, 557],
  [1719100800000, 617],
  [1719187200000, 473],
  [1719273600000, 602],
  [1719360000000, 498],
  [1719446400000, 1189],
  [1719532800000, 265],
  [1719619200000, 176],
  [1719705600000, 0],
  [1719792000000, 155],
  [1719878400000, 159],
  [1719964800000, 347],
  [1720051200000, 356],
  [1720137600000, 259],
  [1720224000000, 146],
  [1720310400000, 136],
  [1720396800000, 148],
  [1720483200000, 170],
  [1720569600000, 231],
  [1720656000000, 238],
  [1720742400000, 195],
  [1720828800000, 197],
  [1720915200000, 264],
  [1721001600000, 175],
  [1721088000000, 225],
  [1721174400000, 209],
  [1721260800000, 178],
  [1721347200000, 861],
  [1721433600000, 477],
  [1721520000000, 853],
  [1721606400000, 1101],
  [1721692800000, 1304],
  [1721779200000, 806],
  [1721865600000, 874],
  [1721952000000, 755],
  [1722038400000, 778],
  [1722124800000, 679],
  [1722211200000, 1067],
  [1722297600000, 899],
  [1722384000000, 0],
  [1722470400000, 659],
  [1722556800000, 636],
  [1722643200000, 704],
  [1722729600000, 786],
  [1722816000000, 914],
  [1722902400000, 727],
  [1722988800000, 720],
  [1723075200000, 644],
  [1723161600000, 635],
  [1723248000000, 564],
  [1723334400000, 0],
  [1723420800000, 647],
  [1723507200000, 768],
  [1723593600000, 642],
  [1723680000000, 659],
  [1723766400000, 734],
  [1723852800000, 467],
  [1723939200000, 0],
  [1724025600000, 0],
  [1724112000000, 638],
  [1724198400000, 615],
  [1724284800000, 0],
  [1724371200000, 731],
  [1724457600000, 428],
  [1724544000000, 729],
  [1724630400000, 400],
  [1724716800000, 536],
  [1724803200000, 609],
  [1724889600000, 0],
  [1724976000000, 446],
  [1725062400000, 426],
  [1725148800000, 489],
  [1725235200000, 645],
  [1725321600000, 465],
  [1725408000000, 576],
  [1725494400000, 528],
  [1725580800000, 607],
  [1725667200000, 560],
  [1725753600000, 0],
  [1725840000000, 628],
  [1725926400000, 602],
  [1726012800000, 675],
  [1726099200000, 0],
  [1726185600000, 629],
  [1726272000000, 2838],
  [1726358400000, 1155],
  [1726444800000, 723],
  [1726531200000, 642],
  [1726617600000, 0],
  [1726704000000, 779],
  [1726790400000, 723],
  [1726876800000, 0],
  [1726963200000, 669],
  [1727049600000, 512],
  [1727136000000, 492],
  [1727222400000, 581],
  [1727308800000, 456],
  [1727395200000, 369],
  [1727481600000, 665],
  [1727568000000, 676],
  [1727654400000, 384],
  [1727740800000, 285],
  [1727827200000, 544],
  [1727913600000, 570],
  [1728000000000, 0],
  [1728086400000, 792],
  [1728172800000, 0],
  [1728259200000, 1229],
  [1728345600000, 0],
  [1728432000000, 1083],
  [1728518400000, 677],
  [1728604800000, 0],
  [1728691200000, 0],
  [1728777600000, 466],
  [1728864000000, 303],
  [1728950400000, 376],
  [1729036800000, 0],
  [1729123200000, 0],
  [1729209600000, 0],
  [1729296000000, 0],
  [1729382400000, 0],
  [1729468800000, 472],
  [1729555200000, 814],
  [1729641600000, 412],
  [1729728000000, 415],
  [1729814400000, 0],
  [1729900800000, 376],
  [1729987200000, 0],
  [1730073600000, 527],
  [1730160000000, 832],
  [1730246400000, 635],
  [1730332800000, 0],
  [1730419200000, 572],
  [1730505600000, 539],
  [1730592000000, 652],
  [1730678400000, 573],
  [1730764800000, 0],
  [1730851200000, 2357],
  [1730937600000, 2356],
  [1731024000000, 1860],
  [1731110400000, 1651],
  [1731196800000, 1813],
  [1731283200000, 1173],
  [1731369600000, 1142],
  [1731456000000, 905],
  [1731542400000, 805],
  [1731628800000, 632],
  [1731715200000, 796],
  [1731801600000, 1118],
  [1731888000000, 1499],
  [1731974400000, 701],
  [1732060800000, 601],
  [1732147200000, 623],
  [1732233600000, 683],
  [1732320000000, 724],
  [1732406400000, 817],
  [1732492800000, 603],
  [1732579200000, 618],
  [1732665600000, 687],
  [1732752000000, 682],
  [1732838400000, 728],
  [1732924800000, 680],
  [1733011200000, 630],
  [1733097600000, 705],
  [1733184000000, 878],
  [1733270400000, 774],
  [1733356800000, 948],
  [1733443200000, 605],
  [1733529600000, 686],
  [1733616000000, 632],
  [1733702400000, 671],
  [1733788800000, 682],
  [1733875200000, 666],
  [1733961600000, 987],
  [1734048000000, 883],
  [1734134400000, 562],
  [1734220800000, 655],
  [1734307200000, 989],
  [1734393600000, 933],
  [1734480000000, 913],
  [1734566400000, 859],
  [1734652800000, 571],
  [1734739200000, 826],
  [1734825600000, 799],
  [1734912000000, 765],
  [1734998400000, 746],
  [1735084800000, 634],
  [1735171200000, 448],
  [1735257600000, 566],
  [1735344000000, 479],
  [1735430400000, 993],
  [1735516800000, 546],
  [1735603200000, 798],
  [1735689600000, 710],
  [1735776000000, 666],
  [1735862400000, 747],
  [1735948800000, 816],
  [1736035200000, 714],
  [1736121600000, 756],
  [1736208000000, 0],
  [1736294400000, 691],
  [1736380800000, 706],
  [1736467200000, 500],
  [1736553600000, 620],
  [1736640000000, 1475],
  [1736726400000, 953],
  [1736812800000, 871],
  [1736899200000, 517],
  [1736985600000, 661],
  [1737072000000, 0],
  [1737158400000, 887],
  [1737244800000, 1191],
  [1737331200000, 1024],
  [1737417600000, 0],
  [1737504000000, 1529],
  [1737590400000, 1126],
  [1737676800000, 906],
  [1737763200000, 1314],
  [1737849600000, 933],
  [1737936000000, 1138],
  [1738022400000, 864],
  [1738108800000, 885],
  [1738195200000, 0],
  [1738281600000, 0],
  [1738368000000, 0],
  [1738454400000, 486],
  [1738540800000, 707],
  [1738627200000, 0],
  [1738713600000, 0],
  [1738800000000, 0],
  [1738886400000, 768],
  [1738972800000, 0],
  [1739059200000, 0],
  [1739145600000, 0],
  [1739232000000, 0],
  [1739318400000, 0],
  [1739404800000, 0],
  [1739491200000, 0],
  [1739577600000, 450],
  [1739664000000, 0],
  [1739750400000, 0],
  [1739836800000, 0],
  [1739923200000, 0],
  [1740009600000, 0],
  [1740096000000, 0],
  [1740182400000, 0],
  [1740268800000, 0],
  [1740355200000, 0],
  [1740441600000, 0],
  [1740528000000, 0],
  [1740614400000, 0],
  [1740700800000, 0],
  [1740787200000, 0],
  [1740873600000, 0],
  [1740960000000, 0],
  [1741046400000, 0],
  [1741132800000, 0],
  [1741219200000, 0],
  [1741305600000, 0],
  [1741392000000, 0],
  [1741478400000, 0],
  [1741564800000, 0],
  [1741651200000, 0],
  [1741737600000, 0],
  [1741824000000, 0],
  [1741910400000, 0],
  [1741996800000, 0],
  [1742083200000, 0],
  [1742169600000, 0],
  [1742256000000, 0],
  [1742342400000, 0],
  [1742428800000, 0],
  [1742515200000, 0],
  [1742601600000, 0],
  [1742688000000, 0],
  [1742774400000, 0],
  [1742860800000, 0],
  [1742947200000, 0],
  [1743033600000, 0],
  [1743120000000, 0],
  [1743206400000, 0],
  [1743292800000, 0],
  [1743379200000, 0],
  [1743465600000, 0],
  [1743552000000, 0],
  [1743638400000, 0],
  [1743724800000, 0],
  [1743811200000, 0],
  [1743897600000, 0],
  [1743984000000, 0],
  [1744070400000, 0],
  [1744156800000, 0],
  [1744243200000, 0],
  [1744329600000, 0],
  [1744416000000, 0],
  [1744502400000, 0],
  [1744588800000, 0],
  [1744675200000, 0],
  [1744761600000, 0],
  [1744848000000, 0],
  [1744934400000, 0],
  [1745020800000, 0],
  [1745107200000, 0],
  [1745193600000, 0],
  [1745280000000, 0],
  [1745366400000, 0],
  [1745452800000, 0],
  [1745539200000, 0],
  [1745625600000, 0],
  [1745712000000, 0],
  [1745798400000, 0],
  [1745884800000, 0],
  [1745971200000, 0],
  [1746057600000, 0],
  [1746144000000, 0],
  [1746230400000, 0],
  [1746316800000, 0],
  [1746403200000, 0],
  [1746489600000, 0],
  [1746576000000, 0],
  [1746662400000, 0],
  [1746748800000, 0],
  [1746835200000, 0],
  [1746921600000, 0],
  [1747008000000, 0],
  [1747094400000, 0],
  [1747180800000, 0],
  [1747267200000, 0],
  [1747353600000, 0],
  [1747440000000, 0],
  [1747526400000, 0],
  [1747612800000, 0],
  [1747699200000, 0],
  [1747785600000, 0],
  [1747872000000, 0],
  [1747958400000, 0],
  [1748044800000, 0],
  [1748131200000, 0],
  [1748217600000, 0],
  [1748304000000, 0],
  [1748390400000, 0],
  [1748476800000, 0],
  [1748563200000, 0],
  [1748649600000, 0],
  [1748736000000, 0],
  [1748822400000, 0],
  [1748908800000, 0],
  [1748995200000, 0],
  [1749081600000, 0],
  [1749168000000, 0],
  [1749254400000, 0],
  [1749340800000, 0],
  [1749427200000, 0],
  [1749513600000, 0],
  [1749600000000, 0],
  [1749686400000, 153],
  [1749772800000, 0],
  [1749859200000, 7],
  [1749945600000, 0],
  [1750032000000, 310],
  [1750118400000, 0],
  [1750204800000, 0],
  [1750291200000, 0],
  [1750377600000, 0],
  [1750464000000, 0],
  [1750550400000, 0],
  [1750636800000, 12],
  [1750723200000, 0],
  [1750809600000, 0],
  [1750896000000, 0],
  [1750982400000, 0],
  [1751068800000, 0],
  [1751155200000, 3],
  [1751241600000, 0],
  [1751328000000, 6],
  [1751414400000, 0],
  [1751500800000, 0],
  [1751587200000, 0],
  [1751673600000, 0],
  [1751760000000, 0],
  [1751846400000, 0],
  [1751932800000, 18],
  [1752019200000, 0],
  [1752105600000, 0],
  [1752192000000, 0],
  [1752278400000, 0],
  [1752364800000, 5],
  [1752451200000, 1],
  [1752537600000, 5],
  [1752624000000, 0],
  [1752710400000, 0],
  [1752796800000, 0],
  [1752883200000, 0],
  [1752969600000, 0],
  [1753056000000, 0],
  [1753142400000, 0],
  [1753228800000, 4],
  [1753315200000, 0],
  [1753401600000, 0],
  [1753488000000, 0],
  [1753574400000, 0],
  [1753660800000, 0],
  [1753747200000, 0],
  [1753833600000, 0],
  [1753920000000, 0],
  [1754006400000, 0],
  [1754092800000, 0],
  [1754179200000, 0],
  [1754265600000, 0],
  [1754352000000, 0],
  [1754438400000, 0],
  [1754524800000, 2],
  [1754611200000, 0],
  [1754697600000, 0],
  [1754784000000, 1],
  [1754870400000, 0],
  [1754956800000, 0],
  [1755043200000, 0],
  [1755129600000, 0],
  [1755216000000, 0],
  [1755302400000, 0],
  [1755388800000, 0],
  [1755475200000, 0],
  [1755561600000, 0],
  [1755648000000, 0],
  [1755734400000, 0],
  [1755820800000, 0],
  [1755907200000, 0],
  [1755993600000, 0],
  [1756080000000, 1],
  [1756166400000, 0],
  [1756252800000, 0],
  [1756339200000, 0],
]

const { $colorMode } = useNuxtApp()

const { chart: chartInstance, tools, addLineSeries, renderChart, destroyChart } = useLightChart()

const chartRef = ref<HTMLElement>()
const exportElement = shallowRef<HTMLElement>()

const { exportChart } = useChartExport()

const darkMode = computed({
  get() {
    return $colorMode.value === 'dark'
  },
  set(value) {
    if (value) {
      $colorMode.preference = 'dark'
    } else {
      $colorMode.preference = 'light'
    }
  },
})

const initChart = () => {
  if (chartInstance.value === undefined) {
    renderChart(
      chartRef,
      {
        grid: {
          horzLines: {
            color: '#C3C3C3',
          },
          vertLines: {
            visible: false,
          },
        },
        crosshair: {
          horzLine: {
            visible: false,
            labelVisible: false,
          },
        },
        rightPriceScale: {
          visible: false,
        },
        leftPriceScale: {
          visible: true,
          borderVisible: false,
          entireTextOnly: true,
          scaleMargins: {
            bottom: 0.01,
          },
        },
        timeScale: {
          visible: false,
          borderVisible: false,
          borderColor: 'rgba(197, 203, 206, 1)',
          fixRightEdge: true,
          rightOffset: 5,
          lockVisibleTimeRangeOnResize: true,
        },
        handleScale: {
          pinch: false,
          mouseWheel: false,
          axisPressedMouseMove: false,
        },
        handleScroll: {
          mouseWheel: false,
          vertTouchDrag: false,
          horzTouchDrag: false,
          pressedMouseMove: false,
        },
        localization: {
          priceFormatter: (price: any) => numericDisplay(price),
        },
        startCalculation: format(1672012800000, 'yyyy-MM-dd'),
        latestCalculation: format(1756512000000, 'yyyy-MM-dd'),
      },
      darkMode.value,
    )
  }

  const series = addLineSeries('test', {
    priceScaleId: 'left',
    lastValueVisible: false,
    priceLineVisible: false,
  })

  const chartSeriesData = dummyData.map(([time, value]) => ({
    time: format(time!, 'yyyy-MM-dd'),
    value,
  }))

  series?.setData(chartSeriesData)
  tools.reArrangeChart({ autosize: true })
  tools.prepareTooltip({
    darkMode: darkMode.value,
  })
}

const onExport = async (format: 'pdf' | 'png' | 'jpg') => {
  try {
    exportChart(
      chartInstance.value!,
      {
        asset: {
          logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/1.png',
          name: 'Bitcoin',
          symbol: 'BTC',
        },
        legends: [
          { name: 'All', color: '#8B5CF6' },
          { name: 'Reddit', color: '#EA5C15' },
          { name: 'Telegram', color: '#007DCF' },
          // { name: 'Twitter', color: '#1DA1F2' },
          // { name: 'Discord', color: '#5865F2' },
          // { name: 'Facebook', color: '#1877F2' },
          // { name: 'Instagram', color: '#E1306C' },
          // { name: 'LinkedIn', color: '#0077B5' },
          // { name: 'Tiktok', color: '#000000' },
        ],
      },
      {
        fileName: `financial-analysis`,
        format,
      },
    )
  } catch (error) {
    console.error('PDF export failed:', error)
  }
}

watch([chartRef], ([chartElement]) => {
  if (chartElement) {
    initChart()
  }
})

watch(darkMode, (value) => {
  tools.setDark(value)
})

// onUnmounted(() => {
//   destroyChart()
// })
</script>
<template>
  <div class="p-5">
    <div class="flex flex-col p-2">
      <UButton
        v-for="item in ['pdf', 'png', 'jpg']"
        :key="`esg-chart-export-${item}-button`"
        color="midnight"
        variant="ghost"
        block
        :label="`Export as ${item.toUpperCase()}`"
        @click="onExport(item as any)"
      />
    </div>

    <div class="w-full">
      <div
        ref="chartRef"
        class="h-[600px] w-full"
      />
    </div>

    <div
      ref="exportElement"
      class="h-[693px] w-[1648px] border-2 bg-white p-8 dark:bg-black"
      style="display: none"
    >
      <!-- Header -->
      <div>
        <!-- position: absolute; top: 0; left: 100%; z-index: 99 -->
        <h1>Downloaded from Nodiens, {{ format(new Date(), 'MMM dd, yyyy kk:mm zzz') }}</h1>
      </div>

      <!-- Body -->
      <div
        id="chart-watermark"
        class="relative w-full"
      >
        <ColorScheme>
          <img
            v-if="darkMode"
            src="~/assets/media/chart-watermark-night.png"
            class="absolute w-4/12"
            style="left: 33%; top: 50%"
            alt="Chart watermark"
          />
          <img
            v-else
            src="~/assets/media/chart-watermark.png"
            class="absolute w-4/12"
            style="left: 33%; top: 50%"
            alt="Chart watermark"
          />
        </ColorScheme>
      </div>
    </div>
  </div>
</template>
