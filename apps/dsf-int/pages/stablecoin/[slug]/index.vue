<script setup lang="ts">
import { format, subYears } from 'date-fns'

// Constants
import { HTTP_STATUS } from '~/constants/error'
import { LEGEND_MOOD_TRUST, LEGEND_PRICE_VOLUME, LEGEND_PLATFORMS } from '~/constants/legends'
import { ONBOARDING_STABLE_SUMMARY_PAGE, ONBOARDING_PATHS } from '~/constants/onboarding'
import { ASSET_STABLECOIN_TOOLTIPS } from '~/constants/common-tooltips'

// Helpers
import { numericDisplay } from '~/helper/number-helper'

// Types
import type { AssetType } from '~/types/appTypes'
import type { v2AssetList } from '~/server/trpc/trpc'
import type { OverviewChartDataset } from '~/components/molecule/OverviewChart/OverviewChart.vue'

const { $apiClient } = useNuxtApp()
const toast = useToast()
const route = useRoute()
const router = useRouter()

const { isFeatureAllowed } = useFeature()

const params = route.params as Partial<{ slug: string }>

if (!params.slug) {
  throw createError({ statusCode: 404, statusMessage: 'Page Not Found' })
}

const maxVolumeChart = ref(0)
const isScrolling = ref(false)
let scrollTimeout: NodeJS.Timeout | null = null

const selectedAsset = useState<v2AssetList['data'][0] | undefined>()

const startDate = format(subYears(new Date(), 1), 'yyyy-MM-dd')
const endDate = format(new Date(), 'yyyy-MM-dd')

const { data: onboardingData, status: onboardingStatus } = await useLazyAsyncData(
  'tokenStablecoinOverviewOnboarding',
  async () => {
    const { data } = await $apiClient.GET('/nodiens/api/v1/account/onboarding', {
      params: {
        query: {
          path: ONBOARDING_PATHS.STABLECOIN_SUMMARY,
        },
      },
    })

    return data?.payload.isChecked || false
  },
  {
    server: false,
  },
)

const { data: summaryData, status: summaryStatus } = useLazyAsyncData(
  `overview.stablecoin.${params.slug}`,
  async () => {
    const { response, data, error } = await $apiClient.GET('/nodiens/api/v1/asset/{type}/{slug}', {
      params: {
        path: {
          slug: params.slug!,
          type: 'stable',
        },
      },
    })

    if (error) {
      if (response.status === HTTP_STATUS.INTERNAL_SERVER_ERROR) {
        toast.add({
          title: 'Error',
          description: 'Something went wrong. Please try again later.',
          icon: 'i-heroicons-exclamation-triangle',
          color: 'red',
        })
      }

      if (response.status === HTTP_STATUS.NOT_FOUND) {
        toast.add({
          title: 'Potential Issue with Data',
          description: 'Asset not found',
          icon: 'i-heroicons-exclamation-triangle',
          color: 'amber',
        })

        setTimeout(() => {
          navigateTo('/', { replace: true })
        }, 3000)
      }
    }

    return data?.payload || null
  },
  {
    server: false,
  },
)

const isOverviewPending = computed(() => {
  return onboardingStatus.value === 'pending' || summaryStatus.value === 'pending'
})

watch(summaryData, (newOverviewData) => {
  // Initiate selected asset
  if (newOverviewData) {
    const { name, type, symbol } = newOverviewData
    selectedAsset.value = {
      slug: params.slug!,
      logo: newOverviewData.logo ?? '',
      name,
      type,
      symbol,
    }
  }
})

const lastCalculatedDate = computed(() =>
  summaryData.value?.latestCalculation ? format(summaryData.value.latestCalculation, 'MMM dd, yyyy') : '',
)

const { data: chartDataMoodTrust, status: chartDataMoodTrustStatus } = useLazyAsyncData(
  `stable-mood-trust-${params.slug}`,
  async () => {
    const apiParams = {
      params: {
        path: { slug: params.slug! },
        query: { layer1: true, startDate, endDate },
      },
    }

    const [moodRes, trustRes] = await Promise.allSettled([
      $apiClient.GET('/nodiens/api/v1/mood-index/{slug}', apiParams),
      $apiClient.GET('/nodiens/api/v1/trust-index/{slug}', apiParams),
    ])

    if (moodRes.status !== 'fulfilled' || trustRes.status !== 'fulfilled') return []

    const moodSeries =
      moodRes.value?.data?.payload?.layer1?.map(([time, value]) => ({
        time: format(new Date(time), 'yyyy-MM-dd'),
        value,
      })) ?? []

    const trustSeries =
      trustRes.value?.data?.payload?.layer1?.map(([time, value]) => ({
        time: format(new Date(time), 'yyyy-MM-dd'),
        value,
      })) ?? []

    if (moodSeries.length === 0 && trustSeries.length === 0) return []

    return [
      {
        type: 'line',
        name: 'Mood',
        color: 'rgba(0,163,255,1)',
        data: moodSeries,
        showLastMarker: true,
        markerBackgroundColor: 'rgba(0,163,255,1)',
        markerShadowColor: 'rgba(0,163,255,0.35)',
      },
      {
        type: 'line',
        name: 'Trust',
        color: 'rgba(79,189,109,1)',
        data: trustSeries,
        showLastMarker: true,
        markerBackgroundColor: 'rgba(79,189,109,1)',
        markerShadowColor: 'rgba(79,189,109,0.35)',
      },
    ] satisfies OverviewChartDataset[]
  },
  {
    server: false,
  },
)

const { data: chartDataMarketcapVolume, status: chartDataMarketcapVolumeStatus } = useLazyAsyncData(
  `stable-marketcap-volume-${params.slug}`,
  async () => {
    const [marketRes, volumeRes] = await Promise.allSettled([
      $apiClient.GET('/nodiens/api/v1/financial/history/{slug}', {
        params: {
          path: { slug: params.slug! },
          query: { startDate, endDate, metric: 'marketCap' },
        },
      }),

      $apiClient.GET('/nodiens/api/v1/price-market/volume-history/{slug}', {
        params: {
          path: { slug: params.slug! },
          query: { startDate, endDate },
        },
      }),
    ])

    if (marketRes.status !== 'fulfilled' || volumeRes.status !== 'fulfilled') return {}

    const priceSeries =
      marketRes.value?.data?.payload?.entries.map(([time, value]) => ({
        time: format(new Date(time), 'yyyy-MM-dd'),
        value,
      })) ?? []

    const volumeSeries =
      volumeRes.value?.data?.payload?.entries.map(([time, value]) => ({
        time: format(new Date(time), 'yyyy-MM-dd'),
        value,
      })) ?? []

    return {
      marketCap: [
        {
          type: 'area',
          name: 'Market Cap',
          color: '#FFA500',
          data: priceSeries,
          showLastMarker: true,
          markerBackgroundColor: 'rgba(255,165,0,1)',
          markerShadowColor: 'rgba(255,165,0,0.35)',
          areaTopColor: 'rgba(255,165,0,0.50)',
          areaBottomColor: 'rgba(255,165,0,0.20)',
        },
      ],
      volume: [
        {
          type: 'histogram',
          name: 'Volume',
          color: '#C3C3C3',
          data: volumeSeries,
          showLastMarker: true,
        },
      ],
    } satisfies Record<string, OverviewChartDataset[]>
  },
  {
    server: false,
  },
)

watch(chartDataMarketcapVolumeStatus, (newStatus) => {
  if (newStatus === 'success') {
    maxVolumeChart.value = Math.max(
      ...(chartDataMarketcapVolume.value?.volume?.[0]?.data?.map((d) => d.value ?? 0) ?? [0]),
    )
  }
})

const isChartDataMarketcapVolumeEmpty = computed(() => {
  return (
    (chartDataMarketcapVolume.value?.marketCap?.[0]?.data?.length ?? 0) === 0 &&
    (chartDataMarketcapVolume.value?.volume?.[0]?.data?.length ?? 0) === 0
  )
})

const { data: chartDataLiquidityCost, status: chartDataLiquidityCostStatus } = useLazyAsyncData(
  `stable-liq-cost-${params.slug}`,
  async () => {
    const [cexRes, dexRes] = await Promise.allSettled([
      $apiClient.GET('/nodiens/api/v1/financial/history/{slug}', {
        params: {
          path: { slug: params.slug! },
          query: { startDate, endDate, metric: 'liquidityConcentrationCex' },
        },
      }),
      $apiClient.GET('/nodiens/api/v1/financial/history/{slug}', {
        params: {
          path: { slug: params.slug! },
          query: { startDate, endDate, metric: 'liquidityConcentrationDex' },
        },
      }),
    ])

    if (cexRes.status !== 'fulfilled' || dexRes.status !== 'fulfilled') return []

    const cexSeries =
      cexRes.value?.data?.payload?.entries?.map(([time, value]) => ({
        time: format(new Date(time), 'yyyy-MM-dd'),
        value,
      })) ?? []

    const dexSeries =
      dexRes.value?.data?.payload?.entries?.map(([time, value]) => ({
        time: format(new Date(time), 'yyyy-MM-dd'),
        value,
      })) ?? []

    if (cexSeries.length === 0 && dexSeries.length === 0) return []

    return [
      {
        type: 'line',
        name: 'CEX',
        color: 'rgba(255,0,0,1)',
        data: cexSeries,
        showLastMarker: true,
        markerBackgroundColor: 'rgba(255,0,0,1)',
        markerShadowColor: 'rgba(255,0,0,0.35)',
      },
      {
        type: 'line',
        name: 'DEX',
        color: 'rgba(3,120,35,1)',
        data: dexSeries,
        showLastMarker: true,
        markerBackgroundColor: 'rgba(3,120,35,1)',
        markerShadowColor: 'rgba(3,120,35,0.35)',
      },
    ] satisfies OverviewChartDataset[]
  },
  {
    server: false,
  },
)

const { data: chartDataPrice, status: chartDataPriceStatus } = useLazyAsyncData(
  `stable-price-${params.slug}`,
  async () => {
    const response = await $apiClient.GET('/nodiens/api/v1/financial/history/{slug}', {
      params: {
        path: { slug: params.slug! },
        query: { startDate, endDate, metric: 'price' },
      },
    })

    const priceSeries =
      response.data?.payload?.entries.map(([time, value]) => ({
        time: format(new Date(time), 'yyyy-MM-dd'),
        value,
      })) ?? []

    if (priceSeries.length === 0) {
      return []
    }

    return [
      {
        type: 'area',
        name: 'Price',
        color: 'rgba(255,165,0,1)',
        data: priceSeries,
        showLastMarker: true,
        markerBackgroundColor: 'rgba(255,165,0,1)',
        markerShadowColor: 'rgba(255,165,0,0.35)',
        areaTopColor: 'rgba(255,165,0,0.50)',
        areaBottomColor: 'rgba(255,165,0,0.20)',
      },
    ] satisfies OverviewChartDataset[]
  },
  {
    server: false,
  },
)

const { data: chartDataMessage, status: chartMessageStatus } = useLazyAsyncData(
  `stable-community-message-${params.slug}`,
  async () => {
    const response = await $apiClient.GET('/nodiens/api/v1/community/history/{metric}/{slug}', {
      params: {
        path: { slug: params.slug!, metric: 'MESSAGE_COUNT' },
        query: { startDate, endDate, layer1: false, layer2: ['reddit', 'telegram'] },
      },
    })

    const telegramSeries = (
      response.data?.payload?.layer2?.find((d) => d.snsPlatform === 'telegram')?.history ?? []
    ).map(([time, value]) => ({
      time: format(new Date(time), 'yyyy-MM-dd'),
      value,
    }))

    const redditSeries = (response.data?.payload?.layer2?.find((d) => d.snsPlatform === 'reddit')?.history ?? []).map(
      ([time, value]) => ({
        time: format(new Date(time), 'yyyy-MM-dd'),
        value,
      }),
    )

    if (telegramSeries.length === 0 && redditSeries.length === 0) {
      return []
    }

    return [
      {
        type: 'histogram',
        name: 'Reddit',
        color: 'rgba(234, 92, 21, 1)',
        data: redditSeries,
      },
      {
        type: 'histogram',
        name: 'Telegram',
        color: 'rgba(0, 125, 207, 1)',
        data: telegramSeries,
      },
    ] satisfies OverviewChartDataset[]
  },
  {
    server: false,
  },
)

const { data: chartDataCommunity, status: chartCommunityStatus } = useLazyAsyncData(
  `stable-community-member-${params.slug}`,
  async () => {
    const response = await $apiClient.GET('/nodiens/api/v1/community/history/{metric}/{slug}', {
      params: {
        path: { slug: params.slug!, metric: 'MEMBER_COUNT' },
        query: {
          startDate,
          endDate,
          layer1: false,
          layer2: ['reddit', 'telegram'],
        },
      },
    })

    const telegramHistory = (
      response.data?.payload?.layer2?.find((d) => d.snsPlatform === 'telegram')?.history ?? []
    ).map(([time, value]) => ({
      time: format(new Date(time), 'yyyy-MM-dd'),
      value,
    }))

    const redditHistory = (response.data?.payload?.layer2?.find((d) => d.snsPlatform === 'reddit')?.history ?? []).map(
      ([time, value]) => ({
        time: format(new Date(time), 'yyyy-MM-dd'),
        value,
      }),
    )

    if (telegramHistory.length === 0 && redditHistory.length === 0) {
      return []
    }

    return [
      {
        type: 'line',
        name: 'Reddit',
        color: '#EA5C15',
        data: redditHistory,
        showLastMarker: true,
        markerBackgroundColor: 'rgba(234,92,21,1)',
        markerShadowColor: 'rgba(234,92,21,0.35)',
      },
      {
        type: 'line',
        name: 'Telegram',
        color: '#007DCF',
        data: telegramHistory,
        showLastMarker: true,
        markerBackgroundColor: 'rgba(0,125,207,1)',
        markerShadowColor: 'rgba(0,125,207,0.35)',
      },
    ] satisfies OverviewChartDataset[]
  },
  {
    server: false,
  },
)

const searchAsset = async (q: string) => {
  const response = await $apiClient.GET('/nodiens/api/v1/community/asset', {
    params: {
      query: {
        ...(q && { terms: q }),
      },
    },
  })
  return response.data?.payload?.data ?? []
}

const finishOnboarding = async () => {
  await $apiClient.PATCH('/nodiens/api/v1/account/onboarding', {
    body: {
      path: ONBOARDING_PATHS.STABLECOIN_SUMMARY,
    },
  })
}

const handleScroll = () => {
  isScrolling.value = true

  if (scrollTimeout) {
    clearTimeout(scrollTimeout)
  }

  scrollTimeout = setTimeout(() => {
    isScrolling.value = false
  }, 300) // 150ms timeout after scrolling stops
}

const onChartClick = () => {
  const url = `/community/mood/${(route.params.slug || '').toString()}`
  if (!isScrolling.value) {
    return url
  }
  return ''
}

const handleAssetChange = async (asset: AssetType) => {
  if (asset?.type === 'STABLECOIN') {
    router.push(`/stablecoin/${asset?.slug}`)
  } else {
    router.push(`/crypto/${asset?.slug}`)
  }
}

onMounted(async () => {
  window.addEventListener('touchmove', handleScroll)
})

onBeforeUnmount(() => {
  window.removeEventListener('touchmove', handleScroll)
})
</script>

<template>
  <div class="flex flex-col gap-3 p-5">
    <Head>
      <Title>{{ summaryData?.name }} Community Sentiment, Market Dynamics, and CEX/DEX Liquidity live charts</Title>
    </Head>

    <div class="mb-5 flex flex-col gap-3 md:flex-row md:items-center md:justify-between">
      <AssetSelector
        v-model="selectedAsset"
        :search-assets="searchAsset"
        :loading="isOverviewPending"
        @change="handleAssetChange"
      />

      <span class="text-dark-grey text-base font-medium">Last Update: {{ lastCalculatedDate }}</span>
    </div>

    <OverviewAssetMetaSection
      id="onboarding-step-1"
      tooltips="stablecoin"
      :is-loading="isOverviewPending"
      :data="summaryData"
    />

    <div
      id="onboarding-step-2"
      class="flex scroll-mt-72 flex-col gap-3"
    >
      <div class="grid grid-cols-1 gap-3 md:grid-cols-2">
        <CheckFeature
          :allowed="
            !!isFeatureAllowed('sentiment_metrics.mood_index_level_1') &&
            !!isFeatureAllowed('sentiment_metrics.trust_index_level_1')
          "
          :loading="chartDataMoodTrustStatus === 'pending'"
        >
          <div
            id="onboarding-step-3"
            class="col-span-1 scroll-mt-72"
          >
            <CardChart
              title="Mood Index & Trust Index"
              :subtitle="ASSET_STABLECOIN_TOOLTIPS.mood_trust_index"
              :legends="LEGEND_MOOD_TRUST"
              :is-loading="isOverviewPending"
              body-class="chart-container v1-4xl:min-h-[510px] h-[338px] 2xl:min-h-[400px]"
            >
              <NuxtLink
                :href="onChartClick()"
                external
              >
                <OverviewChart :datasets="chartDataMoodTrust || []" />
              </NuxtLink>
            </CardChart>
          </div>
        </CheckFeature>
        <div
          id="onboarding-step-4"
          class="col-span-1 scroll-mt-72"
        >
          <CardChart
            title="Market Cap & Volume"
            :subtitle="ASSET_STABLECOIN_TOOLTIPS.maket_cap_volume"
            :legends="[
              {
                label: 'Market Cap',
                colorClass: 'bg-accent-purple-600',
              },
              {
                label: 'Volume',
                colorClass: 'bg-neutrals-300',
              },
            ]"
            :is-loading="chartDataMarketcapVolumeStatus === 'pending'"
            body-class="v1-4xl:min-h-[510px] h-[338px] 2xl:min-h-[400px]"
          >
            <NuxtLink
              :href="`/financial?asset1=${route.params.slug}&metric=marketCap&size=100`"
              external
            >
              <NoDataAvailable
                v-if="isChartDataMarketcapVolumeEmpty"
                class="h-full"
              />
              <div
                v-else
                class="flex h-full flex-col gap-3"
              >
                <div class="flex-1">
                  <OverviewChart
                    :datasets="chartDataMarketcapVolume?.marketCap || []"
                    :price-formatter="(price) => '$' + numericDisplay(price)"
                    hide-timescale
                  />
                </div>
                <div class="h-[100px] 2xl:h-[120px]">
                  <OverviewChart
                    :datasets="chartDataMarketcapVolume?.volume || []"
                    :price-formatter="(price) => '$' + numericDisplay(price)"
                    :auto-scale-info-provider="
                      maxVolumeChart > 0
                        ? () => ({
                            priceRange: {
                              minValue: 0,
                              maxValue: maxVolumeChart * 2 * 450000000000, // Need to adjust scale, you can modifying this value
                            },
                            margins: {
                              above: 10,
                              below: 10,
                            },
                          })
                        : undefined
                    "
                  />
                </div>
              </div>
            </NuxtLink>
          </CardChart>
        </div>
      </div>

      <div class="grid grid-cols-1 gap-3 md:grid-cols-2">
        <div
          id="onboarding-step-5"
          class="col-span-1 scroll-mt-60"
        >
          <CardChart
            key="avg-liquidity-cost-chart"
            title="Liq Cost"
            :subtitle="`The average percentage of a crypto asset's market price paid to execute a market order of 100.00 USD on the tracked Centralized Exchanges (CEXs) and Decentralized Exchanges (DEXs). This represents the average cost of trading a specific crypto asset based on the Marginal Cost of Immediacy (MCI) metric.`"
            :legends="[
              {
                label: 'CEX',
                colorClass: 'bg-accent-red-600',
              },
              {
                label: 'DEX',
                colorClass: 'bg-accent-green-700',
              },
            ]"
            :is-loading="chartDataLiquidityCostStatus === 'pending'"
            body-class="v1-4xl:min-h-[510px] h-[338px] 2xl:min-h-[400px]"
          >
            <NuxtLink
              :href="onChartClick()"
              external
            >
              <OverviewChart
                :datasets="chartDataLiquidityCost || []"
                :price-formatter="(price) => numericDisplay(price) + '%'"
              />
            </NuxtLink>
          </CardChart>
        </div>
        <div
          id="onboarding-step-6"
          class="col-span-1 scroll-mt-60"
        >
          <CardChart
            key="price-chart"
            title="Price"
            :subtitle="`Price is the market price of a single unit of a crypto asset, denominated in United States Dollars (USD). This reflects the trading price from various exchanges, offering a snapshot of a crypto asset's value daily.`"
            :legends="[LEGEND_PRICE_VOLUME[0]]"
            :is-loading="chartDataPriceStatus === 'pending'"
            body-class="v1-4xl:min-h-[510px] h-[338px] 2xl:min-h-[400px]"
            enable-log-button
          >
            <template #default="{ logarithmic }">
              <NuxtLink
                :href="onChartClick()"
                external
              >
                <OverviewChart
                  :logarithmic="logarithmic"
                  :datasets="chartDataPrice || []"
                  :price-formatter="(price) => '$' + numericDisplay(price, 4)"
                />
              </NuxtLink>
            </template>
          </CardChart>
        </div>
      </div>

      <div class="grid grid-cols-1 gap-3 md:grid-cols-2">
        <CheckFeature
          :allowed="!!isFeatureAllowed('sentiment_metrics.messages')"
          :loading="isOverviewPending"
        >
          <div
            id="onboarding-step-7"
            class="col-span-1 scroll-mt-60"
          >
            <CardChart
              key="message-chart"
              title="# Messages (14d)"
              subtitle="The total number of posts, comments, and messages exchanged across the tracked communities of a crypto asset over a rolling 14-day period. This shows the overall level of activity and interaction within these communities."
              :legends="LEGEND_PLATFORMS"
              :is-loading="chartMessageStatus === 'pending'"
              body-class="v1-4xl:min-h-[510px] h-[338px] 2xl:min-h-[400px]"
            >
              <NuxtLink
                :href="onChartClick()"
                external
              >
                <OverviewChart
                  :datasets="chartDataMessage || []"
                  :decimals="0"
                />
              </NuxtLink>
            </CardChart>
          </div>
        </CheckFeature>
        <CheckFeature
          :allowed="!!isFeatureAllowed('sentiment_metrics.community_size')"
          :loading="isOverviewPending"
        >
          <div
            id="onboarding-step-8"
            class="col-span-1 scroll-mt-60"
          >
            <CardChart
              key="community-size-chart"
              title="Community Size"
              subtitle="The total number of active and inactive members across the tracked communities of a crypto asset. This shows the overall size and potential reach of these communities."
              :legends="LEGEND_PLATFORMS"
              :is-loading="chartCommunityStatus === 'pending'"
              body-class="chart-container v1-4xl:min-h-[510px] h-[338px] 2xl:min-h-[400px]"
            >
              <NuxtLink
                :href="onChartClick()"
                external
              >
                <OverviewChart
                  :datasets="chartDataCommunity || []"
                  :decimals="0"
                />
              </NuxtLink>
            </CardChart>
          </div>
        </CheckFeature>
      </div>
    </div>

    <ClientOnly>
      <Onboarding
        v-if="summaryStatus !== 'pending' && onboardingStatus !== 'pending' && !onboardingData"
        :steps="ONBOARDING_STABLE_SUMMARY_PAGE"
        :visible="!onboardingData"
        @on-finish="finishOnboarding"
      />
    </ClientOnly>
  </div>
</template>
