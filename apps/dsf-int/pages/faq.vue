<script setup lang="ts">
// Types
import type { Faq } from 'app-types/payload-types'
import useCmsTextHandler from '~/composables/useCmsTextHandler'

const { $colorMode } = useNuxtApp()

const route = useRoute()
const router = useRouter()
const { textColorWatcher } = useCmsTextHandler()

const { data, refresh, pending } = await useLazyAsyncData<{ docs: Faq[] }>(
  'faqList',
  async (ctx) => {
    return $fetch<{ docs: Faq[] }>(`${ctx?.$config.public.payloadApi}/faqs/enabled`, {
      method: 'GET',
      query: {
        limit: 99,
        category: route?.query?.category === 'general' ? route?.query?.category : `${route?.query?.category}-indices`,
      },
    })
  },
  {
    server: false,
    immediate: true,
  },
)

const formattedFaq = computed(() => {
  return data.value?.docs?.map((faq, faqIndex) => ({
    label: faq.question,
    defaultOpen: faqIndex === 0,
    content: faq.answerHtml,
  }))
})

const isDarkMode = computed(() => $colorMode.value === 'dark')

const categoryList = computed<
  {
    label: string
    value: string
  }[]
>(() => {
  return [
    { label: 'General', value: 'general' },
    { label: 'Community Indexes', value: 'community' },
    { label: 'Financial Indexes', value: 'financial' },
    { label: 'ESG Indexes', value: 'climate' },
  ]
})

const handleSelectCategory = (category: string) => router.replace({ query: { category } })

const activeCategory = computed(() =>
  categoryList.value.find((category: { label: string; value: string }) => category.value === route.query.category),
)

watch(
  () => route?.query,
  () => refresh(),
  { deep: true },
)

watch([data, isDarkMode], () => {
  nextTick(() => {
    textColorWatcher('.faqContentHtml', isDarkMode.value)
  })
})

onMounted(() => {
  if (!route.query.category) {
    router.replace({ query: { category: categoryList.value[0]?.value } })
  }
})

useHead({
  title: 'Frequently Asked Questions',
})
</script>

<template>
  <div>
    <HeaderPage
      page-name="faq"
      center
      hide-navigation
    />
    <BodyAuxiliary>
      <div class="mb-10 hidden items-center justify-center gap-5 md:flex">
        <UButton
          v-for="(category, i) in categoryList"
          :key="i"
          color="black"
          size="xl"
          :variant="route.query.category === category.value ? 'solid' : 'outline'"
          padded
          :ui="{ rounded: 'rounded' }"
          @click="handleSelectCategory(category.value)"
        >
          {{ category.label }}
        </UButton>
      </div>

      <div class="mb-10 items-center justify-center gap-5 md:hidden">
        <USelectMenu
          v-slot="{ open }"
          size="xl"
          :options="categoryList"
          option-attribute="label"
          value-attribute="value"
          :model-value="route.query.category as string"
          @change="handleSelectCategory"
        >
          <UButton
            color="black"
            class="h-10 flex-1 justify-between"
          >
            {{ activeCategory?.label || '' }}

            <UIcon
              name="i-heroicons-chevron-right-20-solid"
              class="w-5 text-gray-400 transition-transform dark:text-gray-500"
              :class="[open && 'rotate-90 transform']"
            />
          </UButton>
        </USelectMenu>
      </div>
      <template v-if="pending">
        <LottieNodiensLoader />
      </template>
      <UAccordion
        v-else
        color="gray"
        size="lg"
        variant="solid"
        :items="formattedFaq"
      >
        <template #default="{ item, open }">
          <UButton
            color="white"
            variant="solid"
            class="border !text-left shadow-none ring-0 focus:bg-[#fff] dark:bg-transparent dark:focus:bg-transparent"
            :class="{
              'mb-2': !open,
              'rounded-b-none border-b-0': open,
            }"
            size="lg"
          >
            <span>{{ item.label }}</span>

            <template #trailing>
              <UIcon
                name="i-heroicons-chevron-right-20-solid"
                class="ms-auto h-5 w-5 transform transition-transform duration-200"
                :class="[open && 'rotate-90']"
              />
            </template>
          </UButton>
        </template>
        <template #item="{ item }">
          <div
            class="faqContentHtml cms-content -mt-1.5 rounded rounded-t-none border border-t-0 bg-white px-3.5 py-2.5 dark:bg-transparent dark:text-white"
            v-html="item.content"
          />
        </template>
      </UAccordion>
    </BodyAuxiliary>
  </div>
</template>
