<script setup lang="ts">
import { format } from 'date-fns'
import type { TRPCError } from '@trpc/server'
import { generateRandomHexColor } from '~/helper/string-helper'

const { $client } = useNuxtApp()
const toast = useToast()

const { data: userSummary, status: userSummaryLoading } = useLazyAsyncData('getUserSummary', () => {
  return $client.admin.v1.dashboard.userSummary.query()
})

const { data: metricPopularity, status: metricPopularityLoading } = useLazyAsyncData(
  'getMetricPopularity',
  async () => {
    try {
      return await $client.admin.v1.dashboard.metricPopularity.query()
    } catch (e) {
      const _e = e as TRPCError
      toast.add({
        title: 'Oops!',
        color: 'red',
        description: _e.message ?? 'Something went wrong. Please try again later.',
        icon: 'i-heroicons-exclamation-triangle-20-solid',
      })

      return null
    }
  },
)

const totalItems = computed(() => {
  return [
    {
      key: 'summary-users',
      header: 'Total Users',
      icon: 'i-material-symbols:person-2-outline-rounded',
      count: userSummary?.value?.total_user ?? 0,
    },
    {
      key: 'summary-total-revenue',
      header: 'Total Revenue',
      icon: 'i-material-symbols:currency-exchange',
      count: userSummary?.value?.total_revenue ?? 0,
    },
  ]
})

const userStatus = computed(() => {
  const entries = userSummary?.value?.user_status.entry ?? []

  return {
    data: {
      labels: ['Active', 'Inactive'],
      datasets: [
        {
          backgroundColor: ['#00B0FF', '#FF2323'],
          data: [
            entries.find((item) => item.name === 'active')?.count ?? 0,
            entries.find((item) => item.name === 'inactive')?.count ?? 0,
          ],
        },
      ],
    },
    legends: [
      {
        label: 'Active',
        color: '#00B0FF',
      },
      {
        label: 'Inactive',
        color: '#FF2323',
      },
    ],
  }
})

const userPlans = computed(() => {
  const entries = userSummary?.value?.user_plans.entry ?? []

  const initialColors = ['#3EA95B', '#ED723B', '#8B5CF6']
  const colors = [...initialColors]
  while (colors.length < entries.length) {
    colors.push(generateRandomHexColor())
  }

  return {
    data: {
      labels: entries.map((item) => item.name),
      datasets: [
        {
          backgroundColor: colors,
          data: entries.map((item) => item.count),
        },
      ],
    },
    legends: entries.map((item, index) => ({
      label: item.name,
      color: colors[index] ?? '#000000',
    })),
  }
})

const chartDataRevenueHistory = computed(() => {
  return [
    {
      name: 'Total Revenue',
      color: 'rgba(0,163,255,1)',
      data: (userSummary.value?.revenue_history ?? []).map(([tsInSeconds, value]) => ({
        time: format(new Date(tsInSeconds * 1000), 'yyyy-MM-dd'),
        value,
      })),
    },
  ]
})

const chartDataCommunity = computed(() => {
  const communityIndex = metricPopularity.value?.find((x) => x.group_name.toLowerCase() === 'community index')

  const initialColors = ['#007DCF', '#00A210', '#8B5CF6']
  const colors = [...initialColors]
  while (communityIndex && colors.length < communityIndex.metric.length) {
    colors.push(generateRandomHexColor())
  }

  return {
    data: (communityIndex?.metric ?? []).map((item, index) => ({
      color: colors[index] ?? '#000000',
      value: item.view_count,
    })),
    labels: (communityIndex?.metric ?? []).map((item) => item.metric_name),
  }
})

const chartDataClimate = computed(() => {
  const climateIndex = metricPopularity.value?.find((x) => x.group_name.toLowerCase() === 'climate index')

  const initialColors = ['#245732', '#037823', '#00A210']
  const colors = [...initialColors]
  while (climateIndex && colors.length < climateIndex.metric.length) {
    colors.push(generateRandomHexColor())
  }

  return {
    data: (climateIndex?.metric ?? []).map((item, index) => ({
      color: colors[index] ?? '#000000',
      value: item.view_count,
    })),
    labels: (climateIndex?.metric ?? []).map((item) => item.metric_name),
  }
})

const financialIndicesData = computed(
  () => metricPopularity?.value?.find((x) => x.group_name.toLowerCase() === 'financial indices')?.metric,
)

const decentralisationIndicesData = computed(
  () => metricPopularity?.value?.find((x) => x.group_name.toLowerCase() === 'decentralisation indices')?.metric,
)
</script>

<template>
  <div>
    <p class="mb-6 text-2xl font-bold">Summary</p>

    <div class="mb-6">
      <div class="grid grid-cols-1 gap-3 md:grid-cols-8">
        <div class="col-span-1 flex-col gap-3 md:col-span-2">
          <div class="grid grid-cols-1 grid-rows-2 gap-3">
            <AdminDashboardCard
              v-for="item in totalItems"
              :key="item.key"
              :header="item.header"
              :icon="item.icon"
              :count="item.count"
              :pending="userSummaryLoading === 'pending'"
            />
          </div>
        </div>

        <div class="col-span-1 md:col-span-3">
          <AdminSpiderChartCard
            class="h-full"
            title="User Status"
            icon="i-material-symbols:person-2-outline-rounded"
            :legends="userStatus.legends"
            :chart-data="userStatus.data"
            :pending="userSummaryLoading === 'pending'"
          />
        </div>
        <div class="col-span-1 md:col-span-3">
          <AdminSpiderChartCard
            class="h-full"
            title="User Plan"
            icon="i-material-symbols:person-2-outline-rounded"
            :legends="userPlans.legends"
            :chart-data="userPlans.data"
            :pending="userSummaryLoading === 'pending'"
          />
        </div>
      </div>
    </div>

    <AdminChartRevenueHistory
      title="Historical Revenue"
      :legends="[{ label: 'Total Revenue', colorClass: 'bg-base-primary-600' }]"
      :datasets="chartDataRevenueHistory"
      class="mb-6"
      :pending="userSummaryLoading === 'pending'"
    />

    <AdminTableRevenue />

    <AdminTableAssetsRanking />

    <AdminBarChart
      title="Community Index"
      :labels="chartDataCommunity.labels"
      :datasets="chartDataCommunity.data"
      class="mb-6"
    />

    <AdminBarChart
      title="Climate Index"
      :labels="chartDataClimate.labels"
      :datasets="chartDataClimate.data"
      class="mb-6"
    />

    <AdminTableIndices
      title="Financial Indices"
      :data="financialIndicesData"
      class="mb-6"
      :pending="metricPopularityLoading === 'pending'"
    />

    <AdminTableIndices
      title="Decentralisation Indices"
      :data="decentralisationIndicesData"
      class="mb-6"
      :pending="metricPopularityLoading === 'pending'"
    />
  </div>
</template>
