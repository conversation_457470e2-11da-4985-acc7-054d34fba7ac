<template>
  <div>
    <div class="mb-10">
      <p class="mb-1 text-2xl font-bold">Exclude Paywall Management</p>
      <p>Manage user list to exclude from the paywall</p>
    </div>

    <div class="flex flex-col gap-5">
      <div class="flex flex-wrap items-center justify-between gap-5">
        <UInput
          v-model="searchText"
          variant="transparent"
          trailing-icon="i-heroicons-magnifying-glass"
          class="order-2 w-full md:order-1 md:w-auto"
          placeholder="Search Users"
        />
        <UButton
          label="Add User"
          icon="i-heroicons-plus"
          color="midnight"
          variant="solid"
          size="md"
          truncate
          class="bg-neutrals-800 order-1 min-h-[42px] w-full justify-center md:order-2 md:w-auto md:justify-start"
          @click="showModal = true"
        />
      </div>
      <UCard>
        <UTableWrapper
          :columns="columns"
          :data="data?.data"
          :pending="pending"
        >
          <template #id-header="head">
            <SortableColumn
              column="id"
              button-class="-ml-4"
            >
              <span class="whitespace-nowrap text-left">{{ head.column.label }}</span>
            </SortableColumn>
          </template>
          <template #role-header="head">
            <SortableColumn
              column="role"
              button-class="-ml-4"
            >
              <span class="whitespace-nowrap text-left">{{ head.column.label }}</span>
            </SortableColumn>
          </template>
          <template #name-header="head">
            <SortableColumn
              column="name"
              button-class="-ml-4"
            >
              <span class="whitespace-nowrap text-left">{{ head.column.label }}</span>
            </SortableColumn>
          </template>
          <template #company-header="head">
            <SortableColumn
              column="company"
              button-class="-ml-4"
            >
              <span class="whitespace-nowrap text-left">{{ head.column.label }}</span>
            </SortableColumn>
          </template>
          <template #email-header="head">
            <SortableColumn
              column="email"
              button-class="-ml-4"
            >
              <span class="whitespace-nowrap text-left">{{ head.column.label }}</span>
            </SortableColumn>
          </template>
          <template #role-data="{ row }">
            {{ capitalizeFirstLetter(row.role) }}
          </template>
          <template #company-data="{ row }">
            {{ row.company || '-' }}
          </template>
          <template #actions-data="{ row }">
            <div class="flex">
              <UButton
                label="Remove"
                icon="i-material-symbols:delete-outline-sharp"
                size="sm"
                class="!text-gray-500 hover:bg-red-400 hover:!text-red-900 dark:!text-white dark:hover:bg-red-400 dark:hover:!text-red-900"
                variant="ghost"
                @click="handleDelete(row.userId)"
              />
            </div>
          </template>
        </UTableWrapper>
      </UCard>

      <div
        v-if="totalItems > 0"
        class="flex flex-col-reverse items-center justify-center gap-6 text-center md:flex-row md:justify-between"
      >
        <p class="text-neutrals-800 text-sm dark:text-white">
          {{ paginationEntriesLabel }}
        </p>
        <UPagination
          v-model="page"
          :page-count="perPage"
          :total="totalItems"
          :max="isMobileDevice() ? 7 : 10"
          :disabled="false"
          :active-button="{
            color: 'sky',
          }"
        />
      </div>
    </div>
    <AddUserExcludePaywall
      v-model="showModal"
      @add-success="refresh"
    />
  </div>
</template>
<script setup lang="ts">
import { isMobileDevice } from '~/helper/utilities-helper'
import { capitalizeFirstLetter } from '@dsf/int/helper/string-helper'
import type { TRPCError } from '@trpc/server'
import AddUserExcludePaywall from '~/components/molecule/AdminExcludePaywall/AddUserExcludePaywall.vue'

const { $client } = useNuxtApp()
const route = useRoute()

const showModal = ref(false)
const toast = useToast()

const columns = [
  {
    key: 'userId',
    label: 'User ID',
  },
  {
    key: 'role',
    label: 'User Role',
  },
  {
    key: 'name',
    label: 'Name',
  },
  {
    key: 'company',
    label: 'Company',
  },
  {
    key: 'email',
    label: 'Email Address',
  },
  {
    key: 'actions',
    label: 'Actions',
  },
]
const queryString = computed(() => {
  const hasSort = Object.keys(route.query).includes('sort')
  const isAsc = Object.keys(route.query).includes('asc')

  return {
    page: Number(route.query.page ?? 1),
    keyword: (route.query.search ?? undefined) as string | undefined,
    ...(hasSort ? { sort: [route.query.sort, isAsc ? 'asc' : 'desc'] } : {}),
  }
})

const { data, status, refresh } = await useLazyAsyncData(
  'getAdminUsersTable',
  async () => {
    return $client.admin.v1.excludedPaywall.list.query({
      page: queryString?.value.page,
      column: queryString?.value.sort?.[0] as 'email' | 'name' | 'company' | 'role' | 'userId' | undefined,
      order: queryString?.value.sort?.[1] as 'asc' | 'desc' | undefined,
      excludedStatus: true,
      searchTerm: queryString?.value.keyword,
    })
  },
  {
    watch: [queryString],
    immediate: false,
    server: false,
  },
)

const pending = computed(() => status.value === 'pending')

const searchText = computed({
  get: () => route.query.search as string,
  set: async (search) =>
    await navigateTo({
      query: {
        ...route.query,
        search,
      },
    }),
})

const perPage = ref(Number(route.query.perPage ?? 10))
const page = computed({
  get: () => Number(route.query.page ?? 1),
  set: async (page) =>
    await navigateTo({
      query: {
        ...route.query,
        page,
      },
    }),
})
const totalItems = computed(() => data.value?.totalItem ?? 0)

const paginationEntriesLabel = computed(() => {
  const startEntry = (page.value - 1) * perPage.value + 1
  let endEntry = page.value * perPage.value
  endEntry = Math.min(endEntry, totalItems.value)

  return `Showing ${startEntry} to ${endEntry} of ${totalItems.value} Entries`
})

const handleDelete = async (id: string) => {
  const userResponse = confirm('Do you want to proceed?')

  if (!userResponse) {
    return
  }

  try {
    const { success } = await $client.admin.v1.excludedPaywall.unassign.mutate([id])
    toast.add({
      color: 'green',
      title: 'Request successfully sent!',
      description: success || 'Successfully removed!',
      icon: 'i-heroicons-check-circle',
    })
    refresh()
  } catch (e) {
    const _e = e as TRPCError
    toast.add({
      color: 'red',
      title: 'Request was not sent!',
      description: _e.message ?? 'Something went wrong. Please try again later.',
      icon: 'i-heroicons-exclamation-triangle-20-solid',
    })
  }
}

onMounted(() => {
  refresh()
})
</script>
