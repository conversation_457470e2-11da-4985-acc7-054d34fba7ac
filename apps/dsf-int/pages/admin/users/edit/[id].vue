<script setup lang="ts">
import { z } from 'zod'
import { format } from 'date-fns'
import AdminUserPlanHistory from '~/components/molecule/AdminUserPlanHistory/AdminUserPlanHistory.vue'
import { capitalizeFirstLetter } from '~/helper/string-helper'
import type { TRPCError } from '@trpc/server'
import type { FormSubmitEvent } from '#ui/types'

const { $client, $router } = useNuxtApp()
const route = useRoute()

const isLoading = ref(false)
const isEditable = ref(false)
const toast = useToast()

const schema = z.object({
  user_id: z.string().nonempty({ message: 'User ID is required.' }),
  first_name: z.string().nonempty({ message: 'First name cannot be empty' }),
  last_name: z.string().nonempty({ message: 'Last name cannot be empty' }),
  email: z.string().nonempty({ message: 'Email cannot be empty.' }).email({ message: 'Email must be a valid address' }),
  company: z.string().nonempty({ message: 'Company is required.' }),
  user_role: z.string().nonempty({ message: 'User Role is required.' }),
  hashpack_address: z.string().optional(),
  registration_date: z.string().nonempty({ message: 'Registration date is required.' }),
  last_login_date: z.string().optional(),
  onboarding_status: z.string().nonempty({ message: 'Onboarding status is required.' }),
  suspended_reason: z.string().optional(),
  plan: z.string().nonempty({ message: 'Plan is required' }),
  plan_activation_date: z
    .preprocess((val) => (val ? new Date(val as string) : new Date(format(new Date(), 'yyyy-MM-dd'))), z.date())
    .refine((val) => !isNaN(val.getTime()), { message: 'Plan activation date is required.' }),
  plan_expiry_date: z
    .preprocess((val) => (val ? new Date(val as string) : new Date(format(new Date(), 'yyyy-MM-dd'))), z.date())
    .refine((val) => !isNaN(val.getTime()), { message: 'Plan expiry date is required.' }),
})

type Schema = z.output<typeof schema>

const state = reactive({
  user_id: '',
  first_name: '',
  last_name: '',
  email: '',
  company: '',
  user_role: '',
  hashpack_address: '',
  registration_date: '',
  last_login_date: '',
  onboarding_status: 'Pending',
  suspended_reason: '',
  plan: 'Basic',
  plan_activation_date: format(new Date(), 'yyyy-MM-dd'),
  plan_expiry_date: format(new Date(), 'yyyy-MM-dd'),
})

const { data, refresh } = await useLazyAsyncData(
  () => {
    const id = route.params.id
    return $client.admin.v1.user.profile.query(id as string)
  },
  {
    server: false,
    immediate: true,
  },
)

const loadUserForm = () => {
  state.user_id = data?.value?.id ?? ''
  state.first_name = data?.value?.first_name || ''
  state.last_name = data?.value?.last_name || ''
  state.email = data?.value?.email || ''
  state.company = data?.value?.company || ''
  state.user_role = capitalizeFirstLetter(data?.value?.role || '')
  state.hashpack_address = data?.value?.hashpack_account || ''
  state.registration_date = data?.value?.registration_date
    ? format(new Date(data.value.registration_date), 'yyyy-MM-dd HH:mm:ss')
    : ''
  state.last_login_date = data?.value?.last_login ? format(new Date(data.value.last_login), 'yyyy-MM-dd HH:mm:ss') : ''
  state.onboarding_status = data?.value?.onboarding_status
    ? capitalizeFirstLetter(data?.value?.onboarding_status)
    : 'Pending'
  state.suspended_reason = data?.value?.suspended_reason || ''
  state.plan = data?.value?.plan ? capitalizeFirstLetter(data?.value?.plan as string) : 'Basic'
  state.plan_activation_date = format(new Date(), 'yyyy-MM-dd')
  state.plan_expiry_date = format(new Date(), 'yyyy-MM-dd')
}

const onSubmit = async (event: FormSubmitEvent<Schema>) => {
  const { first_name, last_name, company, email } = event.data
  isLoading.value = true

  try {
    const { message } = await $client.admin.v1.user.updateUser.mutate({
      userId: route.params.id as string,
      firstName: first_name,
      lastName: last_name,
      company,
      email,
    })
    toast.add({
      color: 'green',
      title: 'Request successfully sent!',
      description: message || 'Successfully saved!',
      icon: 'i-heroicons-check-circle',
    })
    await refresh()
    loadUserForm()
  } catch (e) {
    const _e = e as TRPCError
    toast.add({
      color: 'red',
      title: 'Request was not sent!',
      description: _e.message ?? 'Something went wrong. Please try again later.',
      icon: 'i-heroicons-exclamation-triangle-20-solid',
    })
  } finally {
    isLoading.value = false
  }
}

onMounted(() => {
  watch(data, () => loadUserForm())
})
</script>

<template>
  <div>
    <UButton
      class="mb-10"
      variant="outline"
      color="black"
      size="xl"
      icon="i-heroicons-arrow-left"
      label="Back to Users"
      @click="$router.push('/admin/users')"
    />
    <div class="mb-10">
      <p class="mb-1 text-2xl font-bold">User Details</p>
      <p>View and edit user information.</p>
    </div>
    <UCard class="mb-6">
      <div class="mb-10">
        <p class="mb-1 text-2xl font-bold">User Information</p>
        <p>Manage user details and settings.</p>
      </div>
      <UForm
        :schema="schema"
        :state="state"
        @submit.prevent="onSubmit"
      >
        <div class="mb-4 grid grid-cols-1 gap-4 md:grid-cols-2">
          <UFormGroup
            label="User ID"
            name="user_id"
          >
            <UInput
              v-model="state.user_id"
              name="user_id"
              disabled
            />
          </UFormGroup>
          <UFormGroup
            label="User Role"
            name="user_role"
          >
            <USelectMenu
              v-model="state.user_role"
              :options="['Admin', 'User']"
              :disabled="!isEditable"
              variant="none"
            />
          </UFormGroup>
        </div>

        <div class="mb-4 grid grid-cols-1 gap-4 md:grid-cols-2">
          <UFormGroup
            label="First Name"
            name="first_name"
          >
            <UInput
              v-model="state.first_name"
              name="first_name"
              :disabled="!isEditable"
            />
          </UFormGroup>
          <UFormGroup
            label="Last Name"
            name="last_name"
          >
            <UInput
              v-model="state.last_name"
              name="last_name"
              :disabled="!isEditable"
            />
          </UFormGroup>
        </div>

        <div class="mb-4 grid grid-cols-1 gap-4 md:grid-cols-2">
          <UFormGroup
            label="Company"
            name="company"
          >
            <UInput
              v-model="state.company"
              name="company"
              type="text"
              :disabled="!isEditable"
            />
          </UFormGroup>
          <UFormGroup
            label="Email Address"
            name="email"
          >
            <UInput
              v-model="state.email"
              name="email"
              type="email"
              :disabled="!isEditable"
            />
          </UFormGroup>
        </div>

        <div class="mb-4 grid grid-cols-1 gap-4 md:grid-cols-2">
          <UFormGroup
            label="Hashpack Address"
            name="hashpack_address"
          >
            <UInput
              v-model="state.hashpack_address"
              name="hashpack_address"
              type="text"
              :disabled="!isEditable"
            />
          </UFormGroup>
          <UFormGroup
            label="Registration Date"
            name="registration_date"
          >
            <UInput
              v-model="state.registration_date"
              name="registration_date"
              type="text"
              disabled
            />
          </UFormGroup>
        </div>

        <div class="mb-4 grid grid-cols-1 gap-4 md:grid-cols-2">
          <UFormGroup
            label="Last Login Date"
            name="last_login_date"
          >
            <UInput
              v-model="state.last_login_date"
              name="last_login_date"
              type="text"
              disabled
            />
          </UFormGroup>
          <UFormGroup
            label="Onboarding Status"
            name="onboarding_status"
          >
            <USelectMenu
              v-model="state.onboarding_status"
              :options="['Pending', 'Completed']"
              :disabled="!isEditable"
              variant="none"
            />
          </UFormGroup>
        </div>

        <div class="mb-4 grid grid-cols-1 gap-4 md:grid-cols-2">
          <UFormGroup
            label="Suspended Reason"
            name="suspended_reason"
          >
            <UInput
              v-model="state.suspended_reason"
              name="suspended_reason"
              type="text"
              :disabled="!isEditable"
            />
          </UFormGroup>
          <UFormGroup
            label="Plan"
            name="plan"
          >
            <USelectMenu
              v-model="state.plan"
              :options="['Basic', 'Enterprise']"
              :disabled="!isEditable"
              variant="none"
            />
          </UFormGroup>
        </div>

        <div class="mb-4 grid grid-cols-1 gap-4 md:grid-cols-2">
          <UFormGroup
            label="Plan Activation Date"
            name="plan_activation_date"
          >
            <UInput
              v-model="state.plan_activation_date"
              name="plan_activation_date"
              type="date"
              :disabled="!isEditable"
            />
          </UFormGroup>
          <UFormGroup
            label="Plan Expiry Date"
            name="plan_expiry_date"
          >
            <UInput
              v-model="state.plan_expiry_date"
              name="plan_expiry_date"
              type="date"
              :disabled="!isEditable"
            />
          </UFormGroup>
        </div>
        <div class="flex items-center justify-between text-center">
          <UFormGroup
            label="Enable Editing"
            class="flex gap-4"
            :ui="{
              wrapper: 'wrapper',
              inner: 'order-2 select-none cursor-pointer',
              container: 'mt-1 relative order-1',
            }"
          >
            <UToggle
              v-model="isEditable"
              color="primary"
              :ui="{
                container: {
                  base: 'dark:bg-white',
                },
              }"
            />
          </UFormGroup>
          <UButton
            color="base-primary"
            size="lg"
            label="Save Changes"
            class="px-5"
            type="submit"
            :loading="isLoading"
            :disabled="!isEditable"
          />
        </div>
      </UForm>
    </UCard>

    <AdminUserPlanHistory class="mb-6" />
    <AdminUserDiscountList class="mb-6" />
    <AdminUserSupportLogList class="mb-6" />
    <AdminUserActions class="mb-6" />
    <AdminUserDangerZone class="mb-6" />
  </div>
</template>
