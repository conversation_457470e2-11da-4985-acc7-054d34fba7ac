<template>
  <div>
    <div class="mb-10">
      <p class="mb-1 text-2xl font-bold">User Management</p>
      <p>Add, edit, and manage user accounts across your platform.</p>
    </div>

    <div class="flex flex-col gap-5">
      <div class="flex flex-wrap items-center justify-between gap-5">
        <UInput
          v-model="searchText"
          variant="transparent"
          trailing-icon="i-heroicons-magnifying-glass"
          class="w-full md:w-auto"
          placeholder="Search Users"
        />
        <div class="flex gap-2">
          <AdminUserListFilter />
          <UButton
            label="Add User"
            icon="i-heroicons-plus"
            color="midnight"
            variant="solid"
            size="md"
            truncate
            class="bg-neutrals-800 min-h-[42px] w-full justify-center md:w-auto md:justify-start"
            to="/admin/users/create"
          />
        </div>
      </div>
      <UCard>
        <UTableWrapper :columns="columns" :data="data?.data" :pending="pending">
          <template #role-data="{ row }">
            {{ capitalizeFirstLetter(row.role) }}
          </template>
          <template #name-data="{ row }"> {{ row.first_name }} {{ row.last_name }} </template>
          <template #company-data="{ row }">
            {{ row.company || '-' }}
          </template>
          <template #auth_providers-data="{ row }">
            {{ capitalizeFirstLetter(row.auth_providers.join(', ')) }}
          </template>
          <template #registration_date-data="{ row }">
            {{ format(row.registration_date, 'yyyy-MM-dd HH:mm:ss') }}
          </template>
          <template #suspended_reason-data="{ row }">
            {{ row.suspended_reason || '-' }}
          </template>
          <template #plan-data="{ row }">
            {{ row.plan || '-' }}
          </template>
          <template #actions-data="{ row }">
            <div class="flex">
              <UButton
                icon="i-material-symbols:edit-square-outline-sharp"
                size="sm"
                class="!text-gray-500 hover:!bg-gray-300 dark:!text-white dark:hover:!bg-gray-500"
                variant="ghost"
                :to="`/admin/users/edit/${row.id}`"
              />
              <UButton
                icon="i-material-symbols:delete-outline-sharp"
                size="sm"
                class="!text-gray-500 hover:bg-red-400 hover:!text-red-900 dark:!text-white dark:hover:bg-red-400 dark:hover:!text-red-900"
                variant="ghost"
                @click="handleDelete(row.id)"
              />
            </div>
          </template>
        </UTableWrapper>
      </UCard>

      <div
        v-if="totalItems > 0"
        class="flex flex-col-reverse items-center justify-center gap-6 text-center md:flex-row md:justify-between"
      >
        <p class="text-neutrals-800 text-sm dark:text-white">
          {{ paginationEntriesLabel }}
        </p>
        <UPagination
          v-model="page"
          :page-count="perPage"
          :total="totalItems"
          :max="isMobileDevice() ? 7 : 10"
          :disabled="false"
          :active-button="{
            color: 'black',
          }"
        />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { isMobileDevice } from '~/helper/utilities-helper'
import { capitalizeFirstLetter } from '@dsf/int/helper/string-helper'
import { format } from 'date-fns'
import AdminUserListFilter from '~/components/molecule/AdminUserListFilter/AdminUserListFilter.vue'

const { $client } = useNuxtApp()
const route = useRoute()
const toast = useToast()

const columns = [
  {
    key: 'id',
    label: 'User ID',
  },
  {
    key: 'role',
    label: 'User Role',
  },
  {
    key: 'name',
    label: 'Name',
  },
  {
    key: 'company',
    label: 'Company',
  },
  {
    key: 'auth_providers',
    label: 'Auth Provider',
  },
  {
    key: 'email',
    label: 'Email Address',
  },
  {
    key: 'registration_date',
    label: 'Registration Date',
  },
  {
    key: 'suspended_reason',
    label: 'Suspended Reason',
  },
  {
    key: 'plan',
    label: 'Plan ID',
  },
  {
    key: 'actions',
    label: 'Actions',
  },
]

const queryString = computed(() => ({
  page: Number(route.query.page ?? 1),
}))

const { data, status, refresh } = await useLazyAsyncData(
  'getAdminUsersTable',
  async () => {
    return $client.admin.v1.user.list.query({
      page: queryString?.value.page,
      // "userType": "user" | "admin"
      // "status": "active" | "inactive" | "suspend"
    })
  },
  {
    watch: [queryString],
    immediate: false,
    server: false,
  }
)

const pending = computed(() => status.value === 'pending')

const searchText = computed({
  get: () => route.query.search as string,
  set: async search =>
    await navigateTo({
      query: {
        ...route.query,
        search,
      },
    }),
})

const perPage = ref(Number(route.query.perPage ?? 10))
const page = computed({
  get: () => Number(route.query.page ?? 1),
  set: async page =>
    await navigateTo({
      query: {
        ...route.query,
        page,
      },
    }),
})
const totalItems = computed(() => data.value?.totalItem ?? 0)

const paginationEntriesLabel = computed(() => {
  const startEntry = (page.value - 1) * perPage.value + 1
  let endEntry = page.value * perPage.value
  endEntry = Math.min(endEntry, totalItems.value)

  return `Showing ${startEntry} to ${endEntry} of ${totalItems.value} Entries`
})

const handleDelete = async (id: string) => {
  const userResponse = confirm('Do you want to proceed?')

  if (!userResponse) {
    return
  }

  try {
    const response = await $client.admin.v1.user.deleteUser.mutate({ id: id.trim() })

    toast.add({
      title: 'Success',
      description: response.message,
    })

    refresh()
  } catch (err) {
    const error = err as Error

    toast.add({
      title: 'Error',
      icon: 'i-heroicons-exclamation-triangle-20-solid',
      description: error.message ?? 'An error occurred while deleting the user.',
      color: 'red',
    })
  }
}

onMounted(() => {
  refresh()
})
</script>
