<script lang="ts" setup>
import { z } from 'zod'
import { format } from 'date-fns'
import type { FormSubmitEvent } from '#ui/types'

const toast = useToast()
const { $client, $router } = useNuxtApp()

const isLoading = ref(false)

const schema = z.object({
  first_name: z.string().nonempty({ message: 'First name cannot be empty' }),
  last_name: z.string().nonempty({ message: 'Last name cannot be empty' }),
  email: z.string().nonempty({ message: 'Email cannot be empty.' }).email({ message: 'Email must be a valid address' }),
  company: z.string().nonempty({ message: 'Company is required.' }),
  user_role: z.string().nonempty({ message: 'User Role is required.' }),
  plan: z.string().nonempty({ message: 'Plan is required' }),
  discount_code: z.string().optional(),
  plan_activation_date: z
    .preprocess((val) => (val ? new Date(val as string) : new Date(format(new Date(), 'yyyy-MM-dd'))), z.date())
    .refine((val) => !isNaN(val.getTime()), { message: 'Plan activation date is required.' }),
  plan_expiry_date: z
    .preprocess((val) => (val ? new Date(val as string) : new Date(format(new Date(), 'yyyy-MM-dd'))), z.date())
    .refine((val) => !isNaN(val.getTime()), { message: 'Plan expiry date is required.' }),
})

type Schema = z.output<typeof schema>

const state = reactive({
  first_name: '',
  last_name: '',
  email: '',
  company: '',
  user_role: 'Admin',
  plan: 'Basic',
  discount_code: '',
  plan_activation_date: format(new Date(), 'yyyy-MM-dd'),
  plan_expiry_date: format(new Date(), 'yyyy-MM-dd'),
})

const resetForm = () => {
  state.first_name = ''
  state.last_name = ''
  state.email = ''
  state.company = ''
  state.user_role = 'Admin'
  state.plan = 'Basic'
  state.discount_code = ''
  state.plan_activation_date = format(new Date(), 'yyyy-MM-dd')
  state.plan_expiry_date = format(new Date(), 'yyyy-MM-dd')
}

const onSubmit = async (event: FormSubmitEvent<Schema>) => {
  try {
    const response = await $client.admin.v1.user.createUser.mutate({
      first_name: event.data.first_name,
      last_name: event.data.last_name,
      email: event.data.email,
      company: event.data.company,
      role: event.data.user_role,
    })

    toast.add({
      title: 'Success',
      description: response.message,
    })

    resetForm()
    navigateTo('/admin/users')
  } catch (err) {
    const error = err as Error

    toast.add({
      title: 'Error',
      icon: 'i-heroicons-exclamation-triangle-20-solid',
      description: error.message ?? 'An error occurred while creating the user.',
      color: 'red',
    })
  }
}
</script>

<template>
  <div>
    <UButton
      class="mb-10"
      variant="outline"
      color="black"
      size="xl"
      icon="i-heroicons-arrow-left"
      label="Back to Users"
      @click="$router.push('/admin/users')"
    />
    <div class="mb-10">
      <p class="mb-1 text-2xl font-bold">Add New User</p>
      <p>Create a new user account in the system.</p>
    </div>
    <UCard>
      <UForm
        :schema="schema"
        :state="state"
        @submit.prevent="onSubmit"
      >
        <div class="mb-4 grid grid-cols-1 gap-4">
          <UFormGroup
            label="User Role"
            name="user_role"
          >
            <USelectMenu
              v-model="state.user_role"
              :options="['Admin', 'User']"
              variant="none"
            />
          </UFormGroup>
        </div>

        <div class="mb-4 grid grid-cols-1 gap-4 md:grid-cols-2">
          <UFormGroup
            label="First Name"
            name="first_name"
          >
            <UInput
              v-model="state.first_name"
              name="first_name"
            />
          </UFormGroup>
          <UFormGroup
            label="Last Name"
            name="last_name"
          >
            <UInput
              v-model="state.last_name"
              name="last_name"
            />
          </UFormGroup>
        </div>

        <div class="mb-4 grid grid-cols-1 gap-4 md:grid-cols-2">
          <UFormGroup
            label="Company"
            name="company"
          >
            <UInput
              v-model="state.company"
              name="company"
              type="text"
              :disabled="isLoading"
            />
          </UFormGroup>
          <UFormGroup
            label="Email Address"
            name="email"
          >
            <UInput
              v-model="state.email"
              name="email"
              type="email"
            />
          </UFormGroup>
        </div>

        <div class="mb-4 grid grid-cols-1 gap-4 md:grid-cols-2">
          <UFormGroup
            label="Plan"
            name="plan"
          >
            <USelectMenu
              v-model="state.plan"
              :options="['Basic', 'Enterprise']"
              variant="none"
            />
          </UFormGroup>
          <UFormGroup
            label="Discount Code"
            name="discount_code"
          >
            <UInput
              v-model="state.discount_code"
              name="discount_code"
              type="text"
              :disabled="isLoading"
            />
          </UFormGroup>
        </div>

        <div class="mb-4 grid grid-cols-1 gap-4 md:grid-cols-2">
          <UFormGroup
            label="Plan Activation Date"
            name="plan_activation_date"
          >
            <UInput
              v-model="state.plan_activation_date"
              name="plan_activation_date"
              type="date"
              :disabled="isLoading"
            />
          </UFormGroup>
          <UFormGroup
            label="Plan Expiry Date"
            name="plan_expiry_date"
          >
            <UInput
              v-model="state.plan_expiry_date"
              name="plan_expiry_date"
              type="date"
              :disabled="isLoading"
            />
          </UFormGroup>
        </div>

        <div class="flex flex-col items-end text-center">
          <UButton
            color="base-primary"
            size="lg"
            label="Add User"
            class="mb-3 px-5"
            type="submit"
            :loading="isLoading"
          />
        </div>
      </UForm>
    </UCard>
  </div>
</template>
