<script setup lang="ts">
import { z } from 'zod'
import type { FormSubmitEvent } from '#ui/types'

// Stores
import { useSessionStore } from '~/stores/session'
import { useUserStore } from '~/stores/profile'

// Types
const runtimeConfig = useRuntimeConfig()

const BROWSERS_LIST = ['Chrome', 'Safari', 'Edge', 'Firefox', 'Opera', 'Others']
const DEVICES_LIST = ['Desktop', 'Tablet', 'Mobile']
const ACCEPTED_FILE_TYPES = ['image/jpeg', 'image/png', 'application/pdf', 'video/mp4', 'video/quicktime']

const userStore = useUserStore()
const { userProfile } = storeToRefs(userStore)
const sessionStore = useSessionStore()
const isLogged = sessionStore.isLogged
const toast = useToast()
const isSubmitting = ref(false)

const schema = z
  .object({
    first_name: z.string().nonempty('First name cannot be empty'),
    last_name: z.string().nonempty('Last name cannot be empty'),
    email: z.string().nonempty('Email cannot be empty.').email(),
    company: z.string().optional(),
    title: z.string().max(255).nonempty('Title cannot be empty.'),
    reproduceSteps: z.string().max(255).nonempty('Reproduction steps cannot be empty.'),
    browser: z.string().nonempty('Browser cannot be empty.'),
    other: z.string().optional(), // conditionally validated below
    device: z.string().nonempty('Device cannot be empty.'),
    files: z
      .array(
        z
          .any()
          .refine((file) => !file || file.size <= 2000000, 'File size is too large')
          .refine((file) => !file || ACCEPTED_FILE_TYPES.includes(file.type), 'Unsupported file format'),
      )
      .optional(),

    is_agree: z.boolean().refine((value) => value === true, {
      message: 'You must accept the Terms and Privacy Policy before proceeding',
    }),
  })
  .superRefine((data, ctx) => {
    if (data.browser?.includes('Others') && !data.other) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Browser cannot be empty.',
        path: ['other'],
      })
    }
  })

type Schema = z.output<typeof schema>

const state = reactive<Schema>({
  first_name: '',
  last_name: '',
  email: '',
  company: '',
  title: '',
  reproduceSteps: '',
  browser: '',
  other: '',
  device: '',
  files: [] as File[],
  is_agree: false,
})

const resetForm = () => {
  state.title = ''
  state.reproduceSteps = ''
  state.browser = ''
  state.other = ''
  state.device = ''
  state.files = []
  state.is_agree = false
}

const onSubmit = async (event: FormSubmitEvent<Schema>) => {
  isSubmitting.value = true
  try {
    const formData = new FormData()
    formData.append('first_name', event.data.first_name)
    formData.append('last_name', event.data.last_name)
    formData.append('email', event.data.email)
    event.data.company && formData.append('company', event.data.company)
    formData.append('title', event.data.title)
    formData.append('message', event.data.reproduceSteps)
    formData.append('browser', event.data.browser)
    formData.append('device', event.data.device)
    event.data.other && formData.append('other', event.data.other)
    event.data.files?.forEach((file) => formData.append('file', file))

    const { message } = await $fetch<{
      message: string
    }>(`${runtimeConfig.public.supabase.url}/functions/v1/bug-report`, {
      method: 'POST',
      mode: 'cors',
      body: formData,
    })

    toast.add({
      color: 'green',
      title: 'Report successfully sent!',
      description: message || 'Successfully saved!',
      icon: 'i-heroicons-check-circle',
    })
    resetForm()
  } catch (e: any) {
    toast.add({
      color: 'red',
      title: 'Report was not sent!',
      description: e.data?.message ?? 'Something went wrong. Please try again later.',
      icon: 'i-heroicons-exclamation-triangle-20-solid',
    })
  } finally {
    isSubmitting.value = false
  }
}

watch(
  userProfile,
  (newProfile) => {
    if (newProfile) {
      state.first_name = (newProfile?.displayName ?? '').split(' ')[0] || ''
      state.last_name = (newProfile?.displayName ?? '').split(' ')[1] || ''
      state.email = newProfile?.email ?? ''
      state.company = newProfile?.company ?? ''
    }
  },
  {
    immediate: true,
    deep: true,
  },
)

useHead({
  title: 'Bug Report',
})
</script>

<template>
  <div>
    <HeaderPage
      page-name="bug-report"
      center
      hide-navigation
    />
    <BodyAuxiliary>
      <UForm
        :schema="schema"
        :state="state"
        @submit.prevent="onSubmit"
      >
        <div class="mb-8 grid grid-cols-1 gap-10 sm:grid-cols-2">
          <UFormGroup
            label="First Name"
            name="first_name"
          >
            <UInput
              v-model="state.first_name"
              name="first_name"
              placeholder="Your first name"
              :disabled="isLogged"
            />
          </UFormGroup>

          <UFormGroup
            label="Last Name"
            name="last_name"
          >
            <UInput
              v-model="state.last_name"
              name="last_name"
              placeholder="Your last name"
              :disabled="isLogged"
            />
          </UFormGroup>
        </div>

        <div class="mb-8 grid grid-cols-1 gap-10 sm:grid-cols-2">
          <UFormGroup
            label="Email Address"
            name="email"
          >
            <UInput
              v-model="state.email"
              name="email"
              placeholder="Your email address"
              :disabled="isLogged"
            />
          </UFormGroup>

          <UFormGroup
            label="Company (optional)"
            name="company"
          >
            <UInput
              v-model="state.company"
              name="company"
              placeholder="Your company"
            />
          </UFormGroup>
        </div>

        <div class="mb-8 grid grid-cols-1 gap-4">
          <UFormGroup
            label="Describe the bug in a few words"
            name="title"
          >
            <UInput
              v-model="state.title"
              name="title"
              placeholder="Example: Mood and Trust Index sorting doesn't work."
            />
          </UFormGroup>
        </div>

        <div class="mb-8 grid grid-cols-1 gap-4">
          <UFormGroup
            label="Provide us with detailed reproduction steps"
            name="reproduceSteps"
          >
            <UTextarea
              v-model="state.reproduceSteps"
              name="reproduceSteps"
              placeholder="Example:&#10;&#10;1. Run the [page name] page as [<EMAIL>]&#10;2. Example: Click on [element name] to trigger the [workflow name] workflow.&#10;3. Example: Observe that [unexpected behavior] happens instead of [expected behavior]"
              :rows="5"
            />
          </UFormGroup>
        </div>

        <div class="mb-8 grid grid-cols-1 gap-10 sm:grid-cols-2">
          <UFormGroup
            label="What browser do you use?"
            name="browser"
          >
            <div class="flex flex-wrap items-start gap-5">
              <USelectMenu
                v-model="state.browser"
                size="md"
                variant="none"
                class="bg-neutrals-200 border-neutrals-300 dark:bg-neutrals-500 dark:border-neutrals-700 grow rounded-none border-b"
                :options="BROWSERS_LIST"
                :ui="{
                  base: 'cursor-pointer hover:cursor-pointer min-h-[42px]',
                  height: 'auto',
                  placeholder: 'text-black',
                }"
                :ui-menu="{
                  base: 'my-0 scrollable',
                  height: 'max-h-full',
                  background: 'bg-neutrals-200 dark:bg-neutrals-500',
                  rounded: 'rounded-none',
                  ring: 'ring-0',
                  option: {
                    padding: 'py-2 px-4 cursor-pointer hover:cursor-pointer',
                  },
                }"
                :popper="{
                  offsetDistance: 0,
                }"
              >
                <template #label>
                  <span
                    v-if="state.browser"
                    class="text-black dark:text-white"
                  >
                    {{ state.browser }}
                  </span>
                  <span
                    v-else
                    class="text-neutrals-500 truncate text-nowrap dark:text-white/50"
                  >
                    Select Browser
                  </span>
                </template>
              </USelectMenu>
              <div
                v-if="state.browser.toLowerCase() === 'others'"
                class="grow"
              >
                <UInput
                  v-model="state.other"
                  name="other"
                  placeholder="Browser Name"
                />
              </div>
            </div>
          </UFormGroup>

          <UFormGroup
            label="What device do you use?"
            name="device"
          >
            <USelectMenu
              v-model="state.device"
              variant="none"
              size="md"
              class="bg-neutrals-200 border-neutrals-300 dark:bg-neutrals-500 dark:border-neutrals-700 grow rounded-none border-b"
              :options="DEVICES_LIST"
              :ui="{
                base: 'cursor-pointer hover:cursor-pointer min-h-[42px]',
                height: 'auto',
                placeholder: 'text-black',
              }"
              :ui-menu="{
                base: 'my-0 scrollable',
                height: 'max-h-full',
                background: 'bg-neutrals-200 dark:bg-neutrals-500',
                rounded: 'rounded-none',
                ring: 'ring-0',

                option: {
                  padding: 'py-2 px-4 cursor-pointer hover:cursor-pointer',
                },
              }"
              :popper="{
                offsetDistance: 0,
              }"
            >
              <template #label>
                <span
                  v-if="state.device"
                  class="text-black dark:text-white"
                >
                  {{ state.device }}
                </span>
                <span
                  v-else
                  class="text-neutrals-500 truncate text-nowrap dark:text-white/50"
                >
                  Select Device
                </span>
              </template>
            </USelectMenu>
          </UFormGroup>
        </div>

        <div class="mb-8 grid grid-cols-1 gap-4">
          <UFormGroup
            label="Any additional files you'd like to share? (optional)"
            name="files"
          >
            <FileInput
              v-model="state.files"
              accept=".png,.jpeg,.jpg,.pdf,.mp4,.mov"
            />
          </UFormGroup>
        </div>

        <UFormGroup
          class="mb-8"
          name="is_agree"
        >
          <TermsAndPrivacy v-model="state.is_agree" />
        </UFormGroup>

        <div class="flex flex-col items-center text-center">
          <UButton
            :loading="isSubmitting"
            color="base-primary"
            size="lg"
            label="Send Report"
            class="mb-3 px-12"
            type="submit"
            block
          />
        </div>
      </UForm>
    </BodyAuxiliary>
  </div>
</template>
