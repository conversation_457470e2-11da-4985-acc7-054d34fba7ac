<script setup lang="ts">
import { useSessionStore } from '~/stores/session'

// Constants
import {
  HOME_TABLE_RANK_OPTION_LABELS,
  HOME_TABLE_RANK_OPTION_DESCRIPTIONS,
  HOME_TABLE_RANK_OPTION_VALUES,
} from '~/constants/options'

useHead({
  title: 'Nodiens | Science-Powered Risk Insights for Web3',
  titleTemplate: '',
})

const { $analytics } = useNuxtApp()
const route = useRoute()
const gtm = useGtm()

const sessionStore = useSessionStore()
const isLogged = sessionStore.isLogged

const showLimitedModal = ref(false)
const searchTimeoutId = ref<NodeJS.Timeout | undefined>()

const updateQuery = async (query: Record<string, string | undefined>) => {
  await navigateTo({
    query: {
      ...route.query,
      ...query,
      page: 1,
    },
  })
}

const coinTypes = computed({
  get: () => [
    [
      {
        label: 'Cryptos',
        disabled: true,
        value: 'cryptocurrency',
        checked:
          (route.query.assetType as string)?.split(',')?.some((assetType) => assetType === 'cryptocurrency') ?? true,
        labelClass: '!text-white',
      },
      {
        label: 'Stablecoins',
        disabled: true,
        value: 'stablecoin',
        checked: (route.query.assetType as string)?.split(',').some((assetType) => assetType === 'stablecoin') ?? true,
        labelClass: '!text-white',
      },
    ],
  ],
  set: async (newVal) => {
    const selected = newVal[0]
      ? newVal[0].filter((assetType) => assetType.checked).map((assetType) => assetType.value)
      : ['']
    await updateQuery({ assetType: selected.join(','), q: undefined })
  },
})

const searchText = computed({
  get: () => (route.query.q ?? '') as string,
  set: (q) => {
    if (searchTimeoutId.value) {
      clearTimeout(searchTimeoutId.value)
    }

    searchTimeoutId.value = setTimeout(async () => {
      await updateQuery({ q })
      searchTimeoutId.value = undefined
    }, 500)
  },
})

const selectedRank = computed({
  get: () => (route.query.rank ?? HOME_TABLE_RANK_OPTION_VALUES.moodIndex) as string,
  set: async (rank) => await updateQuery({ rank }),
})

const rankOptions = computed(() => {
  return Object.values(HOME_TABLE_RANK_OPTION_VALUES)
})

onMounted(() => {
  // Track anayltic page
  gtm?.trackView('Home', '/')

  $analytics.page({
    rank: selectedRank.value,
    coin: (route.query.assetType as string)?.split(','),
  })
})
</script>

<template>
  <div>
    <div class="relative">
      <div class="relative flex flex-col justify-end p-5 pt-16 md:min-h-[623px]">
        <div class="mx-auto max-w-5xl">
          <h1 class="text-center text-3xl font-bold md:text-5xl md:leading-tight">
            Leverage our proprietary risk metrics for crypto assets
          </h1>
        </div>

        <HomeDailySummary class="mt-6 min-h-72 md:mt-14" />
      </div>

      <div class="flex flex-col gap-5 p-5">
        <div class="flex flex-wrap items-center gap-4 gap-x-12 sm:gap-x-4">
          <UButton
            v-if="!isLogged"
            label="Asset"
            icon="i-material-symbols-filter-list"
            color="black"
            size="md"
            truncate
            class="order-1 min-h-[42px] sm:order-1"
            @click="showLimitedModal = true"
          />
          <UDropdown
            v-else
            class="order-1 min-h-[42px] sm:order-1"
            :ui="{
              item: { disabled: 'cursor-default opacity-100' },
            }"
            :items="coinTypes"
            :popper="{ placement: 'bottom-start' }"
          >
            <UButton
              label="Asset"
              icon="i-material-symbols-filter-list"
              color="black"
              size="md"
              truncate
              class="min-h-[42px] w-full sm:max-w-60"
            />
            <template #item="{ item }">
              <UCheckbox
                :model-value="item.checked"
                :label="item.label"
                color="sky"
                @change="
                  coinTypes = coinTypes.map((coinType) =>
                    coinType.map((coin) => (coin.value === item.value ? { ...coin, checked: !coin.checked } : coin)),
                  )
                "
              />
            </template>
          </UDropdown>

          <UInput
            v-model="searchText"
            variant="transparent"
            trailing-icon="i-heroicons-magnifying-glass"
            class="order-3 w-full sm:order-2 sm:w-fit"
            placeholder="Search name or symbol"
            :disabled="!isLogged"
          />
          <UButton
            v-if="!isLogged"
            label="Rank by"
            icon="i-heroicons-arrows-up-down"
            color="black"
            variant="outline"
            size="md"
            truncate
            class="order-2 ml-0 min-h-[42px] grow sm:order-3 sm:ml-auto sm:grow-0"
            @click="showLimitedModal = true"
          >
            <template #trailing>
              <UIcon
                name="i-heroicons-chevron-down-20-solid"
                class="ml-auto h-5 w-5 flex-shrink-0 text-gray-400 dark:text-gray-500"
              />
            </template>
          </UButton>
          <USelectMenu
            v-else
            v-model="selectedRank"
            :options="rankOptions"
            variant="outline"
            color="black"
            size="md"
            class="order-2 ml-0 w-fit grow sm:order-3 sm:ml-auto sm:grow-0"
            selected-icon=""
          >
            <template #label>
              <div class="flex items-center gap-2">
                <UIcon
                  name="i-heroicons-arrows-up-down"
                  class="text-lg font-semibold"
                />
                <span>Rank by</span>
              </div>
            </template>

            <template #option="{ option, selected }">
              <div class="flex items-center gap-2 overflow-hidden">
                <div
                  class="flex h-[18px] w-[18px] shrink-0 grow-0 items-center justify-center rounded-full border-2 bg-transparent"
                  :class="[selected ? 'border-base-primary-600' : 'border-neutrals-300']"
                >
                  <div
                    class="h-[8px] w-[8px] rounded-full"
                    :class="[selected ? 'bg-base-primary-600' : 'bg-transparent']"
                  />
                </div>
                <span class="whitespace-nowrap">
                  {{ HOME_TABLE_RANK_OPTION_LABELS[option as keyof typeof HOME_TABLE_RANK_OPTION_LABELS] }}
                </span>
                <InformationPopover
                  v-if="option === 'mood' || option === 'trust'"
                  class="mt-1"
                >
                  <TooltipContent>
                    <p
                      v-html="
                        HOME_TABLE_RANK_OPTION_DESCRIPTIONS[option as keyof typeof HOME_TABLE_RANK_OPTION_DESCRIPTIONS]
                      "
                    />
                  </TooltipContent>
                </InformationPopover>
              </div>
            </template>
          </USelectMenu>
        </div>

        <HomeTable :coin-types="coinTypes.flat().filter((coin) => coin.checked)" />
      </div>

      <AuthLayer v-if="!isLogged" />

      <UModal v-model="showLimitedModal">
        <UCard>
          <p class="text-center text-lg font-medium text-gray-900 dark:text-white">
            You need to sign in to use this feature.
          </p>
          <div class="mt-5 flex w-full items-center justify-center">
            <NuxtLink to="/auth/login">
              <UButton
                label="Login"
                color="black"
                size="md"
              />
            </NuxtLink>
          </div>
        </UCard>
      </UModal>
    </div>
  </div>
</template>
