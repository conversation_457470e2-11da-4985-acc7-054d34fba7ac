<script setup lang="ts">
import { PAGE_IDS } from '~/constants/cms'

import type { Page } from 'app-types/payload-types'
import useCmsTextHandler from '~/composables/useCmsTextHandler'

const { $colorMode } = useNuxtApp()

const { counterFontWeightHandler, textColorWatcher } = useCmsTextHandler()

const { data } = await useLazyAsyncData<Page>(
  'cmsDisclaimer',
  async (ctx) => {
    return $fetch<Page>(
      `${ctx?.$config.public.payloadApi}/pages/${PAGE_IDS.find((page) => page.name === 'disclaimer')?.id ?? '0'}`,
    )
  },
  {
    server: false,
    immediate: true,
  },
)

const isDarkMode = computed(() => $colorMode.value === 'dark')

watch([data, isDarkMode], () => {
  nextTick(() => {
    counterFontWeightHandler('#disclaimer')
    textColorWatcher('#disclaimer', isDarkMode.value)
  })
})

useHead({
  title: 'Legal Disclaimer',
})
</script>

<template>
  <div>
    <HeaderPage
      page-name="disclaimer"
      hide-navigation
      center
    />
    <Suspense>
      <BodyAuxiliary>
        <div
          id="disclaimer"
          class="cms-content"
          v-html="data?.content"
        />
      </BodyAuxiliary>
    </Suspense>
  </div>
</template>
