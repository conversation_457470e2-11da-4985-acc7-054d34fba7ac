<script setup lang="ts">
import { PAGE_IDS } from '~/constants/cms'

import type { Page } from 'app-types/payload-types'
import useCmsTextHandler from '~/composables/useCmsTextHandler'

const { $colorMode } = useNuxtApp()

const { textColorWatcher } = useCmsTextHandler()

const { data } = await useLazyAsyncData<Page>(
  'cmsDisclaimer',
  async (ctx) => {
    return $fetch<Page>(
      `${ctx?.$config.public.payloadApi}/pages/${PAGE_IDS.find((page) => page.name === 'privacy')?.id ?? '0'}`,
    )
  },
  {
    server: false,
    immediate: true,
  },
)

const isDarkMode = computed(() => $colorMode.value === 'dark')

watch([data, isDarkMode], () => {
  nextTick(() => {
    textColorWatcher('#privacy-policy', isDarkMode.value)
  })
})

useHead({
  title: 'Privacy Policy',
})
</script>

<template>
  <div>
    <HeaderPage
      page-name="privacy"
      hide-navigation
      center
    />

    <BodyAuxiliary>
      <div
        id="privacy-policy"
        class="cms-content"
        v-html="data?.content"
      />
    </BodyAuxiliary>
  </div>
</template>
