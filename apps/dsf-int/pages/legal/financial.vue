<script setup lang="ts">
import { PAGE_IDS } from '~/constants/cms'

import type { Page } from 'app-types/payload-types'

const { data } = await useLazyAsyncData<Page>(
  'cmsFinancialDisclaimer',
  async (ctx) => {
    return $fetch<Page>(
      `${ctx?.$config.public.payloadApi}/pages/${PAGE_IDS.find((page) => page.name === 'financial-disclaimer')?.id ?? '0'}`,
    )
  },
  {
    server: false,
    immediate: true,
  },
)

useHead({
  title: 'Financial Disclaimer',
})
</script>

<template>
  <div>
    <HeaderPage
      page-name="financial-disclaimer"
      hide-navigation
      center
    />
    <Suspense>
      <BodyAuxiliary>
        <div v-html="data?.content" />
      </BodyAuxiliary>
    </Suspense>
  </div>
</template>
