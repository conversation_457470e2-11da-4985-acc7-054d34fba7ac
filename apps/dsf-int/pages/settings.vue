<script setup lang="ts">
import { IconHedera } from '~/assets/svg'
</script>

<template>
  <div class="flex h-full flex-col px-6 py-4">
    <UCard class="flex h-full w-full flex-col">
      <div class="flex h-full w-full flex-col rounded-sm md:flex-row">
        <div
          class="border-neutrals-200 scrollable mb-5 mr-0 flex w-full flex-row overflow-y-auto border-b border-r-0 px-6 md:mb-0 md:mr-5 md:w-3/12 md:flex-col md:border-b-0 md:border-r"
        >
          <NuxtLink
            to="/settings/profile"
            class="text-neutrals-300 block flex w-full items-center px-3 py-3 text-sm md:px-5"
            active-class="!text-black dark:!text-white font-medium"
          >
            <UIcon name="i-heroicons-user" />

            <span class="ml-2">Profile</span>
          </NuxtLink>
          <NuxtLink
            to="/settings/password"
            class="text-neutrals-300 block flex w-full items-center px-3 py-3 text-sm md:px-5"
            active-class="!text-black dark:!text-white font-medium"
          >
            <UIcon name="i-heroicons-lock-closed" />

            <span class="ml-2">Password</span>
          </NuxtLink>
          <NuxtLink
            to="/settings/hedera"
            class="text-neutrals-300 block flex w-full items-center px-3 py-3 text-sm md:px-5"
            active-class="!text-black dark:!text-white font-medium"
          >
            <IconHedera class="h-[24px] w-[24px] text-black dark:text-white" />

            <span class="ml-2">Hedera</span>
          </NuxtLink>
          <NuxtLink
            to="/settings/billing"
            class="text-neutrals-300 block flex w-full items-center px-3 py-3 text-sm md:px-5"
            active-class="!text-black dark:!text-white font-medium"
          >
            <UIcon name="i-heroicons-document-text" />

            <span class="ml-2">Billing</span>
          </NuxtLink>
          <!-- <NuxtLink
            to="/settings/activity-log"
            class="text-neutrals-300 block w-full px-5 py-3 text-sm"
            active-class="border-l-2 border-base-primary-500 !text-black dark:!text-white font-medium"
          >
            <UIcon name="i-heroicons-bell-alert" />

            <span class="ml-2">Activity Log</span>
          </NuxtLink> -->
        </div>

        <div class="w-full flex-1 px-6">
          <NuxtPage />
        </div>
      </div>
    </UCard>
  </div>
</template>
