<script setup lang="ts">
import type { TRPCError } from '@trpc/server'

const route = useRoute()
const { $client } = useNuxtApp()
const toast = useToast()

const { success, uuid } = route.query

useAsyncData(async () => {
  if (!success || !uuid) {
    await navigateTo('/')
  }
})

definePageMeta({
  layout: 'email',
})

useHead({
  title: 'Unsubscribe Success',
})

function doSubscribe() {
  $client.v2.public.newsletter.setSubscription
    .mutate({
      uuid: uuid!.toString(),
      subscription: true,
    })
    .then(async (res) => {
      if (res) {
        await navigateTo(`/newsletter/resubscribe-success?success=ok&uuid=${uuid!.toString()}`)
      }
    })
    .catch((e: TRPCError) => {
      toast.add({
        title: 'Oops!',
        color: 'red',
        description: e.message ?? 'Something went wrong. Please try again later.',
        icon: 'i-heroicons-exclamation-triangle-20-solid',
      })
    })
}
</script>

<template>
  <div>
    <h1 class="m-0 mb-5 text-2xl font-semibold leading-6">You're already unsubscribed</h1>
    <p class="m-0 mb-5 leading-5">You'll no longer receive Nodiens newsletters from Horizon Risk Labs.</p>
    <p class="m-0 mb-5 leading-5">
      Changed your mind?
      <a
        class="text-decoration text-[#007DCF] no-underline hover:underline"
        @click="doSubscribe()"
        >Resubscribe</a
      >
    </p>
    <UButton
      variant="email"
      to="/"
    >
      Continue to our website
    </UButton>
  </div>
</template>
