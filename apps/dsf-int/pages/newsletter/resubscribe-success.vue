<script setup lang="ts">
const route = useRoute()

const { success, uuid } = route.query

useAsyncData(async () => {
  if (!success || !uuid) {
    await navigateTo('/')
  }
})

definePageMeta({
  layout: 'email',
})

useHead({
  title: 'Re-subscribe success',
})
</script>

<template>
  <div>
    <h1 class="m-0 mb-5 text-2xl font-semibold leading-6">You’ve successfully resubscribed</h1>
    <p class="m-0 mb-5 leading-5">You’ll continue to receive Nodiens newsletters from Horizon Risk Labs.</p>
    <p class="m-0 mb-5 leading-5">
      Changed your mind?
      <NuxtLink
        :to="`/newsletter/unsubscribe?uuid=${uuid}`"
        class="text-decoration text-[#007DCF] no-underline hover:underline"
      >
        Unsubscribe
      </NuxtLink>
    </p>
    <UButton
      variant="email"
      to="/"
    >
      Continue to our website
    </UButton>
  </div>
</template>
