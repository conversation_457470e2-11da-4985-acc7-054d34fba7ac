<script setup lang="ts">
import type { TRPCError } from '@trpc/server'

const route = useRoute()
const toast = useToast()

const { uuid } = route.query

const loading = ref(false)
const { $client } = useNuxtApp()
const { data: subscription } = await $client.v2.public.newsletter.getSubscription.useQuery(uuid?.toString())

// Handling validation
useAsyncData(async () => {
  if ((!subscription.value && subscription.value!.uuid === null) || subscription.value!.subscribe === null) {
    await navigateTo('/')
  }
  if (subscription.value!.subscribe === false) {
    await navigateTo(`/newsletter/subscribe?uuid=${uuid}`)
  }
})

function doUnsubscribe() {
  if (subscription.value) {
    loading.value = true
    $client.v2.public.newsletter.setSubscription
      .mutate({
        uuid: subscription.value.uuid!,
        subscription: false,
      })
      .then(async (res) => {
        if (res) {
          await navigateTo(`/newsletter/unsubscribe-success?success=ok&uuid=${subscription.value!.uuid!}`)
        }
      })
      .catch((e: TRPCError) => {
        toast.add({
          title: 'Oops!',
          color: 'red',
          description: e.message ?? 'Something went wrong. Please try again later.',
          icon: 'i-heroicons-exclamation-triangle-20-solid',
        })
      })
      .finally(() => {
        loading.value = false
      })
  } else {
    toast.add({
      title: 'Oops!',
      color: 'red',
      description: 'Something went wrong. Please try again later.',
      icon: 'i-heroicons-exclamation-triangle-20-solid',
    })
  }
}

definePageMeta({
  layout: 'email',
})
useHead({
  title: 'Unsubscribe Newsletter',
})
</script>

<template>
  <div>
    <h1 class="m-0 mb-5 text-2xl font-semibold leading-6">Unsubscribe</h1>
    <p class="m-0 mb-5 leading-5">
      By clicking 'Unsubscribe', you'll no longer receive Nodiens newsletters from Horizon Risk Labs.
    </p>

    <UButton
      variant="email"
      :loading="loading"
      @click="doUnsubscribe()"
    >
      Unsubscribe
    </UButton>
  </div>
</template>
