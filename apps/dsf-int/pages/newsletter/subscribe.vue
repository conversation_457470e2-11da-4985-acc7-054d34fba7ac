<script setup lang="ts">
import type { TRPCError } from '@trpc/server'

const route = useRoute()
const toast = useToast()

const { uuid } = route.query

const { $client } = useNuxtApp()
const { data: subscription } = await $client.v2.public.newsletter.getSubscription.useQuery(uuid?.toString())

// Handling validation
useAsyncData(async () => {
  if ((!subscription.value && subscription.value!.uuid === null) || subscription.value!.subscribe === null) {
    await navigateTo('/')
  }
  if (subscription.value!.subscribe === true) {
    await navigateTo(`/newsletter/unsubscribe?uuid=${uuid}`)
  }
})

function doSubscribe() {
  $client.v2.public.newsletter.setSubscription
    .mutate({
      uuid: uuid!.toString(),
      subscription: true,
    })
    .then(async (res) => {
      if (res) {
        await navigateTo(`/newsletter/resubscribe-success?success=ok&uuid=${uuid!.toString()}`)
      }
    })
    .catch((e: TRPCError) => {
      toast.add({
        title: 'Oops!',
        color: 'red',
        description: e.message ?? 'Something went wrong. Please try again later.',
        icon: 'i-heroicons-exclamation-triangle-20-solid',
      })
    })
}

definePageMeta({
  layout: 'email',
})

useHead({
  title: 'Subscribe Newsletter',
})
</script>

<template>
  <div>
    <h1 class="m-0 mb-5 text-2xl font-semibold leading-6">You're already unsubscribed</h1>
    <p class="m-0 mb-5 leading-5">You'll no longer receive Nodiens newsletters from Horizon Risk Labs.</p>
    <p class="m-0 mb-5 leading-5">
      Changed your mind?
      <a
        class="text-decoration text-[#007DCF] no-underline hover:underline"
        @click="doSubscribe()"
        >Resubscribe</a
      >
    </p>
    <UButton
      variant="email"
      to="/"
    >
      Continue to our website
    </UButton>
  </div>
</template>
