<script setup lang="ts">
import { useUserStore } from '~/stores/profile'
import { Buffer } from 'buffer'

const { $client, $apiClient } = useNuxtApp()
const userStore = useUserStore()
const toast = useToast()
const { userProfile } = storeToRefs(userStore)

const { pairingData, connectWallet, disconnect, getSigner } = useHashpackService()

const isLinkingWallet = ref(false)
const isUnlinkWallet = ref(false)

// NOTE: haspackAccountId will be added to the user profile soon.
// @ts-ignore
const hasWallet = computed(() => userProfile.value?.hashpackAccountId)

const updateProfile = async () => {
  const { data } = await $apiClient.GET('/nodiens/api/v1/account/profile')

  if (data) {
    userStore.setProfile(data.payload)
  }
}

const onLinkWallet = async () => {
  isLinkingWallet.value = true

  if (pairingData.value) {
    isLinkingWallet.value = false
    await disconnect()

    return onLinkWallet()
  }

  await connectWallet()
}

const onUnlinkWallet = async () => {
  isUnlinkWallet.value = true

  if (pairingData.value) {
    isUnlinkWallet.value = false
    await disconnect()
    return onUnlinkWallet()
  }

  await connectWallet()
}

const handleWalletOperation = async (accountId: string, operationType: 'link' | 'unlink') => {
  try {
    const signer = getSigner(accountId)
    const getNonce = await $client.v2.auth.userNonce.mutate()

    setTimeout(async () => {
      const signature = await signer.sign([Buffer.from(`${getNonce.nonce}`)])

      const operation =
        operationType === 'link' ? $client.v2.user.account.linkWallet : $client.v2.user.account.unlinkWallet

      const operationResult = await operation.mutate({ accountId, signature })

      if (operationResult) {
        setTimeout(() => {
          updateProfile()
        }, 500)
      }

      return operationResult
    }, 150)
  } catch (e) {
    toast.add({
      title: 'Oops!',
      color: 'red',
      description: 'Something went wrong, please try again later.',
      icon: 'i-heroicons-exclamation-triangle-20-solid',
    })
  }
}

watch(pairingData, async (newPairing) => {
  if (!newPairing) {
    return
  }

  const accountId = newPairing.accountIds[0] ?? ''

  if (isLinkingWallet.value) {
    await handleWalletOperation(accountId, 'link')
  }

  if (isUnlinkWallet.value) {
    await handleWalletOperation(accountId, 'unlink')
  }
})

onMounted(async () => {
  window.Buffer = window.Buffer || Buffer
})

useHead({
  title: 'Hedera',
})
</script>

<template>
  <ClientOnly>
    <template #fallback>
      <div class="flex flex-col items-center justify-center">
        <USkeleton class="mb-6 h-[200px] w-[200px] rounded-full" />

        <USkeleton class="mb-3 h-[25px] w-[200px]" />
        <USkeleton class="mb-3 h-[25px] w-full" />
        <USkeleton class="mb-6 h-[25px] w-full" />

        <USkeleton class="mb-3 h-[45px] w-[150px]" />
      </div>
    </template>

    <div
      class="flex flex-col items-center justify-center"
      v-if="!hasWallet"
    >
      <img
        src="~/assets/media/hedera-illustration.png"
        alt="Hedera Hashpack"
        class="mb-6 h-[200px] w-auto"
      />

      <h2 class="mb-3 text-center text-xl font-semibold text-black md:text-2xl dark:text-white">
        Connect to HashPack Wallet
      </h2>
      <p class="dark:text-neutrals-50 mb-5 text-center text-sm text-gray-500">
        Secure your experience by connecting your HashPack wallet. Access your account and manage your assets all in one
        place, while keeping your data safe and under your control.
      </p>
      <UButton
        label="Link Wallet"
        color="base-primary"
        size="lg"
        @click="onLinkWallet"
      />
    </div>
    <div
      v-else
      class="flex flex-col items-center justify-center"
    >
      <img
        src="~/assets/media/hedera-illustration.png"
        alt="Hedera Hashpack"
        class="mb-6 h-[200px] w-auto"
      />

      <h2 class="mb-3 text-center text-xl font-semibold text-black md:text-2xl dark:text-white">
        HashPack Wallet Connected
      </h2>
      <p class="dark:text-neutrals-50 text-center text-sm text-gray-500">Your wallet is connected and ready to use.</p>
      <div class="my-6 flex flex-col gap-y-1">
        <span class="dark:text-neutrals-50 text-sm text-black">Connected Account ID</span>
        <UInput
          v-model="hasWallet"
          type="text"
          disabled
        />
      </div>
      <UButton
        label="Unlink Wallet"
        color="red"
        size="lg"
        @click="onUnlinkWallet"
      />
    </div>
  </ClientOnly>
</template>
