<script setup lang="ts">
import { format } from 'date-fns'
import { BILLING_HISTORY_TABLE } from '~/constants/billing-history-table'

// Types
import type { v2AssetWithId } from '~/server/trpc/trpc'
import CheckoutModal from '~/components/molecule/CheckoutModal.vue'

const { $client } = useNuxtApp()
const toast = useToast()
const route = useRoute()

const showAssetSelectorModal = ref(false)

const annual = ref(false)
const isManaging = ref(false)
const isSubscribing = ref(false)
const isUpdateAssetPreferencesLoading = ref(false)
const selectedPlanId = ref()
const showModalSubscribe = ref(false)
const _selectedCoins = ref<v2AssetWithId[]>([])
const modalFormState = reactive({
  period: '',
  payment_method: 'stripe',
  promo_code: '',
})

const { data: currentSubscription, refresh: refreshCurrentSubscription } = await useAsyncData(
  'getCurrentSubscription',
  async () => {
    return $client.v2.user.subscription.currentSubscription.query()
  },
  {
    immediate: false,
    server: false,
  },
)

const { data: assetPreferences, refresh: refreshAssetPreferences } = await useAsyncData(
  'getAssetPreferences',
  async () => {
    return $client.v2.user.account.assetPreferences.query()
  },
  {
    immediate: false,
    server: false,
  },
)

const {
  data: billingHistory,
  status: statusBillingHistory,
  refresh: refreshBillingHistory,
} = await useAsyncData(
  'getBillingHistory',
  async () => {
    return $client.v2.user.subscription.billingHistory.query()
  },
  {
    immediate: false,
    server: false,
  },
)

const {
  data,
  status,
  refresh: refreshPricing,
} = await useAsyncData(
  'getPricingList',
  async () => {
    return $client.v2.public.pricings.getPricing.query()
  },
  {
    immediate: false,
    server: false,
  },
)

const selectedPlan = computed(() => {
  return data.value?.find((plan) => plan.id === selectedPlanId.value)
})

const formattedExpiredDate = computed(() => {
  if (currentSubscription.value?.expired_date) {
    return format(new Date(currentSubscription.value.expired_date), 'MMM dd, yyyy')
  }

  return ''
})

const onManageSubscription = async () => {
  isManaging.value = true
  const manage = await $client.v2.user.subscription.manage.mutate()

  if (manage) {
    isManaging.value = false
    window.location.assign(manage.manage_url!.toString())
  }
}

const onCheckout = async () => {
  // User is updating asset preferences if selectedPlan is falsy
  const isUpdatingAssetPreferences = !selectedPlan.value
  if (isUpdatingAssetPreferences) {
    await updateAssetPreferences()
    return
  }
  await subscribe()
}

const subscribe = async () => {
  isSubscribing.value = true

  const subscribe = await $client.v2.user.subscription.subscribe.mutate({
    product_id: selectedPlan.value?.id!,
    is_annual: annual.value,
    asset_id: _selectedCoins.value.map((coin) => coin.id),
  })

  if (subscribe) {
    isSubscribing.value = false
    setTimeout(() => {
      window.location.assign(subscribe!.checkout_url!.toString())
    }, 100)

    // reset csrf-state before redirect to checkout
    window.location.reload()
  }
}

const onSubscribe = async ({ planId, selectedCoins }: { planId: number; selectedCoins: v2AssetWithId[] }) => {
  _selectedCoins.value = selectedCoins || []
  if (currentSubscription.value === null) {
    // Handle Subscribe
    selectedPlanId.value = planId
    showModalSubscribe.value = true
  } else {
    // Handle Upgrade
    isSubscribing.value = true

    const upgradePlan = await $client.v2.user.subscription.switchPlan.mutate({
      product_id: planId,
      is_annual: annual.value,
      assets: selectedCoins.map((coin) => coin.id),
    })

    if (upgradePlan) {
      isSubscribing.value = false
      window.location.assign(upgradePlan.upgrade_url!.toString())
    }
  }
}

const onUpdateAssetPreferences = async ({ selectedCoins }: { selectedCoins: v2AssetWithId[] }) => {
  const showCheckoutModal =
    selectedCoins.length > (currentSubscription.value?.plan?.features.max_assets as unknown as number)
  _selectedCoins.value = selectedCoins
  if (showCheckoutModal) {
    // If user selects MORE than the max_assets allowed on the current plan
    showModalSubscribe.value = true
    return
  }
  // If user selects LESS than the max_assets allowed on the current plan
  await updateAssetPreferences()
}

const updateAssetPreferences = async () => {
  isUpdateAssetPreferencesLoading.value = true

  try {
    const update = await $client.v2.user.account.updateAsset.mutate({
      assets: _selectedCoins.value.map((coin) => coin.id),
    })

    if (update.redirect_uri) {
      window.location.assign(update.redirect_uri.toString())
    }

    if (update) {
      toast.add({
        title: 'Success',
        color: 'green',
        description: 'Your asset selection has been updated.',
        icon: 'i-heroicons-check-circle-20-solid',
      })

      refreshCurrentSubscription()
    }
  } catch (error) {
    toast.add({
      title: 'Error',
      color: 'red',
      description: 'Failed to update asset selection. Please try again.',
      icon: 'i-heroicons-exclamation-triangle-20-solid',
    })
  } finally {
    isUpdateAssetPreferencesLoading.value = false
  }
}

watch(
  annual,
  (newVal) => {
    modalFormState.period = newVal ? 'annual' : 'monthly'
  },
  {
    immediate: true,
  },
)

onMounted(async () => {
  if (route.query.checkout === 'canceled') {
    toast.add({
      title: 'Canceled',
      color: 'red',
      description: 'You just canceled your checkout.',
      icon: 'i-heroicons-exclamation-triangle-20-solid',
    })
  }

  await Promise.all([
    refreshCurrentSubscription(),
    refreshAssetPreferences(),
    refreshBillingHistory(),
    refreshPricing(),
  ])
})

useHead({
  title: 'Billing',
})
</script>

<template>
  <div class="flex w-full flex-col">
    <h2 class="mb-2 text-2xl font-medium text-black dark:text-white">Billing</h2>
    <p class="text-neutrals-800 dark:text-neutrals-50 text-sm">Manage your plan and billing history here.</p>

    <div class="my-5 flex w-full items-center justify-end gap-3">
      <span
        class="text-neutrals-800 dark:text-neutrals-50 text-base"
        :class="{ 'font-semibold': !annual }"
      >
        Monthly billing
      </span>
      <UToggle v-model="annual" />
      <div class="flex items-center gap-3">
        <span
          class="text-neutrals-800 dark:text-neutrals-50 text-base"
          :class="{ 'font-semibold': annual }"
        >
          Annual billing
        </span>
        <span class="bg-accent-green-200 text-accent-green-600 rounded-full px-2.5 py-1 text-base"> Save 20% </span>
      </div>
    </div>

    <div class="scrollable overflow-x-auto p-0.5">
      <USkeleton
        v-if="status === 'pending'"
        class="mb-8 h-[35px] w-[280px]"
      />
      <USkeleton
        v-if="status === 'pending'"
        class="mb-6 h-[35px] w-[180px]"
      />
      <USkeleton
        v-if="status === 'pending'"
        class="mb-4 h-[20px] w-[280px]"
      />
      <USkeleton
        v-if="status === 'pending'"
        class="mb-4 h-[20px] w-[280px]"
      />
      <USkeleton
        v-if="status === 'pending'"
        class="mb-4 h-[20px] w-[280px]"
      />
      <USkeleton
        v-if="status === 'pending'"
        class="mb-4 h-[20px] w-[280px]"
      />
      <section
        class="flex flex-wrap gap-4 lg:flex-nowrap"
        v-else
      >
        <div
          v-for="pricing in data"
          :key="pricing.id"
          class="flex-[1_0_300px]"
        >
          <PricingCard
            :data="pricing"
            :is-annual="annual"
            :is-custom="pricing.name.toUpperCase() === 'ENTERPRISE'"
            :current-subscription="currentSubscription"
            :is-subscribing="isSubscribing"
            @on-subscribe="onSubscribe"
          />
        </div>
      </section>
    </div>

    <section
      v-if="currentSubscription !== null"
      class="mt-10 flex flex-col"
    >
      <h2 class="mb-4 text-2xl font-medium text-black dark:text-white">Manage Your Subscription</h2>
      <UCard>
        <div>
          <h3 class="text-neutrals-800 mb-2 text-xl font-medium dark:text-white">
            Current Plan: {{ currentSubscription?.plan_name }} ({{
              currentSubscription.raw_data?.is_annual ? 'Annual' : 'Monthly'
            }})
          </h3>
          <p class="text-neutrals-800 text-sm dark:text-white">Your next billing date is {{ formattedExpiredDate }}</p>
        </div>

        <div
          v-if="assetPreferences"
          class="mt-6 space-y-2"
        >
          <h3 class="text-neutrals-800 text-xl font-medium dark:text-white">Your Selected Cryptocurrencies</h3>
          <p class="text-neutrals-800 text-sm dark:text-white">You have access to data for these cryptocurrencies:</p>

          <div class="flex max-h-96 flex-wrap items-center gap-3 overflow-y-auto">
            <UBadge
              v-for="asset in assetPreferences"
              :key="asset.symbol"
              color="base-primary"
              variant="asset"
            >
              {{ asset.name }} ({{ asset.symbol }})
            </UBadge>
          </div>
        </div>

        <div
          v-if="currentSubscription.future_preferences"
          class="mt-6 space-y-2"
        >
          <p class="text-brand-400 text-sm">Pending changes (effective {{ formattedExpiredDate }}):</p>

          <div class="flex max-h-96 flex-wrap items-center gap-3 overflow-y-auto">
            <UBadge
              v-for="asset in currentSubscription.future_preferences"
              :key="asset.symbol"
              color="brand"
              variant="asset"
            >
              {{ asset.name }} ({{ asset.symbol }})
            </UBadge>
          </div>
        </div>

        <UButton
          label="Update Asset Selection"
          color="midnight-darker"
          variant="solid"
          size="md"
          class="mt-6"
          block
          :loading="isUpdateAssetPreferencesLoading"
          :disabled="isUpdateAssetPreferencesLoading"
          @click="showAssetSelectorModal = true"
        />

        <UButton
          label="Manage Subscription"
          color="base-primary"
          size="md"
          class="mt-4"
          block
          @click="onManageSubscription"
        />
      </UCard>
    </section>

    <section
      class="mt-10"
      v-if="statusBillingHistory === 'success'"
    >
      <h2 class="mb-4 text-2xl font-medium text-black dark:text-white">Billing History</h2>
      <UCard>
        <div
          v-if="(billingHistory?.length || 0) === 0"
          class="flex flex-col items-center justify-center"
        >
          <UIcon
            name="i-heroicons-information-circle"
            class="text-neutrals-800 mb-3 h-10 w-10 dark:text-white"
          />
          <h3 class="text-neutrals-800 mb-1.5 text-center text-xl font-medium dark:text-white">No Billing History</h3>
          <p class="text-neutrals-800 text-center text-sm dark:text-white">
            Your billing history will appear here once you start using paid services or make any transactions.
          </p>
        </div>
        <!-- Table -->
        <UTable
          v-else
          :columns="BILLING_HISTORY_TABLE"
          :rows="billingHistory!"
        >
          <template #index-data="{ row }">
            {{ (billingHistory?.findIndex((sub) => sub.id === row.id) ?? 0) + 1 }}
          </template>
          <template #description-data="{ row }">
            {{ row.description }}
          </template>
          <template #amount-data="{ row }"> ${{ row.amount }}</template>
          <template #date-data="{ row }">
            {{ format(new Date(row.date), 'yyyy-MM-dd') }}
          </template>
          <template #action-data="{ row }">
            <UButton
              variant="ghost"
              color="midnight"
              size="sm"
              label="Download Invoice"
              icon="i-heroicons-arrow-down-tray"
              :to="row.invoice_url"
            />
          </template>
        </UTable>
      </UCard>
    </section>

    <ClientOnly>
      <CheckoutModal
        v-model="showModalSubscribe"
        v-model:modal-form-state="modalFormState"
        v-model:annual="annual"
        :selected-plan="selectedPlan || currentSubscription?.plan"
        :is-updating-assets="!!currentSubscription?.plan"
        :selected-coins="_selectedCoins"
        :is-subscribing="isSubscribing"
        @on-checkout="onCheckout"
      />

      <PlanAssetSelector
        v-if="currentSubscription !== null"
        v-model="showAssetSelectorModal"
        :plan-id="Number(currentSubscription.plan_id)!"
        :max-selectable-coins="Number(currentSubscription.features.max_assets)"
        :plan-expiry-date="formattedExpiredDate"
        @on-subscribe="onUpdateAssetPreferences"
      />
    </ClientOnly>
  </div>
</template>
