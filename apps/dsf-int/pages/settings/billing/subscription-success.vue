<script setup lang="ts">
import confetti from 'canvas-confetti'

// Assets
import { LottieSuccessAnimation } from '~/assets/lottie'

onMounted(() => {
  confetti({
    particleCount: 100,
    spread: 70,
    origin: {
      x: 0.61,
      y: 0.6,
    },
  })
})

useHead({
  title: 'Subscription Success',
})
</script>

<template>
  <ClientOnly>
    <template #fallback>
      <div class="flex w-full flex-col items-center justify-center">
        <USkeleton class="mb-5 h-12 w-12 rounded-full" />
        <USkeleton class="mb-1 h-6 w-[500px] rounded-md" />
        <USkeleton class="mb- h-6 w-[250px] rounded-md" />
      </div>
    </template>

    <div class="flex w-full flex-col items-center justify-center">
      <LottieAnimation :animationData="LottieSuccessAnimation" :width="300" :height="300" :loop="false" />

      <h3 class="mt-1 text-center text-3xl font-semibold text-black dark:text-white">Subscription Success!</h3>
      <p class="dark:text-neutrals-300 mb-5 mt-2 text-center text-base text-gray-500">
        Your subscription has been successfully created.
      </p>
      <UButton
        to="/settings/billing"
        color="base-primary"
        label="Manage Billing"
        size="lg"
        variant="solid"
        trailing-icon="i-heroicons-arrow-long-right"
      />
    </div>
  </ClientOnly>
</template>
