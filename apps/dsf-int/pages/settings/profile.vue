<script setup lang="ts">
import { z } from 'zod'
import { useUserStore } from '~/stores/profile'
import { splitFullname } from '~/helper/string-helper'
import type { FormSubmitEvent } from '#ui/types'

useHead({
  title: 'Edit Profile',
})

const { $analytics, $authClient, $apiClient } = useNuxtApp()
const toast = useToast()

const userStore = useUserStore()
const { userProfile } = storeToRefs(userStore)

const loadingForm = ref<boolean>(false)
const loadingUpdate = ref<boolean>(false)

const schema = z.object({
  first_name: z.string().nonempty({ message: 'First name cannot be empty.' }),
  last_name: z.string().nonempty({ message: 'Last name cannot be empty.' }),
  email: z.string().email({ message: 'Invalid email format.' }),
})

type Schema = z.output<typeof schema>

const state = reactive<Schema>({
  first_name: '',
  last_name: '',
  email: '',
})

async function loadProfile() {
  loadingForm.value = true
  const { data } = await $apiClient.GET('/nodiens/api/v1/account/profile')

  const split = splitFullname(userProfile.value?.displayName ?? '')
  state.email = data?.payload.email ?? ''
  state.first_name = split.firstName
  state.last_name = split.lastName

  loadingForm.value = false
}

const doSubmit1 = async (event: FormSubmitEvent<Schema>) => {
  loadingUpdate.value = true

  try {
    const { error } = await $authClient.updateUser({
      name: [event.data.first_name, event.data.last_name].filter((str) => str !== '').join(' '),
    })

    if (error) {
      return toast.add({
        color: 'red',
        title: 'Update Failed',
        description: 'Something went wrong, please try again.',
        icon: 'i-heroicons-exclamation-triangle',
      })
    }

    const { data } = await $apiClient.GET('/nodiens/api/v1/account/profile')

    if (!data?.payload) {
      return toast.add({
        color: 'red',
        title: 'Update Failed',
        description: 'Something went wrong, please try again.',
        icon: 'i-heroicons-exclamation-triangle',
      })
    }

    userStore.setProfile(data?.payload)
    return toast.add({
      color: 'green',
      title: 'Update Success',
      description: 'Your user profile has updated',
      icon: 'i-heroicons-check-circle',
    })
  } catch (e) {
    return toast.add({
      color: 'red',
      title: 'Update Failed',
      description: 'Something went wrong, please try again.',
      icon: 'i-heroicons-exclamation-triangle',
    })
  } finally {
    loadingUpdate.value = false
  }
}

onMounted(() => {
  loadProfile()

  // Track anayltic page
  $analytics.page()
})
</script>

<template>
  <div class="w-full">
    <h2 class="mb-5 text-2xl font-medium text-black dark:text-white">Edit Profile</h2>

    <UForm
      :schema="schema"
      :state="state"
      class="flex flex-col gap-y-4"
      @submit.prevent="doSubmit1"
    >
      <UFormGroup
        label="First Name"
        name="first_name"
        size="lg"
        required
      >
        <USkeleton
          v-if="loadingForm"
          class="h-10 w-full"
        />
        <UInput
          v-else
          v-model="state.first_name"
          placeholder="Your First Name"
          :loading="loadingUpdate"
        />
      </UFormGroup>
      <UFormGroup
        label="Last Name"
        name="last_name"
        size="lg"
        required
      >
        <USkeleton
          v-if="loadingForm"
          class="h-10 w-full"
        />
        <UInput
          v-else
          v-model="state.last_name"
          placeholder="Your Last Name"
          :loading="loadingUpdate"
        />
      </UFormGroup>
      <UFormGroup
        label="Email Address"
        name="email"
        size="lg"
        required
      >
        <USkeleton
          v-if="loadingForm"
          class="h-10 w-full"
        />
        <UInput
          v-else
          v-model="state.email"
          placeholder="Your Email Address"
          type="email"
          disabled
        />
      </UFormGroup>
      <UButton
        label="Save"
        color="base-primary"
        type="submit"
        size="lg"
        block
        :loading="loadingUpdate"
      />
    </UForm>
  </div>
</template>
