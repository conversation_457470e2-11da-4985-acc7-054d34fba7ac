<script setup lang="ts">
import { z } from 'zod'
import { checkPasswordRules } from '~/helper/string-helper'
import { usePasswordChecker } from '~/components/molecule/PasswordChecker/usePasswordChecker'
import type { TRPCError } from '@trpc/server'
import type { FormSubmitEvent } from '#ui/types'

useHead({
  title: 'Edit Password',
})

const { $analytics, $authClient } = useNuxtApp()
const toast = useToast()
const isUpdating = ref(false)
const { switchView, view } = usePasswordChecker()

const schema = z
  .object({
    current_password: z.string().nonempty({ message: 'Current password cannot be empty.' }),
    new_password: checkPasswordRules,
    confirm_new_password: z.string().nonempty({ message: 'Confirm password cannot be empty.' }),
  })
  .refine((data) => data.confirm_new_password === data.new_password, {
    path: ['confirm_new_password'],
    message: "New password doesn't match",
  })

type Schema = z.output<typeof schema>

const state = reactive<Schema>({
  current_password: '',
  new_password: '',
  confirm_new_password: '',
})

const resetForm = () => {
  state.current_password = ''
  state.new_password = ''
  state.confirm_new_password = ''
}

const onSubmit = async (event: FormSubmitEvent<Schema>) => {
  const { current_password, confirm_new_password } = event.data
  isUpdating.value = true

  await $authClient
    .changePassword({
      currentPassword: current_password,
      newPassword: confirm_new_password,
    })
    .then(() => {
      toast.add({
        title: 'Update Password Success',
        description: 'Your password has been changed.',
        icon: 'i-heroicons-check-circle',
        color: 'green',
      })
      resetForm()
    })
    .catch((err) => {
      const _err = err as TRPCError
      toast.add({
        title: 'Update Password Failed',
        description: _err.message,
        icon: 'i-heroicons-exclamation-triangle',
        color: 'red',
      })
    })
    .finally(() => {
      isUpdating.value = false
    })
}

onMounted(() => {
  // Track anayltic page
  $analytics.page()
})
</script>

<template>
  <div class="w-full">
    <h2 class="mb-5 text-2xl font-medium text-black dark:text-white">Edit Password</h2>

    <UForm
      :schema="schema"
      :state="state"
      class="flex flex-col gap-y-4"
      @submit.prevent="onSubmit"
    >
      <UFormGroup
        label="Current Password"
        name="current_password"
        size="lg"
        required
      >
        <InputPassword
          v-model="state.current_password"
          placeholder="Current Password"
        />
      </UFormGroup>
      <UFormGroup
        label="New Password"
        name="new_password"
        size="lg"
        required
      >
        <InputPassword
          v-model="state.new_password"
          placeholder="New Password"
          @focus="switchView(false)"
          @blur="switchView(true)"
        />
      </UFormGroup>
      <UFormGroup
        label="Confirm New Password"
        name="confirm_new_password"
        size="lg"
        required
      >
        <InputPassword
          v-model="state.confirm_new_password"
          placeholder="Confirm New Password"
          @focus="switchView(false)"
          @blur="switchView(true)"
        />
      </UFormGroup>

      <PasswordChecker
        v-if="view"
        :password="state.new_password"
      />

      <UButton
        label="Save"
        type="submit"
        color="base-primary"
        size="lg"
        block
        :disabled="isUpdating"
        :loading="isUpdating"
      />
    </UForm>
  </div>
</template>
