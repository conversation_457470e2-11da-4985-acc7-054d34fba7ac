<script setup lang="ts">
import { z } from 'zod'
import type { FormSubmitEvent } from '#ui/types'

// Stores
import { useSessionStore } from '~/stores/session'
import { useUserStore } from '~/stores/profile'

const runtimeConfig = useRuntimeConfig()
const toast = useToast()
const userStore = useUserStore()
const { userProfile } = storeToRefs(userStore)

useHead({
  title: 'Contact Us',
})

const schema = z.object({
  type: z.enum(['business', 'media', 'other'], {
    errorMap: () => ({ message: 'Please select a valid topic' }),
  }),
  first_name: z.string().nonempty('First name cannot be empty'),
  last_name: z.string().nonempty('Last name cannot be empty'),
  email: z.string().nonempty('Email cannot be empty.').email('Email must be a valid address'),
  company: z.string().optional(),
  message: z.string().nonempty('Message cannot be empty'),
  is_agree: z.boolean().refine((value) => value === true, {
    message: 'You must accept the Terms and Privacy Policy before proceeding',
  }),
})

type Schema = z.output<typeof schema>

const state = reactive<Schema>({
  type: 'business',
  first_name: '',
  last_name: '',
  email: '',
  company: '',
  message: '',
  is_agree: false,
})

const isLoading = ref(false)
const sessionStore = useSessionStore()
const isLogged = sessionStore.isLogged

const resetForm = () => {
  state.type = 'business'
  state.message = ''
  state.is_agree = false
}

const onSubmit = async (event: FormSubmitEvent<Schema>) => {
  isLoading.value = true

  try {
    const formData = new FormData()
    formData.append('first_name', event.data.first_name.trim())
    formData.append('last_name', event.data.last_name.trim())
    formData.append('email', event.data.email.trim())
    event.data.company && formData.append('company', event.data.company.trim())
    formData.append('type', event.data.type)
    formData.append('message', event.data.message)

    const res = await $fetch<{
      message: string
    }>(`${runtimeConfig.public.supabase.url}/functions/v1/contact-us`, {
      method: 'POST',
      mode: 'cors',
      body: formData,
    })

    toast.add({
      title: 'Submission Successful!',
      description: res.message,
      icon: 'i-heroicons-check-circle-20-solid',
    })

    resetForm()
  } catch (error: any) {
    toast.add({
      title: 'Submission Failed',
      description: error.data?.message ? error.data?.message : 'Something went wrong. Please try again later.',
      icon: 'i-heroicons-exclamation-triangle-20-solid',
    })
  } finally {
    isLoading.value = false
  }
}

watch(
  userProfile,
  (newProfile) => {
    if (newProfile) {
      state.first_name = (newProfile?.displayName ?? '').split(' ')[0] || ''
      state.last_name = (newProfile?.displayName ?? '').split(' ')[1] ?? ''
      state.email = newProfile?.email ?? ''
      state.company = newProfile?.company ?? ''
    }
  },
  {
    immediate: true,
    deep: true,
  },
)
</script>

<template>
  <div>
    <HeaderPage
      page-name="contact-us"
      center
      hide-navigation
    />

    <BodyAuxiliary>
      <UForm
        :schema="schema"
        :state="state"
        @submit.prevent="onSubmit"
      >
        <UFormGroup
          label="Choose a Topic"
          name="type"
          class="mb-4"
        >
          <USelectMenu
            v-model="state.type"
            name="type"
            placeholder="Choose a topic"
            option-attribute="name"
            value-attribute="value"
            :options="[
              { value: 'business', name: 'Business Enquiry' },
              { value: 'media', name: 'Media Enquiry' },
              { value: 'other', name: 'Other Enquiry' },
            ]"
            variant="none"
            :disabled="isLoading"
            :ui="{
              rounded: 'rounded-none',
              base: 'cursor-pointer hover:cursor-pointer dark:bg-neutrals-500 bg-neutrals-200 rounded-none',
              placeholder: 'dark:text-white/50 text-neutrals-500',
            }"
            :ui-menu="{
              base: '!mb-0 !-mt-2',
              rounded: 'rounded-none',
              ring: 'ring-0',
              background: 'bg-neutrals-200 dark:bg-neutrals-500',
              option: {
                base: 'hover:bg-neutrals-200 cursor-pointer hover:cursor-pointer',
                padding: 'px-3',
              },
            }"
          />
        </UFormGroup>
        <div class="mb-4 grid grid-cols-1 gap-4 md:grid-cols-2">
          <UFormGroup
            label="First Name"
            name="first_name"
          >
            <UInput
              v-model="state.first_name"
              name="first_name"
              :disabled="isLoading || isLogged"
            />
          </UFormGroup>
          <UFormGroup
            label="Last Name"
            name="last_name"
          >
            <UInput
              v-model="state.last_name"
              name="last_name"
              :disabled="isLoading || isLogged"
            />
          </UFormGroup>
        </div>
        <div class="mb-4 grid grid-cols-1 gap-4 md:grid-cols-2">
          <UFormGroup
            label="Email Address"
            name="email"
            class="mb-4"
          >
            <UInput
              v-model="state.email"
              name="email"
              type="email"
              :disabled="isLoading || isLogged"
            />
          </UFormGroup>
          <UFormGroup
            label="Company (optional)"
            name="company"
            class="mb-4"
          >
            <UInput
              v-model="state.company"
              name="company"
              type="text"
              :disabled="isLoading"
            />
          </UFormGroup>
        </div>
        <UFormGroup
          label="Message"
          name="message"
          class="mb-6"
        >
          <UTextarea
            v-model="state.message"
            name="message"
            :rows="5"
            placeholder="Write Something..."
            :disabled="isLoading"
          />
        </UFormGroup>
        <UFormGroup
          class="mb-6"
          name="is_agree"
        >
          <TermsAndPrivacy v-model="state.is_agree" />
        </UFormGroup>
        <div class="flex flex-col items-center text-center">
          <UButton
            color="base-primary"
            size="lg"
            label="Send Message"
            class="mb-3 px-12"
            type="submit"
            :loading="isLoading"
            block
          />
        </div>
      </UForm>
    </BodyAuxiliary>
  </div>
</template>
