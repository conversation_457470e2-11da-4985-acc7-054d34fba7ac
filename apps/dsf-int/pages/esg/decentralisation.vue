<script setup lang="ts">
import { STABLECOIN_LINE_STYLE } from '~/constants/stablecoin'
import { format } from 'date-fns'

// Types
import {
  dexCompareKeyMetrics,
  dexMetricColor,
  dexMetricGroups,
  dexMetrics,
  getDexMetricObjKey,
  TREE_CHART_METRICS,
} from '~/constants/metrics/decentralisation-indices'
import { useRadarLineChart } from '~/composables/useRadarLineChart'
import type { v2AssetCoin } from '~/server/trpc/trpc'

provide('coinApi', '/nodiens/api/v1/esg/decentralisation/asset')
provide('coinFilterLabel', 'project')
provide('queryKey', 'project')

const { $apiClient } = useNuxtApp()
const config = useRuntimeConfig()
const route = useRoute()
const { onBack } = useBackHandler()

const isShareModalOpen = ref(false)

// TODO: Remove once API has properly handled data issues.
const showNoLineData = ref(false)

provide('showNoLineData', showNoLineData)

const fullPath = computed(() => {
  const queryParams = new URLSearchParams(route.query as Record<string, string>)
  return `${config.public.appUrl}/dex?${queryParams}`
})

const { selectedLabelIndex, stablecoinLinechartKey, coin1Filter, coin2Filter, coin3Filter, lineChartTitle } =
  useRadarLineChart()

await useAsyncData(async () => {
  const { data } = await $apiClient.GET('/nodiens/api/v1/esg/decentralisation/asset')

  const assets = data?.payload?.data ?? []

  if (!assets.length) {
    return
  }

  if (!route.query.project1) {
    coin1Filter.value.coin = assets[0] as unknown as v2AssetCoin
    coin2Filter.value.coin = assets[1] as unknown as v2AssetCoin
    coin3Filter.value.coin = assets[2] as unknown as v2AssetCoin
  } else {
    coin1Filter.value.coin =
      (assets.find((asset) => asset.slug === route.query.project1) as unknown as v2AssetCoin) ?? undefined
    coin2Filter.value.coin =
      (assets.find((asset) => asset.slug === route.query.project2) as unknown as v2AssetCoin) ?? undefined
    coin3Filter.value.coin =
      (assets.find((asset) => asset.slug === route.query.project3) as unknown as v2AssetCoin) ?? undefined
  }

  // TODO: This causes a slight delay on changing the selected label in radar chart. Must move somewhere else to remove the delay.
  if (route.query.metric) {
    const selectedMetrics = dexCompareKeyMetrics.findIndex((d: any) => d === route.query.metric)
    selectedLabelIndex.value = selectedMetrics
    lineChartTitle.value = getDexMetricObjKey('label', selectedMetrics) as string
  } else {
    selectedLabelIndex.value = 0
    lineChartTitle.value = getDexMetricObjKey('label', 0) as string
  }
})

const coinsQuery = computed(() => {
  const coins: string[] = []

  if (route.query.project1) {
    coins.push(route.query.project1 as string)
  }
  if (route.query.project2) {
    coins.push(route.query.project2 as string)
  }
  if (route.query.project3) {
    coins.push(route.query.project3 as string)
  }

  return coins
})

const { data: lineChartData, status: lineChartStatus } = await useLazyAsyncData(
  'getLineChart',
  async () => {
    try {
      if (!coinsQuery.value.length) {
        return null
      }

      const metric = getDexMetricObjKey('compareUnit', selectedLabelIndex.value) as any

      if (TREE_CHART_METRICS.includes(metric)) {
        return null
      }

      const { data } = await $apiClient.GET('/nodiens/api/v1/esg/decentralisation/history-comparison/{metric}', {
        params: {
          path: { metric },
          query: { slugs: coinsQuery.value },
        },
      })

      stablecoinLinechartKey.value += 1
      showNoLineData.value = false
      return data?.payload
    } catch (e) {
      showNoLineData.value = true
      console.error(e)
      return null
    }
  },
  {
    watch: [coinsQuery, selectedLabelIndex],
    server: false,
    transform: (payload) => {
      if (!payload?.comparison) return []

      return payload.comparison.map((item) => {
        const timeseries = item.entries

        return {
          name: item.name,
          slug: item.slug,
          symbol: item.symbol,
          firstCalculation: payload?.startDate ? format(payload.startDate, 'yyyy-MM-dd') : '',
          latestCalculation: payload?.endDate ? format(payload.endDate, 'yyyy-MM-dd') : '',
          timeseries,
        }
      })
    },
  },
)

const { data: spiderData, status: spiderStatus } = await useLazyAsyncData(
  'getSpiderData',
  async () => {
    if (!coinsQuery.value.length) {
      return null
    }

    const { data } = await $apiClient.GET('/nodiens/api/v1/esg/decentralisation/daily-comparison', {
      params: {
        query: { slugs: coinsQuery.value },
      },
    })

    return data?.payload
  },
  {
    watch: [coinsQuery],
    server: false,
  },
)

const radarPending = computed(() => spiderStatus.value === 'pending')

const linePending = computed(() => lineChartStatus.value === 'pending')

const radarDataset = computed(() => {
  return {
    labels: dexMetrics.map((label) => {
      const parts = label.split(' ')
      const firstPart = parts.slice(0, 2).join(' ')
      const secondPart = parts.slice(2).join(' ')
      return [firstPart, secondPart]
    }),
    datasets: coinsQuery.value.map((coin, coinIndex) => {
      const coinColor = []

      if (route.query.project1) {
        coinColor.push(STABLECOIN_LINE_STYLE['coin-1'].color)
      }
      if (route.query.project2) {
        coinColor.push(STABLECOIN_LINE_STYLE['coin-2'].color)
      }
      if (route.query.project3) {
        coinColor.push(STABLECOIN_LINE_STYLE['coin-3'].color)
      }

      const coinData = spiderData.value?.find((data) => data.slug === coin)

      return {
        label: coin,
        borderColor: coinColor[coinIndex],
        fill: false,
        data: [
          Number(coinData?.ipAuthDistrGini ?? 0),
          Number(coinData?.ipParticipantDivGini ?? 0),
          Number(coinData?.ipAuthorInflConcHHI ?? 0),
          // @ts-ignore
          Number(coinData?.ipGovOrdinal ?? 0), // red limit - metric for tree chart
          Number(coinData?.rcpDevDistrGini ?? 0),
          Number(coinData?.rcpParticipantDivShannon ?? 0),
          Number(coinData?.rcpDevInflConcHHI ?? 0),
          Number(coinData?.rcdRevrPowerConcHHI ?? 0), // green limit
          Number(coinData?.consensusPowerNeGini ?? 0),
          Number(coinData?.consensusPowerNeTheil ?? 0),
          Number(coinData?.consensusPowerConcNakamoto ?? 0),
          Number(coinData?.consensusPowerConcHHI ?? 0), // purple limit
          // @ts-ignore
          Number(coinData?.coinDistrOrdinal ?? 0), // metric for tree chart
        ],
        borderWidth: 1.5,
        hidden: false,
      }
    }),
  }
})

const titlePage = computed(() => {
  const activeCoinFilter = []
  if (coin1Filter.value.coin) {
    activeCoinFilter.push(coin1Filter.value.coin.name)
  }
  if (coin2Filter.value.coin) {
    activeCoinFilter.push(coin2Filter.value.coin.name)
  }
  if (coin3Filter.value.coin) {
    activeCoinFilter.push(coin3Filter.value.coin.name)
  }
  if (activeCoinFilter.length === 1) {
    return activeCoinFilter[0]
  }
  if (activeCoinFilter.length === 2) {
    return `${activeCoinFilter[0]} and ${activeCoinFilter[1]}`
  }
  if (activeCoinFilter.length === 3 && activeCoinFilter.every((x) => !!x)) {
    return `${activeCoinFilter[0]}, ${activeCoinFilter[1]}, and ${activeCoinFilter[2]}`
  }
  return ''
})
</script>
<template>
  <div>
    <Head>
      <Title> {{ titlePage }} Decentralization Indexes</Title>
    </Head>
    <HeaderPage
      page-name="decentralisation-indices"
      faq-slug="decentralisation-indices"
    />

    <div class="md:px-5 md:py-6">
      <UButton
        class="hidden md:inline-flex"
        variant="outline"
        color="black"
        size="md"
        icon="i-heroicons-arrow-left"
        label="Back"
        @click="onBack"
      />

      <RadarLineChart
        :radar-dataset="radarDataset"
        :radar-loading="radarPending"
        :line-loading="linePending"
        :line-chart-data="lineChartData"
        :metric-color="dexMetricColor"
        :metric-obj-key="getDexMetricObjKey"
        :metric-groups="dexMetricGroups"
        hide-value-unit
        is-decentralisation
      />
    </div>

    <LazyShareModal
      v-if="isShareModalOpen"
      :shared-text="fullPath"
      @on-close="isShareModalOpen = false"
    />
  </div>
</template>
