<script setup lang="ts">
import { COIN_SIZE_LIST } from '~/constants/options'
import { STABLECOIN_LINE_STYLE } from '~/constants/stablecoin'
import {
  financialCompareKeyMetrics,
  financialMetricColor,
  financialMetrics,
  getFinancialMetricObjKey,
  financialMetricGroups,
} from '~/constants/metrics/financial'

// Types
import type { v2AssetCoin, v2FinanceChartQueryInputSizesEnum } from '~/server/trpc/trpc'

// Providers
provide('queryKey', 'asset')
provide('coinApi', '/nodiens/api/v1/financial/asset')
provide('coinFilterLabel', 'coin')

const { $client, $apiClient } = useNuxtApp()
const config = useRuntimeConfig()
const route = useRoute()
const { onBack } = useBackHandler()

const isShareModalOpen = ref(false)

const {
  radarData,
  selectedLabelIndex,
  stablecoinLinechart<PERSON>ey,
  coin1Filter,
  coin2Filter,
  coin3Filter,
  selectedSize,
  lineChartTitle,
} = useRadarLineChart()

// TODO: Remove once API has properly handled data issues.
const showNoLineData = ref(false)

provide('showNoLineData', showNoLineData)

await useAsyncData(async () => {
  const { data } = await $apiClient.GET('/nodiens/api/v1/esg/decentralisation/asset')
  const assets = data?.payload?.data ?? []

  if (!route.query.asset1) {
    coin1Filter.value.coin = assets[0] as v2AssetCoin
    coin2Filter.value.coin = assets[1] as v2AssetCoin
    coin3Filter.value.coin = assets[2] as v2AssetCoin
  } else {
    coin1Filter.value.coin = (assets.find((asset) => asset.slug === route.query.asset1) as v2AssetCoin) ?? undefined
    coin2Filter.value.coin = (assets.find((asset) => asset.slug === route.query.asset2) as v2AssetCoin) ?? undefined
    coin3Filter.value.coin = (assets.find((asset) => asset.slug === route.query.asset3) as v2AssetCoin) ?? undefined
  }

  if (!route.query.size) {
    selectedSize.value = COIN_SIZE_LIST[0] as Size
  } else {
    const foundSize = COIN_SIZE_LIST.find((size) => size.id === route.query.size)
    selectedSize.value = foundSize ?? (COIN_SIZE_LIST[0] as Size)
  }

  // TODO: This causes a slight delay on changing the selected label in radar chart. Must move somewhere else to remove the delay.
  if (route.query.metric) {
    const selectedMetrics = financialCompareKeyMetrics.findIndex((d) => d === route.query.metric)
    selectedLabelIndex.value = selectedMetrics
    lineChartTitle.value = getFinancialMetricObjKey('label', selectedMetrics) as string
  } else {
    selectedLabelIndex.value = 0
    lineChartTitle.value = getFinancialMetricObjKey('label', 0) as string
  }
})

const fullPath = computed(() => {
  const queryParams = new URLSearchParams(route.query as Record<string, string>)
  return `${config.public.appUrl}/financial?${queryParams}`
})

const titlePage = computed(() => {
  const activeCoinFilter = []
  if (coin1Filter.value.coin) {
    activeCoinFilter.push(coin1Filter.value.coin.name)
  }
  if (coin2Filter.value.coin) {
    activeCoinFilter.push(coin2Filter.value.coin.name)
  }
  if (coin3Filter.value.coin) {
    activeCoinFilter.push(coin3Filter.value.coin.name)
  }
  if (activeCoinFilter.length === 1) {
    return activeCoinFilter[0]
  }
  if (activeCoinFilter.length === 2) {
    return `${activeCoinFilter[0]} and ${activeCoinFilter[1]}`
  }
  if (activeCoinFilter.length === 3 && activeCoinFilter.every((x) => !!x)) {
    return `${activeCoinFilter[0]}, ${activeCoinFilter[1]}, and ${activeCoinFilter[2]}`
  }
  return ''
})

const coinsQuery = computed(() => {
  const coins: string[] = []

  if (route.query.asset1) {
    coins.push(route.query.asset1 as string)
  }
  if (route.query.asset2) {
    coins.push(route.query.asset2 as string)
  }
  if (route.query.asset3) {
    coins.push(route.query.asset3 as string)
  }

  return coins
})

const {
  data: spiderData,
  status: radarStatus,
  refresh: refreshRadar,
} = await useLazyAsyncData(
  'getFinancialSpiderData',
  async () => {
    // if (route.query.metric) {
    //   selectedLabelIndex.value = financialCompareKeyMetrics.findIndex((d) => d === route.query.metric)
    // }

    if (coinsQuery.value.length > 0) {
      const data = await $client.v2.user.finance.chart.query({
        slugs: coinsQuery.value,
        sizes: route.query.size as v2FinanceChartQueryInputSizesEnum,
      })
      radarData.value = data
      return data
    }
    return null
  },
  {
    watch: [() => route.query.asset1, () => route.query.asset2, () => route.query.asset3, () => route.query.size],
    immediate: false,
    server: false,
  },
)

const {
  data: lineChartData,
  status: lineStatus,
  refresh: refreshLineChart,
} = await useLazyAsyncData(
  'getFinancialLineChart',
  async () => {
    try {
      const data = await $client.v2.user.finance.comparison.query({
        slugs: coinsQuery.value,
        compareUnit: !getFinancialMetricObjKey('multipleCompareUnit', selectedLabelIndex.value)
          ? (getFinancialMetricObjKey('compareUnit', selectedLabelIndex.value) as string)
          : (getFinancialMetricObjKey('label', selectedLabelIndex.value) as string).toLocaleLowerCase().includes('cex')
            ? (selectedSize.value?.compareSize.cex ?? '')
            : (selectedSize.value?.compareSize.dex ?? ''),
      })

      stablecoinLinechartKey.value += 1

      showNoLineData.value = false

      return data
    } catch (e) {
      showNoLineData.value = true
      console.error(e)
      return null
    }
  },
  {
    watch: [
      () => route.query.asset1,
      () => route.query.asset2,
      () => route.query.asset3,
      selectedLabelIndex,
      selectedSize,
    ],
    immediate: false,
    server: false,
  },
)

const lineChartPending = computed(() => lineStatus.value === 'pending')

const radarPending = computed(() => radarStatus.value === 'pending')

const radarDataset = computed(() => {
  return {
    labels: financialMetrics.map((label) => {
      const parts = label.split(' ')
      if (parts.length === 1) {
        return [parts[0]]
      }
      if (parts.length === 2) {
        return [parts[0], parts[1]]
      }
      const firstPart = parts.slice(0, 2).join(' ')
      const secondPart = parts.slice(2).join(' ')
      return [firstPart, secondPart]
    }),
    datasets: coinsQuery.value.map((coin, coinIndex) => {
      const coinColor = []

      if (route.query.asset1) {
        coinColor.push(STABLECOIN_LINE_STYLE['coin-1'].color)
      }
      if (route.query.asset2) {
        coinColor.push(STABLECOIN_LINE_STYLE['coin-2'].color)
      }
      if (route.query.asset3) {
        coinColor.push(STABLECOIN_LINE_STYLE['coin-3'].color)
      }

      const coinData = spiderData.value?.find((rawData) => rawData.slug === coin)

      return {
        label: coin,
        borderColor: coinColor[coinIndex],
        fill: true,
        rawData: [
          Number(coinData?.rawData?.marketCap ?? 0),
          Number(coinData?.rawData?.price ?? 0),
          Number(coinData?.rawData?.monthVolatility ?? 0),
          Number(coinData?.rawData?.depegDays ?? 0),
          Number(coinData?.rawData?.dayVolumeCex ?? 0),
          Number(coinData?.rawData?.cexPairs ?? 0),
          Number(coinData?.rawData?.cexLiqCost ?? 0),
          Number(coinData?.rawData?.dayVolumeDex ?? 0),
          Number(coinData?.rawData?.dexPairs ?? 0),
          Number(coinData?.rawData?.dexLiqCost ?? 0),
          Number(coinData?.rawData?.dexLiqConcentrate ?? 0),
        ],
        data: [
          Number(coinData?.metrics?.marketCap ?? 0),
          Number(coinData?.metrics?.price ?? 0),
          Number(coinData?.metrics?.monthVolatility ?? 0),
          Number(coinData?.metrics?.depegDays ?? 0),
          Number(coinData?.metrics?.dayVolumeCex ?? 0),
          Number(coinData?.metrics?.cexPairs ?? 0),
          Number(coinData?.metrics?.cexLiqCost ?? 0),
          Number(coinData?.metrics?.dayVolumeDex ?? 0),
          Number(coinData?.metrics?.dexPairs ?? 0),
          Number(coinData?.metrics?.dexLiqCost ?? 0),
          Number(coinData?.metrics?.dexLiqConcentrate ?? 0),
        ],
        borderWidth: 1.5,
        hidden: false,
      }
    }),
  }
})

onMounted(async () => {
  if (route.query.asset1 || route.query.asset2 || route.query.asset3) {
    await Promise.all([refreshRadar(), refreshLineChart()])
  }
})
</script>
<template>
  <div>
    <Head>
      <Title>{{ titlePage }} Financial Indexes</Title>
    </Head>
    <HeaderPage
      page-name="financial-indices"
      faq-slug="financial"
    />

    <div class="md:px-5 md:py-6">
      <UButton
        class="hidden md:inline-flex"
        variant="outline"
        color="black"
        size="md"
        icon="i-heroicons-arrow-left"
        label="Back"
        @click="onBack"
      />

      <RadarLineChart
        :radar-dataset="radarDataset"
        :radar-loading="radarPending"
        :line-loading="lineChartPending"
        :line-chart-data="lineChartData"
        :metric-color="financialMetricColor"
        :metric-obj-key="getFinancialMetricObjKey"
        :metric-groups="financialMetricGroups"
      >
        <template #legend>
          <div class="flex items-center gap-2">
            <div class="text-[#8B5CF6]">*</div>
            <p>This metric only applies to stablecoins.</p>
          </div>
        </template>
        <template #filters>
          <div class="flex flex-col gap-1">
            <div class="relative flex gap-1">
              <span class="text-neutrals-400 text-xs uppercase">size</span>

              <InformationPopover
                class="-mt-1"
                icon-class="!w-4 !h-4"
              >
                <TooltipContent>
                  <p>
                    Select the trade size of a crypto asset to calculate estimates for the liquidity cost. This is
                    reflected in the Marginal Cost of Immediacy (MCI) metric.
                  </p>
                </TooltipContent>
              </InformationPopover>
            </div>

            <USelectMenu
              v-model="selectedSize"
              :ui-menu="{
                container: 'min-w-[200px]',
              }"
              :popper="{
                placement: 'bottom-start',
              }"
              variant="solid"
              color="black"
              size="md"
              class="w-full md:min-w-[125px]"
              :options="COIN_SIZE_LIST"
            >
              <template #label>
                <span
                  v-if="selectedSize"
                  class="text-base"
                >
                  {{ selectedSize?.name }} {{ selectedSize?.symbol }}
                </span>
                <span
                  v-else
                  class="truncate text-nowrap text-base"
                  >Select
                </span>
              </template>

              <template #option="{ option: size }">
                <span class="text-neutrals-700 text-base dark:text-white">{{ size.name }}</span>
              </template>
            </USelectMenu>
          </div>
        </template>
      </RadarLineChart>
    </div>

    <LazyShareModal
      v-if="isShareModalOpen"
      :shared-text="fullPath"
      @on-close="isShareModalOpen = false"
    />
  </div>
</template>
