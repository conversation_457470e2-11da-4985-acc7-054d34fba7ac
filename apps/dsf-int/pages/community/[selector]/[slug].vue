<script setup lang="ts">
import jsPDF from 'jspdf'
import FileSaver from 'file-saver'
import domtoimage from 'dom-to-image'
import { format } from 'date-fns'
import { LineStyle, type Time } from 'lightweight-charts'

// Helpers
import { numericDisplay } from '~/helper/number-helper'
import { blobToBase64 } from '~/helper/utilities-helper'
import { titleCase } from '~/helper/string-helper'

// Constatns
import { COMMUNITY_INDICES_LEVEL_DESCRIPTIONS } from '~/constants/common-tooltips'
import { LEGEND_MOOD_TRUST, LEGEND_MT_COLOR } from '~/constants/legends'
import { HTTP_STATUS } from '~/constants/error'

// Types
import type { v2AssetList } from '~/server/trpc/trpc'
import type { AssetType } from '~/types'
import type { lineDataType } from '~/components/organism/DecentralisationLineChart/types'

interface CommunityParams {
  slug: string
  selector: 'mood' | 'trust'
}

interface Layer {
  id: string
  name: string
  selected: boolean
  type: string
  group?: string
}

const LAYERS = [
  {
    key: 'layer1',
    label: 'Level 1',
    defaultOpen: true,
  },
  {
    key: 'layer2',
    label: 'Level 2',
    defaultOpen: true,
  },
  {
    key: 'layer3',
    label: 'Level 3',
    defaultOpen: false,
  },
]

const config = useRuntimeConfig()
const { $colorMode, $apiClient } = useNuxtApp()
const route = useRoute()
const toast = useToast()

const { onBack } = useBackHandler()
const { isMobile } = useDeviceScreen()
const { isFeatureAllowed } = useFeature()
const {
  chart,
  zoomViews,
  lineStyleLayers,
  log,
  tools,
  renderChart,
  addLineSeries,
  destroyChart,
  renderBottomChart,
  setPriceScaleVisibility,
} = useLightChart()

const params = route.params as Partial<CommunityParams>

const darkMode = computed({
  get() {
    return $colorMode.value === 'dark'
  },
  set(value) {
    if (value) {
      $colorMode.preference = 'dark'
    } else {
      $colorMode.preference = 'light'
    }
  },
})

const chartRef = ref<HTMLElement>()
const exportElement = ref<HTMLElement>()
const chartBottomRef = ref<HTMLElement>()

const isIndexFilterBottomSheetOpen = ref(false)
const isShareModalOpen = ref(false)
const selectedAsset = ref<v2AssetList['data'][0]>()
const selectedLayer1 = ref<boolean>(true)
const selectedLayer2 = ref<string[]>([])
const selectedLayer3 = ref<string[]>([])
const filters = ref<Layer[]>([])

const timeSeriesData = ref<{ [key: string]: lineDataType }>({})
const printTime = ref<string>()

const fullPath = computed(() => `${config.public.appUrl}/${params.slug}/${params.selector}`)

const selectedFilters = computed(() => filters.value.filter((l) => l.selected === true))

const indexItems = computed(() => [
  [
    {
      label: 'Mood',
      to: `/community/mood/${params.slug}`,
    },
    {
      label: 'Trust',
      to: `/community/trust/${params.slug}`,
    },
  ],
])

const { data, status, error } = useLazyAsyncData(
  `${params.selector}.${params.slug}`,
  async () => {
    const endpoint =
      params.selector === 'mood' ? '/nodiens/api/v1/mood-index/{slug}' : '/nodiens/api/v1/trust-index/{slug}'
    const { response, data, error } = await $apiClient.GET(endpoint, {
      params: {
        path: {
          slug: params.slug!,
        },
        query: {
          layer1: selectedLayer1.value,
          ...(selectedLayer2.value.length && { layer2: selectedLayer2.value as any[] }),
          ...(selectedLayer3.value.length && { layer3: selectedLayer3.value as any[] }),
        },
      },
    })

    if (response.status === HTTP_STATUS.INTERNAL_SERVER_ERROR) {
      throw error
    }

    return data?.payload ?? null
  },
  {
    watch: [selectedLayer1, selectedLayer2, selectedLayer3],
    server: false,
  },
)

const isLoading = computed(() => status.value === 'pending')

const isDataEmpty = computed(() => {
  return (
    !data.value ||
    [data.value.layer1, data.value.layer2, data.value.layer3].every((layer) =>
      layer.every((arr) => Array.isArray(arr) && !arr.length),
    )
  )
})

const pageTitle = computed(() => {
  const { selector } = params
  const assetName = data.value?.name

  if (!assetName || !selector) {
    return 'Community Indices'
  }

  const selectorTitle = LEGEND_MOOD_TRUST.find((l) => l.label.toLowerCase().includes(selector))
  return `${assetName} ${selectorTitle?.label}`
})

const isLevelAllowed = computed(() => {
  const selector = params.selector
  const selected = filters.value.filter((x) => x.selected)
  const levelMap = {
    layer1: 'level_1',
    layer2: 'level_2',
    layer3: 'level_3',
  } as Record<string, string>
  return selected.every((item) => !!isFeatureAllowed(`sentiment_metrics.${selector}_index_${levelMap[item.type]}`))
})

const initializeChart = () => {
  // If the chart already exists, destroy it first for clean initialization
  if (chart.value) {
    destroyChart()
    timeSeriesData.value = {}
  }

  // Render a new chart
  renderChart(
    chartRef,
    {
      crosshair: {
        horzLine: {
          visible: false,
          labelVisible: false,
        },
        vertLine: {
          labelVisible: false,
        },
      },
      grid: {
        vertLines: {
          visible: false,
        },
        horzLines: {
          style: LineStyle.Dashed,
          color: '#7C7C7C',
        },
      },
      rightPriceScale: {
        visible: isMobile.value ? false : true,
        borderVisible: false,
        entireTextOnly: true,
        scaleMargins: {
          bottom: 0.05,
        },
      },
      timeScale: {
        visible: false,
        fixRightEdge: true,
        rightOffset: 5,
        lockVisibleTimeRangeOnResize: true,
      },
      handleScale: {
        pinch: false,
        mouseWheel: false,
        axisPressedMouseMove: false,
      },
      handleScroll: {
        mouseWheel: false,
        vertTouchDrag: false,
        horzTouchDrag: false,
        pressedMouseMove: false,
      },
      ...(data.value?.startDate && { startCalculation: format(data.value.startDate, 'yyyy-MM-dd') }),
      ...(data.value?.endDate && { latestCalculation: format(data.value.endDate, 'yyyy-MM-dd') }),
    },
    darkMode.value,
  )

  if (!chart.value || !data.value) {
    console.error('Chart or Data not ready')
    return
  }

  chart.value.applyOptions({
    localization: {
      priceFormatter: (price: any) => numericDisplay(price),
    },
    rightPriceScale: {
      autoScale: true,
      alignLabels: true,
    },
    leftPriceScale: {
      autoScale: true,
      alignLabels: true,
    },
  })

  const layers = { layer1: data.value?.layer1, layer2: data.value?.layer2, layer3: data.value?.layer3 }

  if (layers) {
    type Layer = keyof typeof layers
    timeSeriesData.value = {}

    // Add all selected filter data to the chart
    filters.value.forEach((filter) => {
      const key = `${filter.type.toUpperCase()}-${filter.name}`
      const type = filter.type as Layer
      if (filter.selected === true) {
        const instance = addLineSeries(filter.name, {
          priceScaleId: 'right',
          priceLineVisible: false,
          lineWidth:
            lineStyleLayers[params.selector!] && lineStyleLayers[params.selector!]?.[type]
              ? lineStyleLayers[params.selector!]?.[type]?.width
              : 1,
          color: LEGEND_MT_COLOR[`${type}-${filter.group?.toLowerCase() ?? filter.name.toLowerCase()}`],
          title: `${filter.type.replace('layer', 'l').toUpperCase()}-${filter.name}`,
        })

        if (instance) {
          let chartData = [] as { value: number; time: string }[]

          if (type === 'layer1' && layers['layer1']) {
            chartData = layers['layer1'].map((d) => ({
              value: d[1],
              time: format(d[0], 'yyyy-MM-dd'),
            }))
          } else if (type === 'layer2' && layers['layer2']) {
            const data = layers['layer2'].find((d) => d.snsPlatform === filter.id)
            if (data) {
              chartData = data.history.map((d) => ({
                value: d[1],
                time: format(d[0], 'yyyy-MM-dd'),
              }))
            }
          } else if (type === 'layer3' && layers['layer3']) {
            const data = layers['layer3'].find((d) => d.communityId === Number(filter.id))
            if (data) {
              chartData = data.history.map((d) => ({
                value: d[1],
                time: format(d[0], 'yyyy-MM-dd'),
              }))
            }
          }

          if (chartData) {
            instance.setData(chartData)
            timeSeriesData.value[key] = instance
          }
        }
      }
    })
  }

  tools.reArrangeChart({ autosize: true })
  tools.prepareTooltip({ darkMode: darkMode.value })
  renderBottomChart(chartBottomRef, '#range-slider', darkMode.value)
}

const formatGroupName = (group: string) => {
  switch (group.toLowerCase()) {
    case 'reddit':
      return 'r'
    case 'telegram':
      return 't.me'
    default:
      return group
  }
}

const searchAsset = async (q: string) => {
  const response = await $apiClient.GET('/nodiens/api/v1/community/asset', {
    params: {
      query: {
        ...(q && { terms: q }),
      },
    },
  })

  return response?.data?.payload?.data || []
}

const handleAssetChange = async (asset: AssetType) => {
  navigateTo(`/community/${params.selector}/${asset.slug}`)
}

const onLevelChange = (id: string) => {
  const index = filters.value.findIndex((d) => d.id === id)
  if (filters.value[index]) {
    filters.value[index].selected = !filters.value[index].selected
  }

  // Check if need to refresh data on level change
  const selectedValue = filters.value.filter((data) => data.selected === true)
  if (selectedValue.length > 0) {
    selectedLayer1.value = filters.value.some((data) => data.type === 'layer1' && data.selected === true)
    selectedLayer2.value = filters.value
      .filter((data) => data.type === 'layer2' && data.selected === true)
      .map((r) => r.id)
    selectedLayer3.value = filters.value
      .filter((data) => data.type === 'layer3' && data.selected === true)
      .map((r) => r.id)
  } else {
    selectedLayer1.value = false
    selectedLayer2.value = []
    selectedLayer3.value = []
  }
}

const bulkFilterUpdate = (newFilters: { id: string; selected: boolean }[]) => {
  newFilters.forEach((newFilter) => {
    const index = filters.value.findIndex((oldFilter) => oldFilter.id === newFilter.id)
    if (filters.value[index]) {
      filters.value[index].selected = newFilter.selected
    }
  })

  selectedLayer1.value = filters.value.some((data) => data.type === 'layer1' && data.selected === true)
  selectedLayer2.value = filters.value
    .filter((data) => data.type === 'layer2' && data.selected === true)
    .map((r) => r.id)
  selectedLayer3.value = filters.value
    .filter((data) => data.type === 'layer3' && data.selected === true)
    .map((r) => r.id)
}

const printContent = (target: string, blob?: Blob) => {
  const fileName = `${params.slug}_${params.selector}-index_${format(new Date(), 'yyyyMMddHHmm')}_(Nodiens)`

  if (target !== 'pdf') {
    FileSaver.saveAs(blob!, `${fileName}.${target}`)
  } else {
    blobToBase64(blob!, (str) => {
      const pdf = new jsPDF({
        orientation: 'landscape',
      })
      pdf.addImage(str, 'PNG', 10, 10, 280, 110)

      pdf.save(`${fileName}.${target}`)
    })
  }
}

const print = (target: string) => {
  let previousValue: { from: Time; to: Time } | undefined
  if (chart.value) {
    const opt = chart.value.timeScale().getVisibleRange()
    if (opt) {
      previousValue = {
        from: opt.from,
        to: opt.to,
      }
    }
    chart.value.timeScale().applyOptions({
      visible: true,
    })
    chart.value.applyOptions({
      width: 1584,
      height: 534,
    })
  }
  const canvas = chart.value?.takeScreenshot()
  if (chart.value) {
    chart.value.timeScale().applyOptions({
      visible: false,
    })
    chart.value.applyOptions({
      width: chartRef.value?.offsetWidth,
      height: chartRef.value?.offsetHeight,
    })
    if (previousValue) {
      chart.value.timeScale().setVisibleRange({
        from: previousValue.from,
        to: previousValue.to,
      })
    }
  }
  if (canvas && exportElement.value) {
    canvas.style.maxWidth = '100%'
    exportElement.value!.style.display = 'block'
    printTime.value = format(new Date(), 'MMM dd, yyyy kk:mm zzz')
    exportElement.value?.appendChild(canvas)

    const chartWatermark = document.getElementById('chart-watermark')
    if (chartWatermark) {
      const img = chartWatermark.querySelector('img')
      if (img) {
        img.style.left = `${canvas.offsetWidth / 4}px`
        img.style.width = `${canvas.offsetWidth / 2}px`
        img.style.top = `${canvas.offsetHeight / 3.5}px`
      }
    }

    const body = document.querySelector('body')

    if (body) {
      body.style.overflow = 'hidden'
      domtoimage
        .toBlob(exportElement.value)
        .then((blob: any) => {
          printContent(target, blob)
        })
        .finally(() => {
          body.style.overflow = 'unset'
          exportElement.value!.style.display = 'none'
          const canvas = exportElement.value?.querySelector('canvas')
          if (canvas) {
            canvas.remove()
          }
        })
    }
  }
}

watch(error, (newError) => {
  if (newError) {
    toast.add({
      title: 'Error',
      color: 'red',
      description: 'Something went wrong. Please try again later.',
      icon: 'i-heroicons-exclamation-triangle-20-solid',
    })
  }
})

watch(data, (newData) => {
  // Update selected asset
  if (newData) {
    const { logo, name, slug, symbol } = newData
    selectedAsset.value = { logo: logo ?? '', name, slug, symbol, type: '' }
  } else {
    selectedAsset.value = undefined
  }

  // Initiate level filters
  if (newData?.options && !filters.value.length) {
    const initialFilters = Object.keys(newData.options).reduce((r: typeof filters.value, key) => {
      const t = key as string
      const layerOptions = newData.options[key as unknown as 'layer1' | 'layer2' | 'layer3']

      if (layerOptions) {
        Object.values(layerOptions).forEach((opt) => {
          if (t === 'layer3') {
            const communities = opt.community as { label: string; value: string }[]
            return communities.forEach((comm) => {
              r.push({
                id: comm.value,
                name: `${formatGroupName(opt.platform)}/${comm.label.toLowerCase()}`,
                type: t,
                group: opt.platform,
                selected: false,
              })
            })
          }

          return r.push({
            id: opt.value,
            name: opt.label,
            type: t,
            selected: false,
          })
        })

        if (layerOptions === true && t === 'layer1') {
          r.push({
            id: params.selector!,
            name: titleCase(params.selector!),
            selected: true,
            type: t,
          })
        }
      }

      return r
    }, [])

    filters.value = initialFilters
  }
})

watch([data, chartRef, chartBottomRef], ([chartData, chartEl, sliderRef]) => {
  if (chartData && chartEl && sliderRef) {
    setTimeout(() => {
      initializeChart()
    }, 50)
  }
})

watch(isMobile, (value) => {
  if (value) {
    setPriceScaleVisibility('right', false)
  } else {
    setPriceScaleVisibility('right', true)
  }
})

watch(darkMode, (value) => {
  tools.setDark(value)
})

onUnmounted(() => destroyChart())
</script>

<template>
  <div>
    <Head>
      <Title> {{ pageTitle }} </Title>
    </Head>

    <HeaderPage page-name="community-indices" />

    <div class="md:px-5 md:py-6">
      <div class="mx-4 hidden items-center justify-end md:mx-0 md:flex md:justify-between">
        <UButton
          class="hidden md:inline-flex"
          variant="outline"
          color="black"
          size="md"
          icon="i-heroicons-arrow-left"
          label="Back"
          @click="onBack"
        />

        <div
          v-if="data"
          class="flex flex-col items-end gap-x-2 md:flex-row md:items-center"
        >
          <p class="text-2xl font-semibold text-black dark:text-white">{{ data?.latestValue ?? '-' }}</p>
          <div class="flex items-center">
            <img
              v-if="data?.aDayChangeIndicator === '>'"
              src="~@/assets/svg/arrow-triangle-up.svg"
              width="16"
              height="16"
              class="h-[16px] w-[16px]"
            />
            <img
              v-else-if="data?.aDayChangeIndicator === '<'"
              src="~@/assets/svg/arrow-triangle-down.svg"
              width="16"
              height="16"
              class="h-[16px] w-[16px]"
            />
            <span
              v-if="data?.aDayChangePercentage"
              :class="[
                'text-lg font-medium',
                data?.aDayChangeIndicator === '>'
                  ? 'text-[#00A210]'
                  : data?.aDayChangeIndicator === '<'
                    ? 'text-[#D70000]'
                    : '',
              ]"
            >
              {{ data.aDayChangePercentage }} (1d)
            </span>
          </div>
        </div>
      </div>
      <UCard class="space-y-2 rounded-none md:mt-5 md:rounded-lg">
        <!-- Filters -->
        <div class="flex flex-col flex-wrap gap-2 md:flex-row md:items-center md:justify-between">
          <!-- Select Menus -->
          <div class="flex flex-1 flex-col gap-2 md:flex-row md:items-center">
            <AssetSelector
              v-model="selectedAsset"
              :search-assets="searchAsset"
              :loading="isLoading"
              @change="handleAssetChange"
            />

            <!-- Index & Layer Select Menu -->
            <USkeleton
              v-if="isLoading"
              class="h-9 w-40"
            />

            <div
              v-else
              class="flex w-full items-stretch gap-2 md:w-auto"
            >
              <UDropdown
                :items="indexItems"
                class="flex-1"
              >
                <UButton
                  size="md"
                  color="black"
                  variant="solid"
                  block
                >
                  <span class="flex-1 text-left">{{ titleCase(params.selector ?? 'Select Index') }}</span>
                  <UIcon
                    name="i-heroicons-chevron-down-solid"
                    class="text-xl"
                  />
                </UButton>
              </UDropdown>

              <!-- Handler Mobile Bottom Sheet -->
              <UButton
                v-if="isMobile"
                size="md"
                icon="i-heroicons-plus"
                color="black"
                variant="solid"
                class="flex-1"
                @click="isIndexFilterBottomSheetOpen = true"
              >
                Level

                <template #trailing>
                  <InformationPopover
                    icon-class="mt-1.5"
                    :revert-icon="true"
                  >
                    <TooltipContent>
                      <p>
                        Select the granularity level for calculating the mood index or trust index of a crypto asset.
                      </p>
                    </TooltipContent>
                  </InformationPopover>
                </template>
              </UButton>
              <UPopover
                v-else
                class="flex flex-1"
              >
                <UButton
                  size="sm"
                  icon="i-heroicons-plus"
                  color="black"
                  variant="solid"
                  block
                >
                  Level

                  <template #trailing>
                    <InformationPopover
                      icon-class="mt-1.5"
                      :revert-icon="true"
                    >
                      <TooltipContent>
                        <p>
                          Select the granularity level for calculating the Mood Index or Trust Index of a crypto asset.
                        </p>
                      </TooltipContent>
                    </InformationPopover>
                  </template>
                </UButton>

                <template #panel>
                  <div class="min-w-[300px] p-2">
                    <div
                      v-for="(layer, layerIndex) in LAYERS"
                      :key="`${layer.key}_${layerIndex}`"
                      class="mb-4"
                    >
                      <!-- Layer Title -->
                      <div class="mb-1 flex items-center gap-1">
                        <span class="truncate text-sm font-medium">{{ layer.label }}</span>
                        <InformationPopover
                          class="left-[24%] mt-2"
                          icon-class="text-neutrals-300 dark:text-white"
                          :ui="{
                            container: 'z-[60] !p-0',
                            wrapper: 'absolute',
                            base: 'absolute z-10',
                            popper: {
                              strategy: 'absolute',
                            },
                          }"
                        >
                          <TooltipContent>
                            <p
                              v-html="
                                (COMMUNITY_INDICES_LEVEL_DESCRIPTIONS as Record<string, string>)[
                                  layer.key.toLowerCase()
                                ]
                              "
                            />
                          </TooltipContent>
                        </InformationPopover>
                      </div>

                      <!-- Layer Options -->
                      <div class="flex max-h-[160px] flex-col gap-2 overflow-y-auto">
                        <div
                          v-if="layer.key !== 'layer3'"
                          class="flex flex-col gap-y-2"
                        >
                          <div
                            v-for="filter in filters.filter((r) => r.type === layer.key)"
                            :key="filter.id"
                          >
                            <UCheckbox
                              :disabled="isLoading"
                              v-if="layer.key === filter.type"
                              v-model:model-value="filter.selected"
                              :ui="
                                (() => {
                                  switch (
                                    `${filter.type}-${filter.group?.toLowerCase() ?? filter.name.toLowerCase()}`
                                  ) {
                                    case 'layer1-trust':
                                      return {
                                        background:
                                          'bg-white checked:bg-green-500 checked:hover:bg-green-500 checked:focus:bg-green-500 dark:bg-neutrals-700 dark:checked:bg-green-500 dark:checked:hover:bg-green-500 dark:checked:focus:bg-green-500',
                                      }
                                    case 'layer2-telegram':
                                      return {
                                        background:
                                          'bg-white checked:bg-violet-600 checked:hover:bg-violet-600 checked:focus:bg-violet-600 dark:bg-neutrals-700 dark:checked:bg-violet-600 dark:checked:hover:bg-violet-600 dark:checked:focus:bg-violet-600',
                                      }
                                    case 'layer2-reddit':
                                      return {
                                        background:
                                          'bg-white checked:bg-brand-500 checked:hover:bg-brand-500 checked:focus:bg-brand-500 dark:bg-neutrals-700 dark:checked:bg-brand-500 dark:checked:hover:bg-brand-500 dark:checked:focus:bg-brand-500',
                                      }
                                    default:
                                      return {
                                        background:
                                          'bg-white checked:bg-base-primary-500 checked:hover:bg-base-primary-500 checked:focus:bg-base-primary-500 dark:bg-neutrals-700 dark:checked:bg-base-primary-500 dark:checked:hover:bg-base-primary-500 dark:checked:focus:bg-base-primary-500',
                                      }
                                  }
                                })()
                              "
                              :name="filter.id"
                              :label="filter.name"
                              @input="onLevelChange(filter.id)"
                            />
                          </div>
                        </div>
                        <div
                          v-else
                          class="flex flex-col gap-y-2"
                        >
                          <div class="mb-2 flex flex-col gap-y-2">
                            <strong class="text-neutrals-800 text-xs font-medium dark:text-white">Reddit</strong>
                            <div
                              v-for="filter in filters.filter(
                                (r) => r.type === layer.key && r.group?.toLowerCase() === 'reddit',
                              )"
                              :key="filter.id"
                            >
                              <UCheckbox
                                :disabled="isLoading"
                                v-if="layer.key === filter.type"
                                v-model:model-value="filter.selected"
                                :ui="
                                  (() => {
                                    switch (
                                      `${filter.type}-${filter.group?.toLowerCase() ?? filter.name.toLowerCase()}`
                                    ) {
                                      case 'layer3-reddit':
                                        return {
                                          background:
                                            'bg-white checked:bg-brand-300 checked:hover:bg-brand-300 checked:focus:bg-brand-300 dark:bg-neutrals-700 dark:checked:bg-brand-300 dark:checked:hover:bg-brand-300 dark:checked:focus:bg-brand-300',
                                        }
                                      default:
                                        return {
                                          background:
                                            'bg-white checked:bg-base-primary-500 checked:hover:bg-base-primary-500 checked:focus:bg-base-primary-500 dark:bg-neutrals-700 dark:checked:bg-base-primary-500 dark:checked:hover:bg-base-primary-500 dark:checked:focus:bg-base-primary-500',
                                        }
                                    }
                                  })()
                                "
                                :name="filter.id"
                                :label="filter.name"
                                @input="onLevelChange(filter.id)"
                              />
                            </div>
                          </div>
                          <div class="flex flex-col gap-y-2">
                            <strong class="text-neutrals-800 text-xs font-medium dark:text-white">Telegram</strong>
                            <div
                              v-for="filter in filters.filter(
                                (r) => r.type === layer.key && r.group?.toLowerCase() === 'telegram',
                              )"
                              :key="filter.id"
                            >
                              <UCheckbox
                                v-if="layer.key === filter.type"
                                v-model:model-value="filter.selected"
                                :ui="
                                  (() => {
                                    switch (
                                      `${filter.type}-${filter.group?.toLowerCase() ?? filter.name.toLowerCase()}`
                                    ) {
                                      case 'layer3-telegram':
                                        return {
                                          background:
                                            'bg-white checked:bg-violet-300 checked:hover:bg-violet-300 checked:focus:bg-violet-300 dark:bg-neutrals-700 dark:checked:bg-violet-300 dark:checked:hover:bg-violet-300 dark:checked:focus:bg-violet-300',
                                        }
                                      default:
                                        return {
                                          background:
                                            'bg-white checked:bg-base-primary-500 checked:hover:bg-base-primary-500 checked:focus:bg-base-primary-500 dark:bg-neutrals-700 dark:checked:bg-base-primary-500 dark:checked:hover:bg-base-primary-500 dark:checked:focus:bg-base-primary-500',
                                        }
                                    }
                                  })()
                                "
                                :name="filter.id"
                                :label="filter.name"
                                @input="onLevelChange(filter.id)"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
              </UPopover>
            </div>
          </div>

          <!-- Time Options -->
          <div class="flex items-center gap-x-3 md:ml-auto lg:ml-0">
            <div
              v-if="isLoading"
              class="flex items-center gap-x-2"
            >
              <USkeleton
                v-for="item in 5"
                :key="item"
                class="h-9 w-12"
              />
            </div>
            <div
              v-else-if="selectedFilters.length > 0"
              class="flex grow items-center justify-between gap-x-2"
            >
              <UButton
                v-for="zoom in zoomViews"
                :key="zoom.id"
                :variant="zoom.active ? 'outline' : 'ghost'"
                :color="zoom.active ? 'brand' : 'black'"
                :class="['px-1.5 !font-normal sm:px-2.5', { 'dark:!text-neutrals-50 !text-black/50': !zoom.active }]"
                @click="isLevelAllowed ? tools.setZoom(zoom.id) : null"
                :disabled="!isLevelAllowed"
              >
                {{ zoom.label }}
              </UButton>
            </div>
            <USkeleton
              v-if="isLoading"
              class="h-10 w-9"
            />
            <UButton
              v-else-if="selectedFilters.length > 0"
              size="md"
              icon="i-material-symbols-share"
              color="soft-gray"
              variant="outline"
              @click="isShareModalOpen = true"
              :disabled="!isLevelAllowed"
            />
          </div>
        </div>

        <!-- Legends -->
        <div class="mt-5 flex items-start justify-between gap-x-3">
          <div class="flex flex-wrap items-center gap-3">
            <UButton
              v-for="layerItem in selectedFilters"
              :key="layerItem.id"
              trailing-icon="i-heroicons-x-mark"
              size="md"
              color="soft-gray"
              variant="outline"
              class="!font-normal"
              @click="onLevelChange(layerItem.id)"
            >
              <span>{{ layerItem.type.replace('layer', 'L') }}-{{ layerItem.name }}</span>
              <template #leading>
                <div
                  class="h-[10px] w-[10px] rounded-full"
                  :style="`background:${LEGEND_MT_COLOR[`${layerItem.type.toLowerCase()}-${layerItem.group?.toLowerCase() ?? layerItem.name.toLowerCase()}`]}`"
                />
              </template>
            </UButton>
          </div>

          <div class="hidden items-center gap-x-3 md:flex">
            <USkeleton
              v-if="isLoading"
              class="h-9 w-12"
            />
            <UButton
              v-else-if="selectedFilters.length > 0"
              size="md"
              variant="outline"
              :color="log ? 'brand' : 'soft-gray'"
              class="!font-normal"
              @click="isLevelAllowed ? tools.handleLog() : null"
              :disabled="!isLevelAllowed"
            >
              LOG
            </UButton>
            <USkeleton
              v-if="isLoading"
              class="h-9 w-9"
            />
            <UPopover
              v-else-if="selectedFilters.length > 0"
              :disabled="!isLevelAllowed"
            >
              <UButton
                size="md"
                color="soft-gray"
                variant="outline"
                icon="i-heroicons-arrow-down-tray"
                :disabled="!isLevelAllowed"
              />

              <template #panel>
                <div class="flex flex-col p-2">
                  <UButton
                    v-for="item in ['pdf', 'png', 'jpg']"
                    :key="item"
                    color="midnight"
                    variant="ghost"
                    block
                    :label="`Export as ${item.toUpperCase()}`"
                    @click="print(item)"
                  />
                </div>
              </template>
            </UPopover>
          </div>
        </div>

        <!-- Chart -->
        <div class="relative mt-5 h-[424px] w-full md:h-[484px]">
          <CheckFeature
            :allowed="isLevelAllowed"
            :loading="isLoading"
            class="h-full w-full"
          >
            <div
              v-if="isLoading"
              class="absolute left-0 top-0 z-10 grid h-full w-full place-items-center"
            >
              <LottieNodiensLoader />
            </div>

            <div
              v-else-if="!selectedFilters.length"
              class="absolute left-0 top-0 z-10 h-full w-full"
            >
              <div
                class="border-dark-grey dark:bg-midnight relative flex h-full flex-col items-center justify-center gap-y-2 border-[3px] border-dashed p-5 text-center dark:border-white/90"
              >
                <h3 class="text-xl font-semibold text-[#1B1F2B] dark:text-white/90">
                  Select either 1 index to begin plotting
                </h3>
                <p class="text-sm text-[#1B1F2B] dark:text-white/90">
                  Click on the index of your choice to visualize its impact. Dive into the nuances of mood analysis with
                  a single click.
                </p>
              </div>
            </div>

            <NoDataAvailable
              v-else-if="isDataEmpty"
              class="h-[340px] md:h-[400px]"
            />

            <div
              v-else
              :key="`chart-${params.selector}-${params.slug}`"
            >
              <div
                ref="chartRef"
                class="h-[340px] md:h-[400px]"
              />
              <div class="relative my-2 h-[84px]">
                <div
                  id="range-slider"
                  class="block bg-transparent"
                />
                <div
                  class="absolute top-0 flex h-[50px] w-full justify-center overflow-hidden"
                  style="z-index: 0"
                >
                  <div
                    ref="chartBottomRef"
                    class="h-[50px] w-full overflow-hidden rounded border border-neutral-400"
                    style="max-width: 98.6607%"
                  />
                </div>
              </div>
            </div>
          </CheckFeature>
        </div>
      </UCard>
    </div>

    <!-- Mobile Bottom Sheet -->
    <ClientOnly>
      <IndexFilterBottomSheet
        v-model="isIndexFilterBottomSheetOpen"
        :layers="LAYERS"
        :filters="JSON.stringify(filters)"
        @update="bulkFilterUpdate"
      />
    </ClientOnly>

    <LazyShareModal
      v-if="isShareModalOpen"
      :shared-text="fullPath"
      @on-close="isShareModalOpen = false"
    />

    <!-- Export Element -->
    <div
      ref="exportElement"
      class="pdf h-[693px] w-[1648px] bg-white p-8 dark:bg-black"
      style="display: none"
    >
      <div class="flex justify-between gap-2">
        <div class="flex flex-col">
          <div class="flex items-center gap-2 font-normal">
            <img
              :src="data?.logo ? '/api/v2/prx-image?url=' + data.logo + '&responseType=blob' : ''"
              alt="logo"
              class="mr-3 h-6 w-6"
            />
            <div class="text-base">
              {{ data?.name ?? '-' }}
              <span class="text-neutrals-300">({{ data?.symbol.toUpperCase() ?? '-' }})</span>
            </div>
          </div>
          <div>Downloaded from Nodiens, {{ printTime }}</div>
        </div>

        <div class="flex flex-wrap items-start gap-2">
          <UButton
            v-for="layerItem in selectedFilters"
            :key="layerItem.id"
            size="md"
            color="soft-gray"
            variant="outline"
          >
            <span>{{ layerItem.type.replace('layer', 'L') }} {{ layerItem.name }}</span>
            <template #leading>
              <div
                class="h-[10px] w-[10px] rounded-full"
                :style="`background:${lineStyleLayers?.[params.selector!]?.[layerItem.type]?.color}`"
              />
            </template>
          </UButton>
        </div>
      </div>
      <div
        id="chart-watermark"
        class="relative w-full"
      >
        <ColorScheme>
          <img
            v-if="darkMode"
            src="~/assets/media/chart-watermark-night.png"
            class="absolute w-4/12"
            style="left: 33%; top: 50%"
            alt="Chart watermark"
          />
          <img
            v-else
            src="~/assets/media/chart-watermark.png"
            class="absolute w-4/12"
            style="left: 33%; top: 50%"
            alt="Chart watermark"
          />
        </ColorScheme>
      </div>
    </div>
  </div>
</template>

<style>
#range-slider {
  margin: auto;
  width: 100%;
  height: 50px;
  background: transparent;
  overflow: hidden;
}
#range-slider .range-slider__thumb:nth-child(3) {
  transform: translate(0%, -50%) !important;
}
#range-slider .range-slider__thumb:nth-child(4) {
  transform: translate(-100%, -50%) !important;
}
.dark #range-slider .range-slider__thumb {
  border: 1px solid#e5e5e5;
}
#range-slider .range-slider__thumb {
  width: 14px;
  height: 100%;
  border-radius: 4px;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='18' height='18' fill='%23333' viewBox='0 0 24 24'%3E%3Cpath d='M12,16A2,2 0 0,1 14,18A2,2 0 0,1 12,20A2,2 0 0,1 10,18A2,2 0 0,1 12,16M12,10A2,2 0 0,1 14,12A2,2 0 0,1 12,14A2,2 0 0,1 10,12A2,2 0 0,1 12,10M12,4A2,2 0 0,1 14,6A2,2 0 0,1 12,8A2,2 0 0,1 10,6A2,2 0 0,1 12,4Z' /%3E%3C/svg%3E")
    #fff;
  border: 1px solid #c3c3c3;
  background-repeat: no-repeat;
  background-position: center;
}
.dark #range-slider .range-slider__range {
  border: 1px solid #d9d9d9;
}
#range-slider .range-slider__range {
  border-radius: 6px;
  background: rgba(0, 163, 255, 0.1);
  border: 1px solid #c3c3c3;
  box-sizing: border-box;
}
</style>
