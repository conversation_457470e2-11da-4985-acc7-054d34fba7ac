<script setup lang="ts">
import {
  COMMUNITY_TABLE_RANK_VALUES,
  COMMUNITY_TABLE_MOOD_RANK_OPTION_LABELS,
  COMMUNITY_TABLE_TRUST_RANK_OPTION_LABELS,
  COMMUNITY_TABLE_RANK_OPTION_DESCRIPTIONS,
} from '~/constants/options'
import { capitalizeFirstLetter } from '~/helper/string-helper'

const route = useRoute()

const searchTimeoutId = ref<NodeJS.Timeout | undefined | number>()

const assetTypes = computed({
  get: () => [
    [
      {
        label: 'Cryptos',
        disabled: true,
        value: 'cryptocurrency',
        checked:
          (route.query.assetType as string)?.split(',')?.some((assetType) => assetType === 'cryptocurrency') ?? true,
        labelClass: '!text-white',
      },
      {
        label: 'Stablecoins',
        disabled: true,
        value: 'stablecoin',
        checked: (route.query.assetType as string)?.split(',').some((assetType) => assetType === 'stablecoin') ?? true,
        labelClass: '!text-white',
      },
    ],
  ],
  set: async (newVal) => {
    const selected = newVal[0]
      ? newVal[0].filter((assetType) => assetType.checked).map((assetType) => assetType.value)
      : ['']
    await updateQuery({ assetType: selected.join(','), q: undefined })
  },
})

const updateQuery = async (newQuery: Record<string, string | undefined>) => {
  await navigateTo({
    query: {
      ...route.query,
      ...newQuery,
      page: 1,
    },
  })
}

const searchText = computed({
  get: () => (route.query.q ?? '') as string,
  set: (q) => {
    if (searchTimeoutId.value) {
      clearTimeout(searchTimeoutId.value)
    }
    searchTimeoutId.value = setTimeout(async () => {
      await updateQuery({ q })
      searchTimeoutId.value = undefined
    }, 500)
  },
})

const selectedType = computed({
  get: () => (route.query.index ?? 'mood') as string,
  set: async (newIndex) =>
    await updateQuery({
      index: newIndex,
      ...(newIndex === 'topic_analytic' && { q: undefined, rank: undefined, assetType: undefined }),
    }),
})

const selectedRank = computed({
  get: () => (route.query.rank ?? COMMUNITY_TABLE_RANK_VALUES.rankIndicator) as string,
  set: async (rank) => await updateQuery({ rank }),
})

const rankOptions = computed(() => {
  return Object.values(COMMUNITY_TABLE_RANK_VALUES)
})

const rankLabels = computed(() => {
  return selectedType.value === 'mood'
    ? COMMUNITY_TABLE_MOOD_RANK_OPTION_LABELS
    : COMMUNITY_TABLE_TRUST_RANK_OPTION_LABELS
})

const titlePage = computed(() => {
  return route.query.index === 'topic_analytic'
    ? 'Topic Trends'
    : `${capitalizeFirstLetter(route.query.index?.toString() ?? 'mood')} Index`
})
</script>

<template>
  <div>
    <Head>
      <Title>{{ titlePage }} overview today</Title>
    </Head>
    <HeaderPage
      :page-name="selectedType === 'topic_analytic' ? 'topic-trends' : 'community-indices'"
      faq-slug="community"
      comparison-slug="community-indices"
    />

    <div class="px-5 py-6">
      <!-- TopBar Table -->
      <div
        v-if="selectedType !== 'topic_analytic'"
        class="mb-4 flex flex-wrap gap-4"
      >
        <div class="flex grow flex-wrap items-center gap-4 gap-x-10 sm:gap-x-4">
          <UDropdown
            :ui="{
              item: { disabled: 'cursor-default opacity-100' },
            }"
            :items="assetTypes"
            :popper="{ placement: 'bottom-start' }"
            class="order-1"
          >
            <UButton
              label="Asset"
              icon="i-material-symbols-filter-list"
              color="black"
              size="md"
              truncate
              class="min-h-[42px] w-full sm:max-w-60"
            />
            <template #item="{ item }">
              <UCheckbox
                :modelValue="item.checked"
                :label="item.label"
                color="sky"
                @change="
                  assetTypes = assetTypes.map((assetType) =>
                    assetType.map((asset) =>
                      asset.value === item.value ? { ...asset, checked: !asset.checked } : asset,
                    ),
                  )
                "
              />
            </template>
          </UDropdown>

          <UInput
            v-model="searchText"
            variant="transparent"
            placeholder="Search name or symbol"
            trailing-icon="i-heroicons-magnifying-glass"
            class="order-3 grow sm:order-2 sm:grow-0"
          />

          <USelectMenu
            v-model="selectedRank"
            :options="rankOptions"
            variant="outline"
            color="black"
            size="md"
            class="order-2 ml-auto"
            selected-icon=""
          >
            <template #label>
              <div class="flex items-center gap-2">
                <UIcon
                  name="i-heroicons-arrows-up-down"
                  class="text-lg font-semibold"
                />
                <span>Rank by</span>
              </div>
            </template>

            <template #option="{ option, selected }">
              <div class="flex items-center gap-2 overflow-hidden">
                <div
                  class="flex h-[18px] w-[18px] shrink-0 grow-0 items-center justify-center rounded-full border-2 bg-transparent"
                  :class="[selected ? 'border-base-primary-600' : 'border-neutrals-300']"
                >
                  <div
                    class="h-[8px] w-[8px] rounded-full"
                    :class="[selected ? 'bg-base-primary-600' : 'bg-transparent']"
                  />
                </div>
                <span class="... truncate">{{ rankLabels[option as keyof typeof rankLabels] }}</span>
                <InformationPopover
                  class="mt-1"
                  v-if="option === COMMUNITY_TABLE_RANK_VALUES.rankIndicator"
                >
                  <TooltipContent>
                    <p
                      v-html="
                        COMMUNITY_TABLE_RANK_OPTION_DESCRIPTIONS[
                          selectedType as keyof typeof COMMUNITY_TABLE_RANK_OPTION_DESCRIPTIONS
                        ]
                      "
                    />
                  </TooltipContent>
                </InformationPopover>
              </div>
            </template>
          </USelectMenu>
        </div>
      </div>

      <CommunityTable :coin-types="assetTypes.flat().filter((coin) => coin.checked)" />
    </div>
  </div>
</template>
