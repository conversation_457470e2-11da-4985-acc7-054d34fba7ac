import { format } from 'date-fns'
import { LineStyle } from 'lightweight-charts'

// Constants
import { HTTP_STATUS } from '~/constants/error'
import { ESG_CHART_TYPE_OPTION_LABEL } from '~/constants/options'
import { CARBON_EMISSION_TYPE_OPTIONS, CLIMATE_METRIC_SLUGS, ENERGY_CONSUMPTION_TYPE_OTIONS } from '~/constants/climate'

// Helpers
import { displayWattage, displayWeight, numericDisplay } from '~/helper/number-helper'

// Types
import type { WhitespaceData } from 'lightweight-charts'
import type { HLCAreaData } from '~/composables/custom-plugin/data'
import type { AssetType } from '~/types/appTypes'
import type {
  CarbonEmissionMetric,
  CarbonEmissionPayload,
  EnergyConsumptionMetric,
  EnergyConsumptionPayload,
  EsgAsset,
} from '~/types'

/**
 * Helper number used to temp transfrom small values to larger values to display on lightweight chart
 * This approach is need to show small values, because apparently lightweight chart doesn't support small numbers
 */
const HELPER_NUMBER = 100000

export const useClimateChart = () => {
  const route = useRoute()
  const params = route.params as Partial<{ type: string; slug: string }>
  const query = route.query as Partial<{ metric: string }>

  if (!params.type || !params.slug) {
    throw createError({ statusCode: 404, statusMessage: 'Page Not Found' })
  }

  const { $apiClient } = useNuxtApp()
  const config = useRuntimeConfig()
  const router = useRouter()
  const toast = useToast()
  const { onBack } = useBackHandler()
  const { isMobile } = useDeviceScreen()

  const { isFeatureAllowed } = useFeature()

  const isClimateFeatureAllowed = computed(
    () =>
      !!isFeatureAllowed('esg_climate_metrics.power_use') ||
      !!isFeatureAllowed('esg_climate_metrics.energy_cons_tx') ||
      !!isFeatureAllowed('esg_climate_metrics.energy_cons_tx_node'),
  )

  const {
    chart: chartInstance,
    zoomViews,
    log,
    tools,
    addLineSeries,
    addAreaRangeSeries,
    setPriceScaleVisibility,
    renderChart,
    renderBottomChart,
    destroyChart,
  } = useLightChart()

  const esgChartRef = ref<HTMLElement>()
  const esgBottomChartRef = ref<HTMLElement>()

  const isShareModalOpen = ref(false)
  const selectedType = ref<string>(params.type)
  const selectedMetric = ref(
    (query.metric ?? params.type === CLIMATE_METRIC_SLUGS.energyConsumption) ? 'powerUse' : 'emission',
  )

  const selectedAsset = useState<EsgAsset | undefined>()
  const consensusMechanism = useState<(string | null)[]>(() => [])

  const { isDark } = useAppColorMode()

  const shareLink = computed(() => `${config.public.appUrl}/climate/${params.type}/${params.slug}`)

  const { data: chartData, status: chartStatus } = useLazyAsyncData(
    `esg-energy-cons-${params.slug}-${selectedMetric.value}`,
    async () => {
      if (!params.slug || !isClimateFeatureAllowed.value) {
        return null
      }

      // NOTE: will remove this check once source type chart is available
      if (selectedMetric.value === 'source') {
        return null
      }

      const metrics = {
        powerUse: 'POWER_USE_W',
        powerUsePerTx: 'ENG_CONS_TX_WH',
        powerUsePerTxNode: 'ENG_CONS_TX_NODE_WH',
        emission: 'emission',
        pertx: 'emissionPerTx',
        pertxnode: 'emissionPerTxPerNode',
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
      } as any

      // TODO: Add source type chart api
      const api =
        selectedType.value === CLIMATE_METRIC_SLUGS.energyConsumption
          ? '/nodiens/api/v1/esg/energy-consumption/{slug}'
          : '/nodiens/api/v1/esg/carbon-emission/{slug}'

      const { response, data, error } = await $apiClient.GET(api, {
        params: {
          path: { slug: params.slug },
          query: { metric: metrics[selectedMetric.value] },
        },
      })

      if (error) {
        if (response.status === HTTP_STATUS.INTERNAL_SERVER_ERROR) {
          toast.add({
            title: 'Error',
            description: 'Something went wrong. Please try again later.',
            icon: 'i-heroicons-exclamation-triangle-20-solid',
            color: 'red',
          })
        }

        if (response.status === HTTP_STATUS.NOT_FOUND) {
          toast.add({
            title: 'Potential Issue with Data',
            description: 'Asset not found',
            icon: 'i-heroicons-exclamation-triangle',
            color: 'amber',
          })

          setTimeout(() => {
            router.replace('/climate')
          }, 3000)
        }
      }

      return data?.payload || null
    },
    {
      watch: [selectedMetric],
      server: false,
      transform: (data) => {
        if (!data) {
          return null
        }

        if (params.type === CLIMATE_METRIC_SLUGS.energyConsumption) {
          const payload = data as EnergyConsumptionPayload
          return {
            feeds: payload.feeds,
            dailyTimeseries: payload.daily_entries.map(([time, value]) => ({
              time: format(new Date(time), 'yyyy-MM-dd'),
              value: {
                tps: value.throughput,
                validator: value.validators,
              },
            })),
            metricTimeseries: payload.analytic_entries.map(([time, value]) => ({
              time: format(new Date(time), 'yyyy-MM-dd'),
              value: {
                min: value.lower,
                max: value.upper,
                avg: value.avg,
              },
            })),
            consensusNames: payload.consensusNames,
            startDate: payload.startDate ? format(payload.startDate, 'yyyy-MM-dd') : '',
            endDate: payload.endDate ? format(payload.endDate, 'yyyy-MM-dd') : '',
            isTypeOptionAvailable: payload.availabilityOfCarbonEmission,
          }
        } else if (params.type === CLIMATE_METRIC_SLUGS.carbonEmission) {
          const payload = data as CarbonEmissionPayload

          return {
            feeds: payload.feeds,
            dailyTimeseries: payload.dailyMetrics.map(([time, value]) => ({
              time: format(new Date(time), 'yyyy-MM-dd'),
              value: {
                tps: value.throughput,
                validator: value.validatorCount,
              },
            })),
            metricTimeseries: payload.analytics.map(([time, value]) => ({
              time: format(new Date(time), 'yyyy-MM-dd'),
              value: {
                min: value.lowerBound,
                max: value.upperBound,
                avg: value.bestGuess,
              },
            })),
            consensusNames: payload.consensus,
            startDate: payload.startDate ? format(payload.startDate, 'yyyy-MM-dd') : '',
            endDate: payload.endDate ? format(payload.endDate, 'yyyy-MM-dd') : '',
            isTypeOptionAvailable: payload.availabilityOfEnergyConsumption,
            type: payload.type,
            layer: 'L' + payload.layer,
          }
        }

        return null
      },
    },
  )

  const isLoading = computed(() => chartStatus.value === 'pending')

  const isChartEmpty = computed(() => {
    if (chartStatus.value === 'pending') {
      return false
    }

    if (!chartData.value) {
      return true
    }

    return chartData.value.dailyTimeseries.length === 0 && chartData.value.metricTimeseries.length === 0
  })

  const typeOptions = computed(() => {
    const isOptionAvailable = chartData.value?.isTypeOptionAvailable === true
    const disabledCarbonEmissionOption = params.type === CLIMATE_METRIC_SLUGS.energyConsumption && !isOptionAvailable
    const disabledEnergyConsumptionOption = params.type === CLIMATE_METRIC_SLUGS.carbonEmission && !isOptionAvailable

    return [
      {
        label: 'Energy Cons',
        value: CLIMATE_METRIC_SLUGS.energyConsumption,
        disabled: disabledCarbonEmissionOption,
      },
      {
        label: 'CO₂ Emissions',
        value: CLIMATE_METRIC_SLUGS.carbonEmission,
        disabled: disabledEnergyConsumptionOption,
      },
    ]
  })

  const metricOptions = computed(() => {
    return params.type === CLIMATE_METRIC_SLUGS.energyConsumption
      ? ENERGY_CONSUMPTION_TYPE_OTIONS
      : CARBON_EMISSION_TYPE_OPTIONS
  })

  const smallestSelectedAvgUnit = computed(() => {
    if (!chartData.value) {
      return ''
    }

    const averageValues = chartData.value?.metricTimeseries.map((item) => item.value.avg) ?? []
    const min = Math.min(...averageValues)

    const unit =
      params.type === CLIMATE_METRIC_SLUGS.energyConsumption
        ? displayWattage(min).split(' ')[1]
        : displayWeight(min).split(' ')[1]
    const postfix =
      params.type === CLIMATE_METRIC_SLUGS.energyConsumption && selectedMetric.value !== 'powerUse' ? 'h' : ''

    return unit + postfix
  })

  const esgChartTypeOptionLabel = computed(() => ESG_CHART_TYPE_OPTION_LABEL[selectedMetric.value])

  const isLeftPriceScaleTitleVisible = computed(() => {
    return chartData.value?.metricTimeseries.some((item) => item.value.avg !== 0)
  })

  const isRightPriceScaleTitleVisible = computed(() => {
    return chartData.value?.dailyTimeseries.some((item) => item.value.tps !== 0)
  })

  const initializeChart = () => {
    if (chartInstance.value === undefined) {
      renderChart(
        esgChartRef,
        {
          grid: {
            horzLines: {
              color: '#C3C3C3',
              style: LineStyle.Dashed,
            },
            vertLines: {
              visible: false,
            },
          },
          crosshair: {
            horzLine: {
              visible: false,
              labelVisible: false,
            },
          },
          leftPriceScale: {
            visible: isMobile.value ? false : true,
            borderVisible: false,
            entireTextOnly: true,
            textColor: '#C3C3C3',
            scaleMargins: {
              bottom: 0.01,
            },
          },
          rightPriceScale: {
            visible: isMobile.value ? false : true,
            borderVisible: false,
            entireTextOnly: true,
            textColor: '#C3C3C3',
            scaleMargins: {
              bottom: 0.01,
            },
          },
          timeScale: {
            visible: false,
            borderVisible: false,
            borderColor: 'rgba(197, 203, 206, 1)',
            fixRightEdge: true,
            rightOffset: 5,
            lockVisibleTimeRangeOnResize: true,
          },
          handleScale: {
            pinch: false,
            mouseWheel: false,
            axisPressedMouseMove: false,
          },
          handleScroll: {
            mouseWheel: false,
            vertTouchDrag: false,
            horzTouchDrag: false,
            pressedMouseMove: false,
          },
          ...(chartData.value?.startDate && { startCalculation: chartData.value.startDate }),
          ...(chartData.value?.endDate && { latestCalculation: chartData.value.endDate }),
        },
        isDark.value,
      )
    }

    if (chartInstance.value && chartData.value) {
      const { dailyTimeseries, metricTimeseries } = chartData.value

      if (metricTimeseries.length > 0) {
        // Add Average line series
        const avgSeries = addLineSeries('avg', {
          priceScaleId: 'left',
          color: '#4FBD6D',
          lineWidth: 2,
          crosshairMarkerVisible: false,
          lastValueVisible: false,
          priceLineVisible: false,
          priceFormat: {
            type: 'custom',
            formatter: (price: number) => {
              return displayWattage(price / HELPER_NUMBER).split(' ')[0]
            },
          },
        })

        const avgSeriesData = metricTimeseries.map((item) => {
          return {
            time: item.time,
            value: item.value.avg * HELPER_NUMBER,
            customValues: {
              name: 'Best Guess',
            },
          }
        })

        // Remove duplicate dates
        avgSeriesData.forEach((item, index) => {
          if (avgSeriesData.findIndex((i) => i.time === item.time) !== index) {
            avgSeriesData.splice(index, 1)
          }
        })

        avgSeries?.setData(avgSeriesData)

        // Add Range series
        const rangeSeries = addAreaRangeSeries('range', {
          priceScaleId: 'left',
          lastValueVisible: false,
          priceLineVisible: false,
          closeLineWidth: 1,
          highLineWidth: 1,
          lowLineWidth: 1,
          areaTopColor: 'rgba(79, 189, 109, 0.15)',
          areaBottomColor: 'rgba(79, 189, 109, 0.15)',
          color: 'rgba(79, 189, 109, 0.15)',
          closeLineColor: 'rgba(255, 255, 255, 0)',
          highLineColor: 'rgba(79, 189, 109, 0)',
          lowLineColor: 'rgba(79, 189, 109, 0)',
          priceFormat: {
            type: 'custom',
            formatter: (price: number) => {
              return displayWattage(price / HELPER_NUMBER)
            },
          },
        })

        const rangeSeriesData: (HLCAreaData | WhitespaceData)[] = metricTimeseries.map((item) => {
          let avg = item.value.avg

          if (avg < item.value.min) {
            avg = item.value.min
          } else if (avg > item.value.max) {
            avg = item.value.max
          }

          return {
            time: item.time,
            low: item.value.min * HELPER_NUMBER,
            high: item.value.max * HELPER_NUMBER,
            close: avg * HELPER_NUMBER,
            customValues: {
              name: 'Range',
            },
          }
        })

        // Remove duplicate dates
        rangeSeriesData.forEach((item, index) => {
          if (rangeSeriesData.findIndex((i) => i.time === item.time) !== index) {
            rangeSeriesData.splice(index, 1)
          }
        })

        rangeSeries?.setData(rangeSeriesData)
      } else {
        setPriceScaleVisibility('left', false)
      }

      if (dailyTimeseries.length > 0) {
        // Add TPS line series
        const tpsSeries = addLineSeries('tps', {
          priceScaleId: 'right',
          color: '#ff0000',
          lineWidth: 2,
          crosshairMarkerVisible: false,
          lastValueVisible: false,
          priceLineVisible: false,
          priceFormat: {
            type: 'custom',
            formatter: (price: number) => {
              return numericDisplay(price / HELPER_NUMBER)
            },
          },
        })

        // Remove duplicate dates
        dailyTimeseries.forEach((item, index) => {
          if (dailyTimeseries.findIndex((i) => i.time === item.time) !== index) {
            dailyTimeseries.splice(index, 1)
          }
        })

        const tpsSeriesData = dailyTimeseries.map((item) => {
          return {
            time: item.time,
            value: item.value.tps * HELPER_NUMBER,
            customValues: {
              name: 'Throughput',
            },
          }
        })

        // Remove duplicate dates
        tpsSeriesData.forEach((item, index) => {
          if (tpsSeriesData.findIndex((i) => i.time === item.time) !== index) {
            tpsSeriesData.splice(index, 1)
          }
        })

        tpsSeries?.setData(tpsSeriesData)
      } else {
        setPriceScaleVisibility('right', false)
      }

      tools.reArrangeChart({ autosize: true })
      tools.prepareTooltip({
        topOffset: -30,
        darkMode: isDark.value,
        valueFormatter: (value, seriesKey) => {
          const unit = selectedMetric.value === 'powerUse' ? '' : 'h'

          return seriesKey === 'tps'
            ? `${numericDisplay(value / HELPER_NUMBER)} tps`
            : displayWattage(value / HELPER_NUMBER) + unit
        },
        customLineCallback: (pointX) => {
          const value = dailyTimeseries[pointX]?.value?.validator ?? ''
          return {
            unit: '#',
            label: 'Validators',
            value: value ? numericDisplay(value, 0) : '',
          }
        },
      })
      renderBottomChart(esgBottomChartRef, '#range-slider', isDark.value)
    }
  }

  const searchAsset = async (q: string) => {
    const api =
      selectedType.value === CLIMATE_METRIC_SLUGS.energyConsumption
        ? '/nodiens/api/v1/esg/energy-consumption/asset'
        : '/nodiens/api/v1/esg/carbon-emission/asset'

    return await $apiClient
      .GET(api, {
        params: {
          query: {
            ...(q && { terms: q }),
          },
        },
      })
      .then((res) => res.data?.payload?.data ?? [])
  }

  const handleAssetChange = (asset: AssetType) => {
    router.push(`/climate/${selectedType.value}/${asset?.slug}`)
  }

  watch(isMobile, (value) => {
    if (value) {
      setPriceScaleVisibility('left', false)
      setPriceScaleVisibility('right', false)
    } else {
      setPriceScaleVisibility('left', true)
      setPriceScaleVisibility('right', true)
    }
  })

  watch(isDark, (value) => {
    tools.setDark(value)
  })

  // Initiate selected asset and consensus mechanism
  watch(chartData, (newChartData) => {
    if (newChartData) {
      selectedAsset.value = {
        slug: newChartData.feeds.slug,
        symbol: newChartData.feeds.symbol,
        name: newChartData.feeds.name,
        logo: newChartData.feeds.logo,
      }

      consensusMechanism.value = [newChartData.consensusNames]
    }
  })

  watch([chartData, esgChartRef, esgBottomChartRef], ([newChartData, chartElement, sliderEl]) => {
    if (newChartData && chartElement && sliderEl) {
      initializeChart()
    }
  })

  watch(selectedType, (type) => {
    if (type) {
      navigateTo(`/climate/testing/${type}/${params.slug}`)
    }
  })

  watch(selectedMetric, (metric) => {
    if (chartInstance.value) {
      destroyChart()
    }
    router.replace({
      query: { metric },
    })
  })

  onUnmounted(() => {
    destroyChart()
  })

  return {
    esgChartRef,
    esgBottomChartRef,
    isClimateFeatureAllowed,
    isShareModalOpen,
    shareLink,
    selectedType,
    selectedMetric,
    selectedAsset,
    consensusMechanism,
    esgChartTypeOptionLabel,
    typeOptions,
    metricOptions,
    smallestSelectedAvgUnit,
    isLeftPriceScaleTitleVisible,
    isRightPriceScaleTitleVisible,
    isLoading,
    isChartEmpty,
    log,
    zoomViews,
    tools,
    searchAsset,
    handleAssetChange,
  }
}
