import type { v2Pricing } from '~/server/trpc/trpc'

export const usePricingText = () => {
  const formatPlan = (plan: v2Pricing, planName: string) => {
    const { features } = plan as unknown as {
      features: {
        max_assets: number
        add_on_price: number
        financial_metrics: any
        sentiment_metrics: any
        esg_climate_metrics: Record<string, boolean>
      }
    }

    const moodLabel = planName === 'ENTERPRISE' ? 'Institutional' : planName === 'PRO' ? 'Professional' : 'Basic'

    return [
      {
        name: `${features.max_assets} asset${features.max_assets > 1 ? 's' : ''} included`,
        value: true,
      },
      {
        name: `Additional assets starting from $${features.add_on_price.toFixed(2)} cad.`,
        value: true,
      },
      {
        name: 'Price, Volume, and Market Cap',
        value:
          features.financial_metrics.price &&
          features.financial_metrics.total_volume &&
          features.financial_metrics.market_cap,
      },
      {
        name: 'Community Metrics and Bots Scanner',
        value:
          features.sentiment_metrics.messages &&
          features.sentiment_metrics.bots_tracker &&
          features.sentiment_metrics.community_size,
      },
      {
        name: `Mood, Trust, and Vulgarity Index (${moodLabel})`,
        value:
          features.sentiment_metrics.mood_ranking &&
          features.sentiment_metrics.trust_ranking &&
          features.sentiment_metrics.vulgarity_index,
      },
      {
        name: 'Liquidity Concentration and Costs',
        value:
          features.financial_metrics.cex_liquidity_cost &&
          features.financial_metrics.dex_liquidity_cost &&
          features.financial_metrics.cex_liquidity_concentration &&
          features.financial_metrics.dex_liquidity_concentration,
      },
      {
        name: 'Volatility, Pairs, and Depeg Rates',
        value:
          features.financial_metrics.volatility_30_days &&
          features.financial_metrics.cex_pairs &&
          features.financial_metrics.dex_pairs &&
          features.financial_metrics.depeg_rata,
      },
      {
        name: 'ESG and Climate Metrics',
        value: Object.values(features.esg_climate_metrics).includes(true),
      },
    ]
  }

  return {
    formatPlan,
  }
}
