import { COIN_SIZE_LIST } from '~/constants/options'
import type { CoinFilter } from '~/constants/example/stablecoin'

export interface Size {
  id: string
  name: string
  compareSize: {
    cex: string
    dex: string
  }
  symbol?: string
}

export const useRadarLineChart = () => {
  const loading = useState(() => false)
  const stablecoinLinechartKey = useState(() => 0)
  const radarChartLoaded = useState(() => false)
  const selectedLabelIndex = useState(() => 0)
  const lineChartTitle = useState(() => '')
  const assetFound = useState<boolean>(() => false)
  const radarRef = useState<{
    getBase64: () => string
  }>()
  const radarData: any = useState()
  const isShareModalOpen = useState(() => false)
  const selectedSize = useState<Size>(() => COIN_SIZE_LIST[0] as Size)
  const isSelectedEmpty = computed(() => !coin1Filter.value.coin && !coin2Filter.value.coin && !coin3Filter.value.coin)

  const coin1Filter = useState<CoinFilter>('coin-1-filter', () => ({
    name: '',
    coin: undefined,
    type: 'coin-1',
    color: '#FF2323',
  }))

  const coin2Filter = useState<CoinFilter>('coin-2-filter', () => ({
    name: '',
    coin: undefined,
    type: 'coin-2',
    color: '#00B0FF',
  }))

  const coin3Filter = useState<CoinFilter>('coin-3-filter', () => ({
    name: '',
    coin: undefined,
    type: 'coin-3',
    color: '#3EA95B',
  }))

  const onGetRadarChartImage = (): string => {
    if (radarRef?.value) {
      return radarRef?.value?.getBase64()
    }
    return ''
  }

  const clearCoinFilters = () => {
    coin1Filter.value.coin = undefined
    coin2Filter.value.coin = undefined
    coin3Filter.value.coin = undefined
    stablecoinLinechartKey.value = 0
    selectedLabelIndex.value = 0
    lineChartTitle.value = ''
    assetFound.value = false
  }

  const valueUnitDecision = (metric?: string) => {
    switch (metric) {
      case 'dayVolumeCex':
      case 'dayVolumeDex':
      case 'marketCap':
      case 'price':
        return 'currency'
      case 'depegDays':
      case '':
        return 'percentage'
      default:
        return ''
    }
  }

  const valueDecimalDecision = (metric?: string): number | undefined => {
    if (metric === 'dexPairs') {
      return 0
    }

    if (metric === 'cexPairs') {
      return 0
    }

    if (metric === 'depegDays') {
      return 0
    }

    return undefined
  }

  return {
    loading,
    coin1Filter,
    coin2Filter,
    coin3Filter,
    radarChartLoaded,
    isSelectedEmpty,
    selectedLabelIndex,
    radarRef,
    lineChartTitle,
    radarData,
    stablecoinLinechartKey,
    isShareModalOpen,
    onGetRadarChartImage,
    selectedSize,
    clearCoinFilters,
    assetFound,
    valueUnitDecision,
    valueDecimalDecision,
  }
}
