import type { DeepPartial, HorzScaleOptions, IChart<PERSON><PERSON>, <PERSON>Width, Time, TimeChartOptions } from 'lightweight-charts'
import { PriceScaleMode, createChart } from 'lightweight-charts'
import { format, formatISO, isBefore, parse, parseISO, set, sub } from 'date-fns'
import jsPDF from 'jspdf'
import type { RangeSlider } from 'range-slider-input'
import rangeSlider from 'range-slider-input'
import type {
  addAreaDataType,
  addAreaOptType,
  addHistogramDataType,
  addHistogramOptType,
  addLineDataType,
  addLineOptType,
  addRangeAreaDataType,
  addRangeAreaOptType,
  addStackedBarsDataType,
  addStackedBarsOptType,
  Series,
} from './types/light-chart'
import { numericDisplay } from '~/helper/number-helper'
import { formatXApex } from '~/helper/string-helper'
import { HLCAreaSeries } from './custom-plugin/hlc-area-series'
import { StackedBarsSeries } from './custom-plugin/stacked-bars-series'

export default function useLightChart() {
  const lineStyleLayers: {
    [key: string]: {
      [key: string]: {
        color: string
        width: LineWidth
      }
    }
  } = {
    trust: {
      layer1: {
        color: '#00A210',
        width: 3,
      },
      layer2: {
        color: '#4FBD6D',
        width: 2,
      },
      layer3: {
        color: '#84E9A0',
        width: 1,
      },
    },
    mood: {
      layer1: {
        color: '#00A3FF',
        width: 3,
      },
      layer2: {
        color: '#4BC9FF',
        width: 2,
      },
      layer3: {
        color: '#B4E8FF',
        width: 1,
      },
    },
    stablecoin: {
      layer1: {
        color: '#FF2323',
        width: 3,
      },
      layer2: {
        color: '#00B0FF',
        width: 2,
      },
      layer3: {
        color: '#3EA95B',
        width: 1,
      },
    },
  }
  let timeLineData: { [key: string | number]: number }
  const zoomEnable = ref<boolean>(false)
  const log = ref<boolean>(false)
  const chart = shallowRef<IChartApi>()
  const bottomChart = shallowRef<IChartApi>()
  const bottomPanel = shallowRef<RangeSlider>()
  const milliFormat = shallowRef<boolean>(false)
  const series = ref<{ [key: string]: Series }>({})
  const containerRef = ref<HTMLElement>()
  const tooltipElement = ref<HTMLElement>()

  const zoomViews = useState<
    {
      id: string
      label: string
      active: boolean
      from: string | number
      to: string | number
    }[]
  >(() => [])

  const activeZoomId = computed(() => {
    return zoomViews.value.find((r) => r.active === true)?.id ?? 'all'
  })

  function setPriceScaleVisibility(side: 'left' | 'right', visible: boolean) {
    chart.value?.priceScale(side).applyOptions({
      visible,
    })
  }

  function setZoom(id: string) {
    if (chart.value) {
      const zoomArrId = zoomViews.value.findIndex((r) => r.id === id)
      const prevActive = zoomViews.value.findIndex((r) => r.active === true)

      if (zoomArrId === -1 || !zoomViews.value[zoomArrId]) {
        console.error('Zoom view not found')
        return
      }

      chart.value?.timeScale().setVisibleRange({
        from: milliFormat.value
          ? (Number(zoomViews.value[zoomArrId].from) as Time)
          : (zoomViews.value[zoomArrId].from as Time),
        to: milliFormat.value
          ? (Number(zoomViews.value[zoomArrId].to) as Time)
          : (zoomViews.value[zoomArrId].to as Time),
      })

      if (prevActive > -1 && zoomViews.value[prevActive]) {
        zoomViews.value[prevActive].active = false
      }
      zoomViews.value[zoomArrId].active = true

      const { from, to }: { from: string | number; to: string | number } = zoomViews.value.find(
        (zoom) => zoom.id === id,
      ) || { from: '', to: '' }
      bottomPanel.value?.value([
        milliFormat.value ? Number(from) : typeof from === 'string' ? new Date(from).getTime() : Number(from),
        milliFormat.value ? Number(to) : typeof to === 'string' ? new Date(to).getTime() : Number(to),
      ])
    } else {
      console.error('Chart is not initialized')
    }
  }

  function handleLog() {
    chart.value?.priceScale('right').applyOptions({
      mode: log.value ? PriceScaleMode.Normal : PriceScaleMode.Logarithmic,
      autoScale: true,
    })
    chart.value?.priceScale('left').applyOptions({
      mode: log.value ? PriceScaleMode.Normal : PriceScaleMode.Logarithmic,
      autoScale: true,
    })
    log.value = !log.value
  }

  function resizeHandler() {
    if (!chart.value || !containerRef.value) {
      return
    }

    const dimenstions = containerRef.value.getBoundingClientRect()
    chart.value.resize(dimenstions.width, dimenstions.height)
  }

  function setDark(darkMode: boolean) {
    if (chart.value) {
      chart.value.applyOptions({
        layout: {
          background:
            darkMode === true
              ? {
                  color: '#000000',
                }
              : {
                  color: '#FFFFFF',
                },
        },
      })

      if (tooltipElement.value) {
        tooltipElement.value.style.background = darkMode ? '#5B5B5B' : '#EBEBEB'
        tooltipElement.value.style.color = darkMode ? '#fff' : '#000'
      }
    }

    if (bottomChart.value) {
      bottomChart.value.applyOptions({
        layout: {
          background:
            darkMode === true
              ? {
                  color: '#000000',
                }
              : {
                  color: '#FFFFFF',
                },
        },
      })
    }
  }

  function renderChart(
    container: typeof containerRef,
    options?: DeepPartial<TimeChartOptions> & {
      latestCalculation?: string
      startCalculation?: string
      milliFormat?: boolean
    },
    darkMode?: boolean,
  ) {
    if (container.value) {
      milliFormat.value = options?.milliFormat ?? false
      containerRef.value = container.value
      const instance = createChart(container.value, options)
      instance.applyOptions({
        layout: {
          attributionLogo: false,
          textColor: '#7C7C7C',
          background:
            darkMode === true
              ? {
                  color: '#000000',
                }
              : {
                  color: '#FFFFFF',
                },
        },
      })
      chart.value = instance

      if (options?.latestCalculation || options?.startCalculation) {
        const date = new Date()
        zoomViews.value = [
          {
            id: '7d',
            label: '7D',
            active: false,
            from: format(sub(date, { days: 7 }), options?.milliFormat ? 'T' : 'yyyy-MM-dd'),
            to: format(date, options?.milliFormat ? 'T' : 'yyyy-MM-dd'),
          },
          {
            id: '1m',
            label: '1M',
            active: false,
            from: format(sub(date, { months: 1 }), options?.milliFormat ? 'T' : 'yyyy-MM-dd'),
            to: format(date, options?.milliFormat ? 'T' : 'yyyy-MM-dd'),
          },
          {
            id: '3m',
            label: '3M',
            active: false,
            from: format(sub(date, { months: 3 }), options?.milliFormat ? 'T' : 'yyyy-MM-dd'),
            to: format(date, options?.milliFormat ? 'T' : 'yyyy-MM-dd'),
          },
          {
            id: 'ytd',
            label: 'YTD',
            active: false,
            from: format(set(date, { month: 0, date: 1 }), options?.milliFormat ? 'T' : 'yyyy-MM-dd'),
            to: format(date, options?.milliFormat ? 'T' : 'yyyy-MM-dd'),
          },
          {
            id: '1y',
            label: '1Y',
            active: false,
            from: format(sub(date, { years: 1 }), options?.milliFormat ? 'T' : 'yyyy-MM-dd'),
            to: format(date, options?.milliFormat ? 'T' : 'yyyy-MM-dd'),
          },
          {
            id: 'all',
            label: 'ALL',
            active: true,
            from: options?.startCalculation
              ? format(
                  formatISO(options.startCalculation as unknown as number) as unknown as number,
                  options?.milliFormat ? 'T' : 'yyyy-MM-dd',
                )
              : format(sub(date, { years: 1 }), options?.milliFormat ? 'T' : 'yyyy-MM-dd'),
            to: format(date, options?.milliFormat ? 'T' : 'yyyy-MM-dd'),
          },
        ]
      }

      zoomEnable.value = true
      return instance
    } else {
      console.error('Chart target not found')
      return undefined
    }
  }

  function highestSeries() {
    const key = Object.keys(series.value).reduce((res: [string, number] | null, value) => {
      if (series.value[value]?.options().visible === false) {
        return res
      }

      const serie = series.value[value]?.data()

      if (!serie) {
        return res
      }

      if (res === null) {
        res = [value, serie.length]
      }

      if (serie.length > res[1]) {
        res = [value, serie.length]
      }

      return res
    }, null)

    if (key) {
      return series.value[key[0]]
    }

    return null
  }

  function transformDailyToMonthly(data: unknown) {
    const monthlyData = {} as Record<string, { time: string; value: number }>
    ;(data as { time: string }[]).forEach((item: { time: string }) => {
      const date = milliFormat.value ? new Date(item.time) : parseISO(item.time)
      const yearMonth = format(date, 'yyyy-MM')

      if (
        !monthlyData[yearMonth] ||
        isBefore(
          date,
          milliFormat.value ? new Date(monthlyData[yearMonth].time) : parseISO(monthlyData[yearMonth].time),
        )
      ) {
        monthlyData[yearMonth] = { ...item, value: (item as { time: string; value: number }).value ?? 0 }
      }
    })

    return Object.values(monthlyData).sort((a, b) => {
      const _a = milliFormat.value ? new Date(a.time) : parseISO(a.time)
      const _b = milliFormat.value ? new Date(b.time) : parseISO(b.time)
      return _a.getTime() - _b.getTime()
    })
  }

  function renderBottomChart(container: typeof containerRef, panel: string, darkMode?: boolean) {
    const topSeries = highestSeries()

    if (bottomChart.value) {
      bottomChart.value.remove()
      bottomChart.value = undefined
      bottomPanel.value?.removeGlobalEventListeners()
      bottomPanel.value = undefined
    }

    if (bottomChart.value === undefined && topSeries) {
      const timelines = toRaw(topSeries.data())
      const monthlyTimelines = transformDailyToMonthly(timelines)

      bottomChart.value = createChart(container.value!, {
        autoSize: true,
        timeScale: {
          visible: true,
          fixLeftEdge: true,
          fixRightEdge: true,
          lockVisibleTimeRangeOnResize: true,
          tickMarkFormatter: (time: string) => formatXApex(time),
        },
        rightPriceScale: {
          visible: false,
        },
        handleScale: {
          mouseWheel: false,
          axisDoubleClickReset: false,
          axisPressedMouseMove: false,
          pinch: false,
        },
        handleScroll: {
          horzTouchDrag: false,
          mouseWheel: false,
          pressedMouseMove: false,
          vertTouchDrag: false,
        },
        grid: {
          horzLines: {
            visible: false,
          },
          vertLines: {
            color: '#7C7C7C',
          },
        },
        layout: {
          textColor: '#7C7C7C',
          attributionLogo: false,
          background:
            darkMode === true
              ? {
                  color: '#000000',
                }
              : {
                  color: '#FFFFFF',
                },
        },
        crosshair: {
          vertLine: {
            labelVisible: false,
            visible: false,
          },
          horzLine: {
            visible: false,
            labelVisible: false,
          },
        },
        localization: {
          locale: 'en-US',
        },
      })
      const serie = bottomChart.value.addHistogramSeries({
        visible: false,
      })
      serie.setData(
        monthlyTimelines.map((d) => ({
          time: d.time,
          value: d.value,
        })),
      )
      bottomChart.value?.timeScale().fitContent()
      if (panel) {
        setTimeout(() => {
          const min = milliFormat.value
            ? timelines[0]?.time
            : (parse((timelines[0]?.time as string) ?? '', 'yyyy-MM-dd', new Date()).getTime() ?? 0)
          const max = milliFormat.value
            ? timelines[timelines.length - 1]?.time
            : (parse((timelines[timelines.length - 1]?.time ?? '') as string, 'yyyy-MM-dd', new Date()).getTime() ?? 0)
          bottomPanel.value = rangeSlider(document.querySelector(panel!)!, {
            step: 'any',
            onInput(value, userInteraction) {
              if (chart.value) {
                if (!userInteraction) {
                  return
                }

                if (activeZoomId.value !== 'all') {
                  setZoom('all')
                }
                chart.value.timeScale().setVisibleRange({
                  from: milliFormat.value ? (value[0] as Time) : format(new Date(value[0]), 'yyyy-MM-dd'),
                  to: milliFormat.value ? (value[1] as Time) : format(new Date(value[1]), 'yyyy-MM-dd'),
                })
              }
            },
            value: [Number(min), Number(max)],
            min: Number(min),
            max: Number(max),
          })
        }, 300)
      }
    }
  }

  function fitContent() {
    if (!chart.value) {
      return
    }
    chart.value.timeScale().fitContent()
  }

  function destroyChart() {
    chart.value?.remove()
    chart.value = undefined
    tooltipElement.value?.remove()
    tooltipElement.value = undefined
  }

  function reArrangeChart(opt?: { timeScaleOptions?: DeepPartial<HorzScaleOptions>; autosize?: boolean }) {
    if (opt?.timeScaleOptions) {
      chart.value?.timeScale().applyOptions(opt.timeScaleOptions)
    }

    fitContent()

    if (opt?.autosize === true) {
      window.addEventListener('resize', resizeHandler)
    }
  }

  function prepareTimelineData() {
    const keys = Object.keys(series.value)
    if (Object.keys(series.value).length === 1 && timeLineData === undefined && keys[0]) {
      const timelines = series.value[keys[0]]?.data() as { time: string | number; value: number }[]
      if (timelines) {
        timeLineData = {}
        timelines.forEach((timeline: { time: string | number; value: number }) => {
          timeLineData[timeline.time] = timeline.value
        })
      }
    }
  }

  function addLineSeries(name: string, lineOpt?: addLineOptType): addLineDataType {
    const instance = chart.value?.addLineSeries(lineOpt)
    series.value[name] = instance
    return instance
  }

  function addAreaSeries(name: string, serieOpt?: addAreaOptType): addAreaDataType {
    const instance = chart.value?.addAreaSeries(serieOpt)
    series.value[name] = instance
    return instance
  }

  function addHistogramSeries(name: string, serieOpt?: addHistogramOptType): addHistogramDataType {
    const instance = chart.value?.addHistogramSeries(serieOpt)
    series.value[name] = instance
    return instance
  }

  function addAreaRangeSeries(name: string, serieOpt?: addRangeAreaOptType): addRangeAreaDataType {
    const customSeriesView = new HLCAreaSeries()
    const instance = chart.value?.addCustomSeries(customSeriesView, serieOpt)
    series.value[name] = instance
    return instance
  }

  function addStackedBarsSeries(name: string, serieOpt?: addStackedBarsOptType): addStackedBarsDataType {
    const customSeriesView = new StackedBarsSeries()
    const instance = chart.value?.addCustomSeries(customSeriesView, serieOpt)
    series.value[name] = instance
    return instance
  }

  function prepareTooltip(opt?: {
    topOffset?: number
    leftOffset?: number
    darkMode?: boolean
    valueFormatter?: (value: number, seriesKey: string) => string
    customLineCallback?: (pointX: number) => { unit?: string; label: string; value: string }
  }) {
    if (containerRef.value) {
      const chartContainer = containerRef.value.querySelector('table tr')?.querySelectorAll('td')[1]

      // Create and style the tooltip html element
      if (!tooltipElement.value) {
        tooltipElement.value = document.createElement('div')
        tooltipElement.value.setAttribute(
          'style',
          "position: absolute; display: none; padding: 8px; box-sizing: border-box; font-size: 12px; text-align: left; z-index: 9; top: 0px; left: 0px; pointer-events: none; border-radius: 4px;font-family: -apple-system, BlinkMacSystemFont, 'Trebuchet MS', Roboto, Ubuntu, sans-serif; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;",
        )
        tooltipElement.value.style.background = opt?.darkMode ? '#5B5B5B' : '#EBEBEB'
        tooltipElement.value.style.color = opt?.darkMode ? '#fff' : '#000'
        tooltipElement.value.style.boxShadow = '0px 1px 12px 0px #00000033'

        // Add the tooltip into the container
        if (chartContainer) {
          chartContainer.style.position = 'relative'
          chartContainer.appendChild(tooltipElement.value)
        } else {
          containerRef.value.style.position = 'relative'
          containerRef.value.appendChild(tooltipElement.value)
        }
      }

      if (tooltipElement.value) {
        chart.value?.subscribeCrosshairMove((param) => {
          if (
            param.point === undefined ||
            !param.time ||
            param.point.x < 0 ||
            param.point.x > containerRef.value!.clientWidth ||
            param.point.y < 0 ||
            param.point.y > containerRef.value!.clientHeight
          ) {
            tooltipElement.value!.style.display = 'none'
          } else {
            let topValue: unknown
            let topSerie: Series | undefined
            tooltipElement.value!.innerHTML = ''

            if (tooltipElement.value!.querySelector('#chartTime') === null && param?.time) {
              tooltipElement.value!.innerHTML += `
                <p id="chartTime" class="font-normal whitespace-nowrap">${format(param.time as unknown as Date, 'MMM dd, yyyy')}</p>
              `
            }

            Object.keys(series.value).forEach((key) => {
              const serie = series.value[key]
              const serieOptions = serie?.options() as { color?: string; lineColor?: string; title?: string }
              if (serie && serie?.options().visible) {
                const entries = serie.data()
                const findValue = (target: number): unknown => {
                  // Find the values from entries using iteration
                  for (let i = 0; i < entries.length; i++) {
                    const time = entries[i]?.time as number | undefined
                    if (time === undefined) {
                      continue
                    }
                    if (time === target) {
                      return entries[i]
                    }
                    // If previous time is greater than target, return the previous entry
                    else if (time > target) {
                      return i > 0 ? entries[i - 1] : undefined
                    }
                  }

                  // This was case if we don't return anything because the last entry is less than the target
                  return entries.length > 0 && ((entries[entries.length - 1]?.time ?? 0) as number) < target
                    ? entries[entries.length - 1]
                    : undefined
                }
                const data = findValue(param.time as number) as
                  | { value?: number; customValues?: { name?: string }; low?: number; high?: number; values?: number[] }
                  | undefined

                if (typeof data?.value !== 'undefined') {
                  const value = opt?.valueFormatter?.(data.value, key) ?? numericDisplay(data.value)

                  tooltipElement.value!.innerHTML += `
                    <div class="flex justify-between gap-1">
                      <div class="flex items-center gap-1 font-normal whitespace-nowrap">
                        <div class="w-3 h-3 rounded-full" style="background: ${serieOptions.color ?? serieOptions.lineColor}"></div>
                        <span>${data?.customValues?.name ?? serieOptions.title}: </span>
                      </div>
                      <span class="flex-auto">${value}</span>
                    </div>
                  `

                  if (!topValue) {
                    topValue = data.value
                    topSerie = serie
                  } else if (
                    topValue &&
                    typeof data.value === 'number' &&
                    typeof topValue === 'number' &&
                    data.value > topValue
                  ) {
                    topValue = data.value
                    topSerie = serie
                  }
                }

                // AreaRangeSeries Tooltip
                if (typeof data?.low !== 'undefined' && typeof data?.high !== 'undefined') {
                  const lowValue = opt?.valueFormatter?.(data.low, key) ?? numericDisplay(data.low)
                  const highValue = opt?.valueFormatter?.(data.high, key) ?? numericDisplay(data.high)

                  tooltipElement.value!.innerHTML += `
                    <div class="flex justify-between gap-1">
                      <div class="flex items-center gap-1 font-normal whitespace-nowrap">
                        <div class="w-3 h-3 rounded-full" style="background: ${serieOptions?.color ?? serieOptions?.lineColor}"></div>
                        <span>${data?.customValues?.name ?? serieOptions.title}: </span>
                      </div>

                      <span class="flex-auto">${lowValue} - ${highValue}</span>
                    </div>
                  `
                }

                // StackedBarsSeries Tooltip
                if (typeof data?.values !== 'undefined' && Array.isArray(data.values)) {
                  // used to show labels in reverse order
                  const reversedValuesIndexes = [...data.values.keys()].reverse()

                  reversedValuesIndexes.forEach((index) => {
                    const value = data?.values?.[index]
                    tooltipElement.value!.innerHTML += `
                      <div class="flex justify-between gap-1">
                        <div class="flex items-center gap-1 font-normal whitespace-nowrap">
                          <div class="w-3 h-3 rounded-full" style="background: ${(serieOptions as { colors?: string[]; lineColor?: string })?.colors?.[index] ?? serieOptions?.lineColor}"></div>
                          <span>${data?.customValues?.name?.[index] ?? '-'}: </span>
                        </div>
                        <span class="flex-auto">${opt?.valueFormatter?.(value ?? 0, key) ?? numericDisplay(value ?? 0)}</span>
                      </div>
                    `
                  })
                }
              }
            })

            if (
              typeof param?.logical !== 'undefined' &&
              opt?.customLineCallback &&
              tooltipElement.value!.querySelector('#custom-line') === null
            ) {
              const customLine = opt.customLineCallback(param.logical)
              if (customLine.value !== '') {
                tooltipElement.value!.innerHTML += `
                  <div id="custom-line" class="flex justify-between gap-1">
                    <div class="flex items-center gap-1 font-normal whitespace-nowrap">
                      ${customLine?.unit ? `<span class="w-3 h-3 flex items-center justify-center">${customLine.unit}</span>` : ''}
                      <span>${customLine.label ?? '-'}: </span>
                    </div>

                    <span class="flex-auto">${customLine.value}</span>
                  </div>
                `
              }
            }

            tooltipElement.value!.style.display = 'block'
            const coordinate =
              topSerie && typeof topSerie.priceToCoordinate === 'function' && typeof topValue === 'number'
                ? topSerie.priceToCoordinate(topValue)
                : null
            if (coordinate === null) {
              return
            }

            const generatedTooltipWidth = tooltipElement.value!.clientWidth
            const generatedTooltipHeight = tooltipElement.value!.clientHeight

            let shiftedXCoordinate = param.point.x - generatedTooltipWidth / 2 // Move x coordinate Tooltip on the center of hovered point
            let shiftedYCoordinate = coordinate - generatedTooltipHeight // Move y coordinate Tooltip on the top of hovered point

            // Apply Custom Offset
            if (typeof opt?.leftOffset !== 'undefined') {
              shiftedXCoordinate += opt.leftOffset
            }
            if (typeof opt?.topOffset !== 'undefined') {
              shiftedYCoordinate += opt.topOffset
            }

            const DEFAULT_OFFSET = 12
            const chartContainerWidth = chartContainer?.clientWidth ?? containerRef.value!.clientWidth

            // move tooltip to the left/right side of hovered point
            if (shiftedXCoordinate <= 0) {
              shiftedXCoordinate = param.point.x + DEFAULT_OFFSET
            } else if (shiftedXCoordinate + generatedTooltipWidth >= chartContainerWidth) {
              shiftedXCoordinate = param.point.x - generatedTooltipWidth - DEFAULT_OFFSET
            }

            tooltipElement.value!.style.left = `${shiftedXCoordinate}px`
            tooltipElement.value!.style.top = `${shiftedYCoordinate - DEFAULT_OFFSET}px`

            const tooltipTopPosition = Number(tooltipElement.value?.style.top.split('px').join('') ?? 0)
            if (tooltipTopPosition < 0) {
              tooltipElement.value!.style.top = '0px'
            }
          }
        })
      }
    }
  }

  function downloadPdf() {
    const screenshot = chart.value?.takeScreenshot()

    if (screenshot) {
      const pdf = new jsPDF({
        orientation: 'landscape',
      })
      pdf.addImage(screenshot, 'PNG', 10, 10, 190, 90)

      pdf.save('chart_export.pdf')
    }
  }

  function downloadImage(type: string) {
    const screenshot = chart.value?.takeScreenshot()
    if (screenshot) {
      screenshot.toBlob((blob) => {
        if (blob) {
          const link = document.createElement('a')
          link.href = URL.createObjectURL(blob)
          link.download = `chart_export.${type}`

          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
        }
      })
    }
  }

  return {
    chart,
    zoomViews,
    log,
    lineStyleLayers,
    renderChart,
    destroyChart,
    addLineSeries,
    addAreaSeries,
    addAreaRangeSeries,
    addHistogramSeries,
    addStackedBarsSeries,
    setPriceScaleVisibility,
    prepareTimelineData,
    renderBottomChart,
    tools: {
      reArrangeChart,
      prepareTooltip,
      handleLog,
      setZoom,
      downloadPdf,
      fitContent,
      downloadImage,
      setDark,
    },
  }
}
