import { HashpackService } from '~/utils/hashpack-service'

import type { HashpackNetwork } from '~/utils/hashpack-service'

let isInitialized = false

export function useHashpackService() {
  const { $config } = useNuxtApp()
  const hashpackService = HashpackService.getInstance($config.public.hashpackNetwork as keyof typeof HashpackNetwork)
  const pairingData = ref(hashpackService.pairingData)

  onMounted(() => {
    window.addEventListener('hashpack-pairing-update', ((event: CustomEvent) => {
      pairingData.value = event.detail
    }) as EventListener)
  })

  onUnmounted(() => {
    window.removeEventListener('hashpack-pairing-update', (() => null) as EventListener)
  })

  const initializeOnce = async () => {
    if (!isInitialized) {
      isInitialized = true
      await hashpackService.initialize()
    }
  }

  return {
    pairingData,
    initialize: initializeOnce,
    connectWallet: hashpackService.connectWallet.bind(hashpackService),
    disconnect: hashpackService.disconnect.bind(hashpackService),
    getSigner: hashpackService.getSigner.bind(hashpackService),
  }
}
