export const useTable = () => {
  const HIGHLIGHT_CLASSNAME = 'dark:!bg-neutrals-500 !bg-neutrals-200'

  const isColumnSorted = (sortKey: string, isDefault = false) => {
    const route = useRoute()
    const query = route.query
    if (query.rank === sortKey || (!query.rank && isDefault)) {
      return HIGHLIGHT_CLASSNAME
    }
    return ''
  }

  return {
    isColumnSorted,
    HIGHLIGHT_CLASSNAME,
  }
}
