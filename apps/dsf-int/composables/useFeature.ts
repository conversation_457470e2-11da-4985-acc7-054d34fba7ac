import { useUserStore } from '~/stores/profile'
import { useFeatureStore } from '~/stores/features'

export const useFeature = () => {
  const { userProfile } = useUserStore()
  const { currentUserPlan } = storeToRefs(useFeatureStore())

  const { $apiClient } = useNuxtApp()
  const loading = ref(false)

  const getUserPlan = async () => {
    try {
      loading.value = true
      const res = await $apiClient.GET('/nodiens/api/v1/plan/current-subscription')
      currentUserPlan.value = res?.data?.payload?.features
    } catch (error) {
      console.error('Error getting user plan:', error)
      currentUserPlan.value = null
    } finally {
      loading.value = false
    }
  }

  const isFeatureAllowed = (featurePath: string) => {
    return true
    if (userProfile?.excludedPaywall) {
      return true
    }

    if (loading.value && !currentUserPlan.value) {
      return false
    }

    if (!featurePath || !currentUserPlan.value) {
      return false
    }

    try {
      const pathParts = featurePath.split('.')
      if (!pathParts[0] || !pathParts[1]) {
        return false
      }

      const current = toRaw(currentUserPlan.value)
      return current?.[pathParts[0]]?.[pathParts[1]]
    } catch (error) {
      console.error('Error checking feature:', error)
      return false
    }
  }

  onMounted(() => {
    getUserPlan()
  })

  return {
    isFeatureAllowed,
  }
}
