const cmsTextHandler = () => {
  const isShadeOfWhiteRGB = (r: number, g: number, b: number) => {
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255
    return luminance > 0.9 // A luminance > 0.9 is considered white
  }

  const invertRGBColor = (r: number, g: number, b: number) => {
    return `rgb(${255 - r}, ${255 - g}, ${255 - b})`
  }

  const textColorWatcher = (element: string, isDarkMode: boolean) => {
    setTimeout(() => {
      document.querySelectorAll<HTMLElement>(`${element} *`).forEach((el) => {
        const inlineColor = el.style.color
        if (inlineColor && inlineColor.startsWith('rgb')) {
          const rgbValues = inlineColor
            .replace(/[^\d,]/g, '')
            .split(',')
            .map(Number)

          const [r = 0, g = 0, b = 0] = rgbValues

          if (isDarkMode) {
            if (!isShadeOfWhiteRGB(r, g, b)) {
              el.style.color = invertRGBColor(r, g, b)
            }
          } else {
            if (isShadeOfWhiteRGB(r, g, b)) {
              el.style.color = invertRGBColor(r, g, b)
            }
          }
        }
      })
    }, 1)
  }

  const counterFontWeightHandler = (element: string) => {
    document.querySelectorAll<HTMLElement>(`${element} li`).forEach((li) => {
      const p = li.querySelector('p')
      if (p) {
        const hasStrong = p.querySelector('strong') !== null

        if (hasStrong) {
          li.style.setProperty('--before-font-weight', 'bold')
        } else {
          li.style.setProperty('--before-font-weight', 'normal') // Or any default weight
        }
      }
    })
  }

  return {
    textColorWatcher,
    counterFontWeightHandler,
  }
}

export default cmsTextHandler
