import { format } from 'date-fns'
import html2canvas from 'html2canvas'
import jsPDF from 'jspdf'

// Assets
import lightLogo from '~/assets/media/chart-watermark.png'
import darkLogo from '~/assets/media/chart-watermark-night.png'

// Types
import type { IChartApi } from 'lightweight-charts'

interface Asset {
  logo: string
  name: string
  symbol: string
}

interface Legend {
  name: string
  color: string
}

interface ExportMetadata {
  asset: Asset
  legends: Legend[]
}

interface ExportOptions {
  fileName?: string
  format: 'pdf' | 'png' | 'jpg'
}

export const useChartExport = () => {
  const isExporting = ref(false)

  const { isDark } = useAppColorMode()

  const generateTitleElement = (title?: string) => {
    const titleElement = document.createElement('p')
    titleElement.textContent = title || `Downloaded from Nodiens, ${format(new Date(), 'MMM dd, yyyy kk:mm zzz')}`
    titleElement.classList.add('text-lg')
    return titleElement
  }

  const generateAssetElement = (asset: Asset) => {
    const assetContainer = document.createElement('div')
    assetContainer.classList.add('flex', 'items-center', 'gap-2', 'w-fit')

    const img = document.createElement('img')
    img.src = asset.logo
    img.alt = 'asset-logo'
    img.classList.add('w-6', 'h-auto')
    assetContainer.appendChild(img)

    const assetNameElement = document.createElement('p')
    assetNameElement.textContent = asset.name ?? '-'
    assetNameElement.classList.add('pb-3.5')
    assetContainer.appendChild(assetNameElement)

    const assetSymbolElement = document.createElement('span')
    assetSymbolElement.textContent = `(${asset.symbol?.toUpperCase() ?? '-'})`
    assetSymbolElement.classList.add('text-neutrals-300', 'ml-1')
    assetNameElement.appendChild(assetSymbolElement)
    return assetContainer
  }

  const generateLegendsElement = (legends: Legend[]) => {
    const legendsContainer = document.createElement('div')
    legendsContainer.classList.add('flex', 'flex-wrap', 'items-center', 'justify-end', 'gap-2')
    legends.forEach((legend) => {
      const legendItem = document.createElement('div')
      legendItem.classList.add('border-neutrals-300', 'flex', 'items-center', 'gap-2', 'rounded', 'border', 'p-2')

      const legendColor = document.createElement('div')
      legendColor.classList.add('h-2', 'w-2', 'rounded-full')
      legendColor.style.backgroundColor = legend.color
      legendItem.appendChild(legendColor)

      const legendName = document.createElement('span')
      legendName.textContent = legend.name
      legendName.classList.add('text-neutrals-300', 'text-base', 'dark:text-white', '-mt-4')
      legendItem.appendChild(legendName)

      legendsContainer.appendChild(legendItem)
    })
    return legendsContainer
  }

  const getChartScreenshot = (instance?: IChartApi) => {
    if (!instance) return undefined

    instance.timeScale().applyOptions({ visible: true })
    const screenshot = instance.takeScreenshot()
    instance.timeScale().applyOptions({ visible: false })

    return screenshot
  }

  const downloadImage = (dataUrl: string, fileName: string): void => {
    const link = document.createElement('a')
    link.href = dataUrl
    link.download = fileName
    link.click()
  }

  const exportFile = async (
    element: HTMLElement,
    options: ExportOptions,
    ext: 'pdf' | 'png' | 'jpg',
  ): Promise<void> => {
    const postfix = `${format(new Date(), 'yyyyMMddHHmm')}_(Nodiens)`
    const fileName = `${options.fileName || 'chart-export'}_${postfix}`

    const canvas = await html2canvas(element, {
      scale: ext === 'pdf' ? 1 : 2, // Decreased for PDF to reduce the file size
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      logging: false,
    })

    const imgData = canvas.toDataURL(`image/${ext}`, 0.9)
    console.log(canvas.width, canvas.height)
    if (ext === 'pdf') {
      const pdf = new jsPDF({
        orientation: 'landscape',
        unit: 'px',
        format: [canvas.width, canvas.height],
      })

      pdf.addImage(imgData, 'PNG', 0, 0, canvas.width, canvas.height)
      pdf.save(fileName + '.pdf')
      return
    }

    const imageData = canvas.toDataURL(`image/${ext}`, 0.9)
    downloadImage(imageData, fileName + '.' + ext)
  }

  const createExportWrapper = (screenshot: HTMLCanvasElement, metadata: ExportMetadata): HTMLElement => {
    const wrapper = document.createElement('div')
    wrapper.className = 'chart-export-wrapper'
    wrapper.classList.add(
      'fixed',
      '-top-[9999px]',
      '-left-[9999px]',
      '-left-[9999px]',
      'w-[1648px]',
      'p-8',
      'bg-white',
      'dark:bg-black',
      'box-border',
    )

    const headerContainer = document.createElement('div')
    headerContainer.classList.add('flex', 'justify-between', 'gap-5')

    // Add Asset
    const leftContainer = document.createElement('div')
    leftContainer.classList.add('flex-1')
    const assetElement = generateAssetElement(metadata.asset)
    leftContainer.appendChild(assetElement)

    // Add Description
    const titleElement = generateTitleElement()
    leftContainer.appendChild(titleElement)

    // Add legends
    const rightContainer = document.createElement('div')
    rightContainer.classList.add('flex-1')
    const legendsContainer = generateLegendsElement(metadata.legends)
    rightContainer.appendChild(legendsContainer)

    headerContainer.appendChild(leftContainer)
    headerContainer.appendChild(rightContainer)
    wrapper.appendChild(headerContainer)

    // Add chart screenshot
    const chartContainer = document.createElement('div')
    screenshot.style.maxWidth = '100%'
    chartContainer.classList.add('relative', 'mt-8')
    chartContainer.appendChild(screenshot)
    wrapper.appendChild(chartContainer)

    // Add brand log
    const brand = document.createElement('img')
    brand.src = isDark.value ? darkLogo : lightLogo
    brand.alt = 'Nodiens Logo Dark'
    brand.classList.add(
      'absolute',
      'top-1/2',
      'left-1/2',
      'transform',
      '-translate-x-1/2',
      '-translate-y-1/2',
      'pointer-events-none',
      'z-10',
      'block',
    )
    brand.style.width = '50%'
    brand.style.maxWidth = '100%'
    brand.style.height = 'auto'
    chartContainer.appendChild(brand)

    return wrapper
  }

  const exportChart = async (instance: IChartApi, metadata: ExportMetadata, options: ExportOptions): Promise<void> => {
    isExporting.value = true

    try {
      const screenshot = getChartScreenshot(instance)
      if (!screenshot) {
        throw new Error('Failed to capture chart screenshot')
      }

      const exportElement = createExportWrapper(screenshot, metadata)
      document.body.appendChild(exportElement)

      await exportFile(exportElement, options, options.format)

      document.body.removeChild(exportElement)
    } catch (error) {
      console.error('Export failed:', error)
      throw error
    } finally {
      isExporting.value = false
    }
  }

  return {
    isExporting,
    exportChart,
  }
}
