export const useDeviceScreen = () => {
  const isMobile = ref(false)
  const isTablet = ref(false)
  const isDesktop = ref(false)
  const isLargeDesktop = ref(false)

  const checkBreakpoint = () => {
    if (typeof window !== 'undefined') {
      const width = window.innerWidth
      isMobile.value = width <= 768
      isTablet.value = width > 768 && width <= 1024
      isDesktop.value = width >= 1024 && width <= 1440
      isLargeDesktop.value = width > 1440
    }
  }

  onMounted(() => {
    checkBreakpoint()
    window.addEventListener('resize', checkBreakpoint)
  })

  onBeforeUnmount(() => {
    window.removeEventListener('resize', checkBreakpoint)
  })

  return {
    isMobile,
    isTablet,
    isDesktop,
    isLargeDesktop,
  }
}
