import { useR<PERSON>r<PERSON><PERSON><PERSON><PERSON> } from '~/composables/useRadarL<PERSON><PERSON>hart'
import { Position } from '@vue-flow/core'

// Constants
import { HTTP_STATUS } from '~/constants/error'

// Types
import type { CoinFilter } from '~/constants/example/stablecoin'
type DecisionTreeKey = 'ipGovern' | 'votingProcess' | 'decisionMaking'

export const useIpGovOrdinalTree = () => {
  const { coin1Filter, coin2Filter, coin3Filter } = useRadarLineChart()
  const toast = useToast()
  const { $apiClient } = useNuxtApp()

  const { data: treeData } = useLazyAsyncData(async () => {
    const { response, data } = await $apiClient.GET('/nodiens/api/v1/esg/decentralisation/tree/{metric}', {
      params: {
        path: { metric: 'ipGovOrdinal' },
      },
    })

    if (response.status === HTTP_STATUS.INTERNAL_SERVER_ERROR) {
      toast.add({
        title: 'Error',
        description: 'Something went wrong. Please try again later.',
        icon: 'i-heroicons-exclamation-triangle-20-solid',
        color: 'red',
      })
    }

    if (response.status !== HTTP_STATUS.OK) {
      toast.add({
        title: 'Error',
        description: 'Failed to fetch IP governance ordinal tree data.',
        color: 'red',
      })
    }

    return data?.payload || null
  })

  const _ipGovDecisionTree = computed(() => {
    return treeData.value?.decisionTree ?? []
  })

  const coinList = computed<CoinFilter[]>(() => {
    const list = [coin1Filter.value, coin2Filter.value, coin3Filter.value]
    return list.reduce<CoinFilter[]>((acc, curr) => {
      if (curr.coin) {
        acc.push(curr)
      }
      return acc
    }, [])
  })

  const checkColor = (key: DecisionTreeKey, value: boolean) => {
    return coinList.value?.reduce<string[]>((acc, curr) => {
      if (!curr.coin) {
        return acc
      }
      const asset = _ipGovDecisionTree.value.find((x) => x.name === curr.coin?.name)
      if (!asset) {
        console.warn(`${curr.coin.name} is not found in list.`)
      }
      if (asset?.[key] === value) {
        acc.push(curr.color)
      }
      return acc
    }, [])
  }

  const nodes = computed(() => {
    return [
      {
        id: '1',
        type: 'input',
        data: { label: "IP's are governed by?" },
        width: 'auto',
        position: { x: 210, y: 5 },
      },
      {
        id: '2-1',
        data: { label: 'Community', sources: [Position.Left, Position.Bottom] },
        width: 'auto',
        position: { x: 130, y: 100 },
        type: 'multipleSourceHandle',
      },
      {
        id: '2-2',
        data: { label: 'Single Entity', sources: [Position.Right, Position.Bottom] },
        width: 'auto',
        position: { x: 360, y: 100 },
        type: 'multipleSourceHandle',
      },
      {
        id: '3-1',
        data: { label: 'Voting Process' },
        width: 'auto',
        position: { x: 90, y: 200 },
      },
      {
        id: '3-2',
        data: { label: 'Decision Making Structure' },
        width: 'auto',
        position: { x: 340, y: 200 },
      },
      {
        id: '4-1',
        data: { label: 'Yes' },
        width: 'auto',
        position: { x: 70, y: 300 },
      },
      {
        id: '4-2',
        data: { label: 'No' },
        width: 'auto',
        position: { x: 200, y: 300 },
      },
      {
        id: '4-3',
        data: { label: 'Committee' },
        width: 'auto',
        position: { x: 320, y: 300 },
      },
      {
        id: '4-4',
        data: { label: 'Individual' },
        width: 'auto',
        position: { x: 470, y: 300 },
      },
      {
        id: '5-1',
        data: { label: '0.2', colors: checkColor('votingProcess', true) },
        width: 'auto',
        type: 'result',
        hidden: false,
        position: { x: 73, y: 365 },
      },
      {
        id: '5-2',
        data: { label: '0', colors: checkColor('votingProcess', false) },
        width: 'auto',
        type: 'result',
        hidden: false,
        position: { x: 199, y: 365 },
      },
      {
        id: '5-3',
        data: { label: '0.3', colors: checkColor('decisionMaking', true) },
        width: 'auto',
        type: 'result',
        hidden: false,
        position: { x: 344, y: 365 },
      },
      {
        id: '5-4',
        data: { label: '0', colors: checkColor('decisionMaking', false) },
        width: 'auto',
        type: 'result',
        hidden: false,
        position: { x: 498, y: 365 },
      },
      {
        id: '2-1-1',
        data: { label: 'Score: 0.8', type: 'LeftNode', colors: checkColor('ipGovern', true) },
        width: 'auto',
        position: { x: -20, y: 95 },
        type: 'result',
      },
      {
        id: '2-2-1',
        data: {
          label: 'Score: 0',
          type: 'RightNode',
          colors: checkColor('ipGovern', false),
        },
        width: 'auto',
        position: { x: 520, y: 100 },
        type: 'result',
      },
    ]
  })

  const edges = computed(() => {
    return [
      { id: '1_2-1', source: '1', target: '2-1', type: 'smoothstep' },
      { id: '1_2-2', source: '1', target: '2-2', type: 'smoothstep' },
      {
        id: '2-1_3-1',
        source: '2-1',
        target: '3-1',
        type: 'smoothstep',
        sourceHandle: 'source-bottom',
      },
      {
        id: '2-2_3-2',
        source: '2-2',
        target: '3-2',
        type: 'smoothstep',
        sourceHandle: 'source-bottom',
      },
      { id: '3-1_4-1', source: '3-1', target: '4-1', type: 'smoothstep' },
      { id: '3-1_4-2', source: '3-1', target: '4-2', type: 'smoothstep' },
      { id: '3-2_4-3', source: '3-2', target: '4-3', type: 'smoothstep' },
      { id: '3-2_4-4', source: '3-2', target: '4-4', type: 'smoothstep' },
      { id: '4-1_5-1', source: '4-1', target: '5-1' },
      { id: '4-2_5-2', source: '4-2', target: '5-2' },
      { id: '4-3_5-3', source: '4-3', target: '5-3' },
      { id: '4-4_5-4', source: '4-4', target: '5-4' },
      {
        id: '2-1_2-1-1',
        source: '2-1',
        target: '2-1-1',
      },
      {
        id: '2-2_2-2-1',
        source: '2-2',
        target: '2-2-1',
      },
    ]
  })

  return {
    nodes,
    edges,
  }
}
