import { useRadar<PERSON><PERSON><PERSON><PERSON> } from '~/composables/useRadar<PERSON>ine<PERSON>hart'
import { Position } from '@vue-flow/core'

// Constants
import { HTTP_STATUS } from '~/constants/error'

// Types
import type { CoinFilter } from '~/constants/example/stablecoin'
type DecisionTreeKey = 'algoControlled' | 'communityInvolvement' | 'transparency' | 'decisionMaking'

export const useCoinDistrTree = () => {
  const { coin1Filter, coin2Filter, coin3Filter } = useRadarLineChart()
  const toast = useToast()
  const { $apiClient } = useNuxtApp()

  const { data: treeData } = useLazyAsyncData(async () => {
    const { response, data } = await $apiClient.GET('/nodiens/api/v1/esg/decentralisation/tree/{metric}', {
      params: {
        path: {
          metric: 'coinDistrOrdinal',
        },
      },
    })

    if (response.status === HTTP_STATUS.INTERNAL_SERVER_ERROR) {
      toast.add({
        title: 'Error',
        description: 'Something went wrong. Please try again later.',
        icon: 'i-heroicons-exclamation-triangle-20-solid',
        color: 'red',
      })
    }

    if (response.status !== HTTP_STATUS.OK) {
      toast.add({
        title: 'Error',
        description: 'Failed to fetch coin distribution ordinal tree data.',
        color: 'red',
      })
    }

    return data?.payload || null
  })

  const _coinDistrDecisionTree = computed(() => {
    return treeData.value?.decisionTree ?? []
  })

  const coinList = computed<CoinFilter[]>(() => {
    const list = [coin1Filter.value, coin2Filter.value, coin3Filter.value]
    return list.reduce<CoinFilter[]>((acc, curr) => {
      if (curr.coin) {
        acc.push(curr)
      }
      return acc
    }, [])
  })

  const checkColor = (key: DecisionTreeKey, value: boolean) => {
    return coinList.value?.reduce<string[]>((acc, curr) => {
      if (!curr.coin) {
        return acc
      }

      const asset = _coinDistrDecisionTree.value.find((x) => x.name === curr.coin?.name)
      if (!asset) {
        console.warn(`${curr.coin.name} is not found in list.`)
      }
      if (asset?.[key] === value) {
        acc.push(curr.color)
      }
      return acc
    }, [])
  }

  const nodes = computed(() => {
    return [
      {
        id: 'algo',
        type: 'input',
        data: { label: 'Algo Controlled' },
        width: 'auto',
        position: { x: 0, y: 0 },
      },
      {
        id: 'algo-yes',
        data: { label: 'Yes' },
        width: 'auto',
        position: { x: -150, y: 90 },
      },
      {
        id: 'algo-no',
        data: { label: 'No' },
        width: 'auto',
        position: { x: 220, y: 90 },
      },
      {
        id: 'algo-yes-score',
        data: { label: 'Score: 1', colors: checkColor('algoControlled', true) },
        type: 'result',
        position: { x: -165, y: 175 },
      },
      {
        id: 'communityInvolvement',
        data: { label: 'Community Involvement' },
        width: 'auto',
        position: { x: 10, y: 175 },
      },
      {
        id: 'communityInvolvement-yes',
        data: { label: 'Yes' },
        width: 'auto',
        position: { x: -80, y: 265 },
      },
      {
        id: 'communityInvolvement-no',
        data: { label: 'No' },
        width: 'auto',
        position: { x: 250, y: 265 },
      },
      {
        id: 'communityInvolvement-yes-score',
        type: 'result',
        data: { label: 'Score: 0.8', colors: checkColor('communityInvolvement', true) },
        position: { x: -102, y: 350 },
      },
      {
        id: 'transparency',
        data: { label: 'Transparency' },
        width: 'auto',
        position: { x: 100, y: 350 },
      },
      {
        id: 'transparency-yes',
        data: { label: 'Yes', sources: [Position.Left, Position.Right] },
        width: 'auto',
        position: { x: -50, y: 440 },
        type: 'multipleSourceHandle',
      },
      {
        id: 'transparency-no',
        data: { label: 'No', sources: [Position.Left, Position.Bottom] },
        width: 'auto',
        position: { x: 350, y: 440 },
        type: 'multipleSourceHandle',
      },
      {
        id: 'transparency-yes-score',
        data: { label: 'Score: 0.4', type: 'LeftNode', colors: checkColor('transparency', true) },
        type: 'result',
        width: 'auto',
        position: { x: -200, y: 490 },
      },
      {
        id: 'transparency-no-score',
        data: { label: 'Score: 0', colors: checkColor('transparency', false) },
        type: 'result',
        width: 'auto',
        position: { x: 329, y: 510 },
      },
      {
        id: 'decisionMaking',
        data: { label: 'Decision Making Structure', sources: [Position.Left, Position.Right] },
        position: { x: 50, y: 510 },
        type: 'multipleSourceHandle',
      },
      {
        id: 'committee',
        data: { label: 'Committee' },
        width: 'auto',
        position: { x: -50, y: 570 },
      },
      {
        id: 'individual',
        data: { label: 'Individual' },
        width: 'auto',
        position: { x: 250, y: 570 },
      },
      {
        id: 'committee-score',
        data: { label: '0.2', colors: checkColor('decisionMaking', true) },
        type: 'result',
        width: 'auto',
        position: { x: -150, y: 660 },
      },
      {
        id: 'individual-score',
        data: { label: '0', colors: checkColor('decisionMaking', false) },
        type: 'result',
        width: 'auto',
        position: { x: 400, y: 660 },
      },
    ]
  })

  const edges = computed(() => {
    return [
      {
        id: 'algo>algo-yes',
        source: 'algo',
        target: 'algo-yes',
        type: 'smoothstep',
      },
      {
        id: 'algo>algo-no',
        source: 'algo',
        target: 'algo-no',
        type: 'smoothstep',
      },
      {
        id: 'algo-yes>algo-yes-score',
        source: 'algo-yes',
        target: 'algo-yes-score',
        type: 'straight',
      },
      {
        id: 'algo-no>communityInvolvement',
        source: 'algo-no',
        target: 'communityInvolvement',
        type: 'smoothstep',
      },
      {
        id: 'communityInvolvement>communityInvolvement-yes',
        source: 'communityInvolvement',
        target: 'communityInvolvement-yes',
        type: 'smoothstep',
      },
      {
        id: 'communityInvolvement>communityInvolvement-no',
        source: 'communityInvolvement',
        target: 'communityInvolvement-no',
        type: 'smoothstep',
      },
      {
        id: 'communityInvolvement-yes>communityInvolvement-yes-score',
        source: 'communityInvolvement-yes',
        target: 'communityInvolvement-yes-score',
        type: 'straight',
      },
      {
        id: 'communityInvolvement-no>transparency',
        source: 'communityInvolvement-no',
        target: 'transparency',
        type: 'smoothstep',
      },
      {
        id: 'transparency>transparency-yes',
        source: 'transparency',
        target: 'transparency-yes',
        type: 'smoothstep',
      },
      {
        id: 'transparency>transparency-no',
        source: 'transparency',
        target: 'transparency-no',
        type: 'smoothstep',
      },
      {
        id: 'transparency-yes>transparency-yes-score',
        source: 'transparency-yes',
        target: 'transparency-yes-score',
        type: 'smoothstep',
      },
      {
        id: 'transparency-no>transparency-no-score',
        source: 'transparency-no',
        target: 'transparency-no-score',
        type: 'straight',
        sourceHandle: 'source-bottom',
      },
      {
        id: 'transparency-yes>decisionMaking',
        source: 'transparency-yes',
        target: 'decisionMaking',
        type: 'smoothstep',
        sourceHandle: 'source-right',
      },
      {
        id: 'transparency-no>decisionMaking',
        source: 'transparency-no',
        target: 'decisionMaking',
        type: 'smoothstep',
      },
      {
        id: 'decisionMaking>committee',
        source: 'decisionMaking',
        target: 'committee',
        type: 'smoothstep',
        sourceHandle: 'source-left',
      },
      {
        id: 'decisionMaking>individual',
        source: 'decisionMaking',
        target: 'individual',
        type: 'smoothstep',
        sourceHandle: 'source-right',
      },
      {
        id: 'committee>committee-score',
        source: 'committee',
        target: 'committee-score',
        type: 'smoothstep',
      },
      {
        id: 'individual>individual-score',
        source: 'individual',
        target: 'individual-score',
        type: 'smoothstep',
      },
    ]
  })

  return {
    nodes,
    edges,
  }
}
