import type {
  AreaData,
  AreaSeriesOptions,
  AreaStyleOptions,
  DeepPartial,
  HistogramData,
  HistogramSeriesOptions,
  HistogramStyleOptions,
  ISeriesApi,
  LineData,
  LineSeriesOptions,
  LineStyleOptions,
  SeriesOptionsCommon,
  Time,
  WhitespaceData,
} from 'lightweight-charts'
import type { HLCAreaData } from '../custom-plugin/data'
import type { HLCAreaSeriesOptions } from '../custom-plugin/options'
import type { StackedBarsSeriesOptions } from '../custom-plugin/stacked-bars-series/options'

export type addLineDataType =
  | ISeriesApi<
      'Line',
      Time,
      WhitespaceData<Time> | LineData<Time>,
      LineSeriesOptions,
      DeepPartial<LineStyleOptions & SeriesOptionsCommon>
    >
  | undefined
export type addLineOptType = DeepPartial<LineStyleOptions & SeriesOptionsCommon>

export type addAreaDataType =
  | ISeriesApi<
      'Area',
      Time,
      AreaData<Time> | WhitespaceData<Time>,
      AreaSeriesOptions,
      DeepPartial<AreaStyleOptions & SeriesOptionsCommon>
    >
  | undefined
export type addAreaOptType = DeepPartial<AreaStyleOptions & SeriesOptionsCommon>

export type addRangeAreaDataType =
  | ISeriesApi<
      'Custom',
      Time,
      WhitespaceData<Time> | HLCAreaData,
      HLCAreaSeriesOptions,
      DeepPartial<HLCAreaSeriesOptions & SeriesOptionsCommon>
    >
  | undefined
export type addRangeAreaOptType = DeepPartial<HLCAreaSeriesOptions & SeriesOptionsCommon>

export type addHistogramOptType = DeepPartial<HistogramStyleOptions & SeriesOptionsCommon>
export type addHistogramDataType =
  | ISeriesApi<
      'Histogram',
      Time,
      WhitespaceData<Time> | HistogramData<Time>,
      HistogramSeriesOptions,
      DeepPartial<HistogramStyleOptions & SeriesOptionsCommon>
    >
  | undefined

export type addStackedBarsOptType = DeepPartial<StackedBarsSeriesOptions & SeriesOptionsCommon>
export type addStackedBarsDataType =
  | ISeriesApi<
      'Custom',
      Time,
      WhitespaceData<Time>,
      StackedBarsSeriesOptions,
      DeepPartial<StackedBarsSeriesOptions & SeriesOptionsCommon>
    >
  | undefined

export type Series =
  | addLineDataType
  | addAreaDataType
  | addRangeAreaDataType
  | addHistogramDataType
  | addStackedBarsDataType
