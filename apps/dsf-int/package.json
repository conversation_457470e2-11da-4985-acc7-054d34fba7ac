{"name": "@dsf/int", "version": "1.0.0", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nx serve --port 3001", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "stripe:listen": "stripe listen --forward-connect-to http://localhost:3001/api/trpc/v2.hooks.subscription.webhook", "gen:openapi": "openapi-typescript https://dev.exposci.com/nodiens/api/docs/json -o ./types/schema.d.ts", "lint": "eslint .", "typecheck": "nuxi typecheck"}, "dependencies": {"@dsf/nodiens-backend": "workspace:*", "@dsf/ui": "workspace:*", "@iconify-json/material-symbols": "^1.2.19", "@iconify-json/mdi": "^1.2.3", "@nuxt/ui": "^2.20.0", "@pinia/nuxt": "0.9.0", "@sentry/nuxt": "^9.13.0", "@slate-serializers/html": "^2.2.3", "@trpc/client": "^10.45.0", "@trpc/server": "^10.45.0", "@webzlodimir/vue-bottom-sheet": "^3.0.5", "analytics": "^0.8.16", "better-auth": "^1.3.4", "buffer": "^6.0.3", "canvas-confetti": "^1.9.3", "chart.js": "^4.4.9", "dom-to-image": "^2.6.0", "drizzle-orm": "^0.29.3", "fast-jwt": "^4.0.5", "file-saver": "^2.0.5", "hashconnect": "^3.0.13", "hashids": "^2.3.0", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "lightweight-charts": "4.2.3", "mustache": "^4.2.0", "nitro-cors": "^0.7.0", "node-schedule": "^2.1.1", "nodemailer": "^6.9.14", "openapi-fetch": "^0.14.0", "openapi-typescript-helpers": "^0.0.15", "pinia": "2.3.1", "postcss-custom-properties": "^14.0.4", "postgres": "^3.4.3", "range-slider-input": "^2.4.4", "stripe": "^17.4.0", "trpc-nuxt": "^0.10.22", "v-calendar": "^3.1.2", "v-onboarding": "^2.9.0", "vue-chartjs": "^5.3.2", "vue-recaptcha": "^3.0.0-alpha.6", "vue3-lottie": "^3.3.1", "vuedraggable": "^4.1.0", "zod": "^3.24.2"}, "devDependencies": {"@nuxt/devtools": "latest", "@nuxt/eslint-config": "^0.2.0", "@nuxtjs/color-mode": "^3.5.2", "@nuxtjs/eslint-config-typescript": "^12.1.0", "@nuxtjs/google-fonts": "3.2.0", "@nuxtjs/supabase": "^1.5.0", "@nuxtjs/tailwindcss": "^6.13.2", "@tailwindcss/typography": "^0.5.16", "@types/canvas-confetti": "^1.6.4", "@types/dom-to-image": "^2.6.7", "@types/escape-html": "^1.0.4", "@types/file-saver": "^2.0.7", "@types/jsonwebtoken": "^9.0.7", "@types/lodash": "^4.14.202", "@types/mustache": "^4.2.5", "@types/node-schedule": "^2.1.7", "@types/nodemailer": "^6.4.15", "@zadigetvoltaire/nuxt-gtm": "^0.0.13", "app-types": "workspace:*", "chartjs-adapter-date-fns": "^3.0.0", "date-fns": "^4.1.0", "eslint": "^9.24.0", "eslint-config-custom": "*", "eslint-plugin-nuxt": "^4.0.0", "eslint-plugin-vue": "^9.33.0", "nuxt": "3.13.2", "nuxt-clarity-analytics": "0.0.9", "openapi-typescript": "^7.8.0", "trpc-playground": "^1.0.4", "tsconfig": "*", "typescript": "^5.8.3", "vite-plugin-pwa": "^0.17.4", "vite-svg-loader": "^5.1.0", "vue": "3.5.13", "vue-router": "^4.5.0"}}