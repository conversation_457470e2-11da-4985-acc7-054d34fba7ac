// https://nuxt.com/docs/api/configuration/nuxt-config
import type { NuxtConfig } from 'nuxt/schema'
import { VitePWA } from 'vite-plugin-pwa'
import svgLoader from 'vite-svg-loader'
import { join } from 'path'
import { generateRandomKey } from './utils/generator'
import { generateRandomWord } from './helper/string-helper'

// console.log(process.env.NUXT_APP_NODIENS_DB_PASSWORD)

const key = generateRandomKey()

const config: NuxtConfig = defineNuxtConfig({
  ssr: true,

  devtools: {
    enabled: false,

    timeline: {
      enabled: true,
    },
  },

  icon: {
    provider: 'server',
  },

  modules: [
    '@nuxt/ui',
    '@nuxtjs/tailwindcss',
    '@pinia/nuxt',
    'vue-recaptcha/nuxt',
    '@nuxtjs/google-fonts',
    '@nuxtjs/supabase',
    '@nuxtjs/color-mode',
    '@zadigetvoltaire/nuxt-gtm',
    'nuxt-clarity-analytics',
  ],

  alias: {
    types: '/types',
  },

  components: {
    dirs: [{ path: './components', extensions: ['vue'], pathPrefix: false }],
  },

  supabase: {
    redirect: false,
    clientOptions: {
      auth: {
        detectSessionInUrl: false,
        autoRefreshToken: false,
        flowType: 'pkce',
      },
    },
  },
  sentry: {
    sourceMapsUploadOptions: {
      org: 'nodiens',
      project: 'javascript-nuxt',
      authToken:
        '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',
    },
  },
  colorMode: { classSuffix: '', preference: 'dark', fallback: 'dark', storageKey: 'dark-mode' },

  googleFonts: {
    families: {
      Inter: [400, 500, 700],
      'Red+Hat+Display': [700],
      'IBM+Plex+Mono': [400, 500, 600, 700],
    },
    display: 'swap',
    preconnect: true,
    prefetch: true,
    preload: true,
  },

  ui: {
    // @ts-expect-error ignore
    icons: ['mdi', 'heroicons', 'material-symbols'],
  },

  postcss: {
    plugins: {
      tailwindcss: {},
      autoprefixer: {},
      cssnano:
        process.env.NODE_ENV === 'production'
          ? { preset: ['default', { discardComments: { removeAll: true } }] }
          : undefined, // disable cssnano when not in production
    },
  },

  nitro: {
    preset: 'netlify',
  },

  gtm: {
    id: 'GTM-PXRSGZRP',
    defer: true,
    compatibility: true,
    enableRouterSync: true,
  },

  vite: {
    define: {
      'global.Buffer': ['buffer', 'Buffer'],
    },
    plugins: [
      VitePWA({
        registerType: 'autoUpdate',
        manifest: {
          name: 'DSF Intelligence',
          short_name: 'DSF Int',
          description: 'DSF Intelligence App',
          theme_color: '#ed723b',
        },
        devOptions: {
          enabled: true,
        },
      }),
      svgLoader({
        defaultImport: 'url',
      }),
    ],
    resolve: {
      alias: {
        buffer: 'buffer/',
      },
    },
    optimizeDeps: {
      include: ['buffer'],
    },
  },

  runtimeConfig: {
    supabaseUrl: process.env.SUPABASE_URL ?? '',
    dashboardChangePerMonth: Number.parseInt(process.env.NUXT_APP_DASHB0ARD_CHANGE_PER_MONTH ?? '0'),
    timescaleHost: process.env.NUXT_APP_TIMESCALE_HOST ?? '127.0.0.1',
    timescalePort: Number.parseInt(process.env.NUXT_APP_TIMESCALE_PORT ?? '5432'),
    timescaleDbUser: process.env.NUXT_APP_TIMESCALE_DB_USER ?? 'postgres',
    timescaleDbPassword: process.env.NUXT_APP_TIMESCALE_DB_PASSWORD ?? 'password',
    timescaleDbName: process.env.NUXT_APP_TIMESCALE_DB_NAME ?? 'db_name',
    timescaleDbMaxConnection: Number.parseInt(process.env.NUXT_APP_TIMESCALE_DB_MAX_CONNECTION ?? '10'),
    recaptchaSecret: process.env.NUXT_APP_RECAPTCHA_SECRET ?? '',
    timescaleDbSsl: process.env.NUXT_APP_TIMESCALE_DB_SSL === 'true',
    cacheMaxAge: process.env.NUXT_APP_CACHE_MAX_AGE ?? (60 * 30).toString(),
    chatSourceHost: process.env.NUXT_APP_CHATSOURE_HOST,
    chatSourcePort: Number.parseInt(process.env.NUXT_APP_CHATSOURE_PORT ?? '5432'),
    chatSourceDbName: process.env.NUXT_APP_CHATSOURE_DB_NAME,
    chatSourceUser: process.env.NUXT_APP_CHATSOURE_USER,
    chatSourcePassword: process.env.NUXT_APP_CHATSOURE_PASS,
    nodiensDbHost: process.env.NUXT_APP_NODIENS_DB_HOST,
    nodiensDbPort: Number.parseInt(process.env.NUXT_APP_NODIENS_DB_PORT ?? '5432'),
    nodiensDbName: process.env.NUXT_APP_NODIENS_DB_NAME,
    nodiensDbUser: process.env.NUXT_APP_NODIENS_DB_USER,
    nodiensDbPassword: process.env.NUXT_APP_NODIENS_DB_PASSWORD,
    smtpHost: process.env.NUXT_APP_SMTP_HOST,
    smtpPort: Number.parseInt(process.env.NUXT_APP_SMTP_PORT ?? '25'),
    smtpUser: process.env.NUXT_APP_SMTP_USER,
    smtpPassword: process.env.NUXT_APP_SMTP_PASSWORD,
    smtpEmail: process.env.NUXT_APP_SMTP_EMAIL,
    contactUsEmail: process.env.NUXT_APP_CONTACT_US_EMAIL,
    rootDirectory: process.cwd(),
    nuxtDirectory: join(process.cwd(), '.nuxt'),
    whitelistEmail: JSON.parse(process.env.NUXT_APP_WHITELIST_EMAIL ?? 'false'),
    key,
    csrfServerHeader: generateRandomWord(32),
    hashpackNetwork: process.env.NUXT_APP_HASHPACK_NETWORK ?? 'testnet',
    stripePublicKey: process.env.NUXT_APP_STRIPE_PUBLIC_KEY,
    stripeSecretKey: process.env.NUXT_APP_STRIPE_SECRET_KEY,
    stripeWebhookSecretKey: process.env.NUXT_APP_STRIPE_WEBHOOK_SECRET_KEY,
    stripeEnableTestClock: process.env.NUXT_APP_STRIPE_ENABLE_TEST_CLOCK,
    disableCsrf: Boolean(process.env.NUXT_APP_DISABLE_CSRF),
    public: {
      gtm: {
        id: process.env.NUXT_APP_GTM_ID ?? '',
        defer: true,
        compatibility: true,
        enableRouterSync: true,
      },
      appUrl: process.env.NUXT_APP_URL ?? 'http://localhost:3001',
      featureFlagsEnabled: process.env.NUXT_APP_FEATURE_FLAG_ENABLED ?? 'false',
      payloadApi: process.env.NUXT_APP_PAYLOAD_API,
      recaptcha: {
        v2SiteKey: process.env.NUXT_APP_RECAPTCHA_PUBLIC ?? '',
      },
      hashpackNetwork: process.env.NUXT_APP_HASHPACK_NETWORK ?? 'testnet',
      apiBaseUrl: process.env.NUXT_API_BASE_URL ?? 'http://localhost:3000',
      authBasePath: process.env.NUXT_AUTH_BASE_PATH,
    },
  },

  build: {
    transpile: ['trpc-nuxt'],
  },

  devServer: {
    port: 3001,
  },

  compatibilityDate: '2024-10-29',
})

export default config
