@tailwind base;
@tailwind components;
@tailwind utilities;

.transition-height {
  transition-property: height;
  transition-duration: 0.3s; /* Adjust the duration as needed */
  transition-timing-function: ease-in-out; /* Adjust the timing function as needed */
}

.tooltip-esg-chart {
  display: flex;
  flex-direction: column;
  width: 100px;
}

.apexcharts-tooltip.apexcharts-theme-light {
  border: 0px !important;
}

@layer utilities {
  .auth-container {
    background-image: url('~/assets/media/auth-bg.jpg');
    min-height: calc(100vh - 85px);
    background-color: rgba(0, 0, 0, 0.7);
    background-blend-mode: luminosity;
    @apply bg-cover;
  }

  .table-primitive th,
  .table-primitive td {
    @apply min-w-[200px] lg:min-w-fit;
  }
}

@layer utilities {
  .auxiliary-content h3 {
    @apply dark:!text-white;
  }
  .auxiliary-content li::marker {
    @apply dark:!text-white;
  }

  .scrollable {
    overflow-y: auto;
  }

  .scrollable::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .scrollable::-webkit-scrollbar-track {
    background-color: #c3c3c3;
    border-radius: 4px;
  }

  .scrollable::-webkit-scrollbar-thumb {
    background: #5b5b5b;
    border-radius: 4px;
  }

  .cms-content ol {
    counter-reset: item;
    list-style: none;
    padding-left: 0;
  }

  .cms-content p {
    margin: 0;
  }

  .cms-content ol:not(:first-child) {
    margin-top: 10px;
  }

  .cms-content li {
    counter-increment: item;
    margin-bottom: 10px;
    display: flex;
    align-items: flex-start;
    width: 100%;
    flex-wrap: wrap;
  }

  .cms-content li::before {
    content: counters(item, '.') '. ';
    font-weight: var(--before-font-weight, normal);
    display: inline-block;
    margin-right: 0.5em;
    min-width: 1em;
    text-align: right;
    vertical-align: top;
  }

  .cms-content li > p {
    margin: 0;
    flex: 1;
    word-break: break-word;
    white-space: break-spaces;
    width: 100%;
    flex-basis: 60%;
  }

  .cms-content li > p + p {
    margin-top: 0.5em;
  }

  .cms-content li ol {
    flex-basis: 100%;
    padding-left: 1.5em;
    width: 100%;
  }

  .cms-content li ol ol {
    padding-left: 2.5em;
  }

  .cms-content li ol ol ol {
    padding-left: 3.5em;
  }
}

@layer base {
  body * {
    @apply transition-colors duration-75;
    -webkit-overflow-scrolling: auto;
  }

  .dark body {
    @apply bg-dark-base;
  }

  .light body {
    @apply bg-neutrals-100;
  }

  .v1-container {
    @apply mx-auto w-full px-4;
  }

  @screen v1-sm {
    .v1-container {
      max-width: 540px;
    }
  }

  @screen v1-md {
    .v1-container {
      max-width: 720px;
    }
  }

  @screen v1-lg {
    .v1-container {
      max-width: 960px;
    }
  }

  @screen v1-xl {
    .v1-container {
      max-width: 1140px;
    }
  }
}

/* Scrollbar width */
.table-scrollbar::-webkit-scrollbar {
  width: 6px; /* Change to your desired width */
  height: 4px;
}

/* Scrollbar thumb */
.table-scrollbar::-webkit-scrollbar-thumb {
  background: #c3c3c3;
}

/* Scrollbar thumb hover */
.table-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #555;
}

@layer utilities {
  .modal-option-item {
    @apply bg-neutrals-100 flex cursor-pointer flex-col rounded-lg border-2 border-transparent p-3 text-black dark:bg-[#f3f3f3]/50 dark:text-white;
  }
  .modal-option-item.active {
    @apply !bg-base-primary-500/10 !border-base-primary-500 dark:!bg-base-primary-500/10 dark:!border-base-primary-500 !border-2;
  }
}
