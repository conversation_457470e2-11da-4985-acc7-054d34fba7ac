<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with <PERSON><PERSON>pe -->
<svg xmlns="http://www.w3.org/2000/svg" width="1000" height="500" viewBox="0 0 1000 500" style="background: rgba(0,0,0,.0);">
    <style>
@keyframes chat2-5_t { 0% { transform: translate(695.2535px,180.1835px); } 47.619% { transform: translate(695.2535px,180.1835px); } 77.8333% { transform: translate(657.2855px,278.1785px); } 100% { transform: translate(657.2855px,278.1785px); } }
@keyframes chat2-6_t { 0% { transform: translate(654.4985px,169.8015px); } 56.8571% { transform: translate(654.4985px,169.8015px); } 87.0714% { transform: translate(681.4375px,262.8845px); } 100% { transform: translate(681.4375px,262.8845px); } }
@keyframes chat2-7_t { 0% { transform: translate(660.9015px,180.1085px); } 47.9761% { transform: translate(660.9015px,180.1085px); } 78.1904% { transform: translate(623.8765px,276.1135px); } 100% { transform: translate(623.8765px,276.1135px); } }
@keyframes chat2-8_t { 0% { transform: translate(685.4925px,183.1595px); } 50.7142% { transform: translate(685.4925px,183.1595px); } 80.9523% { transform: translate(699.4805px,281.0775px); } 100% { transform: translate(699.4805px,281.0775px); } }
@keyframes mejl2-4_t { 0% { transform: translate(240.281px,119.4284px); } 58.5% { transform: translate(240.281px,119.4284px); } 88.7142% { transform: translate(198.354px,248.4804px); } 100% { transform: translate(198.354px,248.4804px); } }
@keyframes mejl2-5_t { 0% { transform: translate(272.488px,148.1234px); } 48.3333% { transform: translate(272.488px,148.1234px); } 78.5714% { transform: translate(241.532px,246.0944px); } 100% { transform: translate(241.532px,246.0944px); } }
@keyframes mejl2-6_t { 0% { transform: translate(261.548px,143.6304px); } 51.3571% { transform: translate(261.548px,143.6304px); } 81.5714% { transform: translate(287.326px,244.5954px); } 100% { transform: translate(287.326px,244.5954px); } }
@keyframes chat2_t { 0% { transform: translate(456.6075px,114.1545px); } 23.8095% { transform: translate(456.6075px,114.1545px); } 54.7619% { transform: translate(428.6895px,250.1965px); } 100% { transform: translate(428.6895px,250.1965px); } }
@keyframes chat2-2_t { 0% { transform: translate(482.0065px,114.8135px); } 19.0476% { transform: translate(482.0065px,114.8135px); } 50% { transform: translate(483.1105px,239.8195px); } 100% { transform: translate(483.1105px,239.8195px); } }
@keyframes chat2-3_t { 0% { transform: translate(439.5705px,88.2815px); } 21.4285% { transform: translate(439.5705px,88.2815px); } 52.3809% { transform: translate(461.5625px,256.0835px); } 100% { transform: translate(461.5625px,256.0835px); } }
@keyframes chat2-4_t { 0% { transform: translate(488.3355px,89.0935px); } 16.6666% { transform: translate(488.3355px,89.0935px); } 47.619% { transform: translate(551.3565px,255.1585px); } 100% { transform: translate(551.3565px,255.1585px); } }
@keyframes mejl2_t { 0% { transform: translate(37.617px,67.3594px); } 22.4285% { transform: translate(37.617px,67.3594px); } 38.7619% { transform: translate(17.69px,221.3424px); } 100% { transform: translate(17.69px,221.3424px); } }
@keyframes mejl2-3_t { 0% { transform: translate(58.756px,82.6524px); } 14.2857% { transform: translate(58.756px,82.6524px); } 30.5952% { transform: translate(65.131px,221.6454px); } 100% { transform: translate(65.131px,221.6454px); } }
@keyframes mejl2-7_t { 0% { transform: translate(91.081px,81.5634px); } 26.5238% { transform: translate(91.081px,81.5634px); } 42.8571% { transform: translate(126.141px,219.5354px); } 100% { transform: translate(126.141px,219.5354px); } }
@keyframes mejl2-2_t { 0% { transform: translate(91.081px,81.5634px); } 18.3571% { transform: translate(91.081px,81.5634px); } 34.6904% { transform: translate(94.066px,224.5664px); } 100% { transform: translate(94.066px,224.5664px); } }
@keyframes Group_392_t { 0% { transform: translate(262.7825px,195.002px); } 2.3809% { transform: translate(262.7825px,195.002px); } 40.4761% { transform: translate(275.6315px,262.119px); } 100% { transform: translate(275.6315px,262.119px); } }
@keyframes Group_392-2_t { 0% { transform: translate(295.8805px,175.235px); } 4.7619% { transform: translate(295.8805px,175.235px); } 42.8571% { transform: translate(300.7955px,266.252px); } 100% { transform: translate(300.7955px,266.252px); } }
@keyframes Group_392-3_t { 0% { transform: translate(312.9325px,175.235px); } 38.0952% { transform: translate(255.9755px,278.288px); } 100% { transform: translate(255.9755px,278.288px); } }
@keyframes email-1_t { 0% { transform: translate(-109.068px,162.5404px); } 4.7619% { transform: translate(-109.068px,162.5404px); animation-timing-function: cubic-bezier(0,0,0.58,1); } 28.5714% { transform: translate(-76.018px,246.4824px); } 100% { transform: translate(-76.018px,246.4824px); } }
@keyframes email-2_t { 0% { transform: translate(-94.013px,146.5634px); animation-timing-function: cubic-bezier(0,0,0.58,1); } 23.8095% { transform: translate(-126.936px,248.4904px); } 100% { transform: translate(-126.936px,248.4904px); } }
@keyframes email-3_t { 0% { transform: translate(-138.131px,163.3024px); } 9.5238% { transform: translate(-138.131px,163.3024px); animation-timing-function: cubic-bezier(0,0,0.58,1); } 33.3333% { transform: translate(-102.453px,253.1664px); } 100% { transform: translate(-102.453px,253.1664px); } }
@keyframes a0_t { 0% { transform: translate(415px,23.5px); } 11.9047% { transform: translate(415px,23.5px); } 26.1904% { transform: translate(413.713px,47px); } 30.9523% { transform: translate(412px,31.377px); } 38.0952% { transform: translate(416.821px,47px); } 45.238% { transform: translate(419.655px,30.394px); } 52.3809% { transform: translate(420.314px,51.369px); } 100% { transform: translate(420.314px,51.369px); } }
@keyframes a1_t { 0% { transform: translate(510px,96.5px); } 21.4285% { transform: translate(510px,96.5px); } 35.7142% { transform: translate(508.149px,103.521px); } 40.4761% { transform: translate(512.082px,119.678px); } 50% { transform: translate(509.952px,115.5px); } 61.9047% { transform: translate(517.164px,119.869px); } 100% { transform: translate(517.164px,119.869px); } }
@keyframes discord-icon_t { 0% { transform: translate(230.914px,108.112px); animation-timing-function: cubic-bezier(0,0,0.58,1); } 7.1428% { transform: translate(230.011px,101.104px); animation-timing-function: cubic-bezier(0,0,0.58,1); } 11.9047% { transform: translate(230.914px,112.064px); animation-timing-function: cubic-bezier(0,0,0.58,1); } 19.0476% { transform: translate(230.011px,101.104px); animation-timing-function: cubic-bezier(0,0,0.58,1); } 28.5714% { transform: translate(234px,114.828px); animation-timing-function: cubic-bezier(0,0,0.58,1); } 38.0952% { transform: translate(232.805px,101.104px); } 100% { transform: translate(232.805px,101.104px); } }
@keyframes bellow-disc_t { 0% { transform: translate(209.578px,194.572px); animation-timing-function: cubic-bezier(0,0,0.58,1); } 7.1428% { transform: translate(208.675px,203.677px); } 11.9047% { transform: translate(206.527px,191.606px); animation-timing-function: cubic-bezier(0,0,0.58,1); } 21.4285% { transform: translate(210.183px,200.5px); animation-timing-function: cubic-bezier(0,0,0.58,1); } 26.1904% { transform: translate(209.254px,189.625px); animation-timing-function: cubic-bezier(0,0,0.58,1); } 30.9523% { transform: translate(212.212px,200.5px); } 100% { transform: translate(212.212px,200.5px); } }
@keyframes a2_t { 0% { transform: translate(630.004px,107.683px); } 45.238% { transform: translate(630.004px,120.478px); } 57.1428% { transform: translate(639.053px,100.436px); } 69.0476% { transform: translate(636.003px,124.318px); } 78.5714% { transform: translate(632.902px,105.568px); } 100% { transform: translate(632.902px,105.568px); } }
@keyframes a3_t { 0% { transform: translate(726px,113.7px); } 54.7619% { transform: translate(729.706px,144.868px); } 66.6666% { transform: translate(722.735px,128.869px); } 71.4285% { transform: translate(723.567px,145.735px); } 78.5714% { transform: translate(718.699px,132.858px); } 100% { transform: translate(718.699px,132.858px); } }
    </style>
    <g id="chat2-5" data-name="Group 124" transform="translate(713.132,197.136) translate(-17.8785,-16.9525)" style="animation: 4.2s linear infinite both chat2-5_t;">
        <path id="Path_37-11" data-name="Path 37" d="M17.877,9.586C8,9.586,0,16.762,0,25.614C0.010779,28.7975,1.04775,31.8926,2.957,34.44C2.712,37.365,2.006,41.484,0,43.491C0,43.491,6.057,42.639,10.206,40.165C10.206,40.165,10.212,40.158,10.216,40.156C11.499,39.365,12.94,38.442,13.292,38.166C13.51,37.8641,13.9231,37.7798,14.242,37.972C14.4565,38.1032,14.5883,38.3356,14.591,38.587C14.591,39.003,14.585,39.119,11.98,40.745C13.8895,41.3432,15.879,41.6467,17.88,41.645C27.753,41.645,35.757,34.469,35.757,25.617C35.757,16.765,27.75,9.586,17.877,9.586Z" fill="#fff" transform="translate(17.8785,16.9525) translate(-17.8785,-26.5385)"/>
        <path id="Path_39-11" data-name="Path 39" d="M28.226,25.43L18.215,25.43C17.8201,25.43,17.5,25.1099,17.5,24.715C17.5,24.3201,17.8201,24,18.215,24L28.226,24C28.6209,24,28.941,24.3201,28.941,24.715C28.941,25.1099,28.6209,25.43,28.226,25.43Z" fill="#fff" transform="translate(18.2345,11.022) translate(-23.2205,-24.715)"/>
        <path id="Path_40-11" data-name="Path 40" d="M30.592,32.43L12.715,32.43C12.3201,32.43,12,32.1099,12,31.715C12,31.3201,12.3201,31,12.715,31L30.592,31C30.9869,31,31.307,31.3201,31.307,31.715C31.307,32.1099,30.9869,32.43,30.592,32.43Z" fill="#fff" transform="translate(18.2345,16.028) translate(-21.6535,-31.715)"/>
        <path id="Path_41-11" data-name="Path 41" d="M30.592,39.43L12.715,39.43C12.4596,39.43,12.2235,39.2937,12.0958,39.0725C11.9681,38.8513,11.9681,38.5787,12.0958,38.3575C12.2235,38.1363,12.4596,38,12.715,38L30.592,38C30.8474,38,31.0835,38.1363,31.2112,38.3575C31.3389,38.5787,31.3389,38.8513,31.2112,39.0725C31.0835,39.2937,30.8474,39.43,30.592,39.43Z" fill="#fff" transform="translate(18.2345,21.033) translate(-21.6535,-38.715)"/>
    </g>
    <g id="chat2-6" data-name="Group 124" transform="translate(672.377,186.754) translate(-17.8785,-16.9525)" style="animation: 4.2s linear infinite both chat2-6_t;">
        <path id="Path_37-12" data-name="Path 37" d="M17.877,9.586C8,9.586,0,16.762,0,25.614C0.010779,28.7975,1.04775,31.8926,2.957,34.44C2.712,37.365,2.006,41.484,0,43.491C0,43.491,6.057,42.639,10.206,40.165C10.206,40.165,10.212,40.158,10.216,40.156C11.499,39.365,12.94,38.442,13.292,38.166C13.51,37.8641,13.9231,37.7798,14.242,37.972C14.4565,38.1032,14.5883,38.3356,14.591,38.587C14.591,39.003,14.585,39.119,11.98,40.745C13.8895,41.3432,15.879,41.6467,17.88,41.645C27.753,41.645,35.757,34.469,35.757,25.617C35.757,16.765,27.75,9.586,17.877,9.586Z" fill="#fa3232" transform="translate(17.8785,16.9525) translate(-17.8785,-26.5385)"/>
        <path id="Path_39-12" data-name="Path 39" d="M28.226,25.43L18.215,25.43C17.8201,25.43,17.5,25.1099,17.5,24.715C17.5,24.3201,17.8201,24,18.215,24L28.226,24C28.6209,24,28.941,24.3201,28.941,24.715C28.941,25.1099,28.6209,25.43,28.226,25.43Z" fill="#fff" transform="translate(18.2345,11.022) translate(-23.2205,-24.715)"/>
        <path id="Path_40-12" data-name="Path 40" d="M30.592,32.43L12.715,32.43C12.3201,32.43,12,32.1099,12,31.715C12,31.3201,12.3201,31,12.715,31L30.592,31C30.9869,31,31.307,31.3201,31.307,31.715C31.307,32.1099,30.9869,32.43,30.592,32.43Z" fill="#fff" transform="translate(18.2345,16.028) translate(-21.6535,-31.715)"/>
        <path id="Path_41-12" data-name="Path 41" d="M30.592,39.43L12.715,39.43C12.4596,39.43,12.2235,39.2937,12.0958,39.0725C11.9681,38.8513,11.9681,38.5787,12.0958,38.3575C12.2235,38.1363,12.4596,38,12.715,38L30.592,38C30.8474,38,31.0835,38.1363,31.2112,38.3575C31.3389,38.5787,31.3389,38.8513,31.2112,39.0725C31.0835,39.2937,30.8474,39.43,30.592,39.43Z" fill="#fff" transform="translate(18.2345,21.033) translate(-21.6535,-38.715)"/>
    </g>
    <g id="chat2-7" data-name="Group 124" transform="translate(678.78,197.061) translate(-17.8785,-16.9525)" style="animation: 4.2s linear infinite both chat2-7_t;">
        <path id="Path_37-13" data-name="Path 37" d="M17.877,9.586C8,9.586,0,16.762,0,25.614C0.010779,28.7975,1.04775,31.8926,2.957,34.44C2.712,37.365,2.006,41.484,0,43.491C0,43.491,6.057,42.639,10.206,40.165C10.206,40.165,10.212,40.158,10.216,40.156C11.499,39.365,12.94,38.442,13.292,38.166C13.51,37.8641,13.9231,37.7798,14.242,37.972C14.4565,38.1032,14.5883,38.3356,14.591,38.587C14.591,39.003,14.585,39.119,11.98,40.745C13.8895,41.3432,15.879,41.6467,17.88,41.645C27.753,41.645,35.757,34.469,35.757,25.617C35.757,16.765,27.75,9.586,17.877,9.586Z" fill="#e7dc08" transform="translate(17.8785,16.9525) translate(-17.8785,-26.5385)"/>
        <path id="Path_39-13" data-name="Path 39" d="M28.226,25.43L18.215,25.43C17.8201,25.43,17.5,25.1099,17.5,24.715C17.5,24.3201,17.8201,24,18.215,24L28.226,24C28.6209,24,28.941,24.3201,28.941,24.715C28.941,25.1099,28.6209,25.43,28.226,25.43Z" fill="#fff" transform="translate(18.2345,11.022) translate(-23.2205,-24.715)"/>
        <path id="Path_40-13" data-name="Path 40" d="M30.592,32.43L12.715,32.43C12.3201,32.43,12,32.1099,12,31.715C12,31.3201,12.3201,31,12.715,31L30.592,31C30.9869,31,31.307,31.3201,31.307,31.715C31.307,32.1099,30.9869,32.43,30.592,32.43Z" fill="#fff" transform="translate(18.2345,16.028) translate(-21.6535,-31.715)"/>
        <path id="Path_41-13" data-name="Path 41" d="M30.592,39.43L12.715,39.43C12.4596,39.43,12.2235,39.2937,12.0958,39.0725C11.9681,38.8513,11.9681,38.5787,12.0958,38.3575C12.2235,38.1363,12.4596,38,12.715,38L30.592,38C30.8474,38,31.0835,38.1363,31.2112,38.3575C31.3389,38.5787,31.3389,38.8513,31.2112,39.0725C31.0835,39.2937,30.8474,39.43,30.592,39.43Z" fill="#fff" transform="translate(18.2345,21.033) translate(-21.6535,-38.715)"/>
    </g>
    <g id="chat2-8" data-name="Group 124" transform="translate(703.371,200.112) translate(-17.8785,-16.9525)" style="animation: 4.2s linear infinite both chat2-8_t;">
        <path id="Path_37-14" data-name="Path 37" d="M17.877,9.586C8,9.586,0,16.762,0,25.614C0.010779,28.7975,1.04775,31.8926,2.957,34.44C2.712,37.365,2.006,41.484,0,43.491C0,43.491,6.057,42.639,10.206,40.165C10.206,40.165,10.212,40.158,10.216,40.156C11.499,39.365,12.94,38.442,13.292,38.166C13.51,37.8641,13.9231,37.7798,14.242,37.972C14.4565,38.1032,14.5883,38.3356,14.591,38.587C14.591,39.003,14.585,39.119,11.98,40.745C13.8895,41.3432,15.879,41.6467,17.88,41.645C27.753,41.645,35.757,34.469,35.757,25.617C35.757,16.765,27.75,9.586,17.877,9.586Z" fill="#9811fc" transform="translate(17.8785,16.9525) translate(-17.8785,-26.5385)"/>
        <path id="Path_39-14" data-name="Path 39" d="M28.226,25.43L18.215,25.43C17.8201,25.43,17.5,25.1099,17.5,24.715C17.5,24.3201,17.8201,24,18.215,24L28.226,24C28.6209,24,28.941,24.3201,28.941,24.715C28.941,25.1099,28.6209,25.43,28.226,25.43Z" fill="#fff" transform="translate(18.2345,11.022) translate(-23.2205,-24.715)"/>
        <path id="Path_40-14" data-name="Path 40" d="M30.592,32.43L12.715,32.43C12.3201,32.43,12,32.1099,12,31.715C12,31.3201,12.3201,31,12.715,31L30.592,31C30.9869,31,31.307,31.3201,31.307,31.715C31.307,32.1099,30.9869,32.43,30.592,32.43Z" fill="#fff" transform="translate(18.2345,16.028) translate(-21.6535,-31.715)"/>
        <path id="Path_41-14" data-name="Path 41" d="M30.592,39.43L12.715,39.43C12.4596,39.43,12.2235,39.2937,12.0958,39.0725C11.9681,38.8513,11.9681,38.5787,12.0958,38.3575C12.2235,38.1363,12.4596,38,12.715,38L30.592,38C30.8474,38,31.0835,38.1363,31.2112,38.3575C31.3389,38.5787,31.3389,38.8513,31.2112,39.0725C31.0835,39.2937,30.8474,39.43,30.592,39.43Z" fill="#fff" transform="translate(18.2345,21.033) translate(-21.6535,-38.715)"/>
    </g>
    <g id="mejl2-4" data-name="Group 395" transform="translate(661.113,169.802) translate(-420.832,-50.3736)" style="animation: 4.2s linear infinite both mejl2-4_t;">
        <path id="Path_430-10" data-name="Path 430" d="M398.876,53.505C398.795,53.4272,398.733,53.3327,398.693,53.228C398.623,53.0416,398.631,52.8352,398.713,52.654L407.957,32.246C408.129,31.8696,408.573,31.7029,408.95,31.873L442.577,47.1C442.759,47.1808,442.901,47.3315,442.97,47.518C443.04,47.7043,443.033,47.9107,442.951,48.092L433.706,68.5C433.535,68.8771,433.091,69.0444,432.714,68.874L399.086,53.648C399.009,53.612,398.938,53.5637,398.876,53.505Z" fill="#fff" transform="translate(420.832,50.3736) translate(-420.832,-50.3736)"/>
        <path id="Path_431-10" data-name="Path 431" d="M419.489,52.878C419.435,52.8253,419.388,52.7646,419.352,52.698L408.62,33.206C408.464,32.9231,408.506,32.5718,408.725,32.3343C408.945,32.0968,409.292,32.0265,409.586,32.16L441.97,46.829C442.265,46.9614,442.442,47.2681,442.408,47.5897C442.375,47.9112,442.139,48.175,441.823,48.244L420.173,53.069C419.927,53.124,419.671,53.0523,419.489,52.878Z" fill="#dbdbdb" transform="translate(425.469,42.5901) translate(-425.469,-42.5901)"/>
        <path id="Path_432-10" data-name="Path 432" d="M402.183,52.382C401.996,52.2023,401.915,51.9384,401.969,51.6846C402.024,51.4307,402.205,51.2232,402.45,51.136L413.191,47.291C413.446,47.184,413.74,47.2264,413.955,47.4014C414.169,47.5763,414.27,47.8553,414.217,48.1271C414.164,48.3988,413.965,48.6191,413.7,48.7L402.959,52.546C402.69,52.6438,402.389,52.5802,402.183,52.382Z" fill="#dbdbdb" transform="translate(408.092,49.912) translate(-408.092,-49.912)"/>
        <path id="Path_433-10" data-name="Path 433" d="M430.557,65.235C430.479,65.1606,430.418,65.0702,430.379,64.97L426.186,54.36C426.083,54.1099,426.123,53.8241,426.291,53.6117C426.458,53.3993,426.726,53.2933,426.993,53.3341C427.26,53.375,427.485,53.5564,427.581,53.809L431.774,64.418C431.904,64.746,431.788,65.1203,431.495,65.317C431.202,65.5137,430.811,65.4795,430.557,65.235Z" fill="#dbdbdb" transform="translate(428.978,59.3849) translate(-428.978,-59.3849)"/>
    </g>
    <g id="mejl2-5" data-name="Group 395" transform="translate(693.32,198.497) translate(-420.832,-50.3736)" style="animation: 4.2s linear infinite both mejl2-5_t;">
        <path id="Path_430-11" data-name="Path 430" d="M398.876,53.505C398.795,53.4272,398.733,53.3327,398.693,53.228C398.623,53.0416,398.631,52.8352,398.713,52.654L407.957,32.246C408.129,31.8696,408.573,31.7029,408.95,31.873L442.577,47.1C442.759,47.1808,442.901,47.3315,442.97,47.518C443.04,47.7043,443.033,47.9107,442.951,48.092L433.706,68.5C433.535,68.8771,433.091,69.0444,432.714,68.874L399.086,53.648C399.009,53.612,398.938,53.5637,398.876,53.505Z" fill="#fff" transform="translate(420.832,50.3736) translate(-420.832,-50.3736)"/>
        <path id="Path_431-11" data-name="Path 431" d="M419.489,52.878C419.435,52.8253,419.388,52.7646,419.352,52.698L408.62,33.206C408.464,32.9231,408.506,32.5718,408.725,32.3343C408.945,32.0968,409.292,32.0265,409.586,32.16L441.97,46.829C442.265,46.9614,442.442,47.2681,442.408,47.5897C442.375,47.9112,442.139,48.175,441.823,48.244L420.173,53.069C419.927,53.124,419.671,53.0523,419.489,52.878Z" fill="#dbdbdb" transform="translate(425.469,42.5901) translate(-425.469,-42.5901)"/>
        <path id="Path_432-11" data-name="Path 432" d="M402.183,52.382C401.996,52.2023,401.915,51.9384,401.969,51.6846C402.024,51.4307,402.205,51.2232,402.45,51.136L413.191,47.291C413.446,47.184,413.74,47.2264,413.955,47.4014C414.169,47.5763,414.27,47.8553,414.217,48.1271C414.164,48.3988,413.965,48.6191,413.7,48.7L402.959,52.546C402.69,52.6438,402.389,52.5802,402.183,52.382Z" fill="#dbdbdb" transform="translate(408.092,49.912) translate(-408.092,-49.912)"/>
        <path id="Path_433-11" data-name="Path 433" d="M430.557,65.235C430.479,65.1606,430.418,65.0702,430.379,64.97L426.186,54.36C426.083,54.1099,426.123,53.8241,426.291,53.6117C426.458,53.3993,426.726,53.2933,426.993,53.3341C427.26,53.375,427.485,53.5564,427.581,53.809L431.774,64.418C431.904,64.746,431.788,65.1203,431.495,65.317C431.202,65.5137,430.811,65.4795,430.557,65.235Z" fill="#dbdbdb" transform="translate(428.978,59.3849) translate(-428.978,-59.3849)"/>
    </g>
    <g id="mejl2-6" data-name="Group 395" transform="translate(682.38,194.004) translate(-420.832,-50.3736)" style="animation: 4.2s linear infinite both mejl2-6_t;">
        <path id="Path_430-12" data-name="Path 430" d="M398.876,53.505C398.795,53.4272,398.733,53.3327,398.693,53.228C398.623,53.0416,398.631,52.8352,398.713,52.654L407.957,32.246C408.129,31.8696,408.573,31.7029,408.95,31.873L442.577,47.1C442.759,47.1808,442.901,47.3315,442.97,47.518C443.04,47.7043,443.033,47.9107,442.951,48.092L433.706,68.5C433.535,68.8771,433.091,69.0444,432.714,68.874L399.086,53.648C399.009,53.612,398.938,53.5637,398.876,53.505Z" fill="#fff" transform="translate(420.832,50.3736) translate(-420.832,-50.3736)"/>
        <path id="Path_431-12" data-name="Path 431" d="M419.489,52.878C419.435,52.8253,419.388,52.7646,419.352,52.698L408.62,33.206C408.464,32.9231,408.506,32.5718,408.725,32.3343C408.945,32.0968,409.292,32.0265,409.586,32.16L441.97,46.829C442.265,46.9614,442.442,47.2681,442.408,47.5897C442.375,47.9112,442.139,48.175,441.823,48.244L420.173,53.069C419.927,53.124,419.671,53.0523,419.489,52.878Z" fill="#dbdbdb" transform="translate(425.469,42.5901) translate(-425.469,-42.5901)"/>
        <path id="Path_432-12" data-name="Path 432" d="M402.183,52.382C401.996,52.2023,401.915,51.9384,401.969,51.6846C402.024,51.4307,402.205,51.2232,402.45,51.136L413.191,47.291C413.446,47.184,413.74,47.2264,413.955,47.4014C414.169,47.5763,414.27,47.8553,414.217,48.1271C414.164,48.3988,413.965,48.6191,413.7,48.7L402.959,52.546C402.69,52.6438,402.389,52.5802,402.183,52.382Z" fill="#dbdbdb" transform="translate(408.092,49.912) translate(-408.092,-49.912)"/>
        <path id="Path_433-12" data-name="Path 433" d="M430.557,65.235C430.479,65.1606,430.418,65.0702,430.379,64.97L426.186,54.36C426.083,54.1099,426.123,53.8241,426.291,53.6117C426.458,53.3993,426.726,53.2933,426.993,53.3341C427.26,53.375,427.485,53.5564,427.581,53.809L431.774,64.418C431.904,64.746,431.788,65.1203,431.495,65.317C431.202,65.5137,430.811,65.4795,430.557,65.235Z" fill="#dbdbdb" transform="translate(428.978,59.3849) translate(-428.978,-59.3849)"/>
    </g>
    <g id="chat2" data-name="Group 124" transform="translate(474.486,131.107) translate(-17.8785,-16.9525)" style="animation: 4.2s linear infinite both chat2_t;">
        <path id="Path_37-5" data-name="Path 37" d="M17.877,9.586C8,9.586,0,16.762,0,25.614C0.010779,28.7975,1.04775,31.8926,2.957,34.44C2.712,37.365,2.006,41.484,0,43.491C0,43.491,6.057,42.639,10.206,40.165C10.206,40.165,10.212,40.158,10.216,40.156C11.499,39.365,12.94,38.442,13.292,38.166C13.51,37.8641,13.9231,37.7798,14.242,37.972C14.4565,38.1032,14.5883,38.3356,14.591,38.587C14.591,39.003,14.585,39.119,11.98,40.745C13.8895,41.3432,15.879,41.6467,17.88,41.645C27.753,41.645,35.757,34.469,35.757,25.617C35.757,16.765,27.75,9.586,17.877,9.586Z" fill="#fca311" transform="translate(17.8785,16.9525) translate(-17.8785,-26.5385)"/>
        <path id="Path_39-5" data-name="Path 39" d="M28.226,25.43L18.215,25.43C17.8201,25.43,17.5,25.1099,17.5,24.715C17.5,24.3201,17.8201,24,18.215,24L28.226,24C28.6209,24,28.941,24.3201,28.941,24.715C28.941,25.1099,28.6209,25.43,28.226,25.43Z" fill="#fff" transform="translate(18.2345,11.022) translate(-23.2205,-24.715)"/>
        <path id="Path_40-5" data-name="Path 40" d="M30.592,32.43L12.715,32.43C12.3201,32.43,12,32.1099,12,31.715C12,31.3201,12.3201,31,12.715,31L30.592,31C30.9869,31,31.307,31.3201,31.307,31.715C31.307,32.1099,30.9869,32.43,30.592,32.43Z" fill="#fff" transform="translate(18.2345,16.028) translate(-21.6535,-31.715)"/>
        <path id="Path_41-5" data-name="Path 41" d="M30.592,39.43L12.715,39.43C12.4596,39.43,12.2235,39.2937,12.0958,39.0725C11.9681,38.8513,11.9681,38.5787,12.0958,38.3575C12.2235,38.1363,12.4596,38,12.715,38L30.592,38C30.8474,38,31.0835,38.1363,31.2112,38.3575C31.3389,38.5787,31.3389,38.8513,31.2112,39.0725C31.0835,39.2937,30.8474,39.43,30.592,39.43Z" fill="#fff" transform="translate(18.2345,21.033) translate(-21.6535,-38.715)"/>
    </g>
    <g id="chat2-2" data-name="Group 124" transform="translate(499.885,131.766) translate(-17.8785,-16.9525)" style="animation: 4.2s linear infinite both chat2-2_t;">
        <path id="Path_37-8" data-name="Path 37" d="M17.877,9.586C8,9.586,0,16.762,0,25.614C0.010779,28.7975,1.04775,31.8926,2.957,34.44C2.712,37.365,2.006,41.484,0,43.491C0,43.491,6.057,42.639,10.206,40.165C10.206,40.165,10.212,40.158,10.216,40.156C11.499,39.365,12.94,38.442,13.292,38.166C13.51,37.8641,13.9231,37.7798,14.242,37.972C14.4565,38.1032,14.5883,38.3356,14.591,38.587C14.591,39.003,14.585,39.119,11.98,40.745C13.8895,41.3432,15.879,41.6467,17.88,41.645C27.753,41.645,35.757,34.469,35.757,25.617C35.757,16.765,27.75,9.586,17.877,9.586Z" fill="#fa3232" transform="translate(17.8785,16.9525) translate(-17.8785,-26.5385)"/>
        <path id="Path_39-8" data-name="Path 39" d="M28.226,25.43L18.215,25.43C17.8201,25.43,17.5,25.1099,17.5,24.715C17.5,24.3201,17.8201,24,18.215,24L28.226,24C28.6209,24,28.941,24.3201,28.941,24.715C28.941,25.1099,28.6209,25.43,28.226,25.43Z" fill="#fff" transform="translate(18.2345,11.022) translate(-23.2205,-24.715)"/>
        <path id="Path_40-8" data-name="Path 40" d="M30.592,32.43L12.715,32.43C12.3201,32.43,12,32.1099,12,31.715C12,31.3201,12.3201,31,12.715,31L30.592,31C30.9869,31,31.307,31.3201,31.307,31.715C31.307,32.1099,30.9869,32.43,30.592,32.43Z" fill="#fff" transform="translate(18.2345,16.028) translate(-21.6535,-31.715)"/>
        <path id="Path_41-8" data-name="Path 41" d="M30.592,39.43L12.715,39.43C12.4596,39.43,12.2235,39.2937,12.0958,39.0725C11.9681,38.8513,11.9681,38.5787,12.0958,38.3575C12.2235,38.1363,12.4596,38,12.715,38L30.592,38C30.8474,38,31.0835,38.1363,31.2112,38.3575C31.3389,38.5787,31.3389,38.8513,31.2112,39.0725C31.0835,39.2937,30.8474,39.43,30.592,39.43Z" fill="#fff" transform="translate(18.2345,21.033) translate(-21.6535,-38.715)"/>
    </g>
    <g id="chat2-3" data-name="Group 124" transform="translate(457.449,105.234) translate(-17.8785,-16.9525)" style="animation: 4.2s linear infinite both chat2-3_t;">
        <path id="Path_37-9" data-name="Path 37" d="M17.877,9.586C8,9.586,0,16.762,0,25.614C0.010779,28.7975,1.04775,31.8926,2.957,34.44C2.712,37.365,2.006,41.484,0,43.491C0,43.491,6.057,42.639,10.206,40.165C10.206,40.165,10.212,40.158,10.216,40.156C11.499,39.365,12.94,38.442,13.292,38.166C13.51,37.8641,13.9231,37.7798,14.242,37.972C14.4565,38.1032,14.5883,38.3356,14.591,38.587C14.591,39.003,14.585,39.119,11.98,40.745C13.8895,41.3432,15.879,41.6467,17.88,41.645C27.753,41.645,35.757,34.469,35.757,25.617C35.757,16.765,27.75,9.586,17.877,9.586Z" fill="#e7dc08" transform="translate(17.8785,16.9525) translate(-17.8785,-26.5385)"/>
        <path id="Path_39-9" data-name="Path 39" d="M28.226,25.43L18.215,25.43C17.8201,25.43,17.5,25.1099,17.5,24.715C17.5,24.3201,17.8201,24,18.215,24L28.226,24C28.6209,24,28.941,24.3201,28.941,24.715C28.941,25.1099,28.6209,25.43,28.226,25.43Z" fill="#fff" transform="translate(18.2345,11.022) translate(-23.2205,-24.715)"/>
        <path id="Path_40-9" data-name="Path 40" d="M30.592,32.43L12.715,32.43C12.3201,32.43,12,32.1099,12,31.715C12,31.3201,12.3201,31,12.715,31L30.592,31C30.9869,31,31.307,31.3201,31.307,31.715C31.307,32.1099,30.9869,32.43,30.592,32.43Z" fill="#fff" transform="translate(18.2345,16.028) translate(-21.6535,-31.715)"/>
        <path id="Path_41-9" data-name="Path 41" d="M30.592,39.43L12.715,39.43C12.4596,39.43,12.2235,39.2937,12.0958,39.0725C11.9681,38.8513,11.9681,38.5787,12.0958,38.3575C12.2235,38.1363,12.4596,38,12.715,38L30.592,38C30.8474,38,31.0835,38.1363,31.2112,38.3575C31.3389,38.5787,31.3389,38.8513,31.2112,39.0725C31.0835,39.2937,30.8474,39.43,30.592,39.43Z" fill="#fff" transform="translate(18.2345,21.033) translate(-21.6535,-38.715)"/>
    </g>
    <g id="chat2-4" data-name="Group 124" transform="translate(506.214,106.046) translate(-17.8785,-16.9525)" style="animation: 4.2s linear infinite both chat2-4_t;">
        <path id="Path_37-10" data-name="Path 37" d="M17.877,9.586C8,9.586,0,16.762,0,25.614C0.010779,28.7975,1.04775,31.8926,2.957,34.44C2.712,37.365,2.006,41.484,0,43.491C0,43.491,6.057,42.639,10.206,40.165C10.206,40.165,10.212,40.158,10.216,40.156C11.499,39.365,12.94,38.442,13.292,38.166C13.51,37.8641,13.9231,37.7798,14.242,37.972C14.4565,38.1032,14.5883,38.3356,14.591,38.587C14.591,39.003,14.585,39.119,11.98,40.745C13.8895,41.3432,15.879,41.6467,17.88,41.645C27.753,41.645,35.757,34.469,35.757,25.617C35.757,16.765,27.75,9.586,17.877,9.586Z" fill="#9811fc" transform="translate(17.8785,16.9525) translate(-17.8785,-26.5385)"/>
        <path id="Path_39-10" data-name="Path 39" d="M28.226,25.43L18.215,25.43C17.8201,25.43,17.5,25.1099,17.5,24.715C17.5,24.3201,17.8201,24,18.215,24L28.226,24C28.6209,24,28.941,24.3201,28.941,24.715C28.941,25.1099,28.6209,25.43,28.226,25.43Z" fill="#fff" transform="translate(18.2345,11.022) translate(-23.2205,-24.715)"/>
        <path id="Path_40-10" data-name="Path 40" d="M30.592,32.43L12.715,32.43C12.3201,32.43,12,32.1099,12,31.715C12,31.3201,12.3201,31,12.715,31L30.592,31C30.9869,31,31.307,31.3201,31.307,31.715C31.307,32.1099,30.9869,32.43,30.592,32.43Z" fill="#fff" transform="translate(18.2345,16.028) translate(-21.6535,-31.715)"/>
        <path id="Path_41-10" data-name="Path 41" d="M30.592,39.43L12.715,39.43C12.4596,39.43,12.2235,39.2937,12.0958,39.0725C11.9681,38.8513,11.9681,38.5787,12.0958,38.3575C12.2235,38.1363,12.4596,38,12.715,38L30.592,38C30.8474,38,31.0835,38.1363,31.2112,38.3575C31.3389,38.5787,31.3389,38.8513,31.2112,39.0725C31.0835,39.2937,30.8474,39.43,30.592,39.43Z" fill="#fff" transform="translate(18.2345,21.033) translate(-21.6535,-38.715)"/>
    </g>
    <defs>
        <symbol id="Symbol-2" preserveAspectRatio="none" width="608.656" height="328" viewBox="0 0 608.656 328" overflow="visible">
            <text id="Trust_index" data-name="Trust index" fill="#fff" font-size="17" font-family="Gilroy-Medium" font-weight="500" transform="translate(566.828,319.5) translate(-41.8281,4.65804)"><tspan x="0" y="0">Trust index</tspan></text>
            <text id="Spam_Index" data-name="Spam Index" fill="#fff" font-size="17" font-family="Gilroy-Medium" font-weight="500" transform="translate(46.3984,8.5) translate(-46.3984,4.65804)"><tspan x="0" y="0">Spam Index</tspan></text>
        </symbol>
        <symbol id="Symbol-1" preserveAspectRatio="none" width="434.859" height="36" viewBox="0 0 434.859 36" overflow="visible">
            <text id="NLP" fill="#fff" font-size="36" font-family="Gilroy-Bold" font-weight="700" transform="translate(31.4297,18) translate(-31.4297,9.82782)"><tspan x="-62.856" y="0" transform="translate(-31.428,0) translate(31.428,0)">NLP</tspan></text>
            <text id="ML" fill="#fff" font-size="36" font-family="Gilroy-Bold" font-weight="700" transform="translate(219.668,18) translate(-23.3672,9.82782)"><tspan x="-46.728" y="0" transform="translate(-23.364,0) translate(23.364,0)">ML</tspan></text>
            <text id="AI" fill="#fff" font-size="36" font-family="Gilroy-Bold" font-weight="700" transform="translate(418.016,18) translate(-16.8438,9.82782)"><tspan x="-33.696" y="0" transform="translate(-16.848,0) translate(16.848,0)">AI</tspan></text>
        </symbol>
        <filter id="Ellipse_140" x="979" y="1006.5" width="206" height="206" filterUnits="userSpaceOnUse">
            <feOffset dy="3" input="SourceAlpha"/>
            <feGaussianBlur stdDeviation="13" result="blur"/>
            <feFlood flood-opacity="0.063"/>
            <feComposite operator="in" in2="blur"/>
            <feComposite in="SourceGraphic"/>
        </filter>
        <filter id="Ellipse_144" x="395.5" y="0" width="176" height="176" filterUnits="userSpaceOnUse">
            <feOffset dy="3" input="SourceAlpha"/>
            <feGaussianBlur stdDeviation="6.5" result="blur-2"/>
            <feFlood flood-opacity="0.063"/>
            <feComposite operator="in" in2="blur-2"/>
            <feComposite in="SourceGraphic"/>
        </filter>
        <filter id="Ellipse_145" x="501" y="83.5" width="85" height="86" filterUnits="userSpaceOnUse">
            <feOffset dy="3" input="SourceAlpha"/>
            <feGaussianBlur stdDeviation="3" result="blur-3"/>
            <feFlood flood-opacity="0.161"/>
            <feComposite operator="in" in2="blur-3"/>
            <feComposite in="SourceGraphic"/>
        </filter>
        <filter id="Ellipse_148" x="194.5" y="75" width="172" height="170" filterUnits="userSpaceOnUse">
            <feOffset dy="3" input="SourceAlpha"/>
            <feGaussianBlur stdDeviation="6.5" result="blur-4"/>
            <feFlood flood-opacity="0.063"/>
            <feComposite operator="in" in2="blur-4"/>
            <feComposite in="SourceGraphic"/>
        </filter>
        <filter id="Ellipse_149" x="205" y="193.5" width="85" height="84" filterUnits="userSpaceOnUse">
            <feOffset dy="3" input="SourceAlpha"/>
            <feGaussianBlur stdDeviation="3" result="blur-5"/>
            <feFlood flood-opacity="0.161"/>
            <feComposite operator="in" in2="blur-5"/>
            <feComposite in="SourceGraphic"/>
        </filter>
        <filter id="Ellipse_146" x="617.5" y="95" width="158" height="159" filterUnits="userSpaceOnUse">
            <feOffset dy="3" input="SourceAlpha"/>
            <feGaussianBlur stdDeviation="6.5" result="blur-6"/>
            <feFlood flood-opacity="0.063"/>
            <feComposite operator="in" in2="blur-6"/>
            <feComposite in="SourceGraphic"/>
        </filter>
        <filter id="Ellipse_147" x="708" y="102.5" width="68" height="68" filterUnits="userSpaceOnUse">
            <feOffset dy="3" input="SourceAlpha"/>
            <feGaussianBlur stdDeviation="3" result="blur-7"/>
            <feFlood flood-opacity="0.161"/>
            <feComposite operator="in" in2="blur-7"/>
            <feComposite in="SourceGraphic"/>
        </filter>
        <filter id="Ellipse_150" x="422.5" y="177" width="139" height="138" filterUnits="userSpaceOnUse">
            <feOffset dy="3" input="SourceAlpha"/>
            <feGaussianBlur stdDeviation="6.5" result="blur-8"/>
            <feFlood flood-opacity="0.063"/>
            <feComposite operator="in" in2="blur-8"/>
            <feComposite in="SourceGraphic"/>
        </filter>
        <filter id="Ellipse_151" x="502" y="248.5" width="60" height="60" filterUnits="userSpaceOnUse">
            <feOffset dy="3" input="SourceAlpha"/>
            <feGaussianBlur stdDeviation="3" result="blur-9"/>
            <feFlood flood-opacity="0.161"/>
            <feComposite operator="in" in2="blur-9"/>
            <feComposite in="SourceGraphic"/>
        </filter>
        <filter id="Rectangle_93" x="52" y="1410.5" width="708" height="453" filterUnits="userSpaceOnUse">
            <feOffset dy="3" input="SourceAlpha"/>
            <feGaussianBlur stdDeviation="13" result="blur-10"/>
            <feFlood flood-opacity="0.102"/>
            <feComposite operator="in" in2="blur-10"/>
            <feComposite in="SourceGraphic"/>
        </filter>
        <filter id="Rectangle_291" x="517" y="1661.5" width="268" height="309" filterUnits="userSpaceOnUse">
            <feOffset dy="3" input="SourceAlpha"/>
            <feGaussianBlur stdDeviation="13" result="blur-11"/>
            <feFlood flood-opacity="0.102"/>
            <feComposite operator="in" in2="blur-11"/>
            <feComposite in="SourceGraphic"/>
        </filter>
        <filter id="Rectangle_292" x="0" y="1348.5" width="268" height="309" filterUnits="userSpaceOnUse">
            <feOffset dy="3" input="SourceAlpha"/>
            <feGaussianBlur stdDeviation="13" result="blur-12"/>
            <feFlood flood-opacity="0.102"/>
            <feComposite operator="in" in2="blur-12"/>
            <feComposite in="SourceGraphic"/>
        </filter>
    </defs>
    <g id="mejl2" data-name="Group 395" transform="translate(458.449,117.733) translate(-420.832,-50.3736)" style="animation: 4.2s linear infinite both mejl2_t;">
        <path id="Path_430-2" data-name="Path 430" d="M398.876,53.505C398.795,53.4272,398.733,53.3327,398.693,53.228C398.623,53.0416,398.631,52.8352,398.713,52.654L407.957,32.246C408.129,31.8696,408.573,31.7029,408.95,31.873L442.577,47.1C442.759,47.1808,442.901,47.3315,442.97,47.518C443.04,47.7043,443.033,47.9107,442.951,48.092L433.706,68.5C433.535,68.8771,433.091,69.0444,432.714,68.874L399.086,53.648C399.009,53.612,398.938,53.5637,398.876,53.505Z" fill="#fff" transform="translate(420.832,50.3736) translate(-420.832,-50.3736)"/>
        <path id="Path_431-2" data-name="Path 431" d="M419.489,52.878C419.435,52.8253,419.388,52.7646,419.352,52.698L408.62,33.206C408.464,32.9231,408.506,32.5718,408.725,32.3343C408.945,32.0968,409.292,32.0265,409.586,32.16L441.97,46.829C442.265,46.9614,442.442,47.2681,442.408,47.5897C442.375,47.9112,442.139,48.175,441.823,48.244L420.173,53.069C419.927,53.124,419.671,53.0523,419.489,52.878Z" fill="#dbdbdb" transform="translate(425.469,42.5901) translate(-425.469,-42.5901)"/>
        <path id="Path_432-2" data-name="Path 432" d="M402.183,52.382C401.996,52.2023,401.915,51.9384,401.969,51.6846C402.024,51.4307,402.205,51.2232,402.45,51.136L413.191,47.291C413.446,47.184,413.74,47.2264,413.955,47.4014C414.169,47.5763,414.27,47.8553,414.217,48.1271C414.164,48.3988,413.965,48.6191,413.7,48.7L402.959,52.546C402.69,52.6438,402.389,52.5802,402.183,52.382Z" fill="#dbdbdb" transform="translate(408.092,49.912) translate(-408.092,-49.912)"/>
        <path id="Path_433-2" data-name="Path 433" d="M430.557,65.235C430.479,65.1606,430.418,65.0702,430.379,64.97L426.186,54.36C426.083,54.1099,426.123,53.8241,426.291,53.6117C426.458,53.3993,426.726,53.2933,426.993,53.3341C427.26,53.375,427.485,53.5564,427.581,53.809L431.774,64.418C431.904,64.746,431.788,65.1203,431.495,65.317C431.202,65.5137,430.811,65.4795,430.557,65.235Z" fill="#dbdbdb" transform="translate(428.978,59.3849) translate(-428.978,-59.3849)"/>
    </g>
    <g id="mejl2-3" data-name="Group 395" transform="translate(479.588,133.026) translate(-420.832,-50.3736)" style="animation: 4.2s linear infinite both mejl2-3_t;">
        <path id="Path_430-6" data-name="Path 430" d="M398.876,53.505C398.795,53.4272,398.733,53.3327,398.693,53.228C398.623,53.0416,398.631,52.8352,398.713,52.654L407.957,32.246C408.129,31.8696,408.573,31.7029,408.95,31.873L442.577,47.1C442.759,47.1808,442.901,47.3315,442.97,47.518C443.04,47.7043,443.033,47.9107,442.951,48.092L433.706,68.5C433.535,68.8771,433.091,69.0444,432.714,68.874L399.086,53.648C399.009,53.612,398.938,53.5637,398.876,53.505Z" fill="#fff" transform="translate(420.832,50.3736) translate(-420.832,-50.3736)"/>
        <path id="Path_431-6" data-name="Path 431" d="M419.489,52.878C419.435,52.8253,419.388,52.7646,419.352,52.698L408.62,33.206C408.464,32.9231,408.506,32.5718,408.725,32.3343C408.945,32.0968,409.292,32.0265,409.586,32.16L441.97,46.829C442.265,46.9614,442.442,47.2681,442.408,47.5897C442.375,47.9112,442.139,48.175,441.823,48.244L420.173,53.069C419.927,53.124,419.671,53.0523,419.489,52.878Z" fill="#dbdbdb" transform="translate(425.469,42.5901) translate(-425.469,-42.5901)"/>
        <path id="Path_432-6" data-name="Path 432" d="M402.183,52.382C401.996,52.2023,401.915,51.9384,401.969,51.6846C402.024,51.4307,402.205,51.2232,402.45,51.136L413.191,47.291C413.446,47.184,413.74,47.2264,413.955,47.4014C414.169,47.5763,414.27,47.8553,414.217,48.1271C414.164,48.3988,413.965,48.6191,413.7,48.7L402.959,52.546C402.69,52.6438,402.389,52.5802,402.183,52.382Z" fill="#dbdbdb" transform="translate(408.092,49.912) translate(-408.092,-49.912)"/>
        <path id="Path_433-6" data-name="Path 433" d="M430.557,65.235C430.479,65.1606,430.418,65.0702,430.379,64.97L426.186,54.36C426.083,54.1099,426.123,53.8241,426.291,53.6117C426.458,53.3993,426.726,53.2933,426.993,53.3341C427.26,53.375,427.485,53.5564,427.581,53.809L431.774,64.418C431.904,64.746,431.788,65.1203,431.495,65.317C431.202,65.5137,430.811,65.4795,430.557,65.235Z" fill="#dbdbdb" transform="translate(428.978,59.3849) translate(-428.978,-59.3849)"/>
    </g>
    <g id="mejl2-7" data-name="Group 395" transform="translate(511.913,131.937) translate(-420.832,-50.3736)" style="animation: 4.2s linear infinite both mejl2-7_t;">
        <path id="Path_430-7" data-name="Path 430" d="M398.876,53.505C398.795,53.4272,398.733,53.3327,398.693,53.228C398.623,53.0416,398.631,52.8352,398.713,52.654L407.957,32.246C408.129,31.8696,408.573,31.7029,408.95,31.873L442.577,47.1C442.759,47.1808,442.901,47.3315,442.97,47.518C443.04,47.7043,443.033,47.9107,442.951,48.092L433.706,68.5C433.535,68.8771,433.091,69.0444,432.714,68.874L399.086,53.648C399.009,53.612,398.938,53.5637,398.876,53.505Z" fill="#fff" transform="translate(420.832,50.3736) translate(-420.832,-50.3736)"/>
        <path id="Path_431-7" data-name="Path 431" d="M419.489,52.878C419.435,52.8253,419.388,52.7646,419.352,52.698L408.62,33.206C408.464,32.9231,408.506,32.5718,408.725,32.3343C408.945,32.0968,409.292,32.0265,409.586,32.16L441.97,46.829C442.265,46.9614,442.442,47.2681,442.408,47.5897C442.375,47.9112,442.139,48.175,441.823,48.244L420.173,53.069C419.927,53.124,419.671,53.0523,419.489,52.878Z" fill="#dbdbdb" transform="translate(425.469,42.5901) translate(-425.469,-42.5901)"/>
        <path id="Path_432-7" data-name="Path 432" d="M402.183,52.382C401.996,52.2023,401.915,51.9384,401.969,51.6846C402.024,51.4307,402.205,51.2232,402.45,51.136L413.191,47.291C413.446,47.184,413.74,47.2264,413.955,47.4014C414.169,47.5763,414.27,47.8553,414.217,48.1271C414.164,48.3988,413.965,48.6191,413.7,48.7L402.959,52.546C402.69,52.6438,402.389,52.5802,402.183,52.382Z" fill="#dbdbdb" transform="translate(408.092,49.912) translate(-408.092,-49.912)"/>
        <path id="Path_433-7" data-name="Path 433" d="M430.557,65.235C430.479,65.1606,430.418,65.0702,430.379,64.97L426.186,54.36C426.083,54.1099,426.123,53.8241,426.291,53.6117C426.458,53.3993,426.726,53.2933,426.993,53.3341C427.26,53.375,427.485,53.5564,427.581,53.809L431.774,64.418C431.904,64.746,431.788,65.1203,431.495,65.317C431.202,65.5137,430.811,65.4795,430.557,65.235Z" fill="#dbdbdb" transform="translate(428.978,59.3849) translate(-428.978,-59.3849)"/>
    </g>
    <g id="mejl2-2" data-name="Group 395" transform="translate(511.913,131.937) translate(-420.832,-50.3736)" style="animation: 4.2s linear infinite both mejl2-2_t;">
        <path id="Path_430-8" data-name="Path 430" d="M398.876,53.505C398.795,53.4272,398.733,53.3327,398.693,53.228C398.623,53.0416,398.631,52.8352,398.713,52.654L407.957,32.246C408.129,31.8696,408.573,31.7029,408.95,31.873L442.577,47.1C442.759,47.1808,442.901,47.3315,442.97,47.518C443.04,47.7043,443.033,47.9107,442.951,48.092L433.706,68.5C433.535,68.8771,433.091,69.0444,432.714,68.874L399.086,53.648C399.009,53.612,398.938,53.5637,398.876,53.505Z" fill="#fff" transform="translate(420.832,50.3736) translate(-420.832,-50.3736)"/>
        <path id="Path_431-8" data-name="Path 431" d="M419.489,52.878C419.435,52.8253,419.388,52.7646,419.352,52.698L408.62,33.206C408.464,32.9231,408.506,32.5718,408.725,32.3343C408.945,32.0968,409.292,32.0265,409.586,32.16L441.97,46.829C442.265,46.9614,442.442,47.2681,442.408,47.5897C442.375,47.9112,442.139,48.175,441.823,48.244L420.173,53.069C419.927,53.124,419.671,53.0523,419.489,52.878Z" fill="#dbdbdb" transform="translate(425.469,42.5901) translate(-425.469,-42.5901)"/>
        <path id="Path_432-8" data-name="Path 432" d="M402.183,52.382C401.996,52.2023,401.915,51.9384,401.969,51.6846C402.024,51.4307,402.205,51.2232,402.45,51.136L413.191,47.291C413.446,47.184,413.74,47.2264,413.955,47.4014C414.169,47.5763,414.27,47.8553,414.217,48.1271C414.164,48.3988,413.965,48.6191,413.7,48.7L402.959,52.546C402.69,52.6438,402.389,52.5802,402.183,52.382Z" fill="#dbdbdb" transform="translate(408.092,49.912) translate(-408.092,-49.912)"/>
        <path id="Path_433-8" data-name="Path 433" d="M430.557,65.235C430.479,65.1606,430.418,65.0702,430.379,64.97L426.186,54.36C426.083,54.1099,426.123,53.8241,426.291,53.6117C426.458,53.3993,426.726,53.2933,426.993,53.3341C427.26,53.375,427.485,53.5564,427.581,53.809L431.774,64.418C431.904,64.746,431.788,65.1203,431.495,65.317C431.202,65.5137,430.811,65.4795,430.557,65.235Z" fill="#dbdbdb" transform="translate(428.978,59.3849) translate(-428.978,-59.3849)"/>
    </g>
    <g id="Group_392" data-name="Group 392" transform="translate(281.789,213.026) translate(-19.0065,-18.024)" style="animation: 4.2s linear infinite both Group_392_t;">
        <path id="Path_37" data-name="Path 37" d="M19.007,9.586C8.51,9.586,0,17.215,0,26.627C0.0112838,30.0118,1.11385,33.3026,3.144,36.011C2.883,39.12,2.133,43.5,0,45.634C0,45.634,6.44,44.729,10.851,42.098C10.851,42.098,10.858,42.09,10.862,42.088C12.226,41.247,13.762,40.266,14.133,39.972C14.3648,39.6511,14.804,39.5615,15.143,39.766C15.3711,39.9055,15.5113,40.1526,15.514,40.42C15.514,40.862,15.508,40.986,12.738,42.715C14.7668,43.3492,16.8804,43.6706,19.006,43.668C29.506,43.668,38.013,36.039,38.013,26.627C38.013,17.215,29.5,9.586,19.007,9.586Z" fill="#8338ec" transform="translate(19.0065,18.024) translate(-19.0065,-27.61)"/>
        <path id="Path_39" data-name="Path 39" d="M28.9,25.521L18.26,25.521C17.84,25.521,17.4995,25.1805,17.4995,24.7605C17.4995,24.3405,17.84,24,18.26,24L28.9,24C29.32,24,29.6605,24.3405,29.6605,24.7605C29.6605,25.1805,29.32,25.521,28.9,25.521Z" fill="#fff" transform="translate(19.385,11.7195) translate(-23.58,-24.7605)"/>
        <path id="Path_40" data-name="Path 40" d="M31.767,32.521L12.76,32.521C12.34,32.521,11.9995,32.1805,11.9995,31.7605C11.9995,31.3405,12.34,31,12.76,31L31.767,31C32.187,31,32.5275,31.3405,32.5275,31.7605C32.5275,32.1805,32.187,32.521,31.767,32.521Z" fill="#fff" transform="translate(19.3865,17.0415) translate(-22.2635,-31.7605)"/>
        <path id="Path_41" data-name="Path 41" d="M31.767,39.521L12.76,39.521C12.4883,39.521,12.2372,39.376,12.1014,39.1408C11.9655,38.9055,11.9655,38.6155,12.1014,38.3802C12.2372,38.145,12.4883,38,12.76,38L31.767,38C32.187,38,32.5275,38.3405,32.5275,38.7605C32.5275,39.1805,32.187,39.521,31.767,39.521Z" fill="#fff" transform="translate(19.3865,22.3625) translate(-22.2635,-38.7605)"/>
    </g>
    <g id="Group_392-2" data-name="Group 392" transform="translate(314.887,193.259) translate(-19.0065,-18.024)" style="animation: 4.2s linear infinite both Group_392-2_t;">
        <path id="Path_37-6" data-name="Path 37" d="M19.007,9.586C8.51,9.586,0,17.215,0,26.627C0.0112838,30.0118,1.11385,33.3026,3.144,36.011C2.883,39.12,2.133,43.5,0,45.634C0,45.634,6.44,44.729,10.851,42.098C10.851,42.098,10.858,42.09,10.862,42.088C12.226,41.247,13.762,40.266,14.133,39.972C14.3648,39.6511,14.804,39.5615,15.143,39.766C15.3711,39.9055,15.5113,40.1526,15.514,40.42C15.514,40.862,15.508,40.986,12.738,42.715C14.7668,43.3492,16.8804,43.6706,19.006,43.668C29.506,43.668,38.013,36.039,38.013,26.627C38.013,17.215,29.5,9.586,19.007,9.586Z" fill="#ef5827" transform="translate(19.0065,18.024) translate(-19.0065,-27.61)"/>
        <path id="Path_39-6" data-name="Path 39" d="M28.9,25.521L18.26,25.521C17.84,25.521,17.4995,25.1805,17.4995,24.7605C17.4995,24.3405,17.84,24,18.26,24L28.9,24C29.32,24,29.6605,24.3405,29.6605,24.7605C29.6605,25.1805,29.32,25.521,28.9,25.521Z" fill="#fff" transform="translate(19.385,11.7195) translate(-23.58,-24.7605)"/>
        <path id="Path_40-6" data-name="Path 40" d="M31.767,32.521L12.76,32.521C12.34,32.521,11.9995,32.1805,11.9995,31.7605C11.9995,31.3405,12.34,31,12.76,31L31.767,31C32.187,31,32.5275,31.3405,32.5275,31.7605C32.5275,32.1805,32.187,32.521,31.767,32.521Z" fill="#fff" transform="translate(19.3865,17.0415) translate(-22.2635,-31.7605)"/>
        <path id="Path_41-6" data-name="Path 41" d="M31.767,39.521L12.76,39.521C12.4883,39.521,12.2372,39.376,12.1014,39.1408C11.9655,38.9055,11.9655,38.6155,12.1014,38.3802C12.2372,38.145,12.4883,38,12.76,38L31.767,38C32.187,38,32.5275,38.3405,32.5275,38.7605C32.5275,39.1805,32.187,39.521,31.767,39.521Z" fill="#fff" transform="translate(19.3865,22.3625) translate(-22.2635,-38.7605)"/>
    </g>
    <g id="Group_392-3" data-name="Group 392" transform="translate(331.939,193.259) translate(-19.0065,-18.024)" style="animation: 4.2s linear infinite both Group_392-3_t;">
        <path id="Path_37-7" data-name="Path 37" d="M19.007,9.586C8.51,9.586,0,17.215,0,26.627C0.0112838,30.0118,1.11385,33.3026,3.144,36.011C2.883,39.12,2.133,43.5,0,45.634C0,45.634,6.44,44.729,10.851,42.098C10.851,42.098,10.858,42.09,10.862,42.088C12.226,41.247,13.762,40.266,14.133,39.972C14.3648,39.6511,14.804,39.5615,15.143,39.766C15.3711,39.9055,15.5113,40.1526,15.514,40.42C15.514,40.862,15.508,40.986,12.738,42.715C14.7668,43.3492,16.8804,43.6706,19.006,43.668C29.506,43.668,38.013,36.039,38.013,26.627C38.013,17.215,29.5,9.586,19.007,9.586Z" fill="#d8ca22" transform="translate(19.0065,18.024) translate(-19.0065,-27.61)"/>
        <path id="Path_39-7" data-name="Path 39" d="M28.9,25.521L18.26,25.521C17.84,25.521,17.4995,25.1805,17.4995,24.7605C17.4995,24.3405,17.84,24,18.26,24L28.9,24C29.32,24,29.6605,24.3405,29.6605,24.7605C29.6605,25.1805,29.32,25.521,28.9,25.521Z" fill="#fff" transform="translate(19.385,11.7195) translate(-23.58,-24.7605)"/>
        <path id="Path_40-7" data-name="Path 40" d="M31.767,32.521L12.76,32.521C12.34,32.521,11.9995,32.1805,11.9995,31.7605C11.9995,31.3405,12.34,31,12.76,31L31.767,31C32.187,31,32.5275,31.3405,32.5275,31.7605C32.5275,32.1805,32.187,32.521,31.767,32.521Z" fill="#fff" transform="translate(19.3865,17.0415) translate(-22.2635,-31.7605)"/>
        <path id="Path_41-7" data-name="Path 41" d="M31.767,39.521L12.76,39.521C12.4883,39.521,12.2372,39.376,12.1014,39.1408C11.9655,38.9055,11.9655,38.6155,12.1014,38.3802C12.2372,38.145,12.4883,38,12.76,38L31.767,38C32.187,38,32.5275,38.3405,32.5275,38.7605C32.5275,39.1805,32.187,39.521,31.767,39.521Z" fill="#fff" transform="translate(19.3865,22.3625) translate(-22.2635,-38.7605)"/>
    </g>
    <g id="email-1" data-name="Group 389" transform="translate(311.764,212.914) translate(-420.832,-50.3736)" style="animation: 4.2s linear infinite both email-1_t;">
        <path id="Path_430-3" data-name="Path 430" d="M398.876,53.505C398.795,53.4272,398.733,53.3327,398.693,53.228C398.623,53.0416,398.631,52.8352,398.713,52.654L407.957,32.246C408.129,31.8696,408.573,31.7029,408.95,31.873L442.577,47.1C442.759,47.1808,442.901,47.3315,442.97,47.518C443.04,47.7043,443.033,47.9107,442.951,48.092L433.706,68.5C433.535,68.8771,433.091,69.0444,432.714,68.874L399.086,53.648C399.009,53.612,398.938,53.5637,398.876,53.505Z" fill="#fff" transform="translate(420.832,50.3736) translate(-420.832,-50.3736)"/>
        <path id="Path_431-3" data-name="Path 431" d="M419.489,52.878C419.435,52.8253,419.388,52.7646,419.352,52.698L408.62,33.206C408.464,32.9231,408.506,32.5718,408.725,32.3343C408.945,32.0968,409.292,32.0265,409.586,32.16L441.97,46.829C442.265,46.9614,442.442,47.2681,442.408,47.5897C442.375,47.9112,442.139,48.175,441.823,48.244L420.173,53.069C419.927,53.124,419.671,53.0523,419.489,52.878Z" fill="#dbdbdb" transform="translate(425.469,42.5901) translate(-425.469,-42.5901)"/>
        <path id="Path_432-3" data-name="Path 432" d="M402.183,52.382C401.996,52.2023,401.915,51.9384,401.969,51.6846C402.024,51.4307,402.205,51.2232,402.45,51.136L413.191,47.291C413.446,47.184,413.74,47.2264,413.955,47.4014C414.169,47.5763,414.27,47.8553,414.217,48.1271C414.164,48.3988,413.965,48.6191,413.7,48.7L402.959,52.546C402.69,52.6438,402.389,52.5802,402.183,52.382Z" fill="#dbdbdb" transform="translate(408.092,49.912) translate(-408.092,-49.912)"/>
        <path id="Path_433-3" data-name="Path 433" d="M430.557,65.235C430.479,65.1606,430.418,65.0702,430.379,64.97L426.186,54.36C426.083,54.1099,426.123,53.8241,426.291,53.6117C426.458,53.3993,426.726,53.2933,426.993,53.3341C427.26,53.375,427.485,53.5564,427.581,53.809L431.774,64.418C431.904,64.746,431.788,65.1203,431.495,65.317C431.202,65.5137,430.811,65.4795,430.557,65.235Z" fill="#dbdbdb" transform="translate(428.978,59.3849) translate(-428.978,-59.3849)"/>
    </g>
    <g id="email-2" data-name="Group 389" transform="translate(326.819,196.937) translate(-420.832,-50.3736)" style="animation: 4.2s linear infinite both email-2_t;">
        <path id="Path_430-4" data-name="Path 430" d="M398.876,53.505C398.795,53.4272,398.733,53.3327,398.693,53.228C398.623,53.0416,398.631,52.8352,398.713,52.654L407.957,32.246C408.129,31.8696,408.573,31.7029,408.95,31.873L442.577,47.1C442.759,47.1808,442.901,47.3315,442.97,47.518C443.04,47.7043,443.033,47.9107,442.951,48.092L433.706,68.5C433.535,68.8771,433.091,69.0444,432.714,68.874L399.086,53.648C399.009,53.612,398.938,53.5637,398.876,53.505Z" fill="#fff" transform="translate(420.832,50.3736) translate(-420.832,-50.3736)"/>
        <path id="Path_431-4" data-name="Path 431" d="M419.489,52.878C419.435,52.8253,419.388,52.7646,419.352,52.698L408.62,33.206C408.464,32.9231,408.506,32.5718,408.725,32.3343C408.945,32.0968,409.292,32.0265,409.586,32.16L441.97,46.829C442.265,46.9614,442.442,47.2681,442.408,47.5897C442.375,47.9112,442.139,48.175,441.823,48.244L420.173,53.069C419.927,53.124,419.671,53.0523,419.489,52.878Z" fill="#dbdbdb" transform="translate(425.469,42.5901) translate(-425.469,-42.5901)"/>
        <path id="Path_432-4" data-name="Path 432" d="M402.183,52.382C401.996,52.2023,401.915,51.9384,401.969,51.6846C402.024,51.4307,402.205,51.2232,402.45,51.136L413.191,47.291C413.446,47.184,413.74,47.2264,413.955,47.4014C414.169,47.5763,414.27,47.8553,414.217,48.1271C414.164,48.3988,413.965,48.6191,413.7,48.7L402.959,52.546C402.69,52.6438,402.389,52.5802,402.183,52.382Z" fill="#dbdbdb" transform="translate(408.092,49.912) translate(-408.092,-49.912)"/>
        <path id="Path_433-4" data-name="Path 433" d="M430.557,65.235C430.479,65.1606,430.418,65.0702,430.379,64.97L426.186,54.36C426.083,54.1099,426.123,53.8241,426.291,53.6117C426.458,53.3993,426.726,53.2933,426.993,53.3341C427.26,53.375,427.485,53.5564,427.581,53.809L431.774,64.418C431.904,64.746,431.788,65.1203,431.495,65.317C431.202,65.5137,430.811,65.4795,430.557,65.235Z" fill="#dbdbdb" transform="translate(428.978,59.3849) translate(-428.978,-59.3849)"/>
    </g>
    <g id="email-3" data-name="Group 389" transform="translate(282.701,213.676) translate(-420.832,-50.3736)" style="animation: 4.2s linear infinite both email-3_t;">
        <path id="Path_430-5" data-name="Path 430" d="M398.876,53.505C398.795,53.4272,398.733,53.3327,398.693,53.228C398.623,53.0416,398.631,52.8352,398.713,52.654L407.957,32.246C408.129,31.8696,408.573,31.7029,408.95,31.873L442.577,47.1C442.759,47.1808,442.901,47.3315,442.97,47.518C443.04,47.7043,443.033,47.9107,442.951,48.092L433.706,68.5C433.535,68.8771,433.091,69.0444,432.714,68.874L399.086,53.648C399.009,53.612,398.938,53.5637,398.876,53.505Z" fill="#fff" transform="translate(420.832,50.3736) translate(-420.832,-50.3736)"/>
        <path id="Path_431-5" data-name="Path 431" d="M419.489,52.878C419.435,52.8253,419.388,52.7646,419.352,52.698L408.62,33.206C408.464,32.9231,408.506,32.5718,408.725,32.3343C408.945,32.0968,409.292,32.0265,409.586,32.16L441.97,46.829C442.265,46.9614,442.442,47.2681,442.408,47.5897C442.375,47.9112,442.139,48.175,441.823,48.244L420.173,53.069C419.927,53.124,419.671,53.0523,419.489,52.878Z" fill="#dbdbdb" transform="translate(425.469,42.5901) translate(-425.469,-42.5901)"/>
        <path id="Path_432-5" data-name="Path 432" d="M402.183,52.382C401.996,52.2023,401.915,51.9384,401.969,51.6846C402.024,51.4307,402.205,51.2232,402.45,51.136L413.191,47.291C413.446,47.184,413.74,47.2264,413.955,47.4014C414.169,47.5763,414.27,47.8553,414.217,48.1271C414.164,48.3988,413.965,48.6191,413.7,48.7L402.959,52.546C402.69,52.6438,402.389,52.5802,402.183,52.382Z" fill="#dbdbdb" transform="translate(408.092,49.912) translate(-408.092,-49.912)"/>
        <path id="Path_433-5" data-name="Path 433" d="M430.557,65.235C430.479,65.1606,430.418,65.0702,430.379,64.97L426.186,54.36C426.083,54.1099,426.123,53.8241,426.291,53.6117C426.458,53.3993,426.726,53.2933,426.993,53.3341C427.26,53.375,427.485,53.5564,427.581,53.809L431.774,64.418C431.904,64.746,431.788,65.1203,431.495,65.317C431.202,65.5137,430.811,65.4795,430.557,65.235Z" fill="#dbdbdb" transform="translate(428.978,59.3849) translate(-428.978,-59.3849)"/>
    </g>
    <rect id="Rectangle_285" data-name="Rectangle 285" width="116" height="137" rx="12" fill="#cf72cb" transform="translate(321,369) translate(-58,-68.5)"/>
    <rect id="Rectangle_287" data-name="Rectangle 287" width="117" height="137" rx="12" fill="#eb575e" transform="translate(667.5,369) translate(-58.5,-68.5)"/>
    <rect id="Rectangle_290" data-name="Rectangle 290" width="140" height="22" rx="4" fill="#b759b1" transform="translate(321,311.5) translate(-70,-11)"/>
    <rect id="Rectangle_289" data-name="Rectangle 289" width="140" height="22" rx="4" fill="#d6484d" transform="translate(667,311.5) translate(-70,-11)"/>
    <g transform="translate(483.5,92) translate(-68.5,-68.5)" style="animation: 4.2s linear infinite both a0_t;">
        <g filter="url(#Ellipse_144)" transform="translate(68.5,68.5) translate(-483.5,-85)">
            <ellipse id="Ellipse_144-2" data-name="Ellipse 144" fill="#2e3a5a" rx="68.5" ry="68.5" transform="translate(415,16.5) translate(68.5,68.5)"/>
        </g>
        <path id="twitter_7_" data-name="twitter (7)" d="M67.536,50.517C67.0844,50.0234,66.3574,49.895,65.764,50.204C65.1541,50.4811,64.5231,50.7091,63.877,50.886C64.8851,49.5962,65.6462,48.1313,66.122,46.565C66.2809,45.9657,66.0486,45.3309,65.5403,44.9758C65.0321,44.6206,64.3561,44.6207,63.848,44.976C61.6026,46.1911,59.2159,47.1244,56.742,47.755C51.6492,42.948,43.8915,42.349,38.121,46.317C33.6761,49.3225,31.2291,54.5169,31.742,59.858C22.1061,59.0373,13.2403,54.2627,7.252,46.669C6.94507,46.2979,6.48103,46.0933,6,46.117C5.51109,46.1465,5.06865,46.4166,4.819,46.838C2.78567,50.1613,2.19693,54.1705,3.189,57.938C3.69647,59.8714,4.56618,61.6909,5.752,63.3C5.21716,63.038,4.71392,62.7159,4.252,62.34C3.8098,61.9816,3.201,61.9092,2.68707,62.1539C2.17315,62.3986,1.84556,62.9168,1.845,63.486C1.93221,68.95,4.93227,73.951,9.712,76.6C9.06783,76.5206,8.43199,76.384,7.812,76.192C7.25817,76.0257,6.65823,76.1979,6.2769,76.6326C5.89556,77.0673,5.80298,77.6845,6.04,78.212C8.11448,82.8587,12.2618,86.2527,17.227,87.367C12.4958,90.0362,7.04778,91.1576,1.647,90.574C0.946885,90.4847,0.283521,90.9103,0.073,91.584C-0.144298,92.2526,0.139715,92.9816,0.752,93.327C7.36465,97.2551,14.8868,99.392,22.577,99.527C29.955,99.5,37.1609,97.2963,43.292,93.192C55,85.426,62.291,71.48,61.275,59C63.7789,57.1486,65.9513,54.8867,67.7,52.31C68.058,51.7442,67.9907,51.0084,67.536,50.517Z" fill="#03a9f4" transform="translate(69.5397,68.3711) translate(-33.9647,-71.6141)"/>
    </g>
    <g transform="translate(543.5,130.5) translate(-33.5,-34)" style="animation: 4.2s linear infinite both a1_t;">
        <g filter="url(#Ellipse_145)" transform="translate(33.5,34) translate(-543.5,-123.5)">
            <ellipse id="Ellipse_145-2" data-name="Ellipse 145" rx="33.5" ry="34" fill="#2e3a5a" transform="translate(510,89.5) translate(33.5,34)"/>
        </g>
        <g id="Group_124" data-name="Group 124" transform="translate(34.5265,34.2655) translate(-17.8785,-16.9525)">
            <path id="Path_37-2" data-name="Path 37" d="M17.877,9.586C8,9.586,0,16.762,0,25.614C0.010779,28.7975,1.04775,31.8926,2.957,34.44C2.712,37.365,2.006,41.484,0,43.491C0,43.491,6.057,42.639,10.206,40.165C10.206,40.165,10.212,40.158,10.216,40.156C11.499,39.365,12.94,38.442,13.292,38.166C13.51,37.8641,13.9231,37.7798,14.242,37.972C14.4565,38.1032,14.5883,38.3356,14.591,38.587C14.591,39.003,14.585,39.119,11.98,40.745C13.8895,41.3432,15.879,41.6467,17.88,41.645C27.753,41.645,35.757,34.469,35.757,25.617C35.757,16.765,27.75,9.586,17.877,9.586Z" fill="#fca311" transform="translate(17.8785,16.9525) translate(-17.8785,-26.5385)"/>
            <path id="Path_39-2" data-name="Path 39" d="M28.226,25.43L18.215,25.43C17.8201,25.43,17.5,25.1099,17.5,24.715C17.5,24.3201,17.8201,24,18.215,24L28.226,24C28.6209,24,28.941,24.3201,28.941,24.715C28.941,25.1099,28.6209,25.43,28.226,25.43Z" fill="#fff" transform="translate(18.2345,11.022) translate(-23.2205,-24.715)"/>
            <path id="Path_40-2" data-name="Path 40" d="M30.592,32.43L12.715,32.43C12.3201,32.43,12,32.1099,12,31.715C12,31.3201,12.3201,31,12.715,31L30.592,31C30.9869,31,31.307,31.3201,31.307,31.715C31.307,32.1099,30.9869,32.43,30.592,32.43Z" fill="#fff" transform="translate(18.2345,16.028) translate(-21.6535,-31.715)"/>
            <path id="Path_41-2" data-name="Path 41" d="M30.592,39.43L12.715,39.43C12.4596,39.43,12.2235,39.2937,12.0958,39.0725C11.9681,38.8513,11.9681,38.5787,12.0958,38.3575C12.2235,38.1363,12.4596,38,12.715,38L30.592,38C30.8474,38,31.0835,38.1363,31.2112,38.3575C31.3389,38.5787,31.3389,38.8513,31.2112,39.0725C31.0835,39.2937,30.8474,39.43,30.592,39.43Z" fill="#fff" transform="translate(18.2345,21.033) translate(-21.6535,-38.715)"/>
        </g>
    </g>
    <g id="discord-icon" transform="translate(297.414,173.612) translate(-66.5,-65.5)" style="animation: 4.2s linear infinite both discord-icon_t;">
        <g filter="url(#Ellipse_148)" transform="translate(57.6911,58.868) translate(-280.5,-157)">
            <ellipse id="Ellipse_148-2" data-name="Ellipse 148" rx="66.5" ry="65.5" fill="#2e3a5a" transform="translate(222.809,98.132) translate(66.5,65.5)"/>
        </g>
        <g id="discord" transform="translate(57.5086,59.017) translate(-30.9685,-33.783)">
            <path id="Path_26" data-name="Path 26" d="M8.264,59.673L48.407,59.673L46.49,53.466C46.774,53.714,62.937,67.566,62.937,67.566L62.937,6.968C62.6714,3.04613,59.4129,0.000482552,55.482,0L8.278,0.008C4.35518,-0.0433874,1.11593,3.06055,1,6.982L1,52.7C1.01898,54.5942,1.79993,56.4009,3.16665,57.7124C4.53336,59.024,6.37067,59.73,8.264,59.671ZM37.959,16L37.866,16.034L37.9,16ZM16.476,19.572C19.339,17.3777,22.8141,16.1292,26.419,16L26.805,16.38C20.49,17.883,17.627,20.7,17.627,20.7C26.6289,16.2913,37.1774,16.3602,46.121,20.886C43.5302,18.8319,40.5296,17.3565,37.321,16.559L37.845,16.044C41.3955,16.1975,44.8136,17.4354,47.639,19.591C50.9156,25.6728,52.6951,32.4481,52.83,39.355C52.658,39.147,49.609,44.045,41.763,44.214C41.763,44.214,40.434,42.714,39.488,41.399C44.077,40.081,45.794,37.446,45.794,37.446C36.861,43.071,29.032,42.193,19.665,38.392C19.5977,38.3922,19.5342,38.3611,19.493,38.308L19.493,38.291C19.451,38.2388,19.388,38.208,19.321,38.207L19.152,38.207C18.8442,38.0009,18.5247,37.8129,18.195,37.644C19.7426,39.6012,21.8945,40.9912,24.315,41.597C23.158,42.917,22.015,44.418,22.015,44.418C14.172,44.232,11.315,39.334,11.315,39.334C11.4443,32.4302,13.2129,25.6559,16.475,19.57Z" fill="#5c6bc0" transform="translate(39.7774,40.415) translate(-31.9685,-33.783)"/>
            <path id="Path_27" data-name="Path 27" d="M16.65,17.627C18.6763,17.5532,20.2808,15.8891,20.2808,13.8615C20.2808,11.8339,18.6763,10.1698,16.65,10.096L16.65,10.1C14.6259,10.1738,13.0232,11.8361,13.0232,13.8615C13.0232,15.8869,14.6259,17.5492,16.65,17.623Z" fill="#5c6bc0" transform="translate(46.2768,38.8205) translate(-16.652,-13.8615)"/>
            <path id="Path_28" data-name="Path 28" d="M12.032,17.627C14.0584,17.5562,15.6654,15.8944,15.6684,13.8668C15.6713,11.8392,14.0692,10.1728,12.043,10.096L12.032,10.104C10.0079,10.1778,8.40516,11.8401,8.40516,13.8655C8.40516,15.8909,10.0079,17.5532,12.032,17.627Z" fill="#5c6bc0" transform="translate(33.2786,38.8205) translate(-12.0368,-13.8615)"/>
        </g>
    </g>
    <g id="bellow-disc" transform="translate(243.078,227.572) translate(-33.5,-33)" style="animation: 4.2s linear infinite both bellow-disc_t;">
        <g filter="url(#Ellipse_149)" transform="translate(33.5,33) translate(-247.5,-232.5)">
            <ellipse id="Ellipse_149-2" data-name="Ellipse 149" rx="33.5" ry="33" fill="#2e3a5a" transform="translate(214,199.5) translate(33.5,33)"/>
        </g>
        <g id="Group_126" data-name="Group 126" transform="translate(34.5775,32.1705) translate(-17.6635,-16.7505)">
            <path id="Path_37-3" data-name="Path 37" d="M17.664,9.586C7.908,9.586,0,16.676,0,25.423C0.0104936,28.5686,1.03521,31.627,2.922,34.144C2.68,37.034,1.982,41.104,0,43.087C0,43.087,5.984,42.246,10.085,39.801C10.085,39.801,10.091,39.794,10.095,39.792C11.363,39.011,12.786,38.098,13.135,37.826C13.3505,37.5282,13.7582,37.4451,14.073,37.635C14.2851,37.7646,14.4155,37.9944,14.418,38.243C14.418,38.654,14.412,38.769,11.838,40.375C13.7234,40.9645,15.6876,41.2633,17.663,41.261C27.418,41.261,35.327,34.171,35.327,25.424C35.327,16.677,27.419,9.586,17.664,9.586Z" fill="#fca328" transform="translate(17.6635,16.7505) translate(-17.6635,-26.3365)"/>
            <path id="Path_39-3" data-name="Path 39" d="M28.1,25.413L18.207,25.413C17.8271,25.3987,17.5266,25.0866,17.5266,24.7065C17.5266,24.3264,17.8271,24.0143,18.207,24L28.1,24C28.4799,24.0143,28.7804,24.3264,28.7804,24.7065C28.7804,25.0866,28.4799,25.3987,28.1,25.413Z" fill="#fff" transform="translate(18.0175,10.8905) translate(-23.1535,-24.7065)"/>
            <path id="Path_40-3" data-name="Path 40" d="M30.37,32.413L12.707,32.413C12.3271,32.3987,12.0266,32.0866,12.0266,31.7065C12.0266,31.3264,12.3271,31.0143,12.707,31L30.37,31C30.7499,31.0143,31.0504,31.3264,31.0504,31.7065C31.0504,32.0866,30.7499,32.3987,30.37,32.413Z" fill="#fff" transform="translate(18.0175,15.8365) translate(-21.5385,-31.7065)"/>
            <path id="Path_41-3" data-name="Path 41" d="M30.37,39.413L12.707,39.413C12.4483,39.4227,12.2049,39.2902,12.0726,39.0676C11.9403,38.845,11.9403,38.568,12.0726,38.3454C12.2049,38.1228,12.4483,37.9903,12.707,38L30.37,38C30.6287,37.9903,30.8721,38.1228,31.0044,38.3454C31.1367,38.568,31.1367,38.845,31.0044,39.0676C30.8721,39.2902,30.6287,39.4227,30.37,39.413Z" fill="#fff" transform="translate(18.0175,20.7825) translate(-21.5385,-38.7065)"/>
        </g>
    </g>
    <g transform="translate(689.504,167.683) translate(-59.5,-60)" style="animation: 4.2s linear infinite both a2_t;">
        <g filter="url(#Ellipse_146)" transform="translate(59.5,60) translate(-696.5,-171.5)">
            <ellipse id="Ellipse_146-2" data-name="Ellipse 146" rx="59.5" ry="60" fill="#2e3a5a" transform="translate(637,111.5) translate(59.5,60)"/>
        </g>
        <g id="telegram" transform="translate(59.5,60) translate(-31.543,-32.247)">
            <ellipse id="Ellipse_31" data-name="Ellipse 31" rx="31.5" ry="32" fill="#039be5" transform="translate(0.043,0.247) translate(31.5,32)"/>
            <path id="Path_25" data-name="Path 25" d="M6.486,19.137L37.01,7.368C38.427,6.856,39.664,7.714,39.205,9.856L39.205,9.856L34.005,34.341C33.62,36.077,32.588,36.499,31.145,35.681L23.23,29.848L19.413,33.526C19.0307,34.0213,18.4387,34.3092,17.813,34.304L18.375,26.25L33.053,13C33.691,12.438,32.911,12.121,32.069,12.681L13.942,24.089L6.127,21.652C4.427,21.114,4.394,19.952,6.486,19.138Z" fill="#fff" transform="translate(29.8191,33.9215) translate(-22.0921,-21.6535)"/>
        </g>
    </g>
    <g transform="translate(751,138.7) translate(-25,-25)" style="animation: 4.2s linear infinite both a3_t;">
        <g filter="url(#Ellipse_147)" transform="translate(25,25) translate(-742,-133.5)">
            <ellipse id="Ellipse_147-2" data-name="Ellipse 147" fill="#2e3a5a" rx="25" ry="25" transform="translate(717,108.5) translate(25,25)"/>
        </g>
        <g id="Group_125" data-name="Group 125" transform="translate(25.9005,25.6795) translate(-13.3295,-12.6385)">
            <path id="Path_37-4" data-name="Path 37" d="M13.328,9.586C5.967,9.586,0,14.936,0,21.535C0.0066065,23.9079,0.778107,26.2153,2.2,28.115C2.022,30.3,1.5,33.367,0,34.863C2.65668,34.4661,5.22841,33.6283,7.609,32.384L7.616,32.377C8.572,31.787,9.647,31.099,9.909,30.893C10.0718,30.6684,10.3794,30.6058,10.617,30.749C10.7767,30.8467,10.8749,31.0198,10.877,31.207C10.877,31.517,10.877,31.607,8.931,32.816C10.3551,33.2615,11.8388,33.4871,13.331,33.485C20.692,33.485,26.659,28.135,26.659,21.536C26.659,14.937,20.688,9.586,13.328,9.586Z" fill="#febe2c" transform="translate(13.3295,12.6385) translate(-13.3295,-22.2245)"/>
            <path id="Path_39-4" data-name="Path 39" d="M25.5,25.066L18.033,25.066C17.7386,25.066,17.5,24.8274,17.5,24.533C17.5,24.2386,17.7386,24,18.033,24L25.5,24C25.7944,24,26.033,24.2386,26.033,24.533C26.033,24.8274,25.7944,25.066,25.5,25.066Z" fill="#fff" transform="translate(13.5955,8.217) translate(-21.7665,-24.533)"/>
            <path id="Path_40-4" data-name="Path 40" d="M25.861,32.066L12.533,32.066C12.2386,32.066,12,31.8274,12,31.533C12,31.2386,12.2386,31,12.533,31L25.861,31C26.1554,31,26.394,31.2386,26.394,31.533C26.394,31.8274,26.1554,32.066,25.861,32.066Z" fill="#fff" transform="translate(13.594,11.949) translate(-19.197,-31.533)"/>
            <path id="Path_41-4" data-name="Path 41" d="M25.861,39.066L12.533,39.066C12.2386,39.066,12,38.8274,12,38.533C12,38.2386,12.2386,38,12.533,38L25.861,38C26.1554,38,26.394,38.2386,26.394,38.533C26.394,38.8274,26.1554,39.066,25.861,39.066Z" fill="#fff" transform="translate(13.594,15.681) translate(-19.197,-38.533)"/>
        </g>
    </g>
    <rect id="Rectangle_286" data-name="Rectangle 286" width="145" height="169" rx="12" fill="#f0cc49" transform="translate(495.5,362) translate(-72.5,-84.5)"/>
    <rect id="Rectangle_288" data-name="Rectangle 288" width="174" height="27" rx="4" fill="#dbb93f" transform="translate(495,291) translate(-87,-13.5)"/>
</svg>
