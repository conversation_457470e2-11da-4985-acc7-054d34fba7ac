{
  // https://nuxt.com/docs/guide/concepts/typescript
  "extends": ["../../tsconfig.base.json", "./.nuxt/tsconfig.json"],
  "include": ["./types/*.d.ts", ".nuxt/nuxt.d.ts", "**/*", "../../eslint.config.mjs"],
  "compilerOptions": {
    "esModuleInterop": true,
    "skipLibCheck": true,
    "target": "es2022",
    "allowJs": true,
    "resolveJsonModule": true,
    "moduleDetection": "force",
    "isolatedModules": true,
    "verbatimModuleSyntax": true,
    "strict": true,
    "noUncheckedIndexedAccess": true,
    "noImplicitOverride": true,
    "module": "ESNext",
    "moduleResolution": "Bundler",
    "noEmit": true,
    "lib": ["es2022", "dom", "dom.iterable"]
  }
}
