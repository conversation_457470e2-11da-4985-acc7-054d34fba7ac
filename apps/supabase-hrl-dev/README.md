# Supabase Development Directory
This directory was used for managing database migration between local dev machine and supabase project, these can make change was trackable and maintainable because we need to applied some security like RLS and policy.

## Prerequisites
Before you begin, ensure you have met the following requirements:

- [Supabase CLI]((https://supabase.com/docs/guides/local-development/cli/getting-started)) (you can using NPX or global cli)
- Supabase account with some access to nodiens project
- Docker Desktop
- NodeJS* (You might be needed if don't want to install global CLI)

## Development Setup
1. Start your supabase project using this command, make sure there is no other supabase project was running on background.
```cmd
supabase start
```
2. Run this command in root project
```cmd
supabase link
```
3. Select hrl-dev project
4. If you have database password for nodiens project just fill password, this steps will require if you want to manage database migration.
5. Start supabase project after you've finished to linked project

**Important note**: Please stop supabase project if you have finished on your work, because sometimes you have encounter some issue with docker storage if you're not stopping project before closing docker desktop.

---

Please refer into supabase CLI documentation to operating and control supabase project on your local machine : https://supabase.com/docs/guides/local-development/cli/getting-started

## Manage Database Migration
We don't write database migration manually but supabase have powerful tools to generate automatically, because basically supabase was using raw sql query to manage migration.

### How to generate migration files
1. Before you make it changes please syncronize between migration file and your local machine by using this command.
```cmd
supabase migration up
```
2. Make some changes on databases like create table, alter table, adding policy or anything related with database.
3. Since you've done to make changes, just run this command on your root project
```cmd
supabase db diff -f migration_file_name
```
1. After run these command you make look new migration file inside `supabase/migrations`
2. You can push migration to database using this command.
```cmd
supabase db push
```


### Production Rules
Don't push anything to production if migration was not final or not complete, keep sync migration between `hrl-dev` and `hrl-prod`.

### Another References
[[GUIDE] Sync local and prod schemas when they're out of sync](https://github.com/orgs/supabase/discussions/18483)

[How to securing your data](https://supabase.com/docs/guides/database/secure-data)

[How to use drizzle ORM using Supabase DB](https://supabase.com/docs/guides/database/drizzle)

## Other Tips
We can make some serverless function on supabase, but it's only support deno as runtime (typescript).