create table "public"."pricings" (
    "id" bigint generated by default as identity not null,
    "name" character varying not null,
    "description" text not null,
    "monthly_price" real not null,
    "annual_price" real not null,
    "stripe_price_ids" jsonb not null,
    "features" jsonb not null,
    "created_at" timestamp with time zone not null default now(),
    "is_enabled" boolean not null default false
);


alter table "public"."user_subscriptions" add column "active_at" timestamp with time zone;

alter table "public"."user_subscriptions" add column "payment_status" character varying not null default 'waiting_payment'::character varying;

alter table "public"."user_subscriptions" alter column "status" set default 'pending'::character varying;

CREATE UNIQUE INDEX pricings_pkey ON public.pricings USING btree (id);

alter table "public"."pricings" add constraint "pricings_pkey" PRIMARY KEY using index "pricings_pkey";

grant delete on table "public"."pricings" to "anon";

grant insert on table "public"."pricings" to "anon";

grant references on table "public"."pricings" to "anon";

grant select on table "public"."pricings" to "anon";

grant trigger on table "public"."pricings" to "anon";

grant truncate on table "public"."pricings" to "anon";

grant update on table "public"."pricings" to "anon";

grant delete on table "public"."pricings" to "authenticated";

grant insert on table "public"."pricings" to "authenticated";

grant references on table "public"."pricings" to "authenticated";

grant select on table "public"."pricings" to "authenticated";

grant trigger on table "public"."pricings" to "authenticated";

grant truncate on table "public"."pricings" to "authenticated";

grant update on table "public"."pricings" to "authenticated";

grant delete on table "public"."pricings" to "service_role";

grant insert on table "public"."pricings" to "service_role";

grant references on table "public"."pricings" to "service_role";

grant select on table "public"."pricings" to "service_role";

grant trigger on table "public"."pricings" to "service_role";

grant truncate on table "public"."pricings" to "service_role";

grant update on table "public"."pricings" to "service_role";


