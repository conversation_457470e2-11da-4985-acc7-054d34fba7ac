create table "public"."email_whitelisting" (
    "id" bigint generated by default as identity not null,
    "domain" character varying not null,
    "created_at" timestamp with time zone not null default now()
);


CREATE UNIQUE INDEX email_whitelisting_domain_key ON public.email_whitelisting USING btree (domain);

CREATE UNIQUE INDEX email_whitelisting_pkey ON public.email_whitelisting USING btree (id);

alter table "public"."email_whitelisting" add constraint "email_whitelisting_pkey" PRIMARY KEY using index "email_whitelisting_pkey";

alter table "public"."email_whitelisting" add constraint "email_whitelisting_domain_key" UNIQUE using index "email_whitelisting_domain_key";

grant delete on table "public"."email_whitelisting" to "anon";

grant insert on table "public"."email_whitelisting" to "anon";

grant references on table "public"."email_whitelisting" to "anon";

grant select on table "public"."email_whitelisting" to "anon";

grant trigger on table "public"."email_whitelisting" to "anon";

grant truncate on table "public"."email_whitelisting" to "anon";

grant update on table "public"."email_whitelisting" to "anon";

grant delete on table "public"."email_whitelisting" to "authenticated";

grant insert on table "public"."email_whitelisting" to "authenticated";

grant references on table "public"."email_whitelisting" to "authenticated";

grant select on table "public"."email_whitelisting" to "authenticated";

grant trigger on table "public"."email_whitelisting" to "authenticated";

grant truncate on table "public"."email_whitelisting" to "authenticated";

grant update on table "public"."email_whitelisting" to "authenticated";

grant delete on table "public"."email_whitelisting" to "service_role";

grant insert on table "public"."email_whitelisting" to "service_role";

grant references on table "public"."email_whitelisting" to "service_role";

grant select on table "public"."email_whitelisting" to "service_role";

grant trigger on table "public"."email_whitelisting" to "service_role";

grant truncate on table "public"."email_whitelisting" to "service_role";

grant update on table "public"."email_whitelisting" to "service_role";


