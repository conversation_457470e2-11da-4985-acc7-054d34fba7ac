alter table "public"."pricings" enable row level security;

alter table "public"."stripe_payment_intents" enable row level security;

alter table "public"."stripe_session" enable row level security;

alter table "public"."user_asset_preferences" enable row level security;

alter table "public"."user_subscriptions" enable row level security;

create policy "Access for administrative query"
on "public"."pricings"
as permissive
for all
to postgres, service_role
using (true)
with check (true);


create policy "Read access for public"
on "public"."pricings"
as permissive
for select
to public
using (true);


create policy "For administration query"
on "public"."stripe_payment_intents"
as permissive
for all
to service_role, postgres
using (true)
with check (true);


create policy "Access for administrative query"
on "public"."stripe_session"
as permissive
for all
to service_role, postgres
using (true)
with check (true);


create policy "Read access for their own"
on "public"."stripe_session"
as permissive
for select
to public
using ((( SELECT auth.uid() AS uid) = user_id));


create policy "restrict all destructive query"
on "public"."stripe_session"
as permissive
for all
to public
using (false)
with check (false);


create policy "Administrator query"
on "public"."user_asset_preferences"
as permissive
for all
to service_role, postgres
using (true)
with check (true);


create policy "Manage their own data"
on "public"."user_asset_preferences"
as permissive
for all
to public
using ((( SELECT auth.uid() AS uid) = user_id))
with check ((( SELECT auth.uid() AS uid) = user_id));


create policy "permit admin role"
on "public"."user_subscriptions"
as permissive
for all
to postgres, service_role
using (true)
with check (true);


create policy "restrict data manipulation"
on "public"."user_subscriptions"
as permissive
for all
to public
using (false)
with check (false);


create policy "user subscription read"
on "public"."user_subscriptions"
as permissive
for select
to public
using ((( SELECT auth.uid() AS uid) = user_id));



