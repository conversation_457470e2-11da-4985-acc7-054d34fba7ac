CREATE TYPE "public"."LayersTypes" AS enum(
    '0',
    '1',
    '2'
);

CREATE TYPE "public"."type_old" AS enum(
    'CRYPTOCURRENCY',
    'STOCK',
    'STABLECOIN',
    'PLATFORM'
);

ALTER TYPE "public"."status" RENAME TO "status__old_version_to_be_dropped";

CREATE TYPE "public"."status" AS enum(
    'ACTIVE',
    'INACTIVE',
    'NEW'
);

CREATE TABLE "public"."user_asset_preferences"(
    "id" bigint GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "asset_id" integer NOT NULL,
    "user_id" uuid NOT NULL DEFAULT gen_random_uuid()
);

DROP TYPE "public"."status__old_version_to_be_dropped";

CREATE UNIQUE INDEX user_asset_preferences_pkey ON public.user_asset_preferences USING btree(id);

ALTER TABLE "public"."user_asset_preferences"
    ADD CONSTRAINT "user_asset_preferences_pkey" PRIMARY KEY USING INDEX "user_asset_preferences_pkey";

ALTER TABLE "public"."user_asset_preferences"
    ADD CONSTRAINT "user_asset_preferences_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) ON UPDATE CASCADE ON DELETE CASCADE NOT valid;

ALTER TABLE "public"."user_asset_preferences" validate CONSTRAINT "user_asset_preferences_user_id_fkey";

GRANT DELETE ON TABLE "public"."user_asset_preferences" TO "anon";

GRANT INSERT ON TABLE "public"."user_asset_preferences" TO "anon";

GRANT REFERENCES ON TABLE "public"."user_asset_preferences" TO "anon";

GRANT SELECT ON TABLE "public"."user_asset_preferences" TO "anon";

GRANT TRIGGER ON TABLE "public"."user_asset_preferences" TO "anon";

GRANT TRUNCATE ON TABLE "public"."user_asset_preferences" TO "anon";

GRANT UPDATE ON TABLE "public"."user_asset_preferences" TO "anon";

GRANT DELETE ON TABLE "public"."user_asset_preferences" TO "authenticated";

GRANT INSERT ON TABLE "public"."user_asset_preferences" TO "authenticated";

GRANT REFERENCES ON TABLE "public"."user_asset_preferences" TO "authenticated";

GRANT SELECT ON TABLE "public"."user_asset_preferences" TO "authenticated";

GRANT TRIGGER ON TABLE "public"."user_asset_preferences" TO "authenticated";

GRANT TRUNCATE ON TABLE "public"."user_asset_preferences" TO "authenticated";

GRANT UPDATE ON TABLE "public"."user_asset_preferences" TO "authenticated";

GRANT DELETE ON TABLE "public"."user_asset_preferences" TO "service_role";

GRANT INSERT ON TABLE "public"."user_asset_preferences" TO "service_role";

GRANT REFERENCES ON TABLE "public"."user_asset_preferences" TO "service_role";

GRANT SELECT ON TABLE "public"."user_asset_preferences" TO "service_role";

GRANT TRIGGER ON TABLE "public"."user_asset_preferences" TO "service_role";

GRANT TRUNCATE ON TABLE "public"."user_asset_preferences" TO "service_role";

GRANT UPDATE ON TABLE "public"."user_asset_preferences" TO "service_role";

