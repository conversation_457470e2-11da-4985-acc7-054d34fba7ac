alter table "public"."newsletter_subscriptions" enable row level security;

alter table "public"."rate_limiter" enable row level security;

alter table "public"."support_messages" enable row level security;

alter table "public"."support_messages_attachment" enable row level security;

alter table "public"."user_plans" enable row level security;

alter table "public"."users" enable row level security;

CREATE UNIQUE INDEX email_whitelisting_domain_key ON public.email_whitelisting USING btree
(domain);

alter table "public"."email_whitelisting" add constraint "email_whitelisting_domain_key" UNIQUE
using index "email_whitelisting_domain_key";

create policy "postgres roles"
on "public"."email_whitelisting"
as permissive
for all
to postgres
using
(true)
with check
(true);


create policy "read permission"
on "public"."email_whitelisting"
as permissive
for
select
  to service_role, anon, authenticated
using
(true);


create policy "admin roles"
on "public"."newsletter_subscriptions"
as permissive
for all
to postgres, service_role, supabase_admin
using
(true)
with check
(true);


create policy "public insert roles"
on "public"."newsletter_subscriptions"
as permissive
for
insert
to public
with check
  (true)
;


create policy "public roles"
on "public"."newsletter_subscriptions"
as permissive
for
select
  to public
using
(true);


create policy "admin access"
on "public"."rate_limiter"
as permissive
for all
to postgres, service_role, supabase_admin
using
(true)
with check
(true);


create policy "allow update permission"
on "public"."support_messages"
as permissive
for
update
to service_role, postgres, supabase_admin
using (true)
with check
(true);


create policy "open submission"
on "public"."support_messages"
as permissive
for
insert
to public
with check
  (true)
;


create policy "restrict deletion"
on "public"."support_messages"
as permissive
for
delete
to anon, authenticated, authenticator
using
(false);


create policy "query permission"
on "public"."support_messages_attachment"
as permissive
for all
to postgres, service_role, supabase_admin
using
(true)
with check
(true);


create policy "Query only own"
on "public"."user_plans"
as permissive
for all
to public
using
((( SELECT auth.uid() AS uid)
= user_id))
with check
((( SELECT auth.uid() AS uid)
= user_id));


create policy "manage user data"
on "public"."user_plans"
as permissive
for all
to postgres, service_role, supabase_admin
using
(true)
with check
(true);


create policy "Access their own"
on "public"."users"
as permissive
for
select
  to authenticated
using
((( SELECT auth.uid() AS uid)
= user_id));


create policy "Delete prevention"
on "public"."users"
as permissive
for all
to public
using
(false)
with check
(false);


create policy "Insert their own"
on "public"."users"
as permissive
for
select
  to public
using
((( SELECT auth.uid() AS uid)
= user_id));


create policy "Update their own"
on "public"."users"
as permissive
for
select
  to public
using
((( SELECT auth.uid() AS uid)
= user_id));



