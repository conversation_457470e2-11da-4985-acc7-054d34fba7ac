drop policy "Enable read access for all users" on "public"."users";

drop policy "Update their own" on "public"."users";

revoke delete on table "public"."email_whitelisting" from "anon";

revoke insert on table "public"."email_whitelisting" from "anon";

revoke references on table "public"."email_whitelisting" from "anon";

revoke trigger on table "public"."email_whitelisting" from "anon";

revoke truncate on table "public"."email_whitelisting" from "anon";

revoke update on table "public"."email_whitelisting" from "anon";

revoke delete on table "public"."email_whitelisting" from "authenticated";

revoke insert on table "public"."email_whitelisting" from "authenticated";

revoke references on table "public"."email_whitelisting" from "authenticated";

revoke trigger on table "public"."email_whitelisting" from "authenticated";

revoke truncate on table "public"."email_whitelisting" from "authenticated";

revoke update on table "public"."email_whitelisting" from "authenticated";

revoke delete on table "public"."support_messages_device" from "anon";

revoke insert on table "public"."support_messages_device" from "anon";

revoke references on table "public"."support_messages_device" from "anon";

revoke select on table "public"."support_messages_device" from "anon";

revoke trigger on table "public"."support_messages_device" from "anon";

revoke truncate on table "public"."support_messages_device" from "anon";

revoke update on table "public"."support_messages_device" from "anon";

revoke delete on table "public"."support_messages_device" from "authenticated";

revoke insert on table "public"."support_messages_device" from "authenticated";

revoke references on table "public"."support_messages_device" from "authenticated";

revoke select on table "public"."support_messages_device" from "authenticated";

revoke trigger on table "public"."support_messages_device" from "authenticated";

revoke truncate on table "public"."support_messages_device" from "authenticated";

revoke update on table "public"."support_messages_device" from "authenticated";

revoke delete on table "public"."support_messages_device" from "service_role";

revoke insert on table "public"."support_messages_device" from "service_role";

revoke references on table "public"."support_messages_device" from "service_role";

revoke select on table "public"."support_messages_device" from "service_role";

revoke trigger on table "public"."support_messages_device" from "service_role";

revoke truncate on table "public"."support_messages_device" from "service_role";

revoke update on table "public"."support_messages_device" from "service_role";

revoke delete on table "public"."user_newsletters" from "anon";

revoke insert on table "public"."user_newsletters" from "anon";

revoke references on table "public"."user_newsletters" from "anon";

revoke select on table "public"."user_newsletters" from "anon";

revoke trigger on table "public"."user_newsletters" from "anon";

revoke truncate on table "public"."user_newsletters" from "anon";

revoke update on table "public"."user_newsletters" from "anon";

revoke delete on table "public"."user_newsletters" from "authenticated";

revoke insert on table "public"."user_newsletters" from "authenticated";

revoke references on table "public"."user_newsletters" from "authenticated";

revoke select on table "public"."user_newsletters" from "authenticated";

revoke trigger on table "public"."user_newsletters" from "authenticated";

revoke truncate on table "public"."user_newsletters" from "authenticated";

revoke update on table "public"."user_newsletters" from "authenticated";

revoke delete on table "public"."user_newsletters" from "service_role";

revoke insert on table "public"."user_newsletters" from "service_role";

revoke references on table "public"."user_newsletters" from "service_role";

revoke select on table "public"."user_newsletters" from "service_role";

revoke trigger on table "public"."user_newsletters" from "service_role";

revoke truncate on table "public"."user_newsletters" from "service_role";

revoke update on table "public"."user_newsletters" from "service_role";

alter table "public"."support_messages_device" drop constraint "support_messages_device_support_message_id_fkey";

alter table "public"."user_newsletters" drop constraint "user_newsletters_user_id_fkey";

alter table "public"."newsletter_subscriptions" drop constraint "newsletter_subscriptions_user_id_fkey";

alter table "public"."user_subscriptions" drop constraint "user_subscriptions_user_id_fkey";

alter table "public"."support_messages_device" drop constraint "support_messages_device_pkey";

alter table "public"."user_newsletters" drop constraint "user_newsletters_pkey";

drop index if exists "public"."support_messages_device_pkey";

drop index if exists "public"."user_newsletters_pkey";

drop table "public"."support_messages_device";

drop table "public"."user_newsletters";

alter table "public"."users" alter column "role" set default 'user'::userrole;

alter table "public"."users" alter column "role" set not null;

alter table "public"."users" alter column "status" set default 'active'::userstatus;

alter table "public"."users" alter column "status" set not null;

CREATE UNIQUE INDEX newsletter_subscriptions_email_key ON public.newsletter_subscriptions USING btree (email);

alter table "public"."newsletter_subscriptions" add constraint "newsletter_subscriptions_email_key" UNIQUE using index "newsletter_subscriptions_email_key";

alter table "public"."newsletter_subscriptions" add constraint "newsletter_subscriptions_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE SET NULL not valid;

alter table "public"."newsletter_subscriptions" validate constraint "newsletter_subscriptions_user_id_fkey";

alter table "public"."user_subscriptions" add constraint "user_subscriptions_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE SET NULL not valid;

alter table "public"."user_subscriptions" validate constraint "user_subscriptions_user_id_fkey";

create policy "service role insert"
on "public"."register_web3_nonce"
as permissive
for insert
to service_role
with check (true);


create policy "service role update"
on "public"."register_web3_nonce"
as permissive
for update
to service_role
using (true)
with check (true);


create policy "update their own"
on "public"."users"
as permissive
for update
to authenticated
using ((( SELECT auth.uid() AS uid) = user_id))
with check ((( SELECT auth.uid() AS uid) = user_id));



