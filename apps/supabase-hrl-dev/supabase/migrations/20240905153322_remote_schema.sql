

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


CREATE EXTENSION IF NOT EXISTS "pgsodium" WITH SCHEMA "pgsodium";






CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";






CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgjwt" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "postgres_fdw" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";






CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";






CREATE TYPE "public"."messagetype" AS ENUM (
    'bug',
    'feature',
    'business',
    'media',
    'other'
);


ALTER TYPE "public"."messagetype" OWNER TO "postgres";


CREATE TYPE "public"."status" AS ENUM (
    'ACTIVE',
    'INACTIVE'
);


ALTER TYPE "public"."status" OWNER TO "postgres";


CREATE TYPE "public"."userrole" AS ENUM (
    'super_admin',
    'admin',
    'user'
);


ALTER TYPE "public"."userrole" OWNER TO "postgres";


CREATE TYPE "public"."userstatus" AS ENUM (
    'active',
    'inactive',
    'suspend'
);


ALTER TYPE "public"."userstatus" OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."authenticate_user"("p_user_id" "uuid", "p_password" character varying) RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    user_id UUID;
BEGIN
    SELECT id INTO user_id
    FROM auth.users
    WHERE id = p_user_id
    AND encrypted_password = crypt(p_password, encrypted_password);

    RETURN user_id;
END;
$$;


ALTER FUNCTION "public"."authenticate_user"("p_user_id" "uuid", "p_password" character varying) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."check_user_exists"("email" "text") RETURNS boolean
    LANGUAGE "plpgsql"
    AS $_$declare
    user_exists boolean;
begin
    select exists (select 1 from auth.users where auth.users.email = $1) into user_exists;
    return user_exists;
end;$_$;


ALTER FUNCTION "public"."check_user_exists"("email" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."handle_new_user_normal"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$begin
  insert into public.users(user_id, display_name, dashboard, dashboard_changes, company)
  values (new.id, new.raw_user_meta_data ->> 'display_name', '{"platforms": ["reddit"],"indicators": ["trust_index", "mood_index"], "feeds": []}'::jsonb, 0, new.raw_user_meta_data ->> 'company');
  return new;
end;$$;


ALTER FUNCTION "public"."handle_new_user_normal"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."insert_support_message_transaction"("p_company" "text", "p_title" "text", "p_type" "public"."messagetype", "p_first_name" "text", "p_last_name" "text", "p_message" "text", "p_email" "text", "p_os_info" "text", "p_browser" "text") RETURNS TABLE("support_message_id" integer, "company" "text", "title" "text", "type" "public"."messagetype", "first_name" "text", "last_name" "text", "message" "text", "email" "text", "os_info" "text", "browser" "text")
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    v_support_message_id INTEGER;
BEGIN
    -- Insert into support_messages and return the generated id
    INSERT INTO support_messages (company, title, type, first_name, last_name, message, email)
    VALUES (p_company, p_title, p_type, p_first_name, p_last_name, p_message, p_email)
    RETURNING id INTO v_support_message_id;

    -- Insert into support_messages_device using the generated id
    INSERT INTO support_messages_device (os_info, browser, support_message_id)
    VALUES (p_os_info, p_browser, v_support_message_id);

    -- Return the values
    RETURN QUERY SELECT 
        v_support_message_id,
        p_company,
        p_title,
        p_type,
        p_first_name,
        p_last_name,
        p_message,
        p_email,
        p_os_info,
        p_browser;
END;
$$;


ALTER FUNCTION "public"."insert_support_message_transaction"("p_company" "text", "p_title" "text", "p_type" "public"."messagetype", "p_first_name" "text", "p_last_name" "text", "p_message" "text", "p_email" "text", "p_os_info" "text", "p_browser" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_display_name"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    -- Example: Update auth.users table based on some condition or data from raw_meta_data
    UPDATE public.users
    SET display_name = NEW.raw_user_meta_data ->> 'display_name'
    WHERE public.users.user_id = NEW.id; -- assuming there's a foreign key relationship

    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_display_name"() OWNER TO "postgres";


CREATE SERVER "tsdb_server_1" FOREIGN DATA WRAPPER "postgres_fdw" OPTIONS (
    "dbname" 'tsdb',
    "host" 'rd1bc8wgs0.v8ahttb6me.tsdb.cloud.timescale.com',
    "port" '35677',
    "sslmode" 'require'
);


ALTER SERVER "tsdb_server_1" OWNER TO "postgres";


CREATE USER MAPPING FOR "postgres" SERVER "tsdb_server_1" OPTIONS (
    "password" 'o#3773qaT8vVLG73',
    "user" 'read_user'
);



CREATE OR REPLACE VIEW "public"."emails" AS
 SELECT "users"."email"
   FROM "auth"."users";


ALTER TABLE "public"."emails" OWNER TO "postgres";

SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."newsletter_subscriptions" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "user_id" "uuid",
    "email" character varying NOT NULL,
    "subscriptions" "jsonb"
);


ALTER TABLE "public"."newsletter_subscriptions" OWNER TO "postgres";


ALTER TABLE "public"."newsletter_subscriptions" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."newsletter_subscriptions_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."support_messages" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "type" "public"."messagetype" NOT NULL,
    "first_name" character varying NOT NULL,
    "last_name" character varying NOT NULL,
    "message" "text" NOT NULL,
    "email" character varying NOT NULL,
    "company" character varying,
    "title" character varying
);


ALTER TABLE "public"."support_messages" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."support_messages_attachment" (
    "id" bigint NOT NULL,
    "support_message_id" bigint NOT NULL,
    "file_name" character varying NOT NULL,
    "location" "text" NOT NULL
);


ALTER TABLE "public"."support_messages_attachment" OWNER TO "postgres";


COMMENT ON TABLE "public"."support_messages_attachment" IS 'Attachment for support messages';



ALTER TABLE "public"."support_messages_attachment" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."support_messages_attachment_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."support_messages_device" (
    "id" bigint NOT NULL,
    "support_message_id" bigint NOT NULL,
    "os_info" character varying NOT NULL,
    "browser" character varying NOT NULL
);


ALTER TABLE "public"."support_messages_device" OWNER TO "postgres";


COMMENT ON TABLE "public"."support_messages_device" IS 'Device information for support message';



ALTER TABLE "public"."support_messages_device" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."support_messages_device_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



ALTER TABLE "public"."support_messages" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."support_messages_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."user_newsletters" (
    "id" bigint NOT NULL,
    "user_id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "email" character varying NOT NULL,
    "raw_data" "jsonb",
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."user_newsletters" OWNER TO "postgres";


ALTER TABLE "public"."user_newsletters" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."user_newsletters_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."user_plans" (
    "id" bigint NOT NULL,
    "user_id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "plan_id" character varying NOT NULL,
    "expired_at" timestamp with time zone NOT NULL,
    "raw_data" "jsonb"
);


ALTER TABLE "public"."user_plans" OWNER TO "postgres";


ALTER TABLE "public"."user_plans" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."user_plans_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."users" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid",
    "display_name" character varying,
    "dashboard" "jsonb",
    "dashboard_changes" smallint,
    "created_at" timestamp with time zone,
    "suspended_reason" "text",
    "suspended_until" timestamp without time zone,
    "deleted_at" timestamp with time zone,
    "role" "public"."userrole",
    "status" "public"."userstatus",
    "raw_onboarding" "json" DEFAULT '{}'::"json",
    "company" character varying
);


ALTER TABLE "public"."users" OWNER TO "postgres";


ALTER TABLE ONLY "public"."newsletter_subscriptions"
    ADD CONSTRAINT "newsletter_subscriptions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."support_messages_attachment"
    ADD CONSTRAINT "support_messages_attachment_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."support_messages_device"
    ADD CONSTRAINT "support_messages_device_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."support_messages"
    ADD CONSTRAINT "support_messages_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_newsletters"
    ADD CONSTRAINT "user_newsletters_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_plans"
    ADD CONSTRAINT "user_plans_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."newsletter_subscriptions"
    ADD CONSTRAINT "newsletter_subscriptions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."support_messages_attachment"
    ADD CONSTRAINT "support_messages_attachment_support_message_id_fkey" FOREIGN KEY ("support_message_id") REFERENCES "public"."support_messages"("id") ON UPDATE CASCADE ON DELETE CASCADE;



ALTER TABLE ONLY "public"."support_messages_device"
    ADD CONSTRAINT "support_messages_device_support_message_id_fkey" FOREIGN KEY ("support_message_id") REFERENCES "public"."support_messages"("id") ON UPDATE CASCADE ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_newsletters"
    ADD CONSTRAINT "user_newsletters_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_plans"
    ADD CONSTRAINT "user_plans_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



CREATE POLICY "Enable read access for all users" ON "public"."users" FOR SELECT USING (true);





ALTER PUBLICATION "supabase_realtime" OWNER TO "postgres";


REVOKE USAGE ON SCHEMA "public" FROM PUBLIC;
GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";



































































































































































































GRANT ALL ON FUNCTION "public"."authenticate_user"("p_user_id" "uuid", "p_password" character varying) TO "anon";
GRANT ALL ON FUNCTION "public"."authenticate_user"("p_user_id" "uuid", "p_password" character varying) TO "authenticated";
GRANT ALL ON FUNCTION "public"."authenticate_user"("p_user_id" "uuid", "p_password" character varying) TO "service_role";



GRANT ALL ON FUNCTION "public"."check_user_exists"("email" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."check_user_exists"("email" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."check_user_exists"("email" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."handle_new_user_normal"() TO "anon";
GRANT ALL ON FUNCTION "public"."handle_new_user_normal"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."handle_new_user_normal"() TO "service_role";



GRANT ALL ON FUNCTION "public"."insert_support_message_transaction"("p_company" "text", "p_title" "text", "p_type" "public"."messagetype", "p_first_name" "text", "p_last_name" "text", "p_message" "text", "p_email" "text", "p_os_info" "text", "p_browser" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."insert_support_message_transaction"("p_company" "text", "p_title" "text", "p_type" "public"."messagetype", "p_first_name" "text", "p_last_name" "text", "p_message" "text", "p_email" "text", "p_os_info" "text", "p_browser" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."insert_support_message_transaction"("p_company" "text", "p_title" "text", "p_type" "public"."messagetype", "p_first_name" "text", "p_last_name" "text", "p_message" "text", "p_email" "text", "p_os_info" "text", "p_browser" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."update_display_name"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_display_name"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_display_name"() TO "service_role";


















GRANT ALL ON TABLE "public"."emails" TO "anon";
GRANT ALL ON TABLE "public"."emails" TO "authenticated";
GRANT ALL ON TABLE "public"."emails" TO "service_role";



GRANT ALL ON TABLE "public"."newsletter_subscriptions" TO "anon";
GRANT ALL ON TABLE "public"."newsletter_subscriptions" TO "authenticated";
GRANT ALL ON TABLE "public"."newsletter_subscriptions" TO "service_role";



GRANT ALL ON SEQUENCE "public"."newsletter_subscriptions_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."newsletter_subscriptions_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."newsletter_subscriptions_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."support_messages" TO "anon";
GRANT ALL ON TABLE "public"."support_messages" TO "authenticated";
GRANT ALL ON TABLE "public"."support_messages" TO "service_role";



GRANT ALL ON TABLE "public"."support_messages_attachment" TO "anon";
GRANT ALL ON TABLE "public"."support_messages_attachment" TO "authenticated";
GRANT ALL ON TABLE "public"."support_messages_attachment" TO "service_role";



GRANT ALL ON SEQUENCE "public"."support_messages_attachment_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."support_messages_attachment_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."support_messages_attachment_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."support_messages_device" TO "anon";
GRANT ALL ON TABLE "public"."support_messages_device" TO "authenticated";
GRANT ALL ON TABLE "public"."support_messages_device" TO "service_role";



GRANT ALL ON SEQUENCE "public"."support_messages_device_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."support_messages_device_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."support_messages_device_id_seq" TO "service_role";



GRANT ALL ON SEQUENCE "public"."support_messages_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."support_messages_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."support_messages_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."user_newsletters" TO "anon";
GRANT ALL ON TABLE "public"."user_newsletters" TO "authenticated";
GRANT ALL ON TABLE "public"."user_newsletters" TO "service_role";



GRANT ALL ON SEQUENCE "public"."user_newsletters_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."user_newsletters_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."user_newsletters_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."user_plans" TO "anon";
GRANT ALL ON TABLE "public"."user_plans" TO "authenticated";
GRANT ALL ON TABLE "public"."user_plans" TO "service_role";



GRANT ALL ON SEQUENCE "public"."user_plans_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."user_plans_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."user_plans_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."users" TO "anon";
GRANT ALL ON TABLE "public"."users" TO "authenticated";
GRANT ALL ON TABLE "public"."users" TO "service_role";



ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "service_role";






























RESET ALL;
