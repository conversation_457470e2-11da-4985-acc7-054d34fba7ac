alter table "public"."users" add column "excluded_paywall" boolean not null default false;

alter table "public"."users" alter column "raw_onboarding" set not null;

create or replace view "public"."vw_user" as  SELECT users.id,
    users.user_id,
    users.display_name,
    users.dashboard,
    users.dashboard_changes,
    users.suspended_reason,
    users.deleted_at,
    users.role,
    users.status,
    users.raw_onboarding,
    users.company,
    users.hashpack_account_id,
    users.web3_auth,
    users.created_at,
    users.last_name,
    users.first_name,
    users.suspended_at,
    users.excluded_paywall,
    users_1.email,
    users_1.raw_user_meta_data
   FROM (users
     JOIN auth.users users_1 ON ((users.user_id = users_1.id)));



