ALTER TABLE public.users ADD last_name varchar NULL;
ALTER TABLE public.users ADD first_name varchar NULL;

DO $$
DECLARE
    rec RECORD;
BEGIN
  FOR rec IN
  select
    id,
    display_name,
    SPLIT_PART(display_name, ' ', 1) AS first_name,
    CASE
	        WHEN display_name LIKE '% %' THEN 
	            TRIM(SUBSTRING(display_name FROM POSITION(' ' IN display_name) + 1))
	        ELSE NULL
	    END AS last_name
  FROM users LOOP
  UPDATE users
        SET first_name = rec.first_name,
            last_name = rec.last_name
        WHERE id = rec.id;
END
LOOP;
END $$;