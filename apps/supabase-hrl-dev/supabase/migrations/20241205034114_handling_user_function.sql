set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.handle_new_user_normal()
 R<PERSON><PERSON><PERSON> trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$begin
  insert into public.users(user_id, display_name, dashboard, dashboard_changes, company, first_name, last_name)
  values (new.id, new.raw_user_meta_data ->> 'display_name', '{"platforms": ["reddit"],"indicators": ["trust_index", "mood_index"], "feeds": []}'::jsonb, 0, new.raw_user_meta_data ->> 'company', new.raw_user_meta_data ->> 'first_name', new.raw_user_meta_data ->> 'last_name');
  return new;
end;$function$
;


