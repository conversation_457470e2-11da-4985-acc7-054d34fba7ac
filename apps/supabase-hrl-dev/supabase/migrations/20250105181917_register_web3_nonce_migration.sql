drop policy "admin permission" on "public"."users";

create table "public"."register_web3_nonce" (
    "id" bigint generated by default as identity not null,
    "account_id" character varying not null,
    "claimed" boolean not null default false,
    "created_at" timestamp with time zone not null default now()
);


alter table "public"."register_web3_nonce" enable row level security;

alter table "public"."users" drop column "updated_at";

alter table "public"."users" add column "created_at" timestamp with time zone;

alter table "public"."users" alter column "role" drop default;

alter table "public"."users" alter column "role" drop not null;

alter table "public"."users" alter column "status" drop default;

alter table "public"."users" alter column "status" drop not null;

CREATE UNIQUE INDEX register_web3_nonce_account_id_key ON public.register_web3_nonce USING btree (account_id);

CREATE UNIQUE INDEX register_web3_nonce_pkey ON public.register_web3_nonce USING btree (id);

alter table "public"."register_web3_nonce" add constraint "register_web3_nonce_pkey" PRIMARY KEY using index "register_web3_nonce_pkey";

alter table "public"."register_web3_nonce" add constraint "register_web3_nonce_account_id_key" UNIQUE using index "register_web3_nonce_account_id_key";

set check_function_bodies = off;

grant delete on table "public"."register_web3_nonce" to "anon";

grant insert on table "public"."register_web3_nonce" to "anon";

grant references on table "public"."register_web3_nonce" to "anon";

grant select on table "public"."register_web3_nonce" to "anon";

grant trigger on table "public"."register_web3_nonce" to "anon";

grant truncate on table "public"."register_web3_nonce" to "anon";

grant update on table "public"."register_web3_nonce" to "anon";

grant delete on table "public"."register_web3_nonce" to "authenticated";

grant insert on table "public"."register_web3_nonce" to "authenticated";

grant references on table "public"."register_web3_nonce" to "authenticated";

grant select on table "public"."register_web3_nonce" to "authenticated";

grant trigger on table "public"."register_web3_nonce" to "authenticated";

grant truncate on table "public"."register_web3_nonce" to "authenticated";

grant update on table "public"."register_web3_nonce" to "authenticated";

grant delete on table "public"."register_web3_nonce" to "service_role";

grant insert on table "public"."register_web3_nonce" to "service_role";

grant references on table "public"."register_web3_nonce" to "service_role";

grant select on table "public"."register_web3_nonce" to "service_role";

grant trigger on table "public"."register_web3_nonce" to "service_role";

grant truncate on table "public"."register_web3_nonce" to "service_role";

grant update on table "public"."register_web3_nonce" to "service_role";


