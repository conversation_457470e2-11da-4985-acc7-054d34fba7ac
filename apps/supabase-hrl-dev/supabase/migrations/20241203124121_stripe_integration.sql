create table "public"."stripe_payment_intents" (
    "id" bigint generated by default as identity not null,
    "payment_intent_id" character varying,
    "receipt_url" text,
    "created_at" timestamp with time zone not null default now()
);


create table "public"."stripe_session" (
    "id" bigint generated by default as identity not null,
    "user_id" uuid,
    "session_id" text,
    "raw_data" jsonb,
    "product_raw_data" jsonb,
    "amount_capturable" jsonb,
    "status" character varying default 'unpaid'::character varying,
    "canceled_at" timestamp with time zone,
    "created_at" timestamp with time zone not null default now()
);


create table "public"."user_subscriptions" (
    "id" bigint generated by default as identity not null,
    "user_id" uuid,
    "plan_id" character varying,
    "plan_name" character varying,
    "status" character varying default 'waiting_for_payment'::character varying,
    "expired_date" timestamp with time zone,
    "created_at" timestamp with time zone not null default now()
);


CREATE UNIQUE INDEX stripe_payment_intents_pkey ON public.stripe_payment_intents USING btree (id);

CREATE UNIQUE INDEX stripe_session_pkey ON public.stripe_session USING btree (id);

CREATE UNIQUE INDEX user_subscriptions_pkey ON public.user_subscriptions USING btree (id);

alter table "public"."stripe_payment_intents" add constraint "stripe_payment_intents_pkey" PRIMARY KEY using index "stripe_payment_intents_pkey";

alter table "public"."stripe_session" add constraint "stripe_session_pkey" PRIMARY KEY using index "stripe_session_pkey";

alter table "public"."user_subscriptions" add constraint "user_subscriptions_pkey" PRIMARY KEY using index "user_subscriptions_pkey";

alter table "public"."stripe_session" add constraint "stripe_session_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) not valid;

alter table "public"."stripe_session" validate constraint "stripe_session_user_id_fkey";

alter table "public"."user_subscriptions" add constraint "user_subscriptions_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) not valid;

alter table "public"."user_subscriptions" validate constraint "user_subscriptions_user_id_fkey";

grant delete on table "public"."stripe_payment_intents" to "anon";

grant insert on table "public"."stripe_payment_intents" to "anon";

grant references on table "public"."stripe_payment_intents" to "anon";

grant select on table "public"."stripe_payment_intents" to "anon";

grant trigger on table "public"."stripe_payment_intents" to "anon";

grant truncate on table "public"."stripe_payment_intents" to "anon";

grant update on table "public"."stripe_payment_intents" to "anon";

grant delete on table "public"."stripe_payment_intents" to "authenticated";

grant insert on table "public"."stripe_payment_intents" to "authenticated";

grant references on table "public"."stripe_payment_intents" to "authenticated";

grant select on table "public"."stripe_payment_intents" to "authenticated";

grant trigger on table "public"."stripe_payment_intents" to "authenticated";

grant truncate on table "public"."stripe_payment_intents" to "authenticated";

grant update on table "public"."stripe_payment_intents" to "authenticated";

grant delete on table "public"."stripe_payment_intents" to "service_role";

grant insert on table "public"."stripe_payment_intents" to "service_role";

grant references on table "public"."stripe_payment_intents" to "service_role";

grant select on table "public"."stripe_payment_intents" to "service_role";

grant trigger on table "public"."stripe_payment_intents" to "service_role";

grant truncate on table "public"."stripe_payment_intents" to "service_role";

grant update on table "public"."stripe_payment_intents" to "service_role";

grant delete on table "public"."stripe_session" to "anon";

grant insert on table "public"."stripe_session" to "anon";

grant references on table "public"."stripe_session" to "anon";

grant select on table "public"."stripe_session" to "anon";

grant trigger on table "public"."stripe_session" to "anon";

grant truncate on table "public"."stripe_session" to "anon";

grant update on table "public"."stripe_session" to "anon";

grant delete on table "public"."stripe_session" to "authenticated";

grant insert on table "public"."stripe_session" to "authenticated";

grant references on table "public"."stripe_session" to "authenticated";

grant select on table "public"."stripe_session" to "authenticated";

grant trigger on table "public"."stripe_session" to "authenticated";

grant truncate on table "public"."stripe_session" to "authenticated";

grant update on table "public"."stripe_session" to "authenticated";

grant delete on table "public"."stripe_session" to "service_role";

grant insert on table "public"."stripe_session" to "service_role";

grant references on table "public"."stripe_session" to "service_role";

grant select on table "public"."stripe_session" to "service_role";

grant trigger on table "public"."stripe_session" to "service_role";

grant truncate on table "public"."stripe_session" to "service_role";

grant update on table "public"."stripe_session" to "service_role";

grant delete on table "public"."user_subscriptions" to "anon";

grant insert on table "public"."user_subscriptions" to "anon";

grant references on table "public"."user_subscriptions" to "anon";

grant select on table "public"."user_subscriptions" to "anon";

grant trigger on table "public"."user_subscriptions" to "anon";

grant truncate on table "public"."user_subscriptions" to "anon";

grant update on table "public"."user_subscriptions" to "anon";

grant delete on table "public"."user_subscriptions" to "authenticated";

grant insert on table "public"."user_subscriptions" to "authenticated";

grant references on table "public"."user_subscriptions" to "authenticated";

grant select on table "public"."user_subscriptions" to "authenticated";

grant trigger on table "public"."user_subscriptions" to "authenticated";

grant truncate on table "public"."user_subscriptions" to "authenticated";

grant update on table "public"."user_subscriptions" to "authenticated";

grant delete on table "public"."user_subscriptions" to "service_role";

grant insert on table "public"."user_subscriptions" to "service_role";

grant references on table "public"."user_subscriptions" to "service_role";

grant select on table "public"."user_subscriptions" to "service_role";

grant trigger on table "public"."user_subscriptions" to "service_role";

grant truncate on table "public"."user_subscriptions" to "service_role";

grant update on table "public"."user_subscriptions" to "service_role";


