create table "public"."rate_limiter" (
    "id" uuid not null default gen_random_uuid(),
    "identity" character varying not null,
    "type" character varying not null,
    "key" character varying not null,
    "occurrences" integer not null,
    "limit" integer not null,
    "expired_at" timestamp without time zone not null,
    "created_at" timestamp with time zone not null default now()
);


CREATE UNIQUE INDEX rate_limiter_pkey ON public.rate_limiter USING btree (id);

alter table "public"."rate_limiter" add constraint "rate_limiter_pkey" PRIMARY KEY using index "rate_limiter_pkey";

grant delete on table "public"."rate_limiter" to "anon";

grant insert on table "public"."rate_limiter" to "anon";

grant references on table "public"."rate_limiter" to "anon";

grant select on table "public"."rate_limiter" to "anon";

grant trigger on table "public"."rate_limiter" to "anon";

grant truncate on table "public"."rate_limiter" to "anon";

grant update on table "public"."rate_limiter" to "anon";

grant delete on table "public"."rate_limiter" to "authenticated";

grant insert on table "public"."rate_limiter" to "authenticated";

grant references on table "public"."rate_limiter" to "authenticated";

grant select on table "public"."rate_limiter" to "authenticated";

grant trigger on table "public"."rate_limiter" to "authenticated";

grant truncate on table "public"."rate_limiter" to "authenticated";

grant update on table "public"."rate_limiter" to "authenticated";

grant delete on table "public"."rate_limiter" to "service_role";

grant insert on table "public"."rate_limiter" to "service_role";

grant references on table "public"."rate_limiter" to "service_role";

grant select on table "public"."rate_limiter" to "service_role";

grant trigger on table "public"."rate_limiter" to "service_role";

grant truncate on table "public"."rate_limiter" to "service_role";

grant update on table "public"."rate_limiter" to "service_role";


