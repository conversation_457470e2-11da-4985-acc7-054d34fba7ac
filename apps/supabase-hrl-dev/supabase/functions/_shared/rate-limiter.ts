import { Context, Next } from "https://deno.land/x/oak/mod.ts";
import { add, format } from "npm:date-fns";
import { createClient } from "jsr:@supabase/supabase-js@2";
import { Database } from "../database.ts";
import { getIP } from "https://deno.land/x/get_ip/mod.ts";

const serviceRoleKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");
const supabaseUrl = Deno.env.get("SUPABASE_URL");

export async function rateLimiter(ctx: Context, next: Next, opt?: {
  key?: string;
  maxOccurrences?: number;
  expiredAt?: number;
}) {
  const ip = await getIP();
  const supabase = createClient<Database>(
    supabaseUrl ?? "",
    serviceRoleKey ?? "",
  );

  const keyname = opt?.key ??
    ctx.request.url.pathname.replace("/", "").replaceAll("/", "-");
  const maxOccurrences = opt?.maxOccurrences ?? 5;
  const expiredAt = add(new Date(), { minutes: opt?.expiredAt ?? 30 });

  let { data: target } = await supabase
    .from("rate_limiter")
    .select("*")
    .eq("key", keyname)
    .eq("type", "ip")
    .eq("identity", ip)
    .gte("expired_at", format(new Date(), "yyyy-MM-dd HH:mm:ss"))
    .limit(1)
    .single();

  if (target === null) {
    const { data: response } = await supabase
      .from("rate_limiter")
      .insert({
        expired_at: format(
          expiredAt,
          "yyyy-MM-dd HH:mm:ss",
        ),
        identity: ip,
        type: "ip",
        key: keyname,
        limit: maxOccurrences,
        occurrences: 1,
      })
      .select();

    if (response) {
      target = response[0];
    } else {
      ctx.response.body = { message: "Internal server error" };
      ctx.response.status = 500;
      return;
    }
  } else {
    if (target.occurrences === target.limit) {
      ctx.response.body = {
        message: "Too many request, please try again in 30 minute",
      };
      ctx.response.status = 429;
      return;
    } else {
      await supabase.from("rate_limiter").update({
        occurrences: target.occurrences + 1,
      }).eq("id", target.id);
    }
  }

  return next();
}
