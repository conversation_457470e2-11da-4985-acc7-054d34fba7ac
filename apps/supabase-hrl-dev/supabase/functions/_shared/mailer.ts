import nodemailer from 'npm:nodemailer'

const smtpHost = Deno.env.get('SMTP_HOST')
const smtpPort = Deno.env.get('SMTP_PORT')
const smtpUser = Deno.env.get('SMTP_USER')
const smtpPassword = Deno.env.get('SMTP_PASSWORD')

export async function sendEmail(opt: { to: string; subject: string; text?: string; html: string }) {
  if (!smtpHost || !smtpPort || !smtpUser || !smtpPassword) {
    throw new Error('smtp requires smtp environment')
  }

  const transporter = nodemailer.createTransport({
    host: smtpHost,
    port: smtpPort,
    secure: false,
    auth: {
      user: smtpUser,
      pass: smtpPassword,
    },
  })

  const info = await transporter.sendMail({
    from: '<EMAIL>',
    to: opt.to,
    subject: opt.subject,
    text: opt.text,
    html: opt.html,
  })

  return info
}
