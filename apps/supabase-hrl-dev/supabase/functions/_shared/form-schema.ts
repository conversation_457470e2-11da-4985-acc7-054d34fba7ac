import { z } from "https://deno.land/x/zod@v3.20.2/mod.ts"; // Import zod

export function validateForm<T extends z.ZodRawShape>(
  form: FormData,
  schema: z.ZodObject<T> | z.ZodEffects<z.ZodObject<T>>,
) {
  // deno-lint-ignore no-explicit-any
  const tempObject: Record<string, any> = {};
  const tempKey: string[] = [];

  if (schema instanceof z.ZodObject) {
    tempKey.push(...schema.keyof().options as string[]);
  } else {
    tempKey.push(...schema._def.schema.keyof().options as string[]);
  }
  for (const key of tempKey) {
    if (key.includes("file")) {
      const field = form.getAll(key);
      tempObject[key] = field.length === 0 ? undefined : field;
    } else {
      tempObject[key] = form.get(key) ?? undefined;
    }
  }

  return schema.safeParse(tempObject);
}
