export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  _supavisor: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      graphql: {
        Args: {
          operationName?: string
          query?: string
          variables?: Json
          extensions?: Json
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  pgbouncer: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_auth: {
        Args: {
          p_usename: string
        }
        Returns: {
          username: string
          password: string
        }[]
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      newsletter_subscriptions: {
        Row: {
          created_at: string
          email: string
          id: number
          subscriptions: Json | null
          unsubscribe: boolean
          user_id: string | null
          uuid: string
        }
        Insert: {
          created_at?: string
          email: string
          id?: number
          subscriptions?: Json | null
          unsubscribe?: boolean
          user_id?: string | null
          uuid?: string
        }
        Update: {
          created_at?: string
          email?: string
          id?: number
          subscriptions?: Json | null
          unsubscribe?: boolean
          user_id?: string | null
          uuid?: string
        }
        Relationships: [
          {
            foreignKeyName: 'newsletter_subscriptions_user_id_fkey'
            columns: ['user_id']
            referencedRelation: 'users'
            referencedColumns: ['id']
          },
        ]
      }
      rate_limiter: {
        Row: {
          created_at: string
          expired_at: string
          id: string
          identity: string
          key: string
          limit: number
          occurrences: number
          type: string
        }
        Insert: {
          created_at?: string
          expired_at: string
          id?: string
          identity: string
          key: string
          limit: number
          occurrences: number
          type: string
        }
        Update: {
          created_at?: string
          expired_at?: string
          id?: string
          identity?: string
          key?: string
          limit?: number
          occurrences?: number
          type?: string
        }
        Relationships: []
      }
      support_messages: {
        Row: {
          browser: string | null
          company: string | null
          created_at: string
          device: string | null
          email: string
          first_name: string
          id: number
          last_name: string
          message: string
          title: string | null
          type: string
        }
        Insert: {
          browser?: string | null
          company?: string | null
          created_at?: string
          device?: string | null
          email: string
          first_name: string
          id?: number
          last_name: string
          message: string
          title?: string | null
          type: string
        }
        Update: {
          browser?: string | null
          company?: string | null
          created_at?: string
          device?: string | null
          email?: string
          first_name?: string
          id?: number
          last_name?: string
          message?: string
          title?: string | null
          type?: string
        }
        Relationships: []
      }
      support_messages_attachment: {
        Row: {
          file_name: string
          id: number
          location: string
          support_message_id: number
        }
        Insert: {
          file_name: string
          id?: number
          location: string
          support_message_id: number
        }
        Update: {
          file_name?: string
          id?: number
          location?: string
          support_message_id?: number
        }
        Relationships: [
          {
            foreignKeyName: 'support_messages_attachment_support_message_id_fkey'
            columns: ['support_message_id']
            referencedRelation: 'support_messages'
            referencedColumns: ['id']
          },
        ]
      }
      support_messages_device: {
        Row: {
          browser: string
          id: number
          os_info: string
          support_message_id: number
        }
        Insert: {
          browser: string
          id?: number
          os_info: string
          support_message_id: number
        }
        Update: {
          browser?: string
          id?: number
          os_info?: string
          support_message_id?: number
        }
        Relationships: [
          {
            foreignKeyName: 'support_messages_device_support_message_id_fkey'
            columns: ['support_message_id']
            referencedRelation: 'support_messages'
            referencedColumns: ['id']
          },
        ]
      }
      user_newsletters: {
        Row: {
          created_at: string
          email: string
          id: number
          raw_data: Json | null
          user_id: string
        }
        Insert: {
          created_at?: string
          email: string
          id?: number
          raw_data?: Json | null
          user_id?: string
        }
        Update: {
          created_at?: string
          email?: string
          id?: number
          raw_data?: Json | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: 'user_newsletters_user_id_fkey'
            columns: ['user_id']
            referencedRelation: 'users'
            referencedColumns: ['id']
          },
        ]
      }
      user_plans: {
        Row: {
          created_at: string
          expired_at: string
          id: number
          plan_id: string
          raw_data: Json | null
          user_id: string
        }
        Insert: {
          created_at?: string
          expired_at: string
          id?: number
          plan_id: string
          raw_data?: Json | null
          user_id?: string
        }
        Update: {
          created_at?: string
          expired_at?: string
          id?: number
          plan_id?: string
          raw_data?: Json | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: 'user_plans_user_id_fkey'
            columns: ['user_id']
            referencedRelation: 'users'
            referencedColumns: ['id']
          },
        ]
      }
      users: {
        Row: {
          company: string | null
          created_at: string | null
          dashboard: Json | null
          dashboard_changes: number | null
          deleted_at: string | null
          display_name: string | null
          id: string
          raw_onboarding: Json | null
          role: Database['public']['Enums']['userrole'] | null
          status: Database['public']['Enums']['userstatus'] | null
          suspended_reason: string | null
          suspended_until: string | null
          user_id: string | null
        }
        Insert: {
          company?: string | null
          created_at?: string | null
          dashboard?: Json | null
          dashboard_changes?: number | null
          deleted_at?: string | null
          display_name?: string | null
          id?: string
          raw_onboarding?: Json | null
          role?: Database['public']['Enums']['userrole'] | null
          status?: Database['public']['Enums']['userstatus'] | null
          suspended_reason?: string | null
          suspended_until?: string | null
          user_id?: string | null
        }
        Update: {
          company?: string | null
          created_at?: string | null
          dashboard?: Json | null
          dashboard_changes?: number | null
          deleted_at?: string | null
          display_name?: string | null
          id?: string
          raw_onboarding?: Json | null
          role?: Database['public']['Enums']['userrole'] | null
          status?: Database['public']['Enums']['userstatus'] | null
          suspended_reason?: string | null
          suspended_until?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: 'users_user_id_fkey'
            columns: ['user_id']
            referencedRelation: 'users'
            referencedColumns: ['id']
          },
        ]
      }
    }
    Views: {
      emails: {
        Row: {
          email: string | null
        }
        Insert: {
          email?: string | null
        }
        Update: {
          email?: string | null
        }
        Relationships: []
      }
    }
    Functions: {
      authenticate_user: {
        Args: {
          p_user_id: string
          p_password: string
        }
        Returns: string
      }
      check_user_exists: {
        Args: {
          email: string
        }
        Returns: boolean
      }
      insert_support_message_transaction: {
        Args: {
          p_company: string
          p_title: string
          p_type: Database['public']['Enums']['messagetype']
          p_first_name: string
          p_last_name: string
          p_message: string
          p_email: string
          p_os_info: string
          p_browser: string
        }
        Returns: {
          support_message_id: number
          company: string
          title: string
          type: Database['public']['Enums']['messagetype']
          first_name: string
          last_name: string
          message: string
          email: string
          os_info: string
          browser: string
        }[]
      }
    }
    Enums: {
      messagetype: 'bug' | 'feature' | 'business' | 'media' | 'other'
      status: 'ACTIVE' | 'INACTIVE'
      userrole: 'super_admin' | 'admin' | 'user'
      userstatus: 'active' | 'inactive' | 'suspend'
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  storage: {
    Tables: {
      buckets: {
        Row: {
          allowed_mime_types: string[] | null
          avif_autodetection: boolean | null
          created_at: string | null
          file_size_limit: number | null
          id: string
          name: string
          owner: string | null
          owner_id: string | null
          public: boolean | null
          updated_at: string | null
        }
        Insert: {
          allowed_mime_types?: string[] | null
          avif_autodetection?: boolean | null
          created_at?: string | null
          file_size_limit?: number | null
          id: string
          name: string
          owner?: string | null
          owner_id?: string | null
          public?: boolean | null
          updated_at?: string | null
        }
        Update: {
          allowed_mime_types?: string[] | null
          avif_autodetection?: boolean | null
          created_at?: string | null
          file_size_limit?: number | null
          id?: string
          name?: string
          owner?: string | null
          owner_id?: string | null
          public?: boolean | null
          updated_at?: string | null
        }
        Relationships: []
      }
      migrations: {
        Row: {
          executed_at: string | null
          hash: string
          id: number
          name: string
        }
        Insert: {
          executed_at?: string | null
          hash: string
          id: number
          name: string
        }
        Update: {
          executed_at?: string | null
          hash?: string
          id?: number
          name?: string
        }
        Relationships: []
      }
      objects: {
        Row: {
          bucket_id: string | null
          created_at: string | null
          id: string
          last_accessed_at: string | null
          metadata: Json | null
          name: string | null
          owner: string | null
          owner_id: string | null
          path_tokens: string[] | null
          updated_at: string | null
          user_metadata: Json | null
          version: string | null
        }
        Insert: {
          bucket_id?: string | null
          created_at?: string | null
          id?: string
          last_accessed_at?: string | null
          metadata?: Json | null
          name?: string | null
          owner?: string | null
          owner_id?: string | null
          path_tokens?: string[] | null
          updated_at?: string | null
          user_metadata?: Json | null
          version?: string | null
        }
        Update: {
          bucket_id?: string | null
          created_at?: string | null
          id?: string
          last_accessed_at?: string | null
          metadata?: Json | null
          name?: string | null
          owner?: string | null
          owner_id?: string | null
          path_tokens?: string[] | null
          updated_at?: string | null
          user_metadata?: Json | null
          version?: string | null
        }
        Relationships: [
          {
            foreignKeyName: 'objects_bucketId_fkey'
            columns: ['bucket_id']
            referencedRelation: 'buckets'
            referencedColumns: ['id']
          },
        ]
      }
      s3_multipart_uploads: {
        Row: {
          bucket_id: string
          created_at: string
          id: string
          in_progress_size: number
          key: string
          owner_id: string | null
          upload_signature: string
          user_metadata: Json | null
          version: string
        }
        Insert: {
          bucket_id: string
          created_at?: string
          id: string
          in_progress_size?: number
          key: string
          owner_id?: string | null
          upload_signature: string
          user_metadata?: Json | null
          version: string
        }
        Update: {
          bucket_id?: string
          created_at?: string
          id?: string
          in_progress_size?: number
          key?: string
          owner_id?: string | null
          upload_signature?: string
          user_metadata?: Json | null
          version?: string
        }
        Relationships: [
          {
            foreignKeyName: 's3_multipart_uploads_bucket_id_fkey'
            columns: ['bucket_id']
            referencedRelation: 'buckets'
            referencedColumns: ['id']
          },
        ]
      }
      s3_multipart_uploads_parts: {
        Row: {
          bucket_id: string
          created_at: string
          etag: string
          id: string
          key: string
          owner_id: string | null
          part_number: number
          size: number
          upload_id: string
          version: string
        }
        Insert: {
          bucket_id: string
          created_at?: string
          etag: string
          id?: string
          key: string
          owner_id?: string | null
          part_number: number
          size?: number
          upload_id: string
          version: string
        }
        Update: {
          bucket_id?: string
          created_at?: string
          etag?: string
          id?: string
          key?: string
          owner_id?: string | null
          part_number?: number
          size?: number
          upload_id?: string
          version?: string
        }
        Relationships: [
          {
            foreignKeyName: 's3_multipart_uploads_parts_bucket_id_fkey'
            columns: ['bucket_id']
            referencedRelation: 'buckets'
            referencedColumns: ['id']
          },
          {
            foreignKeyName: 's3_multipart_uploads_parts_upload_id_fkey'
            columns: ['upload_id']
            referencedRelation: 's3_multipart_uploads'
            referencedColumns: ['id']
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      can_insert_object: {
        Args: {
          bucketid: string
          name: string
          owner: string
          metadata: Json
        }
        Returns: undefined
      }
      extension: {
        Args: {
          name: string
        }
        Returns: string
      }
      filename: {
        Args: {
          name: string
        }
        Returns: string
      }
      foldername: {
        Args: {
          name: string
        }
        Returns: string[]
      }
      get_size_by_bucket: {
        Args: Record<PropertyKey, never>
        Returns: {
          size: number
          bucket_id: string
        }[]
      }
      list_multipart_uploads_with_delimiter: {
        Args: {
          bucket_id: string
          prefix_param: string
          delimiter_param: string
          max_keys?: number
          next_key_token?: string
          next_upload_token?: string
        }
        Returns: {
          key: string
          id: string
          created_at: string
        }[]
      }
      list_objects_with_delimiter: {
        Args: {
          bucket_id: string
          prefix_param: string
          delimiter_param: string
          max_keys?: number
          start_after?: string
          next_token?: string
        }
        Returns: {
          name: string
          id: string
          metadata: Json
          updated_at: string
        }[]
      }
      operation: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      search: {
        Args: {
          prefix: string
          bucketname: string
          limits?: number
          levels?: number
          offsets?: number
          search?: string
          sortcolumn?: string
          sortorder?: string
        }
        Returns: {
          name: string
          id: string
          updated_at: string
          created_at: string
          last_accessed_at: string
          metadata: Json
        }[]
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type PublicSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (PublicSchema["Tables"] & PublicSchema["Views"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
        Database[PublicTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
      Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (PublicSchema["Tables"] &
        PublicSchema["Views"])
    ? (PublicSchema["Tables"] &
        PublicSchema["Views"])[PublicTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  PublicEnumNameOrOptions extends
    | keyof PublicSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? Database[PublicEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : PublicEnumNameOrOptions extends keyof PublicSchema["Enums"]
    ? PublicSchema["Enums"][PublicEnumNameOrOptions]
    : never
