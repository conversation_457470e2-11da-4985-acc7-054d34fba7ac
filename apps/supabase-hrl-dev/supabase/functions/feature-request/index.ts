// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

// Setup type definitions for built-in Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { Application } from "https://deno.land/x/oak@v17.0.0/mod.ts";
import { rateLimiter } from "../_shared/rate-limiter.ts";
import { createClient } from "jsr:@supabase/supabase-js@2";
import { Database } from "../database.ts";
import { validateForm } from "../_shared/form-schema.ts";
import { z } from "https://deno.land/x/zod@v3.20.2/mod.ts";
import { sendEmail } from "../_shared/mailer.ts";
import { contactUsTemplate } from "../_shared/email/contact_us.ts";
import { oakCors } from "https://deno.land/x/cors@v1.2.2/mod.ts";

const app = new Application();
app.use(
  oakCors({
    origin: "*",
  }),
);

const formFeatureRequest = z.object({
  first_name: z.string(),
  last_name: z.string(),
  email: z.string(),
  company: z.string().optional(),
  message: z.string(),
  title: z.string(),
  file: z.array(
    z
      .custom<File>()
      .refine((file) => !file || (!!file && file.type?.startsWith("image")), {
        message: "Only images is allowed",
      }),
  ).optional().describe("ZodFile"),
});

const serviceRoleKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");
const supabaseUrl = Deno.env.get("SUPABASE_URL");

app.use((ctx, next) =>
  rateLimiter(ctx, next, {
    expiredAt: 30,
  })
);
app.use(async (ctx) => {
  if (serviceRoleKey === undefined || supabaseUrl === undefined) {
    ctx.response.body = { message: "Internal server error" };
    ctx.response.status = 500;
    return;
  }

  if (ctx.request.method !== "POST") {
    ctx.response.body = { message: "Method not allowed" };
    ctx.response.status = 422;
    return;
  }

  const date = new Date();
  const body = await ctx.request.body.formData();
  const payload = validateForm(body, formFeatureRequest);

  if (payload.success === false) {
    ctx.response.status = 422;
    ctx.response.body = payload.error.issues;
    return;
  }

  const supabase = createClient<Database>(
    supabaseUrl ?? "",
    serviceRoleKey ?? "",
  );

  const featureRequest = await supabase.from("support_messages").insert({
    email: payload.data.email,
    first_name: payload.data.first_name,
    last_name: payload.data.last_name,
    message: payload.data.message,
    type: "feature",
    company: payload.data.company,
    title: `Feature Request - ${payload.data.title}`,
  }).select().limit(1).single();

  const uploadResult = await Promise.all(
    payload.data.file?.map(async (d) => {
      const filePath = `${date.getFullYear()}/${date.getMonth() + 1}/${d.name}`;
      const { error } = await supabase.storage.from("supports").upload(
        filePath,
        d,
      );

      if (error === null) {
        return filePath;
      }
    }) ?? [],
  ).then((res) => res.filter((d) => d !== undefined));

  await Promise.all([
    uploadResult.length > 0
      ? supabase.from("support_messages_attachment").insert(
        uploadResult.map((d) => {
          const splitFilename = d.split("/");

          return {
            file_name: splitFilename[splitFilename.length - 1],
            location: d,
            support_message_id: featureRequest.data!.id,
          };
        }),
      )
      : [],
    sendEmail({
      to: payload.data.email,
      text: payload.data.message,
      html: contactUsTemplate({
        email: payload.data.email,
        firstName: payload.data.first_name,
        lastName: payload.data.last_name,
        message: payload.data.message,
        topic: `Feature Request - ${payload.data.title}`,
      }),
      subject: `Feature Request - ${payload.data.title}`,
    }),
  ]);

  ctx.response.body = {
    message:
      "Thank you for your suggestion! Our team will review your feature request and consider it for future updates.",
  };
  ctx.response.status = 200;
});

await app.listen({ port: 8000 });

/* To invoke locally:

  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)
  2. Make an HTTP request:

  curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/support-message' \
    --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0' \
    --header 'Content-Type: application/json' \
    --data '{"name":"Functions"}'

*/
