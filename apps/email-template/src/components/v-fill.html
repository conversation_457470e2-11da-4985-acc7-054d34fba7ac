<script props>
  module.exports = {
    width: props.width || '600px',
    image: props.image || 'https://via.placeholder.com/600x400'
  }
</script>

<!--[if mso]>
<v:rect stroke="f" fillcolor="none" style="width: {{ width }}" xmlns:v="urn:schemas-microsoft-com:vml">
<v:fill type="frame" src="{{{ image }}}" />
<v:textbox inset="0,0,0,0" style="mso-fit-shape-to-text: true"><div><![endif]-->
<content />
<!--[if mso]></div></v:textbox></v:rect><![endif]-->
