<script props>
  module.exports = {
    width: props.width || '600px',
    height: props.height || '400px',
    image: props.image || 'https://via.placeholder.com/600x400'
  }
</script>

<!--[if mso]>
<v:image src="{{{ image }}}" style="width: {{ width }}; height: {{ height }};" xmlns:v="urn:schemas-microsoft-com:vml" />
<v:rect fill="f" stroke="f" style="position: absolute; width: {{ width }}; height: {{ height }};" xmlns:v="urn:schemas-microsoft-com:vml">
<v:textbox inset="0,0,0,0"><div><![endif]-->
<content />
<!--[if mso]></div></v:textbox></v:rect><![endif]-->
