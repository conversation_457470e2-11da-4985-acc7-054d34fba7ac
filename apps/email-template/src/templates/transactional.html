---
title: "Confirm your email address"
preheader: "Please confirm your email address in order to activate your account."
bodyClass: bg-slate-50
---

<x-main>
  <div class="bg-slate-50 sm:px-4 font-sans">
    <table align="center">
      <tr>
        <td class="w-[552px] max-w-full">
          <div class="my-12 sm:my-8 text-center">
            <a href="https://maizzle.com">
              <img src="images/maizzle.png" width="70" alt="Maizzle">
            </a>
          </div>

          <table class="w-full">
            <tr>
              <td class="p-12 sm:px-6 text-base text-slate-700 bg-white rounded shadow-sm">
                <h1 class="m-0 mb-6 text-2xl sm:leading-8 text-black font-semibold">
                  Hello,
                </h1>

                <p class="m-0 leading-6">
                  Is it you we're looking for?
                  <br>
                  <br>
                  Please confirm your email address by clicking the button below:
                </p>

                <x-spacer height="24px" />

                <x-button href="https://maizzle.com">
                  Confirm email address &rarr;
                </x-button>

                <x-divider space-y="32px" class="bg-slate-200" />

                <p class="m-0">
                  If you didn't sign up for Maizzle, you can safely ignore this email.
                  <br>
                  <br>
                  Thanks, <br />The Maizzle Team
                </p>
              </td>
            </tr>
            <tr role="separator">
              <td class="leading-12">&zwj;</td>
            </tr>
            <tr>
              <td class="text-center text-slate-600 text-xs px-6">
                <p class="m-0 mb-4 uppercase">
                  Powered by Maizzle
                </p>

                <p class="m-0 italic">
                  Quickly build HTML emails with Tailwind CSS
                </p>

                <p class="cursor-default">
                  <a href="https://maizzle.com/docs/" class="text-indigo-700 [text-decoration:none] hover:![text-decoration:underline]">Docs</a>
                  &bull;
                  <a href="https://github.com/maizzle" class="text-indigo-700 [text-decoration:none] hover:![text-decoration:underline]">Github</a>
                  &bull;
                  <a href="https://twitter.com/maizzlejs" class="text-indigo-700 [text-decoration:none] hover:![text-decoration:underline]">Twitter</a>
                </p>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  </div>
</x-main>
