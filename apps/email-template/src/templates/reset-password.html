---
title: 'Reset your password'
preheader: 'Please confirm to reset your account password.'
bodyClass: bg-email-base
---

<x-main>
  <div class="font-ibm text-neutrals-800 bg-email-base sm:px-4">
    <table align="center">
      <tr>
        <td class="w-[660px] max-w-full">
          <div class="my-10 px-8 text-left sm:my-6">
            <a href="https://dev--dsf-hrl.netlify.app/">
              <img src="images/logo.png" width="195" alt="Horizon Risk Labs"/>
            </a>
          </div>

          <table class="w-full">
            <tr>
              <td class="rounded bg-white p-8 text-base text-slate-700 shadow-sm sm:px-5">
                <p class="m-0 mb-5 leading-6 text-2xl font-semibold">Your single-use link</p>

                <p class="m-0 mb-5 leading-6">Hi <PERSON>, you or someone else requested to reset the password for
                  <span class="underline"><EMAIL></span> on Nodiens. To do this, click the button
                  below. Ignore this email if you
                  didn't request a password reset and consider contacting the site owner.</p>

                <x-spacer height="24px" />

                <center>
                  <x-button href="https://maizzle.com">Reset Password</x-button>
                </center>
              </td>
            </tr>
            <tr role="separator">
              <td>&zwj;
                <p class="text-xs text-center">
                  <a
                    href="https://www.nodiens.com/"
                    class="visited:text-black"
                    target="_blank"
                  >Nodiens.com</a>
                  is a website operated by Horizon Risk Labs Limited.
                </p>
                <p class="text-xs text-center">This email was sent to <span
                  class="underline"><EMAIL></span>
                </p>
                <p class="text-xs text-center">
                  <a class="text-xs text-black" href="https://www.nodiens.com/"
                     target="_blank">Nodiens.com</a>
                  |
                  <a class="text-xs text-black" href="https://dev--dsf-hrl.netlify.app/terms" target="_blank">Terms &
                    Privacy</a>
                </p>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  </div>
</x-main>
