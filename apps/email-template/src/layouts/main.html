<!DOCTYPE {{{ page.doctype || 'html' }}}>
<html lang="{{ page.language || 'en' }}" xmlns:v="urn:schemas-microsoft-com:vml">
  <head>
    <meta charset="{{ page.charset || 'utf-8' }}" />
    <meta name="x-apple-disable-message-reformatting" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="format-detection" content="telephone=no, date=no, address=no, email=no, url=no" />
    <meta name="color-scheme" content="light dark" />
    <meta name="supported-color-schemes" content="light dark" />

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:wght@400;500;600;700&display=swap"
      rel="stylesheet"
      media="screen"
    />
    <!--[if mso]>
      <noscript>
        <xml>
          <o:OfficeDocumentSettings xmlns:o="urn:schemas-microsoft-com:office:office">
            <o:PixelsPerInch>96</o:PixelsPerInch>
          </o:OfficeDocumentSettings>
        </xml>
      </noscript>
      <style>
        td,
        th,
        div,
        p,
        a,
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
          font-family: 'Segoe UI', sans-serif;
          mso-line-height-rule: exactly;
        }
      </style>
    <![endif]-->
    <if condition="page.title">
      <title>{{{ page.title }}}</title>
    </if>
    <style>
      {{{ page.css }}}
    </style>
    <stack name="head" />
  </head>
  <body
    class="m-0 p-0 py-10 w-full [word-break:break-word] [-webkit-font-smoothing:antialiased] {{ page.bodyClass || '' }}"
  >
    <if condition="page.preheader">
      <div class="hidden">
        {{{ page.preheader }}}
        <each loop="item in Array.from(Array(150))">&#8199;&#65279;&#847; </each>
      </div>
    </if>
    <div
      role="article"
      aria-roledescription="email"
      aria-label="{{{ page.title || '' }}}"
      lang="{{ page.language || 'en' }}"
    >
      <content />
    </div>
  </body>
</html>
