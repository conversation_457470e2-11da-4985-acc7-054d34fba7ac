/** @type {import('tailwindcss').Config} */
module.exports = {
  theme: {
    screens: {
      sm: { max: '600px' },
      xs: { max: '425px' },
    },
    extend: {
      colors: {
        brand: {
          50: '#FEF2F2',
          100: '#FFDFDF',
          200: '#F4AB8C',
          300: '#EE8054',
          400: '#F08B5E',
          500: '#ED723B', // Reddit
          600: '#E05611',
          700: '#D24F0D',
          800: '#C5490A',
          900: '#AC3C02',
        },
        'base-primary': {
          50: '#EEF6FF',
          100: '#B4E8FF', // Mood L-3
          200: '#81D8FF',
          300: '#4BC9FF', // Mood L-2
          400: '#1DBDFF',
          500: '#00B0FF', // STABLECOIN-COIN2
          600: '#00A3FF', // Main Mood/L-1
          700: '#008EE3',
          800: '#007DCF', // Telegram
          900: '#065CAD',
        },
        'accent-green': {
          50: '#F2FBF4',
          100: '#E1F7E7',
          200: '#C5EDCF',
          300: '#84E9A0', // Trust L-3
          400: '#4FBD6D', // Trust L-2
          500: '#3EA95B',
          600: '#2E8B47', // Main Trust/L-1
          700: '#037823', // DEX
          800: '#245732',
          900: '#1F482B',
        },
        'accent-red': {
          50: '#FFF0F0',
          100: '#FFDDDD',
          200: '#FFC0C0',
          300: '#FF9494',
          400: '#FF9494',
          500: '#FF2323', // STABLECOIN-COIN1
          600: '#FF0000', // CEX
          700: '#D70000',
          800: '#B10303',
          900: '#920A0A',
        },
        'accent-purple': {
          50: '#F5F3FF',
          100: '#EDE9FE',
          200: '#DDD6FE',
          300: '#C4B5FD',
          400: '#A78BFA',
          500: '#8B5CF6',
          600: '#7C3AED', // Market Cap
          700: '#6D28D9',
          800: '#5B21B6',
          900: '#4C1D95',
        },
        neutrals: {
          50: '#FBFBFB',
          100: '#F3F3F3',
          200: '#EBEBEB',
          300: '#C3C3C3', // Field
          400: '#7C7C7C',
          500: '#5B5B5B',
          600: '#2B344B',
          700: '#233053',
          800: '#1B1F2B', // Field-Dark
          900: '#000000', // Dark-Mode Background
        },
        error: '#eb1e36',
        'error-le': '#f5c6cb',
        // Based on old design system (dark mode)
        'primary-bg': '#FBFBFB',
        'secondary-bg-color': '#2a3657',
        // Based on old design system (light mode)
        'backdrop-color': '#f2f2f2',
        secondary: '#333',
        'secondary-bg': '#fff',
        // Based on old design system
        'ring-color': '#fbdfd3',
        'email-base': '#F7F7FA',
      },
      spacing: {
        screen: '100vw',
        full: '100%',
        0: '0',
        0.5: '2px',
        1: '4px',
        1.5: '6px',
        2: '8px',
        2.5: '10px',
        3: '12px',
        3.5: '14px',
        4: '16px',
        4.5: '18px',
        5: '20px',
        5.5: '22px',
        6: '24px',
        6.5: '26px',
        7: '28px',
        7.5: '30px',
        8: '32px',
        8.5: '34px',
        9: '36px',
        9.5: '38px',
        10: '40px',
        11: '44px',
        12: '48px',
        14: '56px',
        16: '64px',
        20: '80px',
        24: '96px',
        28: '112px',
        32: '128px',
        36: '144px',
        40: '160px',
        44: '176px',
        48: '192px',
        52: '208px',
        56: '224px',
        60: '240px',
        64: '256px',
        72: '288px',
        80: '320px',
        96: '384px',
      },
      borderRadius: {
        none: '0px',
        sm: '2px',
        DEFAULT: '4px',
        md: '6px',
        lg: '8px',
        xl: '12px',
        '2xl': '16px',
        '3xl': '24px',
      },
      boxShadow: {
        sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
        DEFAULT: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1)',
        md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1)',
        lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1)',
        xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)',
        '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
        inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.05)',
      },
      fontFamily: {
        sans: ['ui-sans-serif', 'system-ui', '-apple-system', '"Segoe UI"', 'sans-serif'],
        serif: ['ui-serif', 'Georgia', 'Cambria', '"Times New Roman"', 'Times', 'serif'],
        mono: ['ui-monospace', 'Menlo', 'Consolas', 'monospace'],
        ibm: ['IBM Plex Mono', 'monospace'],
      },
      fontSize: {
        0: '0',
        xxs: '11px',
        xs: '12px',
        '2xs': '13px',
        sm: '14px',
        '2sm': '15px',
        base: '16px',
        lg: '18px',
        xl: '20px',
        '2xl': '24px',
        '3xl': '30px',
        '4xl': '36px',
        '5xl': '48px',
        '6xl': '60px',
        '7xl': '72px',
        '8xl': '96px',
        '9xl': '128px',
      },
      letterSpacing: theme => ({
        ...theme('width'),
      }),
      lineHeight: theme => ({
        ...theme('width'),
      }),
      maxWidth: theme => ({
        ...theme('width'),
        xs: '160px',
        sm: '192px',
        md: '224px',
        lg: '256px',
        xl: '288px',
        '2xl': '336px',
        '3xl': '384px',
        '4xl': '448px',
        '5xl': '512px',
        '6xl': '576px',
        '7xl': '640px',
      }),
      minHeight: theme => ({
        ...theme('width'),
      }),
      minWidth: theme => ({
        ...theme('width'),
      }),
    },
  },
  corePlugins: {
    preflight: false,
    backgroundOpacity: false,
    borderOpacity: false,
    divideOpacity: false,
    placeholderOpacity: false,
    textOpacity: false,
  },
  plugins: [require('tailwindcss-mso'), require('tailwindcss-box-shadow'), require('tailwindcss-email-variants')],
}
