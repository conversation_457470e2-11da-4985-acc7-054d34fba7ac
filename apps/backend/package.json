{"name": "@dsf/nodiens-backend", "version": "1.0.0", "type": "module", "private": true, "scripts": {"lint": "eslint ."}, "dependencies": {"@elysiajs/node": "^1.2.6", "@sinclair/typebox": "^0.34.33", "elysia": "^1.2.25"}, "devDependencies": {"@types/node": "^22.14.1", "@typescript-eslint/parser": "^8.30.1", "app-types": "workspace:*", "eslint": "^9.24.0", "eslint-config-custom": "*", "tsconfig": "workspace:*", "tsx": "^4.19.3", "typescript": "^5.3.3"}}