{"$schema": "./node_modules/nx/schemas/nx-schema.json", "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/eslint.config.mjs"], "sharedGlobals": ["{workspaceRoot}/.github/workflows/ci.yml"]}, "targetDefaults": {"build": {"dependsOn": ["^build"], "outputs": ["{projectRoot}/.nuxt/**", "{projectRoot}/.output/**", "{projectRoot}/dist/**"], "cache": true}, "lint": {"cache": true}, "dev": {"cache": false}, "typecheck": {"cache": true}}, "defaultBase": "dev", "plugins": [{"plugin": "@nx/js/typescript", "options": {"typecheck": {"targetName": "typecheck"}, "build": {"targetName": "build", "configName": "tsconfig.lib.json", "buildDepsName": "build-deps", "watchDepsName": "watch-deps"}}}, {"plugin": "@nx/nuxt/plugin", "options": {"buildTargetName": "build", "serveTargetName": "serve", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}]}