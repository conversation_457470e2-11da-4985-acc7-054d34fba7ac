# Lefthook configuration for pre-commit hooks
# See: https://lefthook.dev/configuration/

pre-commit:
  parallel: true
  commands:
    prettier:
      glob: '*.{js,jsx,ts,tsx,vue,json,md}'
      exclude: 'package.json'
      run: pnpm prettier --write --check {staged_files}
      stage_fixed: true
    eslint:
      glob: '*.{js,jsx,ts,tsx,vue}'
      run: pnpm eslint --fix {staged_files}
      stage_fixed: true
