# Contributing Guidelines

By participating, you help us build better software together. This document outlines the process and standards we follow.

## Branch Naming Convention

All branch names **must** follow this format:

```
NOD-<issue-number>-<short-description>
```

For example:
- `NOD-42-add-login-functionality`
- `NOD-57-fix-navigation-bug`
- `NOD-13-update-documentation`

The issue number should refer to a ticket in our issue tracking system. The short description should be concise and use kebab-case (lowercase with hyphens).

## Development Workflow

1. Ensure your local main branch is up-to-date: `git checkout main && git pull upstream main`
2. Create your feature branch: `git checkout -b NOD-XX-your-feature-name`
3. Make your changes
4. Write tests for your changes
5. Run existing tests to ensure nothing breaks
6. Commit your changes (see Commit Message Guidelines)
7. Push to your fork: `git push origin NOD-XX-your-feature-name`
8. Open a Pull Request

## Pull Request Process

1. Ensure your PR description clearly describes the problem and solution
2. Link the PR to the relevant issue(s)
3. Include screenshots or animations for UI changes
4. Ensure all automated checks pass
5. Address review feedback promptly

## Code Review Guidelines

Code reviews are a crucial part of our development process to maintain code quality and share knowledge. All contributors should follow these guidelines:

### For Authors

1. **Keep changes focused**: PRs should address a single concern or feature
2. **Self-review**: Before requesting a review, examine your own code critically
3. **Provide context**: Explain the purpose and implementation details of your PR
4. **Respond to feedback**: Address all review comments, asking for clarification if needed
5. **Tests**: Include relevant tests for new functionality and bug fixes
6. **Documentation**: Update documentation when changing functionality

### For Reviewers

1. **Be timely**: Respond to review requests within 48 hours
2. **Be thorough**: Examine code structure, logic, performance, security, and edge cases
3. **Be respectful**: Provide constructive feedback; critique the code, not the author
4. **Focus on important issues**: Major design issues are more important than style preferences
5. **Ask questions**: Use questions rather than demands when the solution isn't obvious
6. **Explain reasoning**: Provide context for your feedback to help the author learn
7. **Approve when ready**: Don't withhold approval for minor issues

### Review Checklist

- Does the code follow our coding standards?
- Is the code maintainable and readable?
- Are there adequate tests with good coverage?
- Is error handling appropriately implemented?
- Does the code have adequate logging?
- Is the solution over-engineered or too complex?
- Are there potential security vulnerabilities?
- Are the naming conventions clear and consistent?
- Is the documentation updated?
- Does the code introduce potential performance issues?

## TypeScript Coding Standards

Our TypeScript code should follow these principles:

- Use English for all code and documentation
- Always declare types for variables and functions (parameters and return values)
- Avoid using `any`
- Create necessary types to represent domain concepts
- Use JSDoc to document public classes and methods
- Follow our naming conventions:
  - PascalCase for classes
  - camelCase for variables, functions, and methods
  - kebab-case for file and directory names
  - UPPERCASE for environment variables
- Write small, focused functions with a single purpose
- Follow SOLID principles and prefer composition over inheritance
- Prefer immutability and use `readonly` for data that doesn't change

## Commit Message Guidelines

We follow a structured format for commit messages:

```
<type>(<scope>): <subject>

<body>

<footer>
```

Types include:
- feat: A new feature
- fix: A bug fix
- docs: Documentation changes
- style: Formatting changes
- refactor: Code refactoring without functionality changes
- test: Adding or updating tests
- chore: Maintenance tasks

Example:
```
feat(auth): implement JWT authentication

Implement JSON Web Token authentication for API endpoints.
Includes token generation, validation, and refresh mechanisms.

Resolves: NOD-42
```

## Testing

- Write integration / E2E tests for all new functionality
- Maintain or improve test coverage
- Follow the AAA pattern (Arrange-Act-Assert)
- Name test variables clearly (inputX, mockX, actualX, expectedX)
- Mock external dependencies when appropriate

## Documentation

- Update README.md with any necessary information
- Document public APIs (through OpenAPI doc)
- Include comments for complex logic
- Keep documentation up-to-date with code changes

Thank you for contributing to the project! Your efforts help us create better software.
