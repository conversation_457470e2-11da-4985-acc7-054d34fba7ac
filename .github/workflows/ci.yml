name: CI

on:
  push:
    branches: ['main']
  pull_request:
    types: [opened, synchronize]
    paths-ignore:
      - 'LICENSE'
      - '.vscode/**'
      - '.github/CODEOWNERS'
      - '.github/FUNDING.yml'
      - '.github/ISSUE_TEMPLATE/**'
      - '.gitattributes'

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 10.8.1

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18.x
          cache: 'pnpm'

      - name: 📦 Install deps
        run: pnpm install
        env:
          PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: '1'

      - name: 💪 Lint
        run: pnpm lint

  typecheck:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 10.8.1

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18.x
          cache: 'pnpm'

      - name: 📦 Install deps
        run: pnpm install
        env:
          PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: '1'

      - name: 🔗 Typecheck
        run: pnpm typecheck

  # test:
  #   runs-on: ${{ matrix.os }}

  #   strategy:
  #     matrix:
  #       node: [18.x]
  #       os: [ubuntu-latest]
  #     fail-fast: false

  #   steps:
  #     - uses: actions/checkout@v3

  #     - name: Install pnpm
  #       uses: pnpm/action-setup@v2

  #     - name: Set node ${{ matrix.node }}
  #       uses: actions/setup-node@v3
  #       with:
  #         node-version: ${{ matrix.node }}
  #         cache: pnpm

  #     - name: Setup
  #       run: npm i -g @antfu/ni

  #     - name: Install
  #       run: nci
  #       env:
  #         PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: "1"

  #     - name: Build
  #       run: nr build

  #     - name: Test
  #       run: nr test
