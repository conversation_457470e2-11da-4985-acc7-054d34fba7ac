# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# compiled output
dist
tmp
out-tsc
out
build
.output

# dependencies
node_modules
.pnp
.pnp.js

# IDEs and editors
.zed
.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# misc
coverage
.sass-cache
connect.lock
libpeerconnection.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
testem.log
.DS_Store
*.pem


# ignore other lock files other than pnpm-lock.yaml
deno.lock
package-lock.json
yarn.lock
bun.lockb
bun.lock

# local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# storybook
storybook-static

# cache
.nuxt
.next
.turbo
.netlify
.vercel
.nx